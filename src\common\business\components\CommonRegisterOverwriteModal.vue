<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="common-register-modal"
    >
        <h1 class="common-register-modal__title">
            检测手机号已注册<br>
            请使用手机号登录珍爱APP！
        </h1>

        <!--<div
            class="common-register-modal__btn common-register-modal__btn-continue"
            v-if="registerResult.MANUAL_OVERWRITE_ACCOUNT.value === validateAccountResult.type"
            @click="handleSelectOverwrite"
            :style="{color: styleConfig.confirmButtonColor, background: styleConfig.confirmButtonBgColor}"
        >
            <template v-if="textConfirm.overwirteText">
                {{ textConfirm.overwirteText }}
            </template>
            <template v-else>
                <div>继续</div>
                <div>（原有帐号的资料将被覆盖）</div>
            </template>
        </div>-->

        <div
            :style="{color: styleConfig.confirmButtonColor, background: styleConfig.confirmButtonBgColor}"
            class="common-register-modal__btn"
            @click="handleSelectOrigin"
        >
            登录原有帐号
        </div>

        <div
            class="common-register-modal__cancel"
            :style="{ color: styleConfig.cancleButtonColor}"
            @click="handleCloseModal"
        >
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

import { registerResult } from '@/common/config/register-dictionary';
import { reportKibana } from "@/common/utils/report";

export default {
    name: 'CommonRegisterOverwriteModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        validateAccountResult: {
            type: Object,
            default: () => {return {};}
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF'
            }
        },
        handleCancle: {
            type: Function,
            default: () => {}
        },
        textConfirm: {
            type: Object,
            default: {
                overwirteText: null,
            }
        }
    },
    data() {
        return {
            registerResult
        };
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    reportKibana(this.pageType, 310, '已注册提醒弹窗-访问');
                }
            },
            immediate: true,
        },
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        handleSelectOverwrite() {
            reportKibana(this.pageType, 312, '已注册提醒弹窗-覆盖注册按钮点击');
            this.$emit('select-overwrite');
        },
        handleSelectOrigin() {
            reportKibana(this.pageType, 311, '已注册提醒弹窗-登录按钮点击');
            this.$emit('select-origin');
        },
        handleCloseModal() {
            this.handleCancle();
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.common-register-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__btn {
        @include flex-center(column);
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #767DFF;
        border-radius: 44px;
        margin-bottom: 28px;

        &-continue {
            padding-top: 3px;
            > div:last-child {
                font-size: 24px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #767DFF;
        text-align: center;
    }
}
</style>
