<template>
    <div class="pre-gender">
        <div class="bg"></div>
        <div class="btn">
            <div
                class="btn-item male"
                @click="goNext(0)"
            >
            </div>
            <div
                class="btn-item female"
                @click="goNext(1)"
            >
            </div>
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { PAGE_TYPE } from "../config";

export default {
    name:"PreGender",
    mounted(){
        this.$report(3, "性别选择访问");
    },
    methods:{
        goNext(val){
            if(val === 0){
                this.$report(3, "性别选择-男");
            } else {
                this.$report(3, "性别选择-女");
            }

            const params = {
                key: "gender",
                value: val
            };
            setLocalRegisterForm(params, PAGE_TYPE);

            this.$router.push({
                path:'/quiz/0'
            });
        }
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.pre-gender{
    height: 100vh;
    overflow: hidden;
    background-color: #0d141a;
    .bg{
        width: 750px;
        height: 1450px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221121/1669028268999_241964_t.png");
    }

    .btn{
        @include flex-center(row, space-between, center);
        position: fixed;
        top: 1028px;
        left: 50%;
        transform: translateX(-50%);

        &-item{
            width: 212px;
            height: 88px;
        }

        > .male{
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668154141472_97641_t.png");
        }

        > .female{
            margin-left: 64px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668154148572_83358_t.png");
        }
    }
}
</style>