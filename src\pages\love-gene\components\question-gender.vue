<template>
    <question-panel
        title="您的性别是"
        :src="cmsConfig.questionBodyImg1"
        :height="590">
        <div class="question-gender">
            <div
                v-for="(item, index) in list"
                :key="index"
                class="question-gender__item"
                @click="click(item.value, index)">
                <div :class="getItemClass(item)"/>
                <div>{{ item.label }}</div>
            </div>
        </div>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-gender",
    components: {
        QuestionPanel,
    },
    computed: {
        ...mapState([
            'registerForm',
            'cmsConfig',
        ]),
    },
    data() {
        return {
            inited: false,
            list: [{
                active: false,
                label: '男士',
                value: 0,
                normalClass: 'question-gender__item-img--male',
                activeClass: 'question-gender__item-img--male-active',
                activeAnimationClass: 'question-gender__item-img--male-active-animation',
            }, {
                active: false,
                label: '女士',
                value: 1,
                normalClass: 'question-gender__item-img--female',
                activeClass: 'question-gender__item-img--female-active',
                activeAnimationClass: 'question-gender__item-img--female-active-animation',
            }],
        }
    },
    created() {
        let changeTime = 0;

        this.$watch('registerForm.gender', (value) => {
            if (changeTime >= 1) {
                this.inited = true;
            }

            changeTime++;

            this.list = [{
                active: value === 0,
                label: '男士',
                value: 0,
                normalClass: 'question-gender__item-img--male',
                activeClass: 'question-gender__item-img--male-active',
                activeAnimationClass: 'question-gender__item-img--male-active-animation',
            }, {
                active: value === 1,
                label: '女士',
                value: 1,
                normalClass: 'question-gender__item-img--female',
                activeClass: 'question-gender__item-img--female-active',
                activeAnimationClass: 'question-gender__item-img--female-active-animation',
            }]
        }, {
            immediate: true,
        });
    },
    methods: {
        ...mapMutations([
            'setRequirement',
            'setRegisterForm',
        ]),
        getItemClass(item) {
            const result = [ 'question-gender__item-img', item.normalClass ];
            if (item.active) {
                const finalClass = this.inited ? item.activeAnimationClass : item.activeClass;
                result.push(finalClass);
            }

            return result;
        },
        click(value) {
            if (value === 0) {
                this.$report(3, '问题页1-点击“性别男”');
            } else {
                this.$report(4, '问题页1-点击“性别女”');
            }

            if (this.lock) {
                return;
            }

            this.lock = true;

            this.setRegisterForm({
                key: 'gender',
                value
            });

            this.setRequirement({
                tags: [],
            });

            setTimeout(() => {
                this.lock = false;

                this.$router.push({
                    path: "/questions/1",
                });
            }, 1000);
        },
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

$male-normal-img: '../assets/images/avatar-male.png';
$male-active-img: '../assets/images/avatar-male-active.png';
$female-normal-img: '../assets/images/avatar-female.png';
$female-active-img: '../assets/images/avatar-female-active.png';

@mixin keyframes($animation-name, $normal-img-url, $active-img-url) {
    @keyframes #{$animation-name} {
        20% {
            background-image: url($active-img-url);
        }
        40% {
            background-image: url($normal-img-url);
        }
        60% {
            background-image: url($active-img-url);
        }
        80% {
            background-image: url($normal-img-url);
        }
        90% {
            background-image: url($active-img-url);
        }
        100% {
            background-image: url($active-img-url);
        }
    }
}

@include keyframes(male-active-animation, $male-normal-img, $male-active-img);
@include keyframes(female-active-animation, $female-normal-img, $female-active-img);

.question-gender {
    margin-top: 108px;
    width: 100%;

    @include flex-center(row, space-between);
    padding: 0 100px;
    font-size: 28px;

    &__item {
        text-align: center;

        &-img {
            width: 160px;
            height: 160px;
            margin-bottom: 32px;
            @include set-img($male-normal-img);

            &--male {
                @include set-img($male-normal-img);
            }

            &--female {
                @include set-img($female-normal-img);
            }

            &--male-active {
                @include set-img($male-active-img);
            }

            &--female-active {
                @include set-img($female-active-img);
            }

            &--male-active-animation {
                animation: male-active-animation 0.8s forwards;
            }

            &--female-active-animation {
                animation: female-active-animation 0.8s forwards;
            }
        }
    }
}
</style>
