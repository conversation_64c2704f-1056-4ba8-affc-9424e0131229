<template>
    <div class="box">
        <div class="contentBox">
            <div class="examBgBox">
                <div
                    v-for="(item) in subjectList"
                    :key="item.problem"
                >
                    <div class="title">
                        {{ item.problem }}
                    </div>
                    <img
                        :src="item.imgUrl"
                        v-if="item.imgUrl"
                        class="subjectImg"
                    />
                    <ul class="item_container">
                        <li
                            v-for="optionOne in item.options"
                            :key="optionOne.labelName"
                            @click="selectAnswer(optionOne)"
                            class="item"
                            :class="{active: currentAnswer === optionOne.label}"
                        >
                            <div>{{ optionOne.labelName }}：</div>{{ optionOne.label }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

  <script>
import navApp from '../../components/nav.vue';
import {subjectList} from './data.js';
import { PAGE_TYPE } from '../../config';
import { setLocalRegisterForm } from '@/common/business/utils/localRegisterForm';
import { storage as Storage } from "@/common/utils/storage";
import debounce from 'lodash-es/debounce';

export default {
    name: 'Exam',
    props: {
        index: {
            type: Number,
        }
    },
    data() {
        this.numCapital = ['零','一','二','三','四','五','六','七','八','九','十'];

        return {
            profileCompleteObj:{},
            viewpointIsComplete: false,
            uploadedPhoto: false,
            currentAnswer: '',
            exam_id: '',
            subjectList: '',
        };
    },
    watch: {
        index(val) {
            // this.$router.go(0);
            this.subjectList = [subjectList[val]];
            this.exam_id = `exam_${val}`;
            this.currentAnswer = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`)[this.exam_id] || '';
            this.report();
        }
    },
    created(){
        this.subjectList = [subjectList[this.index]];
        this.exam_id = `exam_${this.index}`;
        this.currentAnswer = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`)[this.exam_id] || '';
    },
    activated() {
        this.currentAnswer = '';
        this.viewpointIsComplete = false;
        this.uploadedPhoto = false;
        this.subjectList = [subjectList[this.index]];
        this.exam_id = `exam_${this.index}`;
        this.currentAnswer = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`)[this.exam_id] || '';
        this.report();
    },
    methods: {
        report() {
            if (this.index <= 8) {

                this.$report(`6${this.index}`,`题目${this.index + 1}页访问`);
            } else {
                this.$report(`70`,`题目10访问`);
            }

        },
        selectAnswer: debounce(function (item){
            if(this.currentAnswer && this.index + 1 < this.subjectList.length)return;
            this.currentAnswer = item.label;
            const params = {
                key: this.exam_id,
                value: item.label
            };
            setLocalRegisterForm(params, PAGE_TYPE);

            setTimeout(()=>{
                this.currentAnswer = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`)[this.exam_id] || '';
                this.$emit('go-next');

            },300);

        }, 600, {
            leading: true,
            trailing: false,
        }),

        submitExam(){
            if(!this.uploadedPhoto){
                Z.client.invoke('ui', 'openAppView', {page:195, params: { source:1 } });
            } else {
                this.$router.push({ path: '/exam-result', query: { source: 1, }, });
            }
        },
    },
    components: {
        navApp,
    },
};
  </script>

  <style scoped>
  .box{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .contentBox{
    padding: 14px 10px;
    flex: 1;

  }
  .examBgBox{
    height: 100%;
    width: 100%;
    overflow: hidden;
    padding: 0 26px;
    background-size: 100% 100%;
  }
  .numBox{
    margin-top: 54px;
  }
  .num{
    font-size: 13px;
    color: #9395A4;
  }
  .capNum{
    font-weight: 400;
    font-size: 16px;
    color: #111111;
    margin-left: calc(50% - 56px);
  }
  .title{
    margin-bottom: 80px;
    font-weight: 500;
    font-size: 36px;
    color: #333333;
    line-height: 50px;
  }
  .liBox{
    margin-top: 30px;
  }
  .liBox li{
    margin-bottom: 16px;
    border: 1px solid #B8BCCC;
    border-radius: 22px;
    padding: 12px 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #1A202C;
  }
  .subjectImg{
    margin-bottom: 30px;
    width: 383px;
    height: 384px;
  }
    .item_container {
    }
    .item{
        height: 88px;
        background: #ffff;
        border-radius: 45px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding-left: 28px;
        font-size: 24px;
        text-size-adjust: inherit;
        text-align: left;
        margin-bottom: 30px;
        border: 1px solid #B8BCCC;
    }
    .active{
        background: #fff0f0;
        border: none;
        color: #D7204A;
    }

  </style>
