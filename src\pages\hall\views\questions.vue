<template>
    <love-page>
        <div class="questions">
            <!-- 头图部分 -->
            <z-image
                class="questions-banner"
                :src="require('../assets/images/question-banner.png')"
                :width="730"
                :height="168"
            />
            <div class="questions-tip">{{ tipMap[currentQuestionIndex] }}</div>
            <question-progress :step-index="currentQuestionIndex" />

            <!-- 标题 -->
            <z-image
                v-if="currentQuestionIndex !== 1"
                class="questions-title"
                :src="titleImg.src"
                :width="titleImg.width"
                :height="76"
                block
            >
                {{ titleMap[currentQuestionIndex] }}
            </z-image>

            <component :is="currentQuestionComponent" />

            <simple-button
                :disable="!isChecked"
                class="questions-button"
                :width="670"
                background="#FFB900"
                @click="checkStatus"
            >
                {{ currentQuestionIndex === 3 ? "选好了，立即脱单" : "选好了" }}
            </simple-button>
        </div>
    </love-page>
</template>

<script>
import { mapState } from "vuex";
import { createPage } from "@/common/framework";
import QuestionProgress from "../components/question-progress";
import QuestionDrift from "../components/question-drift";
import QuestionCityGender from "../components/question-city-gender";
import QuestionTag from "../components/question-tag";
import QuestionSelection from "../components/question-selection";

const tipMap = [
        "只选你喜欢的约会项目，开启脱单约会",
        "4个问题，精准锁定同频约会对象",
        "精准锁定同频约会对象，开启脱单约会",
        "最后一问，马上开启脱单约会"
    ],
    titleMap = [
        "选择感兴趣的1个约会",
        "",
        "你理想对象的标签",
        "当进入一段恋爱，你会考虑未来吗？"
    ],
    titleImgMap = [
        {
            src: require("../assets/images/question-title1.png"),
            width: 416
        },
        {
            src: require("../assets/images/question-title2.png"),
            width: 608
        }
    ],
    questionMap = {
        0: {
            accessPoint: 2,
            accessPointDesc: "问题页1访问",
            component: "QuestionDrift"
        },
        1: {
            accessPoint: 5,
            accessPointDesc: "问题页2访问",
            component: "QuestionCityGender"
        },
        2: {
            accessPoint: 12,
            accessPointDesc: "问题页3访问",
            component: "QuestionTag"
        },
        3: {
            accessPoint: 18,
            accessPointDesc: "问题页4访问",
            component: "QuestionSelection"
        }
    };

const QUESTION_LENGTH = 4;

export default createPage({
    name: "Questions",
    components: {
        QuestionProgress,
        QuestionDrift,
        QuestionCityGender,
        QuestionTag,
        QuestionSelection
    },
    data() {
        return {
            tipMap,
            titleMap,
            titleImgMap,
            currentQuestionComponent: "",
            currentQuestionIndex: -1, 
            // isChecked: false
        };
    },
    computed: {
        ...mapState(["requirement", "registerForm"]),
        backBtnVisible() {
            return this.currentQuestionIndex > 0;
        },

        titleImg() {
            return this.currentQuestionIndex === 3
                ? this.titleImgMap[1]
                : this.titleImgMap[0];
        },

        isChecked() {
            switch (this.currentQuestionIndex) {
                case 0:
                    return this.requirement.driftTag.text
                case 1:
                    return this.registerForm.gender !== -1 && this.registerForm.workCity
                case 2:
                    return this.requirement.tags.length >= 1
                case 3:
                    return this.requirement.selection
            }
        }
        
    },
    watch: {
        $route: {
            handler() {
                this.currentQuestionIndex = Number(this.$route.params.id);

                if (
                    this.$route.params.id !== "" &&
                    this.currentQuestionIndex < QUESTION_LENGTH
                ) {
                    this.currentQuestion =
                        questionMap[this.currentQuestionIndex];
                    this.init();
                    return;
                }

                this.$toast("链接错误");
            },
            immediate: true
        }
    },
    methods: {
        init() {
            this.$report(
                this.currentQuestionIndex + 2,
                `问题页${this.currentQuestionIndex + 1}访问`
            );
            this.currentQuestionComponent = this.currentQuestion.component;
        },
        checkStatus() {
            switch (this.currentQuestionIndex) {
                case 0:
                    if (!this.requirement.driftTag.text) {
                        return this.$toast("请选择答案");
                    }
                    break;
                case 1:
                    if (
                        this.registerForm.gender === -1 ||
                        !this.registerForm.workCity
                    ) {
                        return this.$toast("请选择答案再进入下一步");
                    }
                    break;
                case 2:
                    if (this.requirement.tags.length < 1) {
                        return this.$toast("请选择答案");
                    }
                    break;
                case 3:
                    if (!this.requirement.selection) {
                        return this.$toast("请选择答案");
                    }
                    break;
            }

            this.goNext();
        },
        goNext() {
            let path;
            if (this.currentQuestionIndex === 3) {
                path = "/transition";
            } else {
                path = `/questions/${this.currentQuestionIndex + 1}`;
            }

            this.$router.push({
                path
            });
        }
    },
});
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.questions {
    padding-bottom: 124px;
    @include flex-center(column, flex-start, center);

    &-banner {
        margin-top: 22px;
    }

    &-tip {
        margin-top: 12px;
        opacity: 0.49;
        font-size: 28px;
        color: #ffffff;
        letter-spacing: 12px;
        text-align: center;
    }

    &-title {
        align-self: flex-start;
        margin: 60px 0 0 40px;
        padding-right: 20px;
        font-weight: 700;
        font-size: 28px;
        color: #FE4F06;
        text-align: center;
        line-height: 76px;
    }

    &-button {
        margin-top: 184px;
    }
}
</style>
