<template>
    <canvas
        v-if="showRadar"
        ref="refRadar"
    ></canvas>
</template>
<script>
import { countScore } from "../config";

// 缩放比
const sr = window.innerWidth / 750;
// 设备像素比
const dpr = window.devicePixelRatio;
const r = sr * dpr;

export default {
    name: "Radar",
    props:{
        width:{
            type:Number,
        },
        height:{
            type:Number
        },
        showScore:{
            type:Boolean,
            default: false
        },
        radius:{
            type: Object
        }
    },
    data(){
        return {
            ctx:null, // 画布实例
            points:[], // 最终绘制点数组
            animatePoints:[], // 动态绘制点数组
            scores:[], // 四个维度分数
            showRadar: true, 
        };
    },
    computed:{
        // 画布中心坐标
        centerX(){
            return this.width * r/2;
        },
        centerY(){
            return this.height * r/2;
        },
        // 低分/中分/高分对应圆半径
        radiusSmall(){
            return this.radius.radiusSmall * r;
        },
        radiusMid(){
            return this.radius.radiusMid * r;
        },
        radiusBig(){
            return this.radius.radiusBig * r;
        },
    },
    mounted(){
        this.initCanvas(this.width,this.height);
        this.animateCanvas();
    },
    methods: {
        initCanvas(width, height) {
            const canvas = this.$refs.refRadar;
            if (canvas) {
                try{
                    // 解决图片模糊
                    // 在保证sr缩放的基础上，画布扩大dpr倍但css长宽不变，
                    // 这样所有的绘画都以dpr倍操作以保证充足的像素数，显示仍为css长宽(画布针对css框会自动缩放)
                    canvas.width = width * r;
                    canvas.height = height * r;
                    this.$refs.refRadar.style.width = width*sr+'px';
                    this.$refs.refRadar.style.height = height*sr+'px';
                    const ctx = canvas.getContext("2d");

                    // 初始化绘制点
                    this.getPoints();

                    // 初始化背景图
                    this.drawImg(
                        ctx,
                        'https://photo.zastatic.com/images/common-cms/it/20221122/1669098766394_728532_t.png',
                        0,0,
                        width*r,
                        height*r
                    );

                    this.ctx = ctx;
                }catch(e){
                    this.showRadar = false;
                }
            }
        },
        animateCanvas(){
            const increment = 0.8;
            if(this.animatePoints[0][1] > this.points[0][1]){
                this.animatePoints[0][1] -= increment * r;
            } 
            if(this.animatePoints[1][0] < this.points[1][0]){
                this.animatePoints[1][0] += increment * r;
            }
            if(this.animatePoints[2][1] < this.points[2][1]){
                this.animatePoints[2][1] += increment * r;
            }
            if(this.animatePoints[3][0] > this.points[3][0]){
                this.animatePoints[3][0] -= increment * r;
            }

            // 绘制多边形
            this.drawPolygon(
                this.ctx,
                this.animatePoints,
                'fill'
            );
            
            // 动画结束
            if( 
                this.animatePoints[0][1] <= this.points[0][1] 
                && this.animatePoints[1][0] >= this.points[1][0]
                && this.animatePoints[2][1] >= this.points[2][1]
                && this.animatePoints[3][0] <= this.points[3][0])
            {
                return this.finishCanvas();
            }

            window.requestAnimationFrame(this.animateCanvas);
        },
        finishCanvas(){
            // 绘制亮点
            this.drawLight(this.ctx);

            // 绘制描边
            this.drawBorder(this.ctx);

            // 绘制分数
            if(this.showScore){
                this.drawScore(this.ctx);
            }
        },
        getRelativePosition(position, offsetWidth, offsetHeight){
            return [
                [position[0][0] - offsetWidth, position[0][1] - offsetHeight],
                [position[1][0] - offsetWidth, position[1][1] - offsetHeight],
                [position[2][0] - offsetWidth, position[2][1] - offsetHeight],
                [position[3][0] - offsetWidth, position[3][1] - offsetHeight],
            ];
        },
        getRadius(type, score){
            // 计算分数对应的落点位置(半径)
            let radius = this.radiusMid;
            const switchRadius = (min,max)=>{
                if(score > max){
                    radius = this.radiusSmall;
                } else if(score < min){
                    radius = this.radiusBig;
                }
            };
            
            // 不同维度对应不同的(分数->半径)映射规则
            switch(type){
            case 'SAFETY':
                switchRadius(12,15);
                break;
            case 'ALONE':
                switchRadius(10,15);
                break;
            case 'ACTION':
                switchRadius(11,14);
                break;
            case 'REGISTER':
                switchRadius(8,12);
                break;
            }

            return radius;
        },
        getPoints(){
            const centerX = this.centerX,
                  centerY = this.centerY;

            const {action, alone, register, safety} = countScore();
            let top = [centerX, centerY - this.getRadius('SAFETY',safety)],
                right = [centerX + this.getRadius('ALONE',alone), centerY],
                bottom = [centerX, centerY + this.getRadius('ACTION',action)],
                left = [centerX - this.getRadius('REGISTER',register), centerY];

            this.scores = [105 - safety, 105 - alone, 105 - action, 103 - register];

            this.animatePoints = [
                [centerX,centerY],
                [centerX,centerY],
                [centerX,centerY],
                [centerX,centerY]
            ];
            this.points = [
                top,
                right,
                bottom,
                left
            ];
        },
        // 绘制圆角矩形
        drawRoundedRect(ctx, x, y, width, height, radius, beginColor, endColor){
            // 设置渐变填充
            const lineargradient = ctx.createLinearGradient(x,y,x,y+height);
            lineargradient.addColorStop(0,beginColor);
            lineargradient.addColorStop(1,endColor);
            ctx.fillStyle = lineargradient;

            ctx.beginPath();
            ctx.moveTo(x, y + radius);
            ctx.lineTo(x, y + height - radius);
            ctx.quadraticCurveTo(x, y + height, x + radius, y + height);
            ctx.lineTo(x + width - radius, y + height);
            ctx.quadraticCurveTo(x + width, y + height, x + width, y + height - radius);
            ctx.lineTo(x + width, y + radius);
            ctx.quadraticCurveTo(x + width, y, x + width - radius, y);
            ctx.lineTo(x + radius, y);
            ctx.quadraticCurveTo(x, y, x, y + radius);
            ctx.fill();
        },
        drawImg(ctx, src, x, y, w, h){
            const img = new Image();   
            img.onload = ()=>{
                ctx.drawImage(img, x, y, w, h);  
            };
            img.src = src; 
        },
        drawLight(ctx){
            const rectWidth = 8*r ,
                  rectHeight = 8*r ,
                  rectRadius = 4*r;
            
            const relativePosition = this.getRelativePosition(this.points, 0.5 * rectWidth, 0.5 * rectHeight),
                  params = [rectWidth, rectHeight, rectRadius, '#fff', '#fff'];
            ctx.save();
            ctx.shadowBlur = 8*r;
            ctx.shadowColor = "#fff";

            relativePosition.forEach(item=>{
                this.drawRoundedRect(ctx, ...item, ...params);
            });
            ctx.restore();
        },
        drawBorder(ctx){
            ctx.strokeStyle = "#fff";
            this.drawPolygon(
                ctx,
                this.animatePoints,
                'stroke'
            );
        },
        drawScore(ctx){
            const centerX = this.centerX,
                  centerY = this.centerY,
                  rectWidth = this.radiusBig * 0.3 ,
                  rectHeight = this.radiusBig * 0.18 ,
                  rectRadius = rectWidth * 0.4;

            const position = [
                [centerX , centerY - this.radiusBig],
                [centerX + this.radiusBig, centerY],
                [centerX , centerY + this.radiusBig],
                [centerX - this.radiusBig, centerY],
                
            ];

            const relativePositionRect = this.getRelativePosition(position, 0.5 * rectWidth, 0.5 * rectHeight),
                  paramsRect = [rectWidth, rectHeight, rectRadius, 'rgb(153,244,255)', 'rgb(22,171,251)'];
            relativePositionRect.forEach((item) => {
                this.drawRoundedRect(ctx, ...item, ...paramsRect);
            });

            const relativePositionText = this.getRelativePosition(position, 0, -0.25 * rectHeight);
            ctx.font = `${12*r}px serif`;
            ctx.fillStyle = "#fff";
            ctx.textAlign = 'center';
            relativePositionText.forEach((item,index)=>{
                ctx.fillText(this.scores[index], item[0], item[1]);
            });
            
        },
        drawPolygon(ctx, points, type){
            // 设置渐变填充
            const lineargradient = ctx.createLinearGradient(0,0,0,this.height*r);
            lineargradient.addColorStop(0,'rgba(153,244,255,0.87)');
            lineargradient.addColorStop(1,'rgba(22,171,251,0.67)');
            ctx.fillStyle = lineargradient;
                    
            ctx.beginPath();
            ctx.moveTo(points[0][0], points[0][1]);
            points.forEach((item)=>{
                ctx.lineTo(item[0], item[1]);
            });
            if(type==="fill"){
                ctx.fill();
            } else if(type==="stroke"){
                ctx.closePath();
                ctx.stroke();
            }
            
        }
    }
};
</script>
