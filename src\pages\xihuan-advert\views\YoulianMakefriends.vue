<template>
    <div class="collection-wrapper" @click="goMin">
        <img class="bg-img" src="https://photo.zastatic.com/images/common-cms/it/20240828/1724826908498_833261.png" alt="">
        <img class="button" src="https://photo.zastatic.com/images/common-cms/it/20240828/1724810798568_822132.png" alt="">
    </div>
</template>

<script>

import Api from "@/common/server/base";
export default {
    data() {
        return {
            query: '',
            channelId: '',
            subChannelId: '',
        };
    },
    async created(){},
    mounted(){
        this.channelId = this.$route.query.channelId;
        this.subChannelId = this.$route.query.subChannelId;

        // 打桩
        Z.tj.kibanaV2({
            resourceKey: '本地交友群',
            accessPoint: 7,
            accessPointDesc: '前置H5页面曝光',
        });
    },
    methods:{
        async goMin () {
            Z.tj.kibanaV2({
                resourceKey: '本地交友群',
                accessPoint: 8,
                accessPointDesc: '前置H5页面点击',
            });
            const params = {
                path: 'pages/activity/make-friends-group/index',
                query: `channelId=${this.channelId}&subChannelId=${this.subChannelId}`,
            };
            let resData = await Api.goYouLianMini(params);
            if (resData.code === 0) {
                location.href = resData.data;
            }
        }
    }
};
</script>

<style lang="scss" scoped>

@keyframes scaleDraw {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
.collection-wrapper{
    width: 100vw;
    height: 100vh;
    background-color: #ffb6d6;
    position: relative;
    display: flex;
    justify-content: center;
    .bg-img {
        width: 100%;
        height: 1448px;
        object-fit: cover;
        z-index: 1;
        position: relative;
    }
    .button {
        position: fixed;
        z-index: 2;
        bottom: 144px;
        width: 394px;
        height: 120px;
        animation-name: scaleDraw;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
        animation-duration: 2s;
    }
}

</style>
