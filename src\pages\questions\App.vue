<template>
    <div
        id="app"
        :style="{ backgroundImage: `url(${bg})` }"
    >
        <keep-alive>
            <router-view />
        </keep-alive>
        <img
            src="https://photo.zastatic.com/images/common-cms/it/20211231/1640950066667_777666_t.jpg"
            style="display: none;"
        />
    </div>
</template>

<script>
import "@/common/styles/reset.scss";
import "@/common/styles/flex.scss";
import { openPlayer } from "./lib/common/player.js";
import Player from "./components/player.vue";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';

export default {
    name: "App",
    data() {
        return {
            bg: ''
        };
    },
    created() {
        this.bg = window.localStorage.getItem("bg");
        const policy = window.localStorage.getItem("policy");
        if (policy) {
            window.localStorage.removeItem("policy");
            this.$router.replace("/login");
            return;
        }
        if (!["/index"].includes(this.$route.path)) {
            this.$router.replace("/index");
        }
        this.$storage.removeFromStorage("__ques__");
        this.$bus.$on("changeBg", data => {
            this.bg = data.bg;
            openPlayer(Player, { musicUrl: data.musicUrl, autoplay: true });
            window.localStorage.setItem("musicUrl", data.musicUrl);
            window.localStorage.setItem("bg", data.bg);
        });

        document.body.addEventListener("focusin", () => {
            document.documentElement.style.overflow = "hidden";
            document.body.style.overflow = "hidden";
        });

        // 【老注册页】用于后台自然流量注册统计
        (function setAbtParams() {
            var abtParams = Z.cookie.get("abt_params");

            try {
                var arr = abtParams.split("|");
                if (arr.length !== 5) {
                    abtParams = "";
                }
            } catch (e) {
                console.log(e);
            }

            if (!abtParams) {
                var pageKey = "za_m_testQustionForm", // 根据不同业务key写入
                    planId = 0,
                    schemeId = 0;
                var channelId = Z.getParam("channelId") || 0;
                var subChannelId = Z.getParam("subChannelId") || 0;
                var tmp = [pageKey, channelId, subChannelId, planId, schemeId];
                Z.cookie.set(
                    "abt_params",
                    tmp.join("|"),
                    ".zhenai.com",
                    "/",
                    24 * 1
                );
            }
        })();
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            1, // 记录点
            "测试题首页曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            3000, // 记录点
            "首页访问（监控）", // 点描述
        );
        judgeIfInToutiaoIos();
    }
};
</script>

<style lang="scss">
html,
body,
#app {
    height: 100%;
    overflow: hidden;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-attachment: fixed;
    animation: fadeIn .5s ease-in;
}
div.van-field__right-icon {
    margin-right: -8px;
}
 @keyframes fadeIn {
    from {
      opacity: .5;
    }
    to {
      opacity: 1;
    }
  }
</style>
