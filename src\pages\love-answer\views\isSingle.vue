<template>
    <div class="quiz">
        <!-- <div :class="{top:true, hidden_mis: true}">
            <img src="https://photo.zastatic.com/images/common-cms/it/20230812/1691829504892_433174_t.png" alt="" :class="{music:true}"></img>
        </div> -->
        <div class="panel">
            <div class="panel-title">请问你是否单身？</div>
            <div
                class="item"
                @click="goNext(0)"
                :class="val === 0?'active':''"
            >
            单身
            </div>
            <div
                class="item"
                @click="goNext(1)"
                :class="val === 1?'active':''"
            >
            非单身
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name:"IsSingle",
    data(){
        return{
            val: ''
        };
    },
    // mounted(){
    //     this.$report(19,'询问单身页访问');
    // },
    activated(){
        this.$report(19,'询问单身页访问');
    },
    methods:{
        goNext(val){
            if(val === 0){
                this.val = 0;
                sessionStorage.setItem('loveAnswerIsSingle', 0)
                // 单身
                this.$router.push({
                    path:'/register/0'
                });
                this.$report(20,'询问单身页-选择单身');

            } else {
                this.val = 1;
                sessionStorage.setItem('loveAnswerIsSingle', 1)
                // this.$router.push({
                //     path:'/register/0'
                // });
                // 非单身
                // 跳转到报告结果页面 result
                this.$router.push({
                    name:'Result'
                });
                this.$report(21,'询问单身页-选择非单身');
            }

            // const params = {
            //     key: "gender",
            //     value: val
            // };
            // setLocalRegisterForm(params, '恋爱人格测试');

        }
    }

};
</script>

<style scoped lang="scss">
.quiz{
    // width: 100vw;
    // height: 100vh;
    // background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825084549_711274_t.png');
    // background-repeat: no-repeat;
    // margin:0px;
    // background-size:100% 100%;
    // background-attachment:fixed;
    // display: flex;
    // align-items: center;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

    .panel{
        padding: 0 88px;
        width: 99vw;
        height: 83vh;
        background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825172826_472496_t.png');
        background-repeat: no-repeat;
        margin: 0;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-self: center;
        font-family: 'love';
        // margin-top: 128px;
        .sreach{
            width: 300px;
            height: 300px;
            object-fit: contain;
            margin-top: 145px;

        }
        .search_text{
            margin-top: 35px;
            color: #8a54f8;
            font-family: 'love';
            font-size: 45px;
            text-align: center;
            margin-bottom: 34px;

        }
        .success{
            font-weight: 600;
            height: 48px;
            font-size: 26px;
            text-align: center;
            color: #2E2E2E ;
            letter-spacing: 4px;
        }
        .text_dialog{
            width: 540px;
            height: 226px;
            object-fit: contain;
        }
        .lock{
            width: 575px;
            height: 100px;
            background: linear-gradient(180deg,#5243FE, #9A55F0);
            display:flex;
            align-items: center;
            justify-content: center;
            font-family: 'love';
            line-height: 54px;
            font-size: 34px;
            color: #FFFFFF;
            letter-spacing: 4px;
            box-shadow: 0 3px 3px 0 #bd92eb66;
            border-radius: 10px;
            margin-top: 60px;

        }

    }
    .top{
        position: fixed;
        top: 0px;
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 34px 25px 0 25px;
    }
    .panel-back{
        width: 147px;
        height: 56px;

    }
    .music{
        width: 74px;
        height: 74px;
    }
    .hidden_mis{
        justify-content: flex-end;
    }
    .flex_box{
        display: flex;
        font-weight: Oblique;
        height: 27px;
        font-size: 20px;
        text-align: left;
        color: #8A54F8 ;
        letter-spacing: 4px;
    }
    .panel-title{
        margin-top: 110px;
        font-weight: 400;
        line-height: 1.2;
        font-family: 'love';
        font-size: 52px;
        text-align: left;
        color: #251A34;
        letter-spacing: 4px;
        margin-bottom: 130px;
    }
    .item{
        height: 89px;
        width: 395px;
        background-color: #A98CE6;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 45px;
        font-family: 'love';

        line-height: 48px;
        font-size: 42px;
        text-align: center;
        color: #FFFFFF ;
        letter-spacing: 4px;
        box-shadow: 0 3px 3px 0 #bd92eb66;

    }
    .active{
        background: linear-gradient(180deg,#5243FE, #9A55F0)
    }



}
</style>
