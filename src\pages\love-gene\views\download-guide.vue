<template>
    <love-page
        class="download-guide">
        <matching-swiper @receive-data="receiveAvatars"/>
        <z-image
            class="download-guide__list"
            :src="require('../assets/images/download-guide-bg.png')"
            :width="672"
            :height="730">
            <div class="download-guide__list-box">
                <div
                    v-for="(item, index) in list"
                    :key="index"
                    @click="listClickDownload"
                    class="download-guide__item">
                    <div class="download-guide__item-left">
                        <div class="download-guide__item-dot"/>
                        <z-avatar
                            :src="item.avatar"
                            :border-width="2"
                            border-color="#fff"
                            :size="100"/>
                        <div class="download-guide__item-name-box">
                            <div>{{ item.name }}</div>
                            <div>{{ item.greet }}</div>
                        </div>
                    </div>
                    <div class="download-guide__item-right">
                        {{ index > 1 ? '1分钟前' : '刚刚' }}
                    </div>
                </div>
            </div>
        </z-image>

        <div class="download-guide__button-container">
            <!--  不能使用 simple-button 的 fix-bottom，因为这个按钮现在使用到动画效果，在某些浏览器上 fixed 元素加动效会出问题 -->
            <simple-button
                ref="btn"
                :width="368"
                background="#767DFF"
                @click="buttonClickDownload">
                去回应
            </simple-button>
        </div>

        <download-guide-modal v-model="modalVisible"/>
    </love-page>
</template>

<script>
import { mapState } from "vuex";
import { downloadApp, openApp, visibilityChangeDelay } from "@/common/utils/download_app";
import MatchingSwiper from "@/pages/love-gene/components/matching-swiper";
import ZAvatar from "@/common/components/z-avatar";
import DownloadGuideModal from "@/pages/love-gene/components/modal/download-guide-modal";
import { createPage } from '@/common/framework';

export default createPage({
    name: 'download-guide',
    visitReport: {
        accessPoint: 25,
        accessPointDesc: '引导下载页访问',
    },
    components: {
        MatchingSwiper,
        ZAvatar,
        DownloadGuideModal,
    },
    data() {
        return {
            modalVisible: false,
            list: [],
            greets: this.$z_.shuffle([
                'hi，你也在附近？',
                '什么情况，这么快就匹配到了？',
                '我好像离你那很近',
                '咦，怎么在这里遇到你',
                '你好呀，听说我是你的理想型',
                '你怎么不回我信息',
                '刚一打开就看见你了',
                '我好像之前见过你',
                '在干嘛呢',
                '哇哦居然是你',
                '我刚看了你的资料，有空聊聊天吗',
                '我在想要怎么跟你打招呼',
                '你也在匹配理想型？',
                '我好像匹配到你了',
                '今天是个好日子',
            ]),
            maleNames: this.$z_.shuffle([
                'Mr jie',
                'Joe',
                '皮尔斯',
                'VO',
                '史努比',
                'AIhoyeung',
                '锤子',
                'Karp',
                '大鹏_ZHAO',
                '清风徐来。',
                '小王小王',
                '疑问句',
                '向前',
                'RUAN',
                '秦而已',
            ]),
            femaleNames: this.$z_.shuffle([
                '如',
                '白白',
                '耶嘿~~',
                'TATA。',
                '草又莓子',
                'Jessi',
                '不加糖',
                'lingling',
                '仙女不仙',
                'cici',
                '橘子汽水',
                '小红帽',
                '鲸落',
                '笨蛋美女',
                '伊诺呀',
            ]),
        }
    },
    computed: {
        ...mapState([
            'registerForm',
            'cmsConfig',
        ]),
    },
    created() {
        this.checkRedirect();
    },
    methods: {
        async receiveAvatars(avatars) {
            const names = this.registerForm.gender === 1 ? this.maleNames : this.femaleNames;
            this.list = avatars.map((item, index) => {
                return {
                    avatar: item,
                    name: names[index],
                    greet: this.greets[index],
                }
            });
        },
        listClickDownload() {
            this.$report(27, '引导下载页访问-消息列表点击');

            if (this.lock) {
                return;
            }

            this.goDownload();
        },
        buttonClickDownload() {
            if (this.lock) {
                return;
            }

            this.$report(26, '引导下载页访问-回应按钮点击');

            this.lock = true;

            const el = this.$refs.btn.$el;

            el.classList.add('download-guide--shake');
            setTimeout(() => {
                el.classList.remove('download-guide--shake');
                this.lock = false;
                this.goDownload();
            }, 800);
        },
        goDownload() {
            if (this.cmsConfig.downloadStatus === 1) {
                this.$utils.handleDownload();
                return;
            }

            this.modalVisible = true;
        },
        checkRedirect() {
            const registerFinished = this.$storage.getItem('registerFinished');

            if (!registerFinished) {
                this.$router.push({
                    path: '/',
                });
                return;
            }

            this.$storage.removeItem('registerFinished');
            this.$storage.removeItem('cachedRequirement');
            this.$storage.removeItem('cachedFormItems');
            this.$storage.removeItem('cachedRegisterForm');
            this.$storage.removeItem('cachedBirthday');
            this.$storage.removeItem('cachedWorkCity');
        },
    }
})
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.download-guide {
    padding: 62px 0 150px;

    &__list {
        @include relative-center();
        margin-top: 24px;
        margin-bottom: 24px;
        padding: 52px 32px;

        &-box {
            height: 630px;
            overflow: scroll;

            &::-webkit-scrollbar {
                display: none;
            }
        }
    }

    &__item {
        @include flex-center(row, space-between);
        font-size: 24px;
        line-height: 36px;
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }

        &-left {
            display: flex;
            @include flex-center(row, null, center);
        }

        &-dot {
            width: 14px;
            height: 14px;
            background: #5BFFF1;
            border-radius: 50%;
            margin-right: 24px;
        }

        &-name-box {
            margin-left: 16px;

            > div:first-child {
                font-weight: 500;
                font-size: 28px;
                line-height: 40px;
                margin-bottom: 23px;
            }
        }
    }

    &__button-container {
        @include flex-center();
        width: 100vw;
        position: fixed;
        bottom: 48px;
    }

    &--shake {
        animation: shake 0.8s;
    }
}

@keyframes shake {
    0% {
        transform: translate(1px, 1px) rotate(0deg);
    }
    10% {
        transform: translate(-1px, -2px) rotate(-1deg);
    }
    20% {
        transform: translate(-3px, 0px) rotate(1deg);
    }
    30% {
        transform: translate(3px, 2px) rotate(0deg);
    }
    40% {
        transform: translate(1px, -1px) rotate(1deg);
    }
    50% {
        transform: translate(-1px, 2px) rotate(-1deg);
    }
    60% {
        transform: translate(-3px, 1px) rotate(0deg);
    }
    70% {
        transform: translate(3px, 1px) rotate(-1deg);
    }
    80% {
        transform: translate(-1px, -1px) rotate(1deg);
    }
    90% {
        transform: translate(1px, 2px) rotate(0deg);
    }
    100% {
        transform: translate(1px, -2px) rotate(-1deg);
    }
}
</style>
