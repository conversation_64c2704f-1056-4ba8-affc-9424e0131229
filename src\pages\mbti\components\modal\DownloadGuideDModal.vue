<template>
    <van-popup
        class="download-guide-d-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="download-guide-d-modal__title">
            根据你的MBTI测试结果，已为你匹配<span>{{ getRandomInt(100,200) }}位同城优质</span>异性
        </h1>

        <div class="download-guide-d-modal__desc">
            <div>
                <img
                    v-for="(item, index) in registerForm.gender === 1 ? modelList.male : modelList.female"
                    :key="index"
                    :src="item"
                >
            </div>
            Ta们与你的匹配度高达：<span>{{ (Math.random() * 4 + 95).toFixed(1) }}%</span>
        </div>

        <p
            class="download-guide-d-modal__download"
        >
            请前往应用市场搜索下载珍爱APP
        </p>

        <div
            class="download-guide-d-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from "vuex";
import { getRandomInt } from '@/common/utils/tools';

export default {
    name: 'DownloadGuideModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            modelList: {
                male: [
                    'https://zhenai4pm-1251661065.cos.ap-beijing.myqcloud.com/adm/model/2837/1640944687453_501631_small.jpg?imageMogr2/thumbnail/32x32',
                    'https://photo.zastatic.com/images/common-cms/it/20211217/1639738144689_98001_t.jpg?imageMogr2/thumbnail/32x32',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639998206228_130635_t.jpg?imageMogr2/thumbnail/32x32',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639994405514_542654_t.jpg?imageMogr2/thumbnail/32x32'
                ],
                female: [
                    'https://photo.zastatic.com/images/common-cms/it/20220119/1642563562183_995828_t.jpg?imageMogr2/thumbnail/32x32', 
                    'https://photo.zastatic.com/images/common-cms/it/20211216/1639652466118_602153.jpg?imageMogr2/thumbnail/32x32',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639995158830_77891_t.jpg?imageMogr2/thumbnail/32x32',
                    'https://photo.zastatic.com/images/common-cms/it/20211223/1640252710254_551247_t.jpg?imageMogr2/thumbnail/32x32'
                ]
            }
        };
    },

    computed: {
        ...mapState([
            'resourceKey',
            'cmsConfig',
            'registerForm'
        ]),
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$reportKibana(this.resourceKey, 400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        getRandomInt,
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$reportKibana(this.resourceKey, 401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.download-guide-d-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 60px 32px 48px;
    overflow-x: hidden;
    @include flex-center(column, null, center);

    &__title {
        width: 494px;
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
        span {
            font-weight: 600;
            color: #2CCEFF;
        }
    }

    &__desc {
        width: 462px;
        font-size: 28px;
        font-weight: 400;
        color: #6C6D75;
        position: relative;
        left: 30px;
        margin-bottom: 32px;
        >div {
            display: flex;
            font-size: 0;
            position: relative;
            left: 80px;
            margin-bottom: 24px;
            img {
                width: 64px;
                height: 64px;
                border: 2px solid #FFFFFF;
                border-radius: 50%;
                position: relative;
            }
            >img:nth-child(2) {
                left: -10px;
            }
            >img:nth-child(3) {
                left: -20px;
            }
            >img:nth-child(4) {
                left: -30px;
            }
        }
        span {
            font-weight: 600;
            color: #2CCEFF;
        }
    }

    &__download {
        color: #26273C;
        font-weight: 500;
        font-size: 28px;
        margin-bottom: 48px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
