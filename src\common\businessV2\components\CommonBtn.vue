<template>
    <div class="common-btn" :style="btnStyle" @click="goNext">{{ btnText }}</div>
</template>
<script>
export default {
    name: "CommonBtn",
    props: {
        btnText: {
            type: String,
            default: null
        },
        btnStyle: {
            type: [Array, Object],
            default: null
        }
    },
    methods: {
        goNext() {
            this.$emit("goNext");
        }
    }
};
</script>

<style lang="scss" scoped>
.common-btn {
    width: 610px;
    height: 116px;
    margin-bottom: 40px;
    font-size: 36px;
    font-weight: 400;
    line-height: 116px;
    text-align: center;
    border-radius: 56px;
    &:active {
        opacity: 0.8;
    }
}
.disabled {
    opacity: .7;
}
</style>
