import ocpxReport from './ocpx_main.js';
import { session } from '@/common/utils/storage.js';

var CHANNEL_ID = Z.getParam('channelId')
var SUB_CHANNEL_ID = Z.getParam('subChannelId')
// 回传url
var url = 'https://ad.toutiao.com/track/activate/?callback=__CLICKID__&link=__LINK__&event_type=__EVENTTYPE__'
url = url.replace('__LINK__', encodeURIComponent('https://a.zhenai.com/abt/from.do?channelId='+ CHANNEL_ID +'&subChannelId='+ SUB_CHANNEL_ID +'&pageKey=za_m_landing'))
        .replace('__CLICKID__', Z.getParam('clickid', document.referrer))


// 用户选择的数据
var oSelectedData = {
    msgValid: false,
}

/**
 *  channelId 为需要回传的渠道
 *  valid为需要回传的条件， valid需要实现，满足条件则返回回传的url， 否则返回 '',
 *  valid为数组，即当前满足channelId前提下，满足valid里的任何一个都会回传，且只回传一次，回传过后会在当前的函数添加hasReported=true静态属性作为标识
 * @type {Array}
 */
var HUI_CHUAN_ITEMS =  [
    // 头条
    {
        channelId: ['913433','913434','913435','913335','913336','913337','913338','913339','913340','913341','913342','913343','913344','913345','913346','913330','913331','913332','913333','913334','913347','913348','913349','913350','913351','913352','913353','913354','913355','913356','913182','913183','913184','913185','913186','912515','912516','912517','912518','912519','912487','912496','912499','912510','912525','912526','912527','911885', '911875'],
        valid: [function () {
            var condition = {
                // 5000以上
                salary: [5,6,7,8,9],
                // 大专以上
                education: [4,5,6,7],
            }
            return getReportUrl(condition)
        }],
    },
    {
        channelId: ['913101','913102','913096','913097','912545','912544','912543','912557','912556','912555','913107','913108','913116','913117'],
        valid: [
            function () {
                var condition = {
                    // 5000以上
                    salary: [5,6,7,8,9],
                    // 大专以上
                    education: [4,5,6,7],
                }
                return getReportUrl(condition)
            },
            function () {
                return oSelectedData.msgValid ? url.replace('__EVENTTYPE__', '3') : ''
            },
        ],
    },
    {
        channelId: ['912542','912541','912540','912554','912553','912552','913109','913110','913118','913119'],
        valid: [
            function () {
                var condition = {
                    // 8000以上
                    salary: [6,7,8,9],
                    // 大专以上
                    education: [4,5,6,7],
                }
                return getReportUrl(condition)
            },
            function () {
                return oSelectedData.msgValid ? url.replace('__EVENTTYPE__', '3') : ''
            },
        ],
    },
    {
        channelId: ['913103','913104','913098','913113','912488','912497','912500','912511','911886', '911876'],
        valid: [function () {
            var condition = {
                // 8000以上
                salary: [6,7,8,9],
                // 大专以上
                education: [4,5,6,7],
            }
            return getReportUrl(condition)
        }],
    },
    {
        channelId: ['912539','912538','912537','912551','912550','912549','913111','913112','913120','913121'],
        valid: [
            function () {
                var condition = {
                    // 5000以上
                    salary: [5,6,7,8,9],
                    // 本科以上
                    education: [5,6,7],
                }
                return getReportUrl(condition)
            },
            function () {
                return oSelectedData.msgValid ? url.replace('__EVENTTYPE__', '3') : ''
            },
        ],
    },
    {
        channelId: ['913105','913106','913114','913115','912520','912521','912522','912523','912524','912489','912498','912501','912512','911887', '911877'],
        valid: [function () {
            var condition = {
                // 5000以上
                salary: [5,6,7,8,9],
                // 本科以上
                education: [5,6,7],
            }
            return getReportUrl(condition)
        }],
    },
    {
        channelId: ['912536','912535','912534','912548','912547','912546','911896'],
        valid: [function () {
            var condition = {
                // 大专以上
                education: [4,5,6,7],
            }
            return getReportUrl(condition)
        }],
    },
    {
        channelId: ['913430','913431','913432','912509','912529','912530','912531','912532'],
        valid: [function () {
            var condition = {
                // 5000以上
                salary: [5,6,7,8,9],
            }
            return getReportUrl(condition)
        }],
    },
    {
        channelId: ['911897'],
        valid: [function () {
            // 1984~1993
            var year = +oSelectedData.year
            if(year>1984 && year<1993){
               return url.replace('__EVENTTYPE__', '19')
            }
            return ''
        }],
    },
    {
        channelId: ['911898'],
        valid: [function () {
            var year = +oSelectedData.year
            if(year>1984 && year<1993 && oSelectedData.marriage==3){
               return url.replace('__EVENTTYPE__', '19')
            }
            return ''
        }],
    },
    {
        channelId: ['913403','913404','913405','913406','913407','913408','913409','913410','913411','913412','913413','913414','913415','913416','913417'],
        valid: [
            function () {
                var condition = {
                    // 8000以上, 5万以下
                    salary: [6,7,8],
                    // 高中及以下
                    education: [3],
                }
                return getReportUrl(condition)
            },
            function () {
                var condition = {
                    // 5000以上, 5万以下
                    salary: [5,6,7,8],
                    // 中专
                    education: [2],
                }
                return getReportUrl(condition)
            },
            function () {
                var condition = {
                    // 5000以上, 5万以下
                    salary: [5,6,7,8],
                    // 大专
                    education: [4],
                }
                return getReportUrl(condition)
            },
            function () {
                var condition = {
                    // 5000以上
                    salary: [5,6,7,8,9],
                    // 大学本科
                    education: [5],
                }
                return getReportUrl(condition)
            },
            function () {
                var condition = {
                    // 5000以上
                    salary: [5,6,7,8,9],
                    // 硕士
                    education: [6],
                }
                return getReportUrl(condition)
            },
        ],
    },

    // 爱奇艺
    {
        channelId: ['913366'],
        media: 'iqiyi',
        valid: [
            function () {
                var condition = {
                    // 5000以上
                    salary: [5,6,7,8,9],
                    // 大专以上
                    education: [4,5,6,7],
                }
                return getReportUrl(condition);
            }
        ],
    },
    {
        channelId: ['913367'],
        media: 'iqiyi',
        valid: [
            function () {
                var condition = {
                    // 大专以上
                    education: [4,5,6,7],
                }
                return getReportUrl(condition);
            }
        ],
    },
    {
        channelId: ['913368'],
        media: 'iqiyi',
        valid: [
            function () {
                return oSelectedData.msgValid
            }
        ],
    },
]

function initIqiyiSdk() {
    var hasInitSdk = false;
    (function () {
        if(hasInitSdk)return;

        var ns = document.createElement('script');ns.type = 'text/javascript';ns.async = true;
        ns.src = '//static.iqiyi.com/js/common/tpct.min.js';
        var f = document.getElementsByTagName('script')[0];f.parentNode.insertBefore(ns,f);

        var hasInitSdk = true;
    })();
}

// 当前命中的回传组
var curHuiChuanItems = (function () {
    var res = []
    for(var i=0; i<HUI_CHUAN_ITEMS.length;i++){
        var item = HUI_CHUAN_ITEMS[i]
        if( item.channelId.indexOf(CHANNEL_ID)!=-1 ){
            res.push(item);

            // 初始化爱奇艺sdk
            if(item.media==='iqiyi'){
                initIqiyiSdk();
            }
        }
    }
    return res
})()

/**
 * 用户选择的数据是否回传符合条件
 * 因大部分逻辑一样，所以单独抽出，对于像年龄的范围情况，则需单独实现
 * 如果符合，会返回回传的url
 * @param  {[type]} condition [description]
 * @return {[type]}           [description]
 */
function getReportUrl(condition) {
    var isMatch = true
    for(var key in condition){
        var value = oSelectedData[key]
        // 用户还没选择此数据
        if(!value){
            isMatch = false
            break
        }
        // 选择数据不在条件范围内
        if( condition[key].indexOf(value)===-1 ){
            isMatch = false
            break
        }
    }
    return isMatch ? url.replace('__EVENTTYPE__', '19') : ''
}

/**
 * 处理回传
 * @return {[type]} [description]
 */
function handleHuiChuan() {
    for(var i=0; i<curHuiChuanItems.length; i++){
        var item = curHuiChuanItems[i]
        for(var j=0; j<item.valid.length; j++){
            var fnValid = item.valid[j]
            var reportUrl = fnValid()
            if(!fnValid.hasReported && fnValid()){
                if(item.media==='iqiyi'){
                    try{
                        window.iqiyiTpct && window.iqiyiTpct.track && window.iqiyiTpct.track(19901);
                    }catch(e){
                        console.log(e)
                    }
                }else{
                    var image2 = new Image();
                    image2.onload = image2.onerror = function () {
                        image2 = null;
                    }
                    image2.src = reportUrl;
                }


                // 只上报一次
                fnValid.hasReported = true
            }
        }
    }
}

var oUserSelect = {
    /**
     * 记录用户选了哪些信息
     * @param  {[type]} data [{ key: 字段, value: 值 }]
     * @return {[type]}      [description]
     */
    mark: function (data) {
       oSelectedData = Object.assign({}, oSelectedData, data);
       ocpxReport(oSelectedData)
        console.log('用户操作：', data, oSelectedData)
        try {
            const vendor = $.ua.device.vendor
            if (data.msgValid && vendor) {
                // 记录手机品牌
                Z.ajax({
                    url:
                        window.location.protocol +
                        "//api.zhenai.com/register/reportBrand.do",
                    type: "POST",
                    dataType: "json",
                    data: {
                        memberId: session.getItem('reg_memberid'),
                        brand: vendor,
                    },
                });
            }
        }catch(e){}
       // 当前渠道没有需要回传
       if(!curHuiChuanItems.length) {
           return;
       }

       handleHuiChuan()
    },
}

export default oUserSelect
