<template>
    <div class="point_div">
        <navApp
            title="2024年恋爱段位考试"
            type="jump"
            @goBack="goBack"
        />
        <div class="point">
            <div class="point_love">
                <div class="point_content">
                    <p class="point_title">
                        最后一步啦，马上为你计算考试成绩
                    </p>
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20240527/1716783028754_320511.png"
                        alt=""
                        class="process"
                    >
                    <div class="point_question">
                        <p class="question_title">
                            请完善你的观点
                        </p>

                        <div
                            class="question_desc"
                            v-for="item in questions"
                            :key="item.questionID"
                        >
                            <p class="q_title">
                                {{ item.questionName }}
                            </p>
                            <van-radio-group
                                v-model="item.answerID"
                                @change="(e)=>radiochange(e,item)"
                            >
                                <van-radio
                                    :name="nRadio.optionsID"
                                    v-for="nRadio in item.optionsList"
                                    checked-color="#D7204A"
                                    icon-size="16px"
                                    :key="nRadio.optionsID"
                                >
                                    {{ nRadio.optionsContent }}
                                </van-radio>
                            </van-radio-group>
                        </div>
                    </div>
                </div>
                <div
                    class="sumbit_box"
                    v-show="questions.length"
                >
                    <div
                        :class="{ 'sumbit_btn_dis':true,'sumbit_btn': isFull, }"
                        @click="sumbitClick"
                    >
                        提交考卷
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import navApp from '../../components/nav.vue';
import { debounce } from 'lodash';
import { reportKibana } from '@/common/utils/report';

export default {
    name: 'LovePoint',
    data() {
        return {
            questions: [],
            uploadedPhoto: undefined, // 是否有头像

        };
    },
    components: {
        navApp,
    },
    computed: {
        // 是否全部填写完毕
        isFull() {
            let answerLength = this.questions.filter((item) => item.answerID).length;

            return answerLength === this.questions.length;
        },
    },
    created() {
        // 获取观点问答题
        this.getPointList();

        // 头像上传弹窗关闭回调
        Z.client.on('event.uploadAvatarFinish', () => {
            console.log('uploadAvatarFinish');
            setTimeout(() => {
                // 跳转到答案页面
                this.$router.push({ path: '/exam-result', query: { source: 1, }, });
            }, 2000);

        });

    },
    methods: {
        sumbitClick: debounce(function() {

            this.reportKibanaUpDate(4, '观点填写页提交考卷按钮点击', this.uploadedPhoto ? 1 : 0);

            let isFull = this.isFullAnswer();

            // 全部填完观点
            if (isFull) {
                // 是否上传了头像  弹出头像上传弹窗

                if (this.uploadedPhoto === false) {
                    Z.client.invoke('ui', 'openAppView', { 'page': 195, 'params': { 'source': 1, }, });
                    return;
                }
                // 跳转到答案页面
                this.$router.push({ path: '/exam-result', query: { source: 1, }, });

            } else {
                // Z.client.invoke('ui', 'showToast', { content: '请完善你的观点', })

            }

        }, 1000, {
            leading: true,
            trailing: false,

        }),
        /*
         * 配置代理 test环境
         * header 配置Cookie token=xxx
         */
        getPointList() {
            Z.ajax({
                type: 'GET', url: 'https://api.zhenai.com/game/loveExam/profileComplete.do',
            }, ({ isError, data, errorMessage, }) => {
                if (isError) {
                    Z.client.invoke('ui', 'showToast', { content: errorMessage, });
                    return;
                }
                let questionsFormat = this.formDataQuestion(data.questions || []);

                let questionsFilter = this.filterFormatQuestion(questionsFormat || []);

                this.questions = questionsFilter;

                this.uploadedPhoto = data.uploadedPhoto;
                this.reportKibanaUpDate(3, '观点填写页曝光', this.questions.length, data.viewsCount || 0);

            }, error => {
                Z.client.invoke('ui', 'showToast', { content: error, });
            });
        },
        formDataQuestion(data) {
            return data.map((item) => {
                item.answerID = item.answerID || '';
                return item;

            });
        },
        filterFormatQuestion(data) {
            return data.filter((item) => {
                return !item.answerID;
            });
        },
        // 是否全部填完答案
        isFullAnswer() {
            let answerLength = this.questions.filter((item) => item.answerID).length;

            return answerLength === this.questions.length;

        },
        // 单选框发生改变
        radiochange(value, data) {
            let { questionID, optionsList, } = data;

            let optionsListanSwer = optionsList.filter((item) => item.optionsID === value);
            let answerContent = optionsListanSwer.length > 0 && optionsListanSwer[0] && optionsListanSwer[0].optionsContent;
            let form = {
                questionID,
                answerID: value,
                answerContent,
                answerReplenishContent: '',
            };

            this.saveAnswer(form);

        },
        saveAnswer(form) {
            this.reportKibanaUpDate(8, '观点填写页观点选择点击', this.questions.length, form.questionID);

            Z.ajax({
                type: 'POST', url: 'https://api.zhenai.com/myView/addViewAnswerRecord.do', data: form,
            }, ({ isError, data, errorMessage, }) => {
                if (isError) {
                    Z.client.invoke('ui', 'showToast', { content: errorMessage, });
                    return;
                }

            }, error => {
                Z.client.invoke('ui', 'showToast', { content: error, });
            });
        },
        goBack() {
            this.$router.replace({ path: '/loveExamIndex', });

        },
        reportKibanaUpDate(accessPoint, accessPointDesc, ext1, ext2) {
            reportKibana({
                resourceKey: 'za_h5_loveTest',
                accessPoint,
                accessPointDesc,
                ext1,
                ext2,
            });
        },
    },
};
</script>
<style scoped lang="scss">
.point_div{
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
  overflow: hidden;

}
.point{
  width: 100vw;
  min-height: 717px;
  background: #FFE7E1;
  padding: 14px 10px 0 10px;
  position: relative;
  box-sizing: border-box;
  .point_love{
    width: 357px;
    height: 681px;
    background: url('https://photo.zastatic.com/images/common-cms/it/20240527/1716783092320_950535.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .point_content{
      width: 100%;
      position: relative;
      margin-top: 55px;
      // height: 580px;
      // overflow-x: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      .point_title{
        width: 100%;
        height: 22.5px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #111111;
        text-align: center;
      }
      .process{
        margin-top: 14.5px;
        width: 220rpx;
        height: 16px;

      }
      .point_question{
        margin-top: 38.5px;
        padding-left: 26px;
        width: 100%;
        box-sizing: border-box;
        .question_title{
          height: 16.5px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #5C5E66;
          letter-spacing: 0;
        }
        .q_title{
          width: 305.5px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 18px;
          color: #111111;
          line-height: 26px;
          margin: 10px 0 16px 0;
        }
      }
    }
  }

  ::v-deep .van-radio-group .van-radio .van-radio__label{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #1A202C;
    line-height: 16px;
  }
  ::v-deep .van-radio-group .van-radio{
    margin-bottom: 12px;
    align-items:flex-start;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    width: 318px;
  }
  ::v-deep .van-radio-group .van-radio .van-radio__icon--checked{
    color: #D7204A;
  }
  ::v-deep .van-radio-group .van-radio .van-radio__icon--checked + .van-radio__label{
    color: #D7204A;
  }
  .question_desc{
    margin-bottom: 30px;
  }
  .sumbit_box{
    width: 100vw;
    height: 50px;
    display: flex;
    justify-content: center;
    margin-top: 10px;

  }
  .sumbit_btn_dis{
    background: #D8D8D8;
    border-radius: 27px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
    line-height:50px;
    text-shadow: 0;

    width: 230px;
    height: 50px;
    position: relative;
    z-index: 1;
  }
  .sumbit_btn{

    text-shadow: 0 3px 5px #db114842;
    background-image: linear-gradient(180deg, #FF9E8B 4%, #FF5B87 100%);

  }

}
</style>
