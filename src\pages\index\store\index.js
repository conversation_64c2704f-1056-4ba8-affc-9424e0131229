import Vuex from 'vuex';
import * as dict from '@/common/config/register-dictionary.js';
import { _getMaterial, _getModelInfo } from '../js/api.js';
import { session } from '@/common/utils/storage.js';
import Api from "@/common/server/base";

const formInfo = [
    {
        index: "gender",
        label: "您的性别",
        value: "",
        selectArr: dict.gender
    },
    {
        index: "workCity",
        label: "您的工作地",
        value: "",
        selectArr: Z.workCity
    },
    {
        index: "birthday",
        label: "您的出生年份",
        value: "",
        selectArr: dict.birthday
    },
    {
        index: "education",
        label: "您的学历",
        value: "",
        selectArr: dict.education
    },
    {
        index: "marriage",
        label: "您的婚姻状况",
        value: "",
        selectArr: dict.marriage
    },
    {
        index: "salary",
        label: "您的月收入",
        value: "",
        selectArr: dict.salary
    }
];

const store = new Vuex.Store({
    state: {
        // cms的配置
        cmsConfig: {
            // 通用
            pageColor: null,  //页面底色（可能是渐变色）
            schemeType: 0, //下载页类型
            // 表单页
            formImg: '', //头图
            buttonColor: null, //底部按钮底色（可能是渐变色）
            formButtonText: '', //主按钮文案
            // 下载页 方案A翻牌推人（默认方案）
            downloadImg: '',  //头图
            flopButtonText: '', //翻牌按钮文案
            downloadButtonText: '', //主按钮文案
            channelType: 1, //该链接是否用于头条投放 1为是(默认) 0为否
            agreementStatus: 0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认)
            // 下载页 方案B企微推荐
            formWecahtRecHead: '', // 头图
            formWechatImg: '', // 二维码
            qRcodeBasemap: '', // 二维码底图
            formWechatText: '', // 文案配置
            headerDiagram: '', // 首页头图
            backgroundColor: '', // 首页背景色
            buttonText: '', // 首页按钮文案
            buttonBackgroundColor: '' // 首页按钮背景色
        },
        // 用于表单显示
        formInfo,
        // 用于注册提交
        registerInfo: {
            gender: "",
            workCity: "",
            birthday: "",
            marriage: "",
            education: "",
            salary: "",
            phone: ""
        },
        // 图形验证码
        code: '',
        // 详情页模特信息
        modelInfo: {
            ageString: '',
            avatar: '',
            educationString: '',
            mainImg: '', // 主图
            momentImg1: '', // 动态图
            momentImg2: '',
            momentImg3: '',
            momentImg4: '',
            momentImg5: '',
            name: '',
            phone: '',
            salaryString: '',
            sex: '',
            weCaht: '',
            workCityString: ''
        },
        // 是否覆盖资料
        isCover: false,
        // 是否允许尝试打开/下载APP
        overwriteRegistrationSwitch: false,
        // 后台返回的memberId
        regMemberId: '',
        // 老注册页打桩使用
        EXT9: '',
        // 协议勾选状态，默认勾选
        hasCheckProtocal: true,
        // 控制是否打开引导刷新
        showError: false,
        // 引导刷新打开时对应的接口请求函数
        showErrorCb: null
    },
    mutations: {
        setCmsConfig(state, target) {
            Object.assign(state.cmsConfig, target);
        },
        setFormInfo(state, target) {
            state.formInfo.forEach(item => {
                Object.keys(target).forEach(key => {
                    if (item.index === key) {
                        return item.value = target[key];
                    }
                });
            });

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localFormInfo', JSON.stringify(state.formInfo));
        },
        setRegisterInfo(state, target) {

            state.registerInfo = Object.assign(state.registerInfo, target);

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localRegisterInfo', JSON.stringify(state.registerInfo));
        },
        setCode(state, target) {
            state.code = target;
        },
        setIsCover(state, target) {
            state.isCover = target;
        },
        setModelInfo(state, target) {
            state.modelInfo = Object.assign(state.modelInfo, target);
        },
        setOverwriteRegistrationSwitch(state, target) {
            state.overwriteRegistrationSwitch = target;
        },
        setRegMemberId(state, target) {
            state.regMemberId = target;
            session.setItem('reg_memberid', target);
        },
        setEXT9(state, target) {
            state.EXT9 = target;
        },
        setHasCheckProtocal(state, target) {
            state.hasCheckProtocal = target;
        },
        setShowError(state, target) {
            state.showError = target.showError;
            state.showErrorCb = target.showErrorCb;
        }
    },
    actions: {
        // 获取cms配置
        async setCmsConfig({ commit }, target) {
            // 默认配置
            let defaultCmsConfig = {
                // 通用
                schemeType: 1,
                pageColor: {
                    background: '#6F6BFD',//页面底色（可能是渐变色）
                },
                // 表单页
                formImg: require("../assets/imgs/banner-collection.png"), //头图
                buttonColor: {
                    background: 'linear-gradient(0deg, #FFC334, #FF8F02)', //底部按钮底色（可能是渐变色）
                    textShadow: '0px 5px 13px rgba(255, 125, 199, 0.7)'
                },
                formButtonText: '立即查看对象', //主按钮文案
                // 下载页 方案A翻牌推人（默认方案）
                downloadImg: require("../assets/imgs/banner-info.png"),  //头图
                flopButtonText: '我要约Ta', //翻牌按钮文案
                downloadButtonText: '下载APP，立即约会', //主按钮文案
                channelType: 1, //该链接是否用于头条投放 1为是(默认) 0为否
                agreementStatus: 0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认)
                headerDiagram: 'https://photo.zastatic.com/images/common-cms/it/20220622/1655878949908_705022_t.png', // 首页头图
                backgroundColor: '#6F6BFD', // 首页背景色
                buttonText: '免费参加', // 首页按钮文案
                buttonBackgroundColor: 'linear-gradient(to bottom, #FF8F02, #FFC334)' // 首页按钮背景色
            };
            // 没有素材id则采用默认配置
            if (!Z.getParam("materialId")) {
                commit("setCmsConfig", defaultCmsConfig);
                return;
            }
            // 拉取CMS配置
            let resData;
            try {
                const hookType = Z.getParam("hookType");
                const id = Z.getParam("materialId");
                if (hookType === '1') {
                    resData = await Api.getHookMaterialInfo({
                        id
                    });
                } else {
                    resData = await _getMaterial({
                        id
                    });
                }
                // 取配置失败也使用默认配置, 不能直接写在cmsConfig中否则会出现切换
                if (resData.isError) {
                    commit("setCmsConfig", defaultCmsConfig);
                    // 此处的this指向store不是vue
                    return this._vm.$toast(resData.errorMessage);
                }

                if (hookType === '1') {
                    const entity = resData.data.entity && resData.data.entity[0];
                    target = {
                        ...resData.data,
                        ...entity,
                    };
                } else {
                    target = resData.data.materialVo;
                }
            } catch (e) {
                console.error(e);
                return this._vm.$toast('网络异常');
            }

            // 处理页面底色和按钮底色
            function handleColor(color) {
                if (color.indexOf('&') > -1) {
                    // 按钮为渐变色
                    let topColor = color.split("&")[0],
                        bottomColor = color.split("&")[1];
                    return {
                        background: `linear-gradient(0deg,${topColor},${bottomColor})`
                    };
                }
                return {
                    background: color
                };
            }

            target.pageColor = handleColor(target.pageColor);
            target.buttonColor = handleColor(target.buttonColor);
            target.buttonColor.textShadow = "0px 5px 13px rgba(255, 125, 199, 0.7)"; // 字体增亮

            // 图片压缩
            target.formImg = target.formImg && target.formImg + '?imageMogr2/thumbnail/750x';
            target.downloadImg = target.downloadImg && target.downloadImg + '?imageMogr2/thumbnail/750x';

            // 设置协议默认值
            commit("setHasCheckProtocal", target.agreementStatus === 0);
            if ([2, 11].includes(target.schemeType)) {
                target.formWecahtRecHead = target.formWecahtRecHead && target.formWecahtRecHead + '?imageMogr2/thumbnail/750x';
                target.formWechatImg = target.formWechatImg && target.formWechatImg + '?imageMogr2/thumbnail/370x370';
                target.qRcodeBasemap = target.qRcodeBasemap && target.qRcodeBasemap + '?imageMogr2/thumbnail/698x717';
            }

            if (!target.headerDiagram) {
                target.headerDiagram = "https://photo.zastatic.com/images/common-cms/it/20220622/1655878949908_705022_t.png";
            }
            if (!target.backgroundColor) {
                target.backgroundColor = '#6F6BFD';
            }
            if (!target.buttonText) {
                target.buttonText = '免费参加';
            }
            if (!target.buttonBackgroundColor) {
                target.buttonBackgroundColor = 'linear-gradient(to bottom, #FF8F02, #FFC334)';
            }
            commit("setCmsConfig", target);
        },
        // 获取下载页模特信息
        async setModelInfo({ commit, state }, target) {
            const { salary, workCity, education, gender, birthday } = state.registerInfo;
            const age = new Date().getFullYear() - new Date(birthday).getFullYear();
            let sendData = Object.assign({}, {
                salary,
                workCity,
                education,
                age,
                sex: gender,
                modelId: target
            });

            if (!sendData.salary || !sendData.workCity) {
                return;
            }

            let resData = await _getModelInfo(sendData);

            if (resData.isError) {
                // 打开引导刷新
                if (resData.errorMessage === "当前网络异常") {
                    state.showError = true;
                    state.showErrorCb = this._actions.setModelInfo[0];
                    return;
                }
                return this._vm.$toast(resData.errorMessage);
            }

            // 请求数据正常，关闭引导刷新
            state.showError = false;
            state.showErrorCb = () => { };

            // 防止运营没有压缩图片,前端兜底处理一遍
            let modelInfoVo = resData.data.modelInfoVo;
            modelInfoVo.avatar += '?imageMogr2/thumbnail/100x100';
            modelInfoVo.mainImg += '?imageMogr2/thumbnail/700x700';
            modelInfoVo.momentImg1 += '?imageMogr2/thumbnail/315x315';
            modelInfoVo.momentImg2 += '?imageMogr2/thumbnail/154x154';
            modelInfoVo.momentImg3 += '?imageMogr2/thumbnail/154x154';
            modelInfoVo.momentImg4 += '?imageMogr2/thumbnail/154x154';
            modelInfoVo.momentImg5 += '?imageMogr2/thumbnail/154x154';

            commit("setModelInfo", modelInfoVo);
        },
    },
    getters: {
        // 根据registerInfo返回已填写的表单项个数
        getProgress: state => {
            let infoArr = Object.keys(state.registerInfo),
                count = 0;

            infoArr.forEach(item => {
                // 手机号需填满11位才算填写完整
                if (item === 'phone') {
                    state.registerInfo.phone.length === 13 ? count++ : "";
                } else if (state.registerInfo[item] !== '') {
                    count++;
                }
            });
            return count;
        },
        // 返回正常格式的手机号
        getNormalPhone: state => {
            return state.registerInfo.phone.replace(/[^(\d)]/g, "");
        }
    }
});

export default store;
