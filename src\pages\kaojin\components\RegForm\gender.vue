<template>
    <div class="wrapper">
        <div class="select_box">
            <div
                v-for="(item,index) in list.options"
                :key="index"
                @click="goNext(item.key)"
                class="item"
                :class="curGender === item.key?'active':''"
            >
                <div class="img_container">
                    <img class="gender_img" v-if="item.key == 0" src="https://photo.zastatic.com/images/common-cms/it/20240521/1716291117459_41762.png" alt="">
                    <img class="gender_img" v-else src="https://photo.zastatic.com/images/common-cms/it/20240521/1716291138540_918536.png" alt="">
                    <img src="https://photo.zastatic.com/images/common-cms/it/20240522/1716343883896_792388_t.png" alt="" class="active_img">
                </div>
                <p class="gender_text">{{ item.text }}</p>
            </div>
        </div>
        <img class="banner" src="https://photo.zastatic.com/images/common-cms/it/20240521/1716281607927_548725.png" alt="">
    </div>
</template>

<script>
import { setLocalRegisterForm } from "../../utils/index";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Gender",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curGender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curGender = val;
            const params = {
                key: "gender",
                value: val,
                isMark: false,
            };
            this.$report(4,'性别页-按钮点击', {
                ext28: val
            });
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .select_box {
        display: flex;
        justify-content: space-between;
        padding: 0 115px;
        width: 100%;
        margin-top: 220px;

    }
    .gender_text {
        font-weight: 600;
        font-size: 34px;
        color: #000000;
        margin-top: 28px;
    }
    .banner {
        margin-top: 124px;
        width: 750px;
        height: 460px;
    }
    .item{
        width: 200px;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        margin-top: 45px;

        line-height: 48px;
        font-size: 30px;
        text-align: center;
        .img_container {
            position: relative;
            width: 200px;
            height: 200px;
        }
        img {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            font-size: 0;
        }

        .active_img {
            width: 38px;
            height: 38px;
            position: absolute;
            right: 0;
            bottom: 0;
            transform: translateX(-25%) translateY(-25%);
            display: none;
        }
    }
    .active{
        .gender_img {
            border: solid 4px #222833;
        }
        .active_img {
            display: block;
        }
    }
}
</style>
