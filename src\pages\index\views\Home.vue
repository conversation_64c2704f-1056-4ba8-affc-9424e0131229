<template>
    <div
        class="home"
        :style="{ backgroundColor: cmsConfig.backgroundColor || '#6F6BFD' }"
    >
        <div class="home_cover" :style="{ backgroundImage: `url(${cmsConfig.headerDiagram})` }">
        </div>
        <div class="home_caption">
            <h3 v-if="!isShowAnime">通过智能算法为你匹配优质异性...</h3>
            <template v-else>
                <img
                    src="https://photo.zastatic.com/images/common-cms/it/20220616/1655365674479_687276_t.png"
                    alt=""
                />
                <p>请完善资料，领取你的异性好友列表</p>
            </template>
        </div>
        <div class="home_anime" v-if="!isShowAnime">
            <div class="home_anime_circle">
                <div class="hac_1"></div>
                <div class="hac_2"></div>
                <div class="hac_3"></div>
                <div class="hac_4"></div>
            </div>
            <transition name="fade">
                <div class="home_anime_avatars" v-if="isShowAvatar">
                    <span
                        v-for="(avatar, i) in avatarList"
                        :class="`haa_${i + 1}`"
                        :style="{ backgroundImage: `url(${avatar})` }"
                    ></span>
                </div>
            </transition>
            <div class="home_anime_glass"></div>
        </div>
        <div class="home_rcommd" v-else>
            <h3 class="home_rcommd_title">优质推荐</h3>
            <za-swiper
                v-if="modelList.length"
                item-width-mode="custom"
                :list="modelList"
                :inner-width="this.$utils.pxToRem(750)"
                :inner-height="this.$utils.pxToRem(600)"
                :span-gap="this.$utils.pxToRem(24)"
                auto-play
            >
                <template slot-scope="{ item }" slot="default">
                    <div class="hr_item">
                        <div class="hr_item_info">
                            <img
                                :src="
                                    `${
                                        item.mainImg
                                    }?imageMogr2/thumbnail/342x342`
                                "
                                alt=""
                            />
                            <div class="hii_nail">
                                <img
                                    :src="
                                        `${
                                            item.momentImg1
                                        }?imageMogr2/thumbnail/158x158`
                                    "
                                    alt=""
                                />
                                <img
                                    :src="
                                        `${
                                            item.momentImg2
                                        }?imageMogr2/thumbnail/158x158`
                                    "
                                    alt=""
                                />
                            </div>
                        </div>
                        <div class="hr_item_desc">
                            <strong>{{ item.name }}</strong>
                            <img
                                src="https://photo.zastatic.com/images/common-cms/it/20220616/1655374914869_145512_t.png"
                                alt=""
                            />
                            <span>已购车</span><span>已购房</span>
                        </div>
                        <div class="hr_item_name">
                            {{ item.age }}岁 <strong>·</strong> {{ item.occupation }}
                        </div>
                    </div>
                </template>
            </za-swiper>
        </div>
        <div
            class="home_btn"
            @click="handleJoin"
            :style="{
                background: cmsConfig.buttonBackgroundColor
            }"
        >
            {{ cmsConfig.buttonText }}
        </div>
        <common-protocol
            class="home-wrapper__protocol"
            :is-checked.sync="isCheckProtocol"
            :agreement-status="cmsConfig.agreementStatus"
            :style-config="{
                textColor: '#fff',
                protocolColor: '#FFC334',
                protocolCheckedUrl:
                    'https://photo.zastatic.com/images/common-cms/it/20220620/1655714642078_803230_t.png'
            }"
        />

        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :style-config="{
                confirmButtonColor: '#ffffff',
                confirmButtonBgColor: '#767dff',
                cancleButtonColor: '#767dff'
            }"
            page-type="大表单翻牌(新流程)"
        />
    </div>
</template>

<script>
import { _getRandomAvatar, _getModelDetail } from "../js/api.js";
import { mapState } from "vuex";
import CommonProtocol from "@/common/business/CommonProtocol.vue";
import CommonProtocolModal from "@/common/business/components/CommonProtocolModal.vue";
import ZaSwiper from "@za/vue-za-swiper";
import "@za/vue-za-swiper/dist/style.css";
import { reportKibana } from "@/common/utils/report.js";
export default {
    name: "Home",
    data() {
        return {
            isCheckProtocol: false,
            isShowModal: false,
            isShowAvatar: false,
            isShowAnime: false,
            avatarList: [],
            modelList: [],
            timer1: null,
            timer2: null
        };
    },
    computed: {
        ...mapState(["cmsConfig"])
    },
    created() {
        this.getRandomAvatar();
        this.getModelDetail();
        reportKibana("大表单翻牌(新流程)", 1, "首页访问");
    },
    mounted() {
        this.timer1 = setTimeout(() => {
            this.isShowAvatar = true;
            clearTimeout(this.timer1);
        }, 500);

        this.timer2 = setTimeout(() => {
            this.isShowAnime = true;
            clearTimeout(this.timer2);
        }, 4000);
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                this.isCheckProtocol = value === 0;
            },
            {
                immediate: true
            }
        );
    },
    components: {
        CommonProtocol,
        CommonProtocolModal,
        ZaSwiper
    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.isShowModal = false;
            setTimeout(() => {
                this.$router.push({
                    path: "/index",
                    query: {
                        s: 1
                    }
                });
            }, 200);
        },
        handleClickA() {},
        async getRandomAvatar() {
            let res = await _getRandomAvatar({ manLimit: 5, womanLimit: 4 });

            if (res.isError) {
                return this.$toast(res.errorMessage);
            }
            this.avatarList = res.data.list.map(item => item.avatar);
        },
        async getModelDetail() {
            let res = await _getModelDetail({ manLimit: 6, womanLimit: 6, ageFloor: 26, ageCeiling: 39 });
            if (res.isError) {
                return this.$toast(res.errorMessage);
            }
            this.modelList = res.data.modelDetails.map(item => {
                return {
                    mainImg: item.mainImg,
                    momentImg1: item.momentImg1,
                    momentImg2: item.momentImg2,
                    name: item.name,
                    age: item.age,
                    occupation: item.occupation
                };
            });
        },
        handleJoin() {
            reportKibana("大表单翻牌(新流程)", 1, "首页-按钮点击");
            if (!this.isCheckProtocol) {
                this.isShowModal = true;
            } else {
                this.$router.push({ path: "/index", query: { s: 1 } });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.common-protocol {
    padding-bottom: 56px;
}
.home {
    min-height: 100vh;
}
.home_cover {
    height: 358px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.home_caption {
    padding-top: 14px;
    margin-top: -4px;
    padding-bottom: 50px;
    text-align: center;
    color: #fff;
    > h3 {
        font-size: 32px;
        font-weight: 600;
    }
    > p {
        padding-top: 4px;
        font-size: 30px;
    }
    > img {
        width: 560px;
        height: 50px;
        margin: 0 auto;
    }
}
.home_anime {
    position: relative;
    width: 100%;
    height: 588px;
    transform: perspective(700px);
    transition: all 1s;
    &_circle {
        position: absolute;
        left: 50%;
        top: 50%;
        width: 250px;
        height: 250px;
        border: 4px solid #fff8ff;
        border-radius: 50%;
        transform: translate(-50%, -50%);
    }
    &_glass {
        position: absolute;
        top: 194px;
        left: 266px;
        width: 218px;
        height: 308px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220616/1655352281480_532388_t.png)
            no-repeat;
        background-size: 100% 100%;
        transform-origin: bottom center;
        animation: swing 1.2s infinite forwards;
        animation-delay: .6s;
    }
}
.home_anime_avatars {
    .haa_1,
    .haa_2,
    .haa_3 {
        position: absolute;
        width: 36px;
        height: 36px;
        border: 5px solid #fff;
        border-radius: 50%;
        background-size: 100% 100%;
        background-clip: padding-box;
        box-shadow: 0 0 10px #fff;
    }
    .haa_1 {
        top: 168px;
        left: 308px;
    }
    .haa_2 {
        bottom: 190px;
        left: 268px;
    }
    .haa_3 {
        top: 252px;
        right: 230px;
    }
    .haa_4,
    .haa_5,
    .haa_6 {
        position: absolute;
        width: 68px;
        height: 68px;
        border: 5px solid #fff;
        border-radius: 50%;
        background-size: 100% 100%;
        background-clip: padding-box;
        box-shadow: 0px 0px 6px rgba(255, 255, 255, 0.3),
            6px 0px 6px rgba(255, 255, 255, 0.3),
            -6px 0px 6px rgba(255, 255, 255, 0.3);
    }
    .haa_4 {
        top: 234px;
        left: 168px;
    }
    .haa_5 {
        top: 82px;
        right: 262px;
    }
    .haa_6 {
        bottom: 130px;
        right: 146px;
    }
    .haa_7,
    .haa_8,
    .haa_9 {
        position: absolute;
        width: 124px;
        height: 124px;
        border: 5px solid #fff;
        border-radius: 50%;
        background-size: 100% 100%;
        background-clip: padding-box;
        // outline: 8px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0px 0px 6px rgba(255, 255, 255, 0.3),
            6px 0px 6px rgba(255, 255, 255, 0.3),
            -6px 0px 6px rgba(255, 255, 255, 0.3);
    }
    .haa_7 {
        top: 8px;
        left: 116px;
    }
    .haa_8 {
        top: 144px;
        right: 40px;
    }
    .haa_9 {
        left: 126px;
        bottom: 16px;
    }
}

.home_rcommd {
    transition: all 1s;
    &_title {
        height: 600px;
        margin: 0 30px;
        border: 4px solid #4138ad;
        border-bottom: 0;
        border-radius: 38px 38px 0 0;
        background: linear-gradient(to bottom, #6200ae, transparent);
        padding: 40px 0 40px 26px;
        color: #fff;
        font-size: 40px;
        font-weight: 600;
        box-sizing: border-box;
    }
}

.za-swiper {
    margin-top: -470px;
}
.hr_item {
    padding: 48px;
    background: #fff;
    box-shadow: 0 24px 48px 0 rgba(201, 203, 222, 0.1);
    border-radius: 30px;
    &_info {
        display: flex;
        > img {
            width: 342px;
            height: 342px;
            margin-right: 32px;
            border-radius: 20px;
        }
    }
    &_desc {
        display: flex;
        align-items: center;
        height: 68px;
        margin-top: 32px;
        color: #26273c;
        > strong {
            margin-right: 26px;
            font-size: 48px;
            font-weight: 600;
        }
        > img {
            width: 138px;
            height: 48px;
            margin-right: 12px;
        }
        > span {
            display: inline-block;
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
            font-size: 24px;
            font-weight: 500;
            background: #f4f4f4;
            border-radius: 8px;
            margin-right: 12px;
        }
    }
    &_name {
        padding-top: 24px;
        color: #6C6D75;
        font-size: 32px;
    }
    .hii_nail {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        > img {
            width: 156px;
            height: 156px;
            border-radius: 20px;
        }
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 1s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
}

@for $i from 1 through 4 {
    .hac_#{$i} {
        position: absolute;
        left: -3px;
        top: -3px;
        width: 250px;
        height: 250px;
        border: 3px solid #fff8ff;
        border-radius: 50%;
        transform-origin: center center;
        opacity: 0;
        transform-style: preserve-3d;
        animation: zoomOut;
        animation-duration: 1.5s;
        animation-delay: $i * 0.5s;
        animation-iteration-count: infinite;
    }
}

.home_btn {
    height: 110px;
    margin: 60px 32px 40px;
    color: #fff;
    font-size: 48px;
    font-weight: bold;
    text-align: center;
    line-height: 110px;
    border-radius: 54px;
}

@keyframes zoomOut {
    from {
        opacity: 0;
    }
    5% {
        opacity: 1;
    }

    30% {
        opacity: 1;
        transform: scale3d(1.4, 1.4, 1);
    }

    60% {
        opacity: 0.5;
        transform: scale3d(1.8, 1.8, 2);
    }

    90% {
        opacity: 0.3;
        transform: scale3d(2.4, 2.4, 3);
    }

    to {
        opacity: 0;
        transform: scale3d(2.4, 2.4, 3);
    }
}
@keyframes swing {
    20% {
        transform: rotate3d(0, 0, 1, 9deg);
    }

    40% {
        transform: rotate3d(0, 0, 1, -6deg);
    }

    60% {
        transform: rotate3d(0, 0, 1, 3deg);
    }

    80% {
        transform: rotate3d(0, 0, 1, -3deg);
    }

    to {
        transform: rotate3d(0, 0, 1, 0deg);
    }
}
</style>
