<template>
<div class="caption">{{content}}</div>
</template>

<script>
export default {
    name: "Caption",
    props: {
        content: {
            type: String,
            required: true
        }
    }
};
</script>

<style lang="scss" scoped>
.caption {
    margin: -16px 32px 32px;
    height: 56px;
    padding-left: 76px;
    color: #fff;
    font-size: 36px;
    font-weight: 500;
    text-align: left;
    line-height: 56px;
    background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577029972_719710_t.png)
        no-repeat;
    background-size: 60px 56px;
    background-position: left center;
}
</style>
