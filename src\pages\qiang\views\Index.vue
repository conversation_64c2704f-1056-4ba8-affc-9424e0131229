<template>
    <div
        class="index-wrap"
        ref="indexRef"
    >
        <img
            class="header"
            :src="styleConfig.homeTopImg"
        >
        <div class="interview">
            <img :src="styleConfig.interviewTitleImg">
            <div
                class="info"
                :style="{'background': styleConfig.indexInformationCard.bgColor, 'border': `2px solid ${styleConfig.indexInformationCard.borderColor}`}"
            >
                <information
                    @handleJump="handleGetContract"
                    :member-info="memberList.promotionsMemberInfoVo"
                />
            </div>
        </div>
        <div class="recommend">
            <img :src="styleConfig.recommendTitleImg">
            <div
                class="info"
                v-for="(item, key) in memberList.recomList"
                :style="{'background': styleConfig.indexInformationCard.bgColor, 'border': `2px solid ${styleConfig.indexInformationCard.borderColor}`}"
                :key="key"
            >
                <information
                    :member-info="item"
                    @handleJump="handleGetContract"
                />
            </div>
        </div>
        <div class="protocol">
            <common-protocol
                :is-checked.sync="isCheckProtocol"
                :agreement-status="cmsConfig.agreementStatus"
                :style-config="{
                    textColor: isQiang ? '#FFFFFF' : '#000000', 
                    protocolColor: isQiang ? '#FFF100' : '#000000', 
                    protocolCheckedUrl: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658131834416_798082_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658289297359_644644_t.png'
                }"
            />
        </div>
        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :page-type="pageType"
            :style-config="{
                confirmButtonColor: '#ffffff',
                confirmButtonBgColor: '#000000',
                cancleButtonColor: '#000000'
            }"
        />
    </div>
</template>

<script>
import Information from '../components/Information';
import CommonProtocol from '@/common/business/CommonProtocol';
import CommonProtocolModal from '@/common/business/components/CommonProtocolModal';


export default {
    name: "Index",
    data() {
        return {
            isCheckProtocol: false,
            isShowModal: false,
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false,
                sixthScreen: false
            },
            screenHeight: 0,
        };
    },
    components: {
        Information,
        CommonProtocol,
        CommonProtocolModal
    },
    computed: {
        isQiang() {
            return this.cmsConfig.reportViewType === 1;
        },
        pageType() {
            return this.cmsConfig.reportViewType === 1 ? '锵锵锵H5' : '咚咚咚H5';
        },
    },
    inject: ["cmsConfig", "memberList", "styleConfig"],

    mounted() {
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                this.isCheckProtocol = value === 0;
            },
            {
                immediate: true
            }
        );
        this.$report(1, '首页访问');

        window.addEventListener("scroll", this.handleExposure);
        this.screenHeight = document.documentElement.clientHeight;
    },
    methods: {
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.isShowModal = false;
            this.$router.push({
                path: "/form"
            });
        },

        handleGetContract() {
            this.$report(3, '首页-主按钮点击');
            if (!this.isCheckProtocol) {
                this.isShowModal = true;
            } else {
                this.$router.push({
                    path: "/form"
                });
            }
        },


        handleExposure() {
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$report(2, "首页第2屏曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$report(2, "首页第3屏曝光");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$report(2, "首页第4屏曝光");
                this.reportLock.fourthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 4 &&
                !this.reportLock.fifthScreen
            ) {
                this.$report(2, "首页第5屏曝光");
                this.reportLock.fifthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 5 &&
                !this.reportLock.sixthScreen
            ) {
                this.$report(2, "首页第6屏曝光");
                this.reportLock.sixthScreen = true;
            }
            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleExposure);
            }
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.index-wrap {
    .header {
        width: 100%;
        height: 624px;
    }

    .interview {
        position: relative;
        width: 670px;
        margin: -220px auto 0px;
        >img {
            width: 100%;
            height: 120px;
        }
        .info {
            position: relative;
            margin: -50px auto 0px;
            padding: 40px;
            border-radius: 40px;
        }
    }
    .recommend {
        @include flex-center(column, null, center);
        margin-top: 64px;
        >img {
            width: 644px;
            height: 120px;
            margin-bottom: 16px;
        }
        .info {
            padding: 40px;
            background: #000000;
            border-radius: 40px;
        }
        >div {
            margin-top: 48px;
        }
    }

    .protocol {
        margin: 48px auto 0px;
        padding-bottom: 48px;
    }
}
</style>
