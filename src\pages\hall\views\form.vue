<template>
    <love-page class="hall-form">
        <form-avatar/>

        <z-image
            class="collection-container"
            :width="646"
            :height="754"
            :src="require('../assets/images/form-bg.png')">
            <!-- 注册项 -->
            <div
                v-for="(item, index) in formItems.slice(1)"
                :key="index"
                class="collection__item"
                @click="openSelect(item)">
                <div class="collection__item-label">{{ item.label }}</div>

                <!-- 工作地、出生年份、学历、婚况、月收入 -->
                <div class="collection__item-select">
                    <span v-if="item.value">{{ item.value }}</span>
                    <span v-else class="collection__item-todo">{{ '待完善' }}</span>
                </div>
            </div>

            <!-- 手机号 -->
            <div class="collection__input">
                <div class="collection__input-label">您的手机号</div>
                <van-field
                    ref="phoneInput"
                    class="collection__input-input"
                    placeholder="请输入11位手机号"
                    :value="registerForm.phone"
                    @input="inputPhone"
                    maxlength="11"
                    type="tel"/>
                <div class="collection__input-clear" @click="clearPhone"/>
            </div>
        </z-image>

        <simple-button
            :width="654"
            :disable="!finished"
            @click="submit"
        >
            提交
        </simple-button>

        <div class="hall-form__protocol">
            <div class="hall-form__protocol-desc">
                已阅读并同意<span @click="goServiceProtocol">《珍爱网服务协议》</span>
                和<span @click="goPersonDescription">《个人信息保护政策》</span>
            </div>
            <div @click="toggleCheckProtocol"
                 class="hall-form__protocol-checker-box">
                <div
                    :class="['hall-form__protocol-checker', isCheckProtocol ? 'hall-form__protocol-checker--check' : 'hall-form__protocol-checker--normal']"/>
            </div>
        </div>

        <!-- 虎扑合规要求 -->
        <div v-if="isHupu" class="hall-form__app-info">
            开发者：深圳市珍爱网信息技术有限公司
            <br>
            应用名称：珍爱
            | 应用版本：7.30.0
        </div>

        <!-- 底部选择框 -->
        <form-selector
            :visible.sync="selectorVisible"
            :select-type="selectType"
            :next-cb="openSelect"
            :select-param="selectParam"/>

        <protocol-modal
            v-model="modalVisible.protocolModal"
            @confirm="confirmCheck"/>
        <register-overwrite-modal
            v-model="modalVisible.registerOverwriteModal"
            :type="registerOverwriteModalType"
            @select-overwrite="overwriteAccount"
            @select-origin="loginOriginAccount"/>
        <msg-code-modal
            v-model="modalVisible.msgCodeModal"
            :validate-code="validateCode"/>
    </love-page>
</template>

<script>
import { createPage } from '@/common/framework';
import FormAvatar from "../components/form-avatar.vue";
import ProtocolModal from "../components/modal/protocol-modal";
import RegisterOverwriteModal from "../components/modal/register-overwrite-modal";
import MsgCodeModal from "../components/modal/msg-code-modal";
import FormSelector from "../components/form-selector";
import { Field } from 'vant';
import { mapMutations, mapState } from "vuex";
import { reportError } from "@/common/utils/report";
import { IS_SAPP } from "@/common/js/const";
import Api from '@/common/server/base';
import { registerResult } from '../enum';
import { pageTypeMap } from "@/common/config/register-dictionary.js";

export default createPage({
    name: "Form",
    components: {
        FormAvatar,
        FormSelector,
        RegisterOverwriteModal,
        MsgCodeModal,
        ProtocolModal,
        VanField: Field,
    },
    visitReport: {
        accessPoint: 7,
        accessPointDesc: '大表单注册页访问',
    },
    data() {
        return {
            registerOverwriteModalType: '',
            modalVisible: {
                protocolModal: false,
                registerOverwriteModal: false,
                msgCodeModal: false,
            },
            selectorVisible: false,
            selectType: '',
            selectParam: {},
            isCheckProtocol: false,
            validateAccountResult: {
                memberInfoVo: {},
                oldMemberID: null,
                overwriteRegistrationSwitch: '',
                type: null,
            },
            lockSubmit: false,
            lockOverwrite: false,
            phoneInputRef: null,
            messageCode: '',
        }
    },
    computed: {
        ...mapState([
            'registerForm',
            'formItems',
            'cmsConfig',
            'regMemberId'
        ]),
        isHupu() {
            return [ "924829", "924830" ].includes(Z.getParam("channelId"));
        },
        allFillIn() {
            return this.$z_.every(this.registerForm, (value) => {
                return !this.$z_.isNil(value) && value !== '';
            });
        },
        validatedPhone() {
            return /^1\d{10}/.test(this.registerForm.phone)
        },
        finished() {
            return this.allFillIn && this.validatedPhone
        },
        pageType() {
            return pageTypeMap.HALL
        }

    },
    created() {
        this.$watch('cmsConfig.agreementStatus', (value) => {
            this.isCheckProtocol = value === 0
        }, {
            immediate: true
        });
    },
    mounted() {
        this.phoneInputRef = this.$refs.phoneInput.$el.getElementsByTagName('input')[0];
    },
    methods: {
        ...mapMutations([
            'setRegisterForm',
            'setRegMemberId',
        ]),
        inputPhone(value) {
            const phone = value.replace(/\D/g, '');
            this.phoneInputRef.value = phone;
            this.setRegisterForm({
                key: 'phone',
                value: phone,
                isMark: false,
            });
        },
        clearPhone() {
            this.setRegisterForm({
                key: 'phone',
                value: '',
                isMark: false,
            });
        },
        async submit(isReport = true) {
            if (isReport) {
                this.$report(8, '大表单注册页-提交按钮点击');
            }

            if (this.lockSubmit) {
                return;
            }

            if (!this.allFillIn) {
                this.$toast('请完善资料再提交');
                return;
            }

            if (!this.validatedPhone) {
                return this.$toast('请输入正确的手机号');
            }

            if (!this.isCheckProtocol) {
                this.modalVisible.protocolModal = true;
                return;
            }

            if (isReport) {
                this.$report(9, '大表单注册页-提交按钮点击（有效点击）');
            }

            const sendData = {
                height: -1, // 表单没有身高项，为兼容老注册页直接传-1
                ...this.registerForm
            };
            delete sendData.phone;

            this.lockSubmit = true;

            try {
                const baseInfoPostResult = await Api.submitWapRegBaseInfo(sendData);

                if (baseInfoPostResult.isError) {
                    this.$toast(baseInfoPostResult.errorMessage);
                    // 老注册页qms上报逻辑
                    reportError('新注册页(大表单H5),注册信息提交失败 |' + baseInfoPostResult.errorMessage + ' | ' + JSON.stringify(this.registerForm));
                    return;
                }

                this.modalVisible.msgCodeModal = true;
            } finally {
                this.lockSubmit = false;
            }
        },
        openSelect(currentItem) {
            if ([ 'workCity', 'birthday' ].includes(currentItem.key)) {
                this.selectType = "selectSlide";
            } else if ([ 'marriage', 'education', 'salary' ].includes(currentItem.key)) {
                this.selectType = "selectBoard";
            }

            this.selectParam = currentItem;
            this.selectorVisible = true;
        },
        confirmCheck() {
            this.modalVisible.protocolModal = false;
            this.isCheckProtocol = true;
            this.submit(false);
        },
        toggleCheckProtocol() {
            if (this.cmsConfig.agreementStatus === 0) {
                return;
            }

            this.isCheckProtocol = !this.isCheckProtocol;
        },
        goServiceProtocol() {
            window.location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
        },
        goPersonDescription() {
            window.location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
        },
        async overwriteAccount() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = this.$gather.getOverwriteAccountParams(this.messageCode,this.pageType);
            const result = await Api.overwriteAccount(sendData); 

            this.lockOverwrite = false;

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            if(result.data.memberID) {
                this.setRegMemberId(result.data.memberID);
            }

            this.closeRegisterOverwriteModal();
            this.finishedRegister(registerResult.MANUAL_OVERWRITE_ACCOUNT.label);
        },
        loginOriginAccount() {
            if (IS_SAPP) {
                Z.client.invoke('ui', 'logout', { canBack: true });
                this.closeRegisterOverwriteModal();
                return;
            }

            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            if (this.validateAccountResult.overwriteRegistrationSwitch) {
                this.$utils.handleDownload();
                return;
            }

            // 跳wap登录页
            window.location.href = `${ location.protocol }//i.zhenai.com/m/portal/login.html`
        },
        async validateCode(messageCode) {
            this.$gather.setAbtZaTTTCookie();
            const sendData = this.$gather.getValidateCodeParams(this.registerForm.phone, messageCode,this.pageType); 
            const result = await Api.submitWapRegNoPasswordInfoV2(sendData);

            if (!result.isError) {
                if(result.data.memberID) {
                    this.setRegMemberId(result.data.memberID);
                }
                setTimeout(() => {
                    this.messageCode = messageCode;
                    this.checkOverrideAccount(result.data);
                }, 0);
            }

            return result;
        },
        checkOverrideAccount(validateAccountResult) {
            const { type } = validateAccountResult;

            this.validateAccountResult = validateAccountResult;

            switch (type) {
                case registerResult.LOGIN_ACCOUNT.value:
                    this.registerOverwriteModalType = registerResult.LOGIN_ACCOUNT.value;
                    this.showRegisterOverwriteModal();
                    break;
                case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                    this.registerOverwriteModalType = registerResult.MANUAL_OVERWRITE_ACCOUNT.value;
                    this.showRegisterOverwriteModal();
                    break;
                case registerResult.NEW_ACCOUNT.value:
                    this.finishedRegister(registerResult.NEW_ACCOUNT.label);
                    break;
                case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                    this.finishedRegister(registerResult.AUTO_OVERWRITE_ACCOUNT.label);
                    break;
            }

            const resultType = this.$z_.find(registerResult, {
                value: type
            });

            if (this.$z_.get(resultType, 'label')) {
                this.$report(50, '手机号验证成功', {
                    ext17: resultType.label,
                });
            }
        },
        showRegisterOverwriteModal() {
            this.modalVisible.registerOverwriteModal = true;
        },
        closeRegisterOverwriteModal() {
            this.modalVisible.registerOverwriteModal = false;
        },
        async handlegudgeonMark() {
            const sendData = {memberId: this.regMemberId || null, memberTaskType: 512};
            const result = await Api.gudgeonMark(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        },
        finishedRegister(ext) {
            this.$report(51, '注册成功并生成ID', {
                ext17: ext
            });
            // h5注册标记
            this.handlegudgeonMark();

            this.$toast('注册成功');
            this.$storage.setItem('registerFinished', true);
            this.$router.push({
                path: '/download',
            });
        },
    }
})
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.hall-form {
    padding: 40px 0;

    .collection {
        &-container {
            @include relative-center();
            margin-bottom: 48px;
            padding: 150px 32px 0;
            font-size: 28px;
            color: #000;
        }

        &__item {
            @include flex-center(row, space-between, center);
            height: 100px;

            &-todo {
                color: #FE4F06;
            }
        }

        &__item-label {
            width: 240px;
        }

        &__item-select {
            position: relative;
            padding-right: 48px;
            font-weight: 700;
        }

        &__item-select::after {
            content: '';
            position: absolute;
            top: -12%;
            right: 0;
            width: 32px;
            height: 32px;
            background: url("../assets/images/icon-arrow-right.png") center center no-repeat;
            background-size: contain;
        }

        &__input {
            position: relative;
            margin-bottom: 24px;
        }

        &__input-label {
            height: 100px;
            line-height: 100px;
        }

        &__input-input {
            padding-top: 13px;
            height: 72px;
            border-radius: 36px;
            background: #F4F4F5;
        }

        &__input-clear {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 80px;
            height: 72px;
            background: url("../assets/images/icon-x.png") center center no-repeat;
            background-size: 32px 32px;
        }
    }

    &__protocol {
        position: relative;
        margin: 48px 39px 0;
        font-size: 24px;
        line-height: 40px;

        &-checker-box {
            position: absolute;
            top: 2px;
            left: 0;
            width: 72px;
            height: 72px;
        }

        &-checker {
            width: 32px;
            height: 32px;

            &--check {
                background: url("../assets/images/icon-circle-check.png") center center no-repeat;
                background-size: contain;
            }

            &--normal {
                border: 4px solid #FFB900;
                border-radius: 50%;
            }
        }

        &-desc {
            padding-left: 46px;

            span {
                color:#FFB900;
            }
        }
    }

    &__app-info {
        opacity: 0.6;
        font-size: 22px;
        line-height: 40px;
        padding: 40px 39px 0;
        text-align: center;
    }
}
</style>
