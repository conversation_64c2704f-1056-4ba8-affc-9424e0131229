<template>
    <div class="main">
        <div
            class="main_bg"
            v-if="questionConfig.titleBackgroundImg"
            :style="{
                'background-image':
                    'url(' + questionConfig.titleBackgroundImg + ')'
            }"
        ></div>
        <div class="main_title" v-else>
            {{ questionConfig.title }}
        </div>
        <div class="main_info">
            <button class="main_info_btn" @click="goQuestions">
                立即测试
            </button>
            <p class="main_info_txt">
                <label
                    for="deal"
                    class="deal"
                    :class="{ active: dealStatus }"
                    v-if="questionConfig.agreementStatus"
                >
                    <input type="checkbox" id="deal" v-model="dealStatus" />
                </label>
                已阅读并同意
                <a href="https://i.zhenai.com/m/portal/register/prDeal.html"
                    >珍爱网服务协议</a
                >
                和
                <a href="https://i.zhenai.com/m/portal/register/serverDeal.html"
                    >个人信息保护政策</a
                >
            </p>
        </div>
        <Modal
            :show="dialogStatus"
            confirmText="确认"
            cancelText="取消"
            @handleConfirm="handleConfirm"
            @handleCancel="handleCancel"
        >
            <p class="modal_con_subtitle" slot="subTitle">
                已阅读并同意
                <a href="https://i.zhenai.com/m/portal/register/prDeal.html"
                    >&nbsp;珍爱网服务协议</a
                >&nbsp;和&nbsp;<br />
                <a href="https://i.zhenai.com/m/portal/register/serverDeal.html"
                    >个人信息保护政策</a
                >
            </p>
        </Modal>
    </div>
</template>

<script>
import { _getQuestionInfo } from "../api.js";
import Modal from "../components/modal.vue";
export default {
    name: "Index",
    data() {
        return {
            questionConfig: {
                channelType: 0,
                musicUrl: "",
                agreementStatus: 0,
                questionBackgroundImg: "",
                title: "",
                titleBackgroundImg: ""
            },
            id: Z.getParam("materialId"),
            dealStatus: false,
            dialogStatus: false
        };
    },
    components: {
        Modal
    },
    created() {
        this.getQuestionInfo(this.id);
    },
    methods: {
        async getQuestionInfo(id) {
            const res = await _getQuestionInfo({ id });
            if (res && !res.isError) {
                const {
                    channelType,
                    musicUrl,
                    agreementStatus,
                    questionBackgroundImg,
                    title,
                    titleBackgroundImg,
                    contentList
                } = res.data || {};
                this.questionConfig.channelType = channelType;
                this.questionConfig.agreementStatus = agreementStatus;
                this.questionConfig.title = title;
                this.questionConfig.titleBackgroundImg = titleBackgroundImg;

                this.$bus.$emit("changeBg", {
                    bg:
                        questionBackgroundImg ||
                        "https://photo.zastatic.com/images/common-cms/it/20211231/1640950066667_777666_t.jpg",
                    musicUrl:
                        musicUrl ||
                        "https://zaw-10009734.cos.ap-beijing-1.myqcloud.com/images/common-cms/it/20220117/1642411718423_314813.mp3"
                });

                window.sessionStorage.setItem(
                    "contentList",
                    JSON.stringify(contentList)
                );
                sessionStorage.setItem("is_toutiao", channelType);
            }
        },
        goQuestions() {
            if (this.questionConfig.agreementStatus && !this.dealStatus) {
                this.dialogStatus = true;
                return false;
            }
            this.$router.push({
                path: "/ques/0"
            });
        },
        handleConfirm() {
            this.dealStatus = true;
            this.dialogStatus = false;
            setTimeout(() => {
                this.$router.push({
                    path: "/ques/0"
                });
            }, 200);
        },
        handleCancel() {
            this.dialogStatus = false;
        }
    }
};
</script>
<style lang="scss" scoped>
@import "../assets/css/common.scss";
.main {
    position: relative;
    padding-top: 134px;
    text-align: center;
    &_bg {
        height: 224px;
        background-size: 100% auto;
        background-position: center center;
        background-repeat: no-repeat;
    }
    &_title {
        min-height: 200px;
        padding: 32px 54px 0;
        color: #fff;
        font-size: 64px;
        font-weight: 500;
        text-align: left;
        line-height: 76px;
    }
    &_info {
        // padding-bottom: 266px;
        &_btn {
            @include btn-style;
            margin: 460px auto 32px;
            color: #767dff;
            box-shadow: 0px 4px 22px 0px #e8e5fe;
        }
        &_txt {
            // padding: 0 54px;
            color: #fff;
            font-size: 26px;
            text-align: center;
            line-height: 36px;
        }
        &_txt > a {
            text-decoration: underline;
            color: #ada2ff;
        }
    }
    .deal {
        position: relative;
        top: 2px;
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 4px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220105/1641363970631_750427_t.png)
            no-repeat;
        background-size: 100% 100%;
        &.active {
            background: url(https://photo.zastatic.com/images/common-cms/it/20220105/1641363968012_104565_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        > input {
            display: none;
        }
    }
}
</style>
