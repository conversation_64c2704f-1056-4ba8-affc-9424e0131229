<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curGender === item.key?'active':''"
        >
            <img v-if="item.key == 0" src="https://photo.zastatic.com/images/common-cms/it/20240103/1704272846375_933992_t.png" alt="">
            <img v-else src="https://photo.zastatic.com/images/common-cms/it/20240103/1704272864374_96359_t.png" alt="">
           <p >{{ item.text }}</p>
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Gender",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curGender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curGender = val;
            const params = {
                key: "gender",
                value: val,
                isMark: false,
            };
            this.$report(3005,'选择性别');
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 100px;
    .item{
        width: 200px;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        margin-top: 45px;

        line-height: 48px;
        font-size: 30px;
        text-align: center;
        img {
            width: 200px;
            height: 200px;
        }
    }
    > .active{
        background: rgba(113, 107, 239, 0.1);
    }
}
</style>
