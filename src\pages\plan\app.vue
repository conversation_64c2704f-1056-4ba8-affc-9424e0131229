<template>
    <div id="app">
        <router-view></router-view>
        <toast/>
    </div>
</template>

<script>
import "@/common/styles/reset.scss";
import Toast from "@/common/components/Toast.vue";
import fixedClickDelay from '@/common/utils/fixed_click_delay.js';
import {mapState,mapMutations,mapGetters} from 'vuex';

fixedClickDelay()

Vue.use(Toast);

export default {
    created(){
        // 资料存本地
        let localFormInfo = JSON.parse(localStorage.getItem("localFormInfo")?localStorage.getItem("localFormInfo"):0);
        let localRegisterInfo = JSON.parse(localStorage.getItem("localRegisterInfo")?localStorage.getItem("localRegisterInfo"):0);

        if(localFormInfo){
            localFormInfo.forEach((item)=>{
                this.setFormInfo({[item.index]:item.value});
            })
        }

        if(localRegisterInfo){
            for(let key in localRegisterInfo){
                this.setRegisterInfo({[key]:localRegisterInfo[key]})
            }
        }

        // 【老注册页】用于后台自然流量注册统计
        (function setAbtParams(){
            var abtParams = Z.cookie.get('abt_params');

            try{
                var arr = abtParams.split('|');
                if(arr.length!==5){
                    abtParams = ''
                }
            }catch(e){
            }

            if(!abtParams){
                var pageKey = 'za_m_planForm',  // 根据不同业务key写入
                    planId = 0,
                    schemeId = 0;
                var channelId = Z.getParam('channelId') || 0;
                var subChannelId = Z.getParam('subChannelId') || 0;
                var tmp = [pageKey, channelId, subChannelId, planId, schemeId]
                Z.cookie.set('abt_params', tmp.join('|'), '.zhenai.com', '/', 24*1);
            }
        })();
    },
    mounted(){
        // 解决ios端，打开协议页面再回退后，页面title不改变问题
        window.addEventListener("pageshow", () => {
            this.setDocumentTitle("珍爱网");
        });
    },
    methods:{
        ...mapMutations([
            'setFormInfo',
            'setRegisterInfo'
        ]),
        setDocumentTitle(title) {
            document.title = title;
            if (/ip(hone|od|ad)/i.test(navigator.userAgent)) {
                var i = document.createElement('iframe');
                i.src = '';
                i.style.display = 'none';
                i.onload = function() {
                    setTimeout(function(){
                        i.remove();
                    }, 0)
                }
                document.body.appendChild(i);
            }
        },
    }
};
</script>

<style lang="scss">
    body{
        margin: 0;
        padding: 0;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    }
</style>
