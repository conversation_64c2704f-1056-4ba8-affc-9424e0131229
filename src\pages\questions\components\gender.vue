<template>
    <div class="gender">
        <div class="gender_info" @click="goNext(0)">
            <div
                :style="{ backgroundImage: `url(https://photo.zastatic.com/images/common-cms/it/20211230/1640831349785_231227_t.png)` }"
            ></div>
            <div class="txt" :class="{ active: showCircle0 }">
                男
            </div>
        </div>
        <div class="gender_info" @click="goNext(1)">
            <div
                :style="{ backgroundImage: `url(https://photo.zastatic.com/images/common-cms/it/20211230/1640831352721_860047_t.png)` }"
            ></div>
            <div class="txt" :class="{ active: showCircle1 }">
                女
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Gender",
    data() {
        return {
            showCircle0: false,
            showCircle1: false
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            2, // 记录点
            "注册-性别页曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    // activated() {
    //     const gender = this.$storage.loadFromStorage("__regInfo__", "gender");
    //     this["showCircle" + gender] = true;
    // },
    methods: {
        goNext(val) {
            if (val == 1) {
                this.showCircle0 = false;
            } else {
                this.showCircle1 = false;
            }
            this["showCircle" + val] = true;
            this.$select.mark({
                gender: val
            });
            this.$storage.saveToStorage("__regInfo__", "gender", val);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss" scoped>
.gender {
    display: flex;
    justify-content: space-between;
    margin: 40px 84px;
    &_info {
        display: flex;
        flex-direction: column;
        align-items: center;
        div {
            width: 240px;
            height: 260px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
        .txt {
            margin-top: 40px;
            width: 108px;
            height: 110px;
            line-height: 110px;
            text-align: center;
            color: #ff93dd;
            font-size: 32px;
            background: #fff;
            border-radius: 80px;
            &.active {
                box-shadow: 0px 4px 22px 0px #e8e5fe;
            }
        }
    }
    &_info:first-child {
        .txt {
            color: #767dff;
        }
    }
}
</style>
