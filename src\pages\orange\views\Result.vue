<template>
    <div class="result">
        <z-image
            :width="751"
            :height="278"
            :src="cmsConfig.goDownHeadImg"
        />
        <z-image
            :width="684"
            :height="524"
            src="https://photo.zastatic.com/images/common-cms/it/20221010/1665396524077_464679_t.png"
            class="result__info"
        >
            <div class="result__info__title">
                您的报名信息
            </div>
            <div
                v-for="(item, index) in registerFormArr"
                :key="index"
                class="result__info__item"
            >
                {{ translateLabel(keyArr[index]) }}：{{ item }}
            </div>
        </z-image>
        <div class="result__tips">
            已为您匹配100+优质同城异性<br />请下载珍爱APP与Ta们约会
        </div>
        <div
            class="result__list"
            v-if="recomList.length"
        >
            <div
                v-for="(item,index) in recomList"
                :key="index"
                class="result__list__item"
            >
                <z-image
                    border-radius="50%"
                    class="avatar"
                    :width="140"
                    :height="140"
                    :src="item.avatar"
                />
                <div class="name">
                    {{ item.nickName }}
                </div>
                <div class="age">
                    {{ item.age }}
                </div>
                <div class="job">
                    {{ item.profession }}
                </div>
            </div>
        </div>
        <div
            class="result__button"
            @click="goDownload"
            :style="{
                backgroundColor:cmsConfig.goDownButtonColor,
            }"
        >
            {{ cmsConfig.goDownButtonText }}
        </div>
    </div>
</template>

<script>
import { storage, session } from "@/common/utils/storage";
import { keyToValue, translateLabel } from "@/common/business/utils/localRegisterForm.js";

export default {
    name: 'Result',
    inject: ["cmsConfig","pageType","download"],
    data() {
        return {
            registerForm: storage.getItem(`cachedRegisterForm-${this.pageType}`),
            registerFormArr: [],
            keyArr:[],
            valueArr:[],
            recomList:session.getItem('recomList')
        };
    },
    watch:{
        registerForm:{
            handler(){
                Object.keys(this.registerForm).forEach(key=>{
                    this.keyArr.push(key);
                    this.valueArr.push(this.registerForm[key]);
                });

                this.registerFormArr = this.keyArr.map((item, index)=>
                    keyToValue(this.keyArr[index],this.valueArr[index])
                );
            },
            immediate: true,
        }
    },
    mounted(){
        this.$report(7, '下载引导页访问');
    },
    methods:{
        translateLabel,
        goDownload(){
            this.$report(7, '下载引导页-点击下载');
            this.download();
        }
    }
};

</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.result {
    @include flex-center(column, center, center);
    padding-bottom: 80px;

    &__info{
        margin-top: 40px;
        padding-top: 40px;

        &__title{
            font-weight: 600;
            font-size: 40px;
            color: #26273C;
            text-align: center;
            line-height: 54px;
        }

        &__item{
            margin-top: 24px;
            font-weight: 400;
            font-size: 32px;
            color: #26273C;
            line-height: 32px;
            text-align: center;
        }
    }
    
    &__tips{
        margin-top: 50px;
        font-weight: 600;
        font-size: 40px;
        color: #26273C;
        text-align: center;
        line-height: 60px;
    }

    &__list{
        @include flex-center(row,space-between,flex-start,wrap);
        padding: 24px 40px 0;     

        &__item{
            margin-top: 24px;
            width: 140px;
            > .avatar{
                width: 140px;
                height: 140px;
            }

            > .name{
                margin-top: 18px;
                font-weight: 400;
                font-size: 28px;
                color: #26273C;
                text-align: center;
            }

            > .age, > .job{
                margin-top: 12px;
                font-weight: 400;
                font-size: 26px;
                color: #AEAFB3;
                text-align: center;
            }   

            > .job{
                line-height: 34px;
            }
        }
    }

    &__button{
        margin-top: 60px;
        width: 654px;
        height: 116px;
        border-radius: 120px;
        font-weight: 500;
        font-size: 36px;
        color: #F4F5FF;
        text-align: center;
        line-height: 116px;
    }
}
</style>