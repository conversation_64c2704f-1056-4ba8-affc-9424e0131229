<template>
    <div class="guide-to-app">
        <div class="inner">
            <a
                class="rule-text"
                @click="handleIntroClick"
            >
                你的脱单计划数据根据37%法制制定>>
            </a>
            <div class="statistic-card">
                <div
                    class="statistic-item"
                    v-for="s in statistics"
                    :key="s.key"
                >
                    <div class="value">
                        <span class="num">{{ s.num }}</span>{{ s.unit }}
                    </div>
                    <div class="label">
                        {{ s.text }}
                    </div>
                </div>
            </div>
            <div class="privilege-card">
                <div class="title"></div>
                <div class="privilege-wrapper">
                    <div
                        class="privilege-item"
                        v-for="p in privilege"
                        :key="p.key"
                    >
                        <div :class="`brand ${p.key}`"></div>
                        <h5 class="name">
                            {{ p.name }}
                        </h5>
                        <p class="text">
                            {{ p.text }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="guide-bottom">
            <button
                class="bottom-btn"
                @click="handleDownloadClick"
            >
                立即下载开启脱单
            </button>
            <div class="bubble">
                立即下载开始你的计划吧
            </div>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="modalVisible"
            @close-modal="closeModal"
            @ok="handleModalOk"
            :modal-type="modalType"
            :modal-param="modalParam"
        />
    </div>
</template>

<script>
import { mapState } from "vuex";
import { Modal } from "../components/common/index.js";
import {reportKibana} from '@/common/utils/report.js';
export default {
    components: {
        Modal
    },
    data() {
        return {
            privilege: [
                { key: "show", name: "优先曝光", text: "置顶推荐" },
                { key: "wechat", name: "不限畅聊", text: "不限次数" },
                { key: "test", name: "心灵测试", text: "了解你的婚恋观" },
                { key: "match", name: "精准匹配", text: "12项筛选条件" }
            ],
            modalVisible: false,
            modalType: "",
            modalParams: {}
        };
    },
    computed: {
        ...mapState(["planInfo", 'materialId']),
        statistics() {
            const statisticMap = [[21, 8, 4], [17, 5, 3], [11, 3, 2]];
            const { hope } = this.planInfo;
            const statistic = statisticMap[hope || 0];
            return [
                {
                    key: "match",
                    num: statistic[0],
                    unit: "人",
                    text: "匹配·周"
                },
                { key: "chat", num: statistic[1], unit: "人", text: "聊天·周" },
                { key: "date", num: statistic[2], unit: "人", text: "见面·月" }
            ];
        }
    },
    created() {},
    mounted() {
        reportKibana("脱单计划H5", 14, "app脱单页访问", { ext16: this.materialId });
    },
    methods: {
        closeModal() {
            this.modalVisible = false;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.modalVisible = true;
        },
        handleModalOk() {},
        handleIntroClick() {
            this.openModal("modalMethod", {});
        },
        handleDownloadClick() {
            reportKibana("脱单计划H5", 15, "app脱单页点击下载按钮", { ext16: this.materialId });
            this.openModal("modalDownload", {});
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.guide-to-app {
  width: 100vw;
  min-height: 100vh;
  @include set-img("../assets/images/bg-privilege.png");
  background-size: cover;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  padding: 340px 0 234px;
  .inner {
    .rule-text {
      display: block;
      font-size: 26px;
      font-weight: 500;
      color: #279bae;
      line-height: 38px;
      margin-left: 58px;
    }
    .statistic-card {
      display: flex;
      margin: 20px 48px;
      height: 216px;
      @include set-img("../assets/images/bg-stastic.png");
      background-size: cover;
      background-position: 0% 50%;
      .statistic-item {
        flex: 1;
        text-align: center;
        margin-top: 86px;
        .value {
          font-size: 26px;
          font-weight: 800;
          color: #ff668a;
          line-height: 40px;
          .num {
            font-size: 40px;
            font-family: SourceHanSansCN-Heavy, SourceHanSansCN;
          }
        }
        .label {
          font-size: 26px;
          font-weight: 400;
          color: #9395a4;
          line-height: 1;
          margin-top: 20px;
        }
      }
    }
    .privilege-card {
      background-color: #f9fbfc;
      margin: 20px 48px;
      border-radius: 80px;
      .title {
        width: 430px;
        height: 82px;
        @include set-img("../assets/images/bg-privilege-title.png");
        background-size: cover;
        background-position: 0% 50%;
        margin: 0 auto;
      }
      .privilege-wrapper {
        margin: 0 58px;
        display: flex;
        flex-flow: row wrap;
        padding-bottom: 40px;
        .privilege-item {
          flex: 0 0 50%;
          text-align: center;
          margin-top: 46px;
          .brand {
            width: 140px;
            height: 156px;
            margin: 0 auto;
            &.wechat {
              @include set-img("../assets/images/brand-wechat.png");
              background-size: contain;
            }
            &.show {
              @include set-img("../assets/images/brand-show.png");
              background-size: contain;
            }
            &.test {
              @include set-img("../assets/images/brand-test.png");
              background-size: contain;
            }
            &.match {
              @include set-img("../assets/images/brand-match.png");
              background-size: contain;
            }
          }
          .name {
            font-family: SourceHanSansCN-Medium, SourceHanSansCN;
            font-size: 26px;
            font-weight: 500;
            color: #191c32;
            line-height: 40px;
          }
          .text {
            font-size: 20px;
            font-weight: 400;
            color: #9395a4;
            line-height: 30px;
          }
        }
      }
    }
  }
  @include set-bottom-btn("../assets/images/bg-button-bubble.png");
}
</style>
