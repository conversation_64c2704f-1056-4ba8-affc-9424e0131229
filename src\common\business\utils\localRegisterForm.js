import z_ from "@/common/zdash";
import oUserSelect from "@/common/ocpx/huichuan.js";
import { storage } from "@/common/utils/storage";
import * as dict from "@/common/config/register-dictionary";

// 重写localStorage.setItem，抛出setItemEvent事件方便监听localstorage
(function(){
    let originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key,newValue){
        //创建setItemEvent事件
        let event = new Event("setItemEvent");
        event.key = key;
        event.newValue = newValue;
        //提交setItemEvent事件
        window.dispatchEvent(event);
        //执行原setItem函数
        originalSetItem.apply(this,arguments);
    };
})();


/**
 * 更新localstorage中的表单项，返回更新后的表单对象
 * @param {*} target
 * @param {*} pageType
 * @returns
 */
function setLocalRegisterForm (target, pageType) {
    const key = z_.get(target, "key");
    const value = z_.get(target, "value");

    // registerForm默认值
    let registerForm = {
        gender: "",
        workCity: "",
        birthday: "",
        education: "",
        marriage: "",
        salary: "",
        phone: ""
    };

    // set数据前先同步当前的缓存中的registerForm
    const cachedRegisterForm = storage.getItem(
        `cachedRegisterForm-${pageType}`
    );
    if (cachedRegisterForm) {
        Object.assign(
            registerForm,
            cachedRegisterForm
        );
    }

    // set数据后同步至缓存中
    Object.assign(registerForm, {
        [key]: value
    });
    storage.setItem(
        `cachedRegisterForm-${pageType}`,
        registerForm
    );

    // 处理OCPX回传，isMark为false时不需要触发ocpx（如提交手机号时）
    let isMark = z_.get(target, "isMark");
    isMark = z_.isNil(isMark) ? true : isMark;
    if (isMark) {
        const getMarkValue =
            z_.get(target, "getMarkValue") ||
            (() => {
                return {
                    [key]: value
                };
            });

        const markValue = getMarkValue();
        if (!z_.isEmpty(markValue) && !z_.isNil(markValue)) {
            if (key == 'workCity') {
                oUserSelect.mark({workCity: sessionStorage.getItem('workCityReport')});
            } else {
                oUserSelect.mark(markValue);
            }
        }
    }

    return registerForm;
}

// 表单项Key到value映射
function keyToValue(type, key){
    let value = '';
    switch(type){
    case 'gender':
    case 'education':
    case 'marriage':
    case 'salary':
        value = dict[type].find(item => {
            return item.key === key;
        }).text;
        break;
    case 'workCity':
        value = findWorkCity(key).join('/');
        break;
    case 'birthday':
        value = new Date(key).getFullYear();
        break;
    case 'phone':
        value = key.replace(/(\d{3})(\d{4})/, '$1 $2 ');
        break;
    }
    return value;
}

// 表单名英文到中文的映射
function translateLabel(key, version){
    if(!version){
        const chineseName = dict.chineseNameMap[key];
        return chineseName;
    }
}

// 工作地代码到工作地映射
function findWorkCity(key){
    let valueArr = [];
    Z.workCity.forEach(province => {
        province.children.forEach(city => {
            if(["北京", "上海", "重庆", "天津"].includes(province.text)){
                if(key === city.key){
                    valueArr = [province.text, city.text];
                }
            } else {
                city.children.forEach(area => {
                    if(key === area.key){
                        valueArr = [province.text, city.text, area.text];
                    }
                });
            }
        });
    });
    return valueArr;
}

export {
    setLocalRegisterForm,
    translateLabel,
    keyToValue,
    findWorkCity
};
