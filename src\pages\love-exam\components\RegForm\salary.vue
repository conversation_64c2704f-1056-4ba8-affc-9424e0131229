<template>
    <div class="wrapper">
        <div class="item_container">
            <div
                v-for="(item,index) in list.options"
                :key="index"
                @click="goNext(item.key)"
                class="item"
                :class="curSalary == item.key?'active':''"
            >
                {{ item.text }}
            </div>
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Salary",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    mounted() {

    },
    data() {
        return {
            lock:false,
            curSalary: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).salary || ''
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curSalary = val;
            const params = {
                key: "salary",
                value: val
            };

            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                if (sessionStorage.getItem('loveAnswerIsSingle') == '1') {

                    this.$router.push({
                        path:`/finish-v3`
                    });
                    return;
                } else {
                    this.$emit('go-next');
                }
            },300);
            this.$report(105, '收入页-按钮点击',);

        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    .item_container {
        margin-top: 140px;
    }
    .item{
        width: 610px;
        height: 88px;
        background: #ffff;
        border-radius: 45px;
        display: flex;
        align-items: center;
        justify-content: center;

        line-height: 88px;
        font-size: 30px;
        text-align: center;
        letter-spacing: 4px;
        margin-bottom: 30px;
        border: 2px solid #B8BCCC;
    }
    .active{
        background: #fff0f0;
        border: none;
        color: #D7204A;
    }
}
</style>
