<template>
    <div class="wrapper">
        <div class="item_container">
            <div
                v-for="(item,index) in list.options"
                :key="index"
                @click="goNext(item.key)"
                class="item"
                :class="(curGender !== '' && curGender == item.key)?'active':''"
            >
                <div class="avatar_container">
                    <img
                        class="avatar"
                        :src="genderHeadMap[item.key]"
                        alt=""
                    >
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20240731/1722421751739_321204.png"
                        alt=""
                        class="active_icon"
                    >
                </div>

                <span>{{ item.text }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Gender",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            genderHeadMap: {
                0: 'https://photo.zastatic.com/images/common-cms/it/20240731/1722421238986_852378.png',
                1: 'https://photo.zastatic.com/images/common-cms/it/20240731/1722421260196_949260.png'
            },
            curGender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curGender = val;
            const params = {
                key: "gender",
                value: val
            };

            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                const isSingle = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).isSingle;
                if (isSingle) {

                    this.$emit('go-next');
                } else {
                    this.$router.push({
                        path:`/examResultBefore`
                    });

                }

            },300);
            this.$report(93,`性别页-按钮点击`);

        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    .item_container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 480px;
        margin-top: 325px;
    }
    .item{
        display: flex;
        flex-direction: column;
        align-items: center;
        display: flex;
        align-items: center;
        justify-content: center;

        line-height: 88px;
        font-size: 30px;
        text-align: center;
        letter-spacing: 4px;
        margin-bottom: 30px;
        .avatar_container {
            position: relative;
            border-radius: 50%;
            width: 180px;
            height: 180px;
        }
        .avatar {
            border-radius: 50%;
            width: 180px;
            height: 180px;
            font-size: 0;
        }
        span {
            margin-top: 28px;
        }
        .active_icon {
            opacity: 0;
            width: 38px;
            height: 38px;
            position: absolute;
            right: 0;
            bottom: 0;
        }
    }
     .active{
        .active_icon {
            opacity: 1;
        }
        .avatar {
            border: solid 4px #000;
        }
    }
}
</style>
