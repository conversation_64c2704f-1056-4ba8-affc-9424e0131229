<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="common-download-guide-modal"
    >
        <h1 class="common-download-guide-modal__title">
            <slot></slot>
        </h1>

        <p class="common-download-guide-modal__desc">
            <slot name="desc"></slot>
        </p>

        <div
            class="common-download-guide-modal__btn"
            @click="closeModal"
            :style="{ background: styleConfig.confirmButtonBgColor, color: styleConfig.confirmButtonColor}"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { reportKibana } from "@/common/utils/report";

export default {
    name: 'CommonDownloadGuideModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
            }
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    reportKibana(this.pageType, 400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            reportKibana(this.pageType, 401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.common-download-guide-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 600;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
