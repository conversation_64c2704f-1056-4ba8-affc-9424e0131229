<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="proced">
            <div class="proced-title">
                {{ procedText.title }}
            </div>
            <div
                class="proced-info"
                v-if="procedNum === 2"
            >
                {{ formatNumber }}
            </div>
            <div
                class="proced-desc"
                v-else
            >
                免费领取倒计时：<span>{{ timeText }}</span>
            </div>
            <p class="proced-tips">
                请前往应用市场搜索下载【珍爱APP】
            </p>
            <div
                class="proced-btn"
                @click="closeModal"
            >
                好的
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup } from "vant";
const textList = [
    {
        title: "前往珍爱APP领取专属课程"
    },
    {
        title: "珍爱APP累计助力脱单用户数"
    }
];
export default {
    name: "Proced",
    components: {
        VanPopup: Popup
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        procedNum: {
            type: Number,
            default: 0
        },
        timeText: {
            type: String
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    data() {
        return {
            count: 260000 + Math.floor(Math.random() * (30 - 26 + 1) * 10000)
        };
    },
    computed: {
        procedText() {
            return textList[this.procedNum - 1];
        },
        formatNumber() {
            return this.count.toString().replace(/\d+/, function(n) {
                return n.replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
            });
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit("input", value);
        },
        closeModal() {
            this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit("input", false);
        }
    }
};
</script>
<style lang="scss" scoped>
.proced {
    width: 558px;
    padding: 48px 40px;
    text-align: center;
    background: #fff;
    border-radius: 32px;
    &-title {
        color: #000000;
        font-size: 36px;
        font-weight: 500;
    }
    &-info {
        height: 66px;
        margin-top: 22px;
        color: #f16598;
        font-size: 32px;
        font-weight: 500;
        line-height: 66px;
        background: linear-gradient(269deg, #f5f0fe 0%, #eff7fe 100%);
        border-radius: 24px;
    }
    &-desc {
        height: 66px;
        line-height: 66px;
        margin-top: 22px;
        color: #4828a2;
        font-size: 32px;
        font-weight: 500;
        background: linear-gradient(269deg, #f5f0fe 0%, #eff7fe 100%);
        border-radius: 24px;
        > span {
            color: #f16598;
            font-size: 32px;
            font-weight: 500;
        }
    }
    &-tips {
        padding-top: 22px;
        color: #6c6d75;
        font-size: 24px;
    }
    &-btn {
        margin: 48px auto 0;
        width: 462px;
        height: 88px;
        color: #fff;
        font-size: 32px;
        font-weight: 500;
        line-height: 88px;
        text-align: center;
        background: #5368f0;
        border-radius: 78px;
        &:active {
            opacity: 0.7;
        }
    }
}
</style>
