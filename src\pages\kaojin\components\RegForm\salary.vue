<template>
    <div class="wrapper">
        <!--<img
            class="user_card"
            :src="bannerMap[gender]"
            alt=""
        >-->
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curSalary === item.key?'active':''"
        >
            <img
                src="https://photo.zastatic.com/images/common-cms/it/20240521/1716282359883_359550.png"
                alt=""
                class="active_img"
            >
            {{ item.text }}
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "../../utils/index";
import { storage as Storage } from "@/common/utils/storage";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { PAGE_TYPE, changeProfile } from "../../config";

export default {
    name: "Sal<PERSON>",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    activated () {
        this.gender = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender;
    },
    mounted() {

    },
    data() {
        return {
            bannerMap: {
                0: 'https://photo.zastatic.com/images/common-cms/it/20240527/1716781443829_652966.png',
                1: 'https://photo.zastatic.com/images/common-cms/it/20240527/1716781452684_164487.png'
            },
            gender: 0,
            lock:false,
            curSalary: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).salary || ''
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curSalary = val;
            const params = {
                key: "salary",
                value: val,
                isMark: false,
            };
            this.$report(6, '年收入页-按钮点击', {
                ext28: val
            });
            setLocalRegisterForm(params, PAGE_TYPE);
            // changeProfile({...params, token: oCookie.get('token')})
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 180px;
    .item{
        width: 550px;
        height: 90px;
        line-height: 90px;
        background: #F1F2F4;
        border-radius: 45px;
        font-size: 32px;
        color: #222833;
        text-align: center;
        margin-bottom: 32px;
        position: relative;
    }
    .active_img {
        position: absolute;
        right: 70px;
        top: 0;
        transform: translateY(-50%);
        width: 35px;
        height: 35px;
        display: none;
    }
    .user_card {
        width: 690px;
        height: 180px;
        margin-bottom: 50px;
    }
    > .active{
        background-image: linear-gradient(90deg, #B2F8F4 0%, #CBFFEB 100%);
        border-radius: 45px;
        .active_img {
            display: block;
        }
    }
}
</style>
