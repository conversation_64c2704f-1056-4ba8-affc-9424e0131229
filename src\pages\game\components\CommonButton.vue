<template>
    <div
        class="button-wrap"
        :style="{width: computedWidth, height: computedHeight, fontSize: computedFontSize}"
        @click="$emit('click', $event)"
    >
        {{ config.des }}<span v-if="config.isNeedIcon" />
    </div>
</template>

<script>
export default {
    name: 'CommonButton',
    props: {
        config: {
            type: Object,
            default: {
                width: 558,
                height: 110,
                fontSize: 36,
                des: '即刻开启挑战',
                isNeedIcon: true
            }
        }
    },
    computed: {
        computedWidth() {
            return this.$utils.pxToRem(this.config.width);
        },
        computedHeight() {
            return this.$utils.pxToRem(this.config.height);
        },
        computedFontSize() {
            return this.$utils.pxToRem(this.config.fontSize);
        }
    }
};
</script>


<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.button-wrap {
    @include flex-center(row, center, center);
    position: relative;
    background: #B0F344;
    border: 2px solid #000000;
    border-radius: 55px;
    color: #000000;
    font-weight: bold;
    &::after {
        content: '';
        position: absolute;
        z-index: -1;
        left: -2px;
        top: 10px;
        width: 100%;
        height: 100%;
        background: #000000;
        border: 2px solid #000000;
        border-radius: 55px;
    }
    span {
        display: inline-block;
        position: relative;
        margin-left: 16px;
        width: 72px;
        height: 72px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220728/1658996454106_409318_t.png");
        animation: move 2s infinite;
    }

    @keyframes move {
        0% {
            left: 0px;
        }
        50% {
            left: 50px;
        }
        100% {
            left: 0px;
        }
    }
}


</style>