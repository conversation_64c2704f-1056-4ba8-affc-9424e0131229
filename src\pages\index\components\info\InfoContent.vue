<template>
    <div class="info-content-wrapper">
        <div class="content__list">
            <div class="list__logo--car"></div>
            <div
                class="list__logo--phone"
                @click="openModal('modalDownload', {}, 'phoneButton')"
            >
                查看完整手机号
            </div>
            <div
                class="list__logo--wechat"
                @click="openModal('modalDownload', {}, 'weChatButton')"
            >
                查看完整微信号
            </div>
            <div class="list__title">
                Ta的详细资料
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    {{ modelInfo.name }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    年龄
                </div>
                <div class="list__item__value">
                    {{ modelInfo.ageString }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    工作地
                </div>
                <div class="list__item__value">
                    {{ modelInfo.workCityString }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    学历
                </div>
                <div class="list__item__value">
                    {{ modelInfo.educationString }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    月收入
                </div>
                <div class="list__item__value">
                    {{ modelInfo.salaryString }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    手机号
                </div>
                <div class="list__item__value">
                    {{ modelInfo.phone }}
                </div>
            </div>

            <div class="list__item">
                <div class="list__item__label">
                    微信号
                </div>
                <div class="list__item__value">
                    {{ modelInfo.weChat }}
                </div>
            </div>
        </div>

        <div class="content__photo">
            <div class="photo__title">
                <div class="photo__title--main">
                    Ta的动态
                </div>
                <div
                    class="photo__title--sub"
                    @click="openModal('modalDownload', {}, 'momentButton')"
                >
                    查看所有{{ momentCount }}条
                </div>
            </div>

            <div class="content-block">
                <div
                    class="photo__block--main"
                    :style="{
                        backgroundImage: `url(${modelInfo['momentImg1']})`
                    }"
                ></div>
                <div class="photo__block--sub">
                    <div
                        class="block--sub__item"
                        v-for="(item, index) in photoArr"
                        :key="index"
                        :style="{
                            backgroundImage: `url(${
                                modelInfo['momentImg' + (index + 2)]
                            })`
                        }"
                    ></div>
                    <div
                        class="block--sub__item--last"
                        @click="openModal('modalDownload', {}, 'photoButton')"
                    >
                        更多照片
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { getRandomInt } from "@/common/utils/tools";

export default {
    components: {},
    data() {
        const photoArr = ["", "", ""];

        return {
            momentCount: getRandomInt(10, 50),
            photoArr
        };
    },
    computed: {
        ...mapState(["modelInfo"])
    },
    created() {},
    mounted() {
        // this.jump()
    },
    methods: {
        openModal(modalType, modalParam, from) {
            let description, accessPoint;
            if (from === "phoneButton") {
                description = "翻牌下载页-查看手机按钮点击";
                accessPoint = 9;
            } else if (from === "weChatButton") {
                description = "翻牌下载页-查看微信按钮点击";
                accessPoint = 10;
            } else if (from === "momentButton") {
                description = "翻牌下载页-查看动态按钮点击";
                accessPoint = 11;
            }
            // 打桩
            this.$report(accessPoint, description, {
                ext16: 1,
                ext18: this.$route.path === "/info" ? 1 : 2 // 1 无盲盒 2 有盲盒
            });
            this.$emit("open-modal", modalType, modalParam);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.info-content-wrapper {
    position: relative;
    margin: -50px auto 0;
    width: 702px;
}

.content__list {
    // 重合部分高度为50px
    padding-top: 50px;
    padding-left: 32px;
    padding: 50px 32px 80px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
    border-radius: 32px;
}

.list__logo--car {
    position: absolute;
    top: 102px;
    right: -20px;
    width: 198px;
    height: 198px;
    @include set-img("../../assets/imgs/car-logo.png");
}

.list__logo--phone {
    position: absolute;
    top: 606px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787cff;
    border-radius: 24px;

    font-size: 24px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
}

.list__logo--wechat {
    position: absolute;
    top: 686px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787cff;
    border-radius: 24px;

    font-size: 24px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
}

.list__title {
    margin-top: 52px;
    font-size: 36px;
    font-family: Source Han Sans SC;
    font-weight: 700;
    color: #26273c;
    line-height: 63px;
}

.list__item {
    @include set-flex(flex-start, center);
    margin-top: 50px;
    height: 30px;
    font-size: 32px;
    color: #26273c;
    line-height: 30px;
}

.list__item__label {
    position: relative;
    top: 50%;
    // transform: translateY(-50%);
    display: inline-block;
    text-align: justify;
    text-align-last: justify;
    width: 160px;
}

.list__item__label::after {
    content: "";
    width: 100%;
    display: inline-block;
    height: 0;
}

.list__item__value {
    position: relative;
    padding-left: 32px;
}

.list__item__value::before {
    content: ":";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

// 昵称单独处理
.list__item:nth-child(5) {
    .list__item__label {
        text-align: left;
        text-align-last: left;
    }

    .list__item__value::before {
        content: "";
    }
}

.content__photo {
    margin: 39px auto 0;
    width: 702px;
    height: 518px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
    border-radius: 32px;
}

.photo__title {
    @include set-flex(space-between, center);
    padding: 0 33px;
    height: 140px;
}

.photo__title--main {
    font-size: 36px;
    font-weight: 700;
    color: #26273c;
    line-height: 140px;
}

.photo__title--sub {
    position: relative;
    padding-right: 28px;
    font-size: 26px;
    font-weight: 400;
    color: #6c6d75;
    line-height: 140px;
}

.photo__title--sub::after {
    content: "";
    @include set-img("../../assets/imgs/right-arrow.png");
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-52%);
    width: 32px;
    height: 32px;
    font-weight: 400;
    color: #6c6d75;
    line-height: 140px;
}

.content-block {
    @include set-flex(space-between, center);
    margin: 0 auto;
    width: 640px;
}

.photo__block--main {
    width: 315px;
    height: 315px;
    border-radius: 16px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.photo__block--sub {
    @include set-flex(space-between, center);
    flex-wrap: wrap;
    align-content: space-between;
    width: 315px;
    height: 315px;
}

.block--sub__item {
    width: 154px;
    height: 154px;
    border-radius: 16px;
    // background: grey;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.block--sub__item--last {
    width: 154px;
    height: 154px;
    background-color: rgba($color: #26273c, $alpha: 0.1);
    border-radius: 16px;

    font-size: 26px;
    font-weight: 400;
    color: rgba($color: #26273c, $alpha: 0.5);
    line-height: 154px;
    text-align: center;
}
</style>
