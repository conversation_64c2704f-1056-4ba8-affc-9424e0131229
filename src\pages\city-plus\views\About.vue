<template>
    <div class="about">
        <Back @on-back="goBack" />
        <keep-alive>
            <component
                :is="curComp"
                :list="curRegItem"
                @val-updated="valUpdate"
            />
        </keep-alive>
    </div>
</template>

<script>
import * as dict from "@/common/config/register-dictionary";
import Gender from "../components/RegForm/gender.vue";
import Birthday from "../components/RegForm/birthday.vue";
import Education from "../components/RegForm/education.vue";
import Marriage from "../components/RegForm/marriage.vue";
import Salary from "../components/RegForm/salary.vue";
import WorkCity from "../components/RegForm/workcity.vue";
import Back from "../components/Back.vue";
export default {
    name: "About",
    data() {
        return {
            regItems: [
                {
                    type: "gender",
                    label: "你的性别是",
                    desc: "完善资料，参与同城脱单约会",
                    options: dict.genderV3
                },
                {
                    type: "workCity",
                    label: "你的工作地",
                    desc: "选择工作地点，为你匹配同城",
                    options: Z.workCity
                },
                {
                    type: "birthday",
                    label: "你的出生年份是",
                    desc: "为你推荐更多的同龄人",
                    options: dict.birthdayV2
                },
                {
                    type: "education",
                    label: "你的学历是",
                    desc: "为你推荐高学历人群",
                    options: dict.education
                },
                {
                    type: "marriage",
                    label: "你的婚姻状况是",
                    desc: "我们只接受单身用户参与同城脱单约会",
                    options: dict.marriage
                },
                {
                    type: "salary",
                    label: "你的月收入是",
                    desc: "为你匹配高收入人群",
                    options: dict.salary
                }
            ],
            curIndex: 0
        };
    },
    components: {
        Gender,
        Birthday,
        Education,
        Marriage,
        Salary,
        WorkCity,
        Back
    },
    watch: {
        $route: {
            handler() {
                this.curIndex = this.$route.query.id ? this.$route.query.id : 0;
            },
            immediate: true,
            deep: true
        }
    },
    computed: {
        curRegItem() {
            return this.regItems[this.curIndex];
        },
        curComp() {
            return (
                this.curRegItem.type[0].toUpperCase() +
                this.curRegItem.type.slice(1)
            );
        }
    },
    methods: {
        valUpdate(val, finish) {
            if (finish) {
                this.$router.push({ path: "/anim" });
            } else {
                this.curIndex++;
                this.$router.push({
                    path: "/about",
                    query: { id: this.curIndex }
                });
            }
        },
        goBack() {
            const REPORTENUM = {
                0: {
                    accessPoint: 4, des: '性别页-返回按钮点击'
                },
                1: {
                    accessPoint: 5, des: '工作地页-返回按钮点击'
                },
                2: {
                    accessPoint: 6, des: '出生年份页-返回按钮点击'
                },
                3: {
                    accessPoint: 7, des: '学历页-返回按钮点击'
                },
                4: {
                    accessPoint: 8, des: '婚况页-返回按钮点击'
                },
                5: {
                    accessPoint: 9, des: '收入页-返回按钮点击'
                }
            };
            this.$report(REPORTENUM[this.curIndex].accessPoint, REPORTENUM[this.curIndex].des);


            if (this.curIndex <= 0) {
                this.$router.push({ path: "/" });
            } else {
                this.curIndex--;
                this.$router.push({
                    path: "/about",
                    query: { id: this.curIndex }
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.about {
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(-30deg, rgba(183, 191, 255, .8) 46%, rgba(250, 251, 255, .8) 86%);
}
</style>
