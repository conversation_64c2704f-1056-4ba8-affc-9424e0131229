<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="common-register-modal"
    >
        <h1 class="title">
            该手机已在珍爱网APP注册过！
        </h1>

        <!-- <div
            class="member-info"
            v-if="registerResult.MANUAL_OVERWRITE_ACCOUNT.value === validateAccountResult.type"
        >
            <div class="member-text">
                原账号信息
            </div>
            <div class="info-wrap">
                <span class="info-item">{{ `${overwriteMemberInfo.gender === 0 ? "男" : "女" }` }}</span>
                <span class="info-item">{{ overwriteMemberInfo.workPlace }}</span>
                <span class="info-item">{{ `${overwriteMemberInfo.age}岁` }}</span>
                <span class="info-item">{{ overwriteMemberInfo.education }}</span>
            </div>
        </div> -->

        <div class="btn-wrap">
            <div
                v-if="registerResult.MANUAL_OVERWRITE_ACCOUNT.value === validateAccountResult.type"
                class="btn btn-continue"
                @click="handleSelectOverwrite"
            >
                <div>继续</div>
                <div>（原有帐号的资料将被覆盖）</div>
            </div>

            <div
                class="btn"
                @click="handleSelectOrigin"
            >
                登录原有帐号
            </div>

            <!-- <div
                class="cancel"
                @click="handleCloseModal"
            >
                取消
            </div> -->
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

import { registerResult } from '@/common/config/register-dictionary';
import { reportKibana } from "@/common/utils/report";

export default {
    name: 'CommonRegisterOverwriteModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        validateAccountResult: {
            type: Object,
            default: () => {return {};}
        },
        handleCancle: {
            type: Function,
            default: () => {}
        },
        overwriteMemberInfo: {
            type: Object,
            default: () => { return {}; }
        },
    },
    data() {
        return {
            registerResult,
        };
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    reportKibana(this.pageType, 310, '已注册提醒弹窗-访问');
                }
            },
            immediate: true,
        },
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        handleSelectOverwrite() {
            reportKibana(this.pageType, 312, '已注册提醒弹窗-覆盖注册按钮点击');
            this.$emit('select-overwrite');
        },
        handleSelectOrigin() {
            reportKibana(this.pageType, 311, '已注册提醒弹窗-登录按钮点击');
            this.$emit('select-origin');
        },
        handleCloseModal() {
            this.handleCancle();
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.common-register-modal {
    @include flex-center(column, null, center);
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 240px 0 44px;
    position: relative;
    overflow: initial;

    &::before {
        content: "";
        display: block;
        width: 264px;
        height: 264px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230523/1684835917787_278201_t.png');
        position: absolute;
        top: -48px;
        left: 50%;
        transform: translateX(-50%);
    }

    .title {
        color: #26273cff;
        font-size: 36px;
        font-weight: 500;
        line-height: 54px;
    }

    .member-info {
        margin-top: 16px;
        .member-text {
            font-size: 28px;
            color: #6C6D75;
            text-align: center;
            line-height: 40px;
        }
        .info-wrap {
            margin-top: 16px;
            display: flex;
            .info-item {
                flex: 0 0 auto;
                background: #F2F4F5;
                border-radius: 16px;
                padding: 8px 20px;
                font-size: 28px;
                color: #2B2D33;
                line-height: 40px;
                margin-left: 8px;
                &:first-child {
                    margin-left: 0;
                }
            }
        }
    }

    .btn {
        @include flex-center(column);
        font-size: 32px;
        background: #767DFF;
        margin: 0;
        background-color: transparent;
        height: 48px;
        border-radius: 0;
        color: #767DFF;

        &-continue {
            padding-top: 3px;
            > div:last-child {
                font-size: 20px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }

        &:first-child {
            margin: 48px 0 20px;
            color: #FFF;
            width: 462px;
            height: 88px;
            background-color: #767DFF;
            border-radius: 44px;
        }
    }

    .cancel {
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #767DFF;
        text-align: center;
    }
}
</style>
