<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in this.list.options"
            :key="index"
            @click="goNext(item)"
            class="item"
            :class="item.score === curOptionContent?'active':''"
        >
            {{ item.desc }}
        </div>
    </div>
</template>

<script>
import { session as Session, storage as Storage  } from '@/common/utils/storage.js';

export default {
    name:"QuizItem",
    props: {
        list: {
            type: Object,
            default: () => {}
        },
        currentIndex: {
            type:Number,
            default:0
        },
        length: {
            type:Number,
            default:0
        }
    },
    data(){
        return {
            answerList:[],
            lock:false
        };
    },
    created() {
        this.answerList = Storage.getItem('sessionAnswerList') || [];
    },
    computed:{
        curOptionContent(){
            let optionContent;
            this.answerList.forEach((item)=>{
                if(item.index === this.list.index){
                    optionContent = item.ansScore;
                }
            });
            return optionContent;
        }
    },
    mounted(){

    },
    methods:{
        goNext(optionContent){
            if(this.lock){
                return;
            }
            this.lock=true;

            // 存储答案至sessionStorage
            if(this.answerList.map(item=>+item.index).includes(this.list.index)){
                this.answerList.forEach(item=>{
                    if(item.index === this.list.index){
                        item.ansScore = optionContent.score;
                    }
                });
            }else{
                this.answerList.push({
                    index: this.list.index,//第几题
                    ansScore: optionContent.score, // 分数 第几个选项
                    optionType: this.list.optionType,
                    resultType: this.list.resultType
                });
            }
            Storage.setItem(`sessionAnswerList`,this.answerList);

            if(this.length === this.currentIndex + 1) {
                setTimeout(()=>{
                    this.$emit('go-next');
                },300);
                setTimeout(()=>{
                    this.lock = false;
                },2000);
            } else {
                setTimeout(()=>{
                    this.lock = false;
                    this.$emit('go-next');
                },300);
            }


        }
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    .item{
        width: 396px;
        height: 89px;
        background-color: #A98CE6;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        font-family: 'love';

        line-height: 48px;
        font-size: 30px;
        text-align: center;
        color: #FFFFFF ;
        letter-spacing: 4px;
        box-shadow: 0 3px 3px 0 #bd92eb66;
    }
    > .active{
        background: linear-gradient(180deg,#5243FE, #9A55F0)
    }
}
</style>
