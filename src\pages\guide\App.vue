<template>
    <div id="app">
        <router-view
            :key="$route.fullPath"
            v-if="canRender"
        />
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';


export default createRoot({
    name: "App",
    data() {
        return {
            canRender: false,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    mounted() {
        judgeIfInToutiaoIos();
        this.initAsync();
    },
    methods: {
        async initAsync() {
            // 处理异步
            this.canRender = true;
        },
    }
});
</script>

<style lang="scss">
// 全局样式
html,body{
    font-family: PingFangSC-Regular;
    font-weight: 400;
}
</style>
