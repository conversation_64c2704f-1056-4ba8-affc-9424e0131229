<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="recommend"
    >
        <z-image
            class="recommend__close"
            :width="72"
            :height="72"
            src="https://photo.zastatic.com/images/common-cms/it/20221010/1665385213180_374260_t.png"
            @click="close"
        />
        <div class="recommend__title">
            你要放弃认识Ta们的机会吗？
        </div>
        <div
            class="recommend__list"
            v-show="recomList.length"
        >
            <div
                v-for="(item,index) in recomList"
                :key="index"
                class="recommend__list__item"
            >
                <z-image
                    border-radius="50%"
                    class="avatar"
                    :width="120"
                    :height="120"
                    :src="item.avatar"
                />
                <div class="name">
                    {{ item.nickName }}
                </div>
                <div class="age">
                    {{ item.age }}
                </div>
                <div class="job">
                    {{ item.profession }}
                </div>
            </div>
        </div>
        <div
            class="recommend__download"
            @click="goDownload"
        >
            立即约会&nbsp;开始下载
        </div>
    </van-popup>
</template>

<script>
import {_getCommonRecommendList} from '../api.js';
import { session } from "@/common/utils/storage";

export default {
    name: 'RecommendModal',
    props: {
        value: {
            type: Boolean,
            default: false,
        }
    },
    data(){
        return {
            recomList:[]
        };
    },
    watch: {
        value: {
            async handler(value) {
                if (value) {
                    this.$report(6, '挽留弹窗曝光');
                    const res = await _getCommonRecommendList({count: 16}).catch(e=>this.$toast(e.message));
                    
                    if (res.isError) {
                        this.$toast(res.errorMessage);
                        return;
                    }
                    session.setItem('recomList',res.data.list);
                    this.recomList = res.data.list.slice(0,4); 
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            if(!value){
                this.$emit('goResult');
            }
            this.$emit('input', value);
        },
        goDownload() {
            this.$report(6, '挽留弹窗-点击下载');
            this.$emit('download');
        },
        close() {
            this.$report(6, '挽留弹窗-点击关闭');
            this.$emit('input', false);
            this.$emit('goResult');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.recommend {
    @include flex-center(column,flex-start,center);
    padding-bottom: 40px;
    width: 622px;
    background: #FFFFFF;
    border-radius: 32px;

    &__close{
        align-self: flex-end;
    }

    &__title{
        font-weight: 500;
        font-size: 36px;
        color: #26273C;
        letter-spacing: 0;
        text-align: center;
        line-height: 36px;
    }

    &__list{
        @include flex-center(row,space-between,flex-start);
        padding: 48px 40px 0;     
        width: 100%;   

        &__item{
            width: 120px;
            > .avatar{
                width: 120px;
                height: 120px;
            }

            > .name{
                margin-top: 18px;
                font-weight: 400;
                font-size: 28px;
                color: #26273C;
                text-align: center;
            }

            > .age, > .job{
                margin-top: 12px;
                font-weight: 400;
                font-size: 26px;
                color: #AEAFB3;
                text-align: center;
            }  
            
            > .job{
                line-height: 34px;
            }
        }
    }

    &__download{
        margin-top: 34px;
        width: 490px;
        height: 88px;
        background: #F971AD;
        border-radius: 44px;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        line-height: 88px;
    }
}
</style>
