<!-- rem兼容方案 -->
<meta id="__j_viewport_meta_tag__" name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover">
<script>
;(function flexible (window, document) {
  var docEl = document.documentElement
  var dpr = window.devicePixelRatio || 1

  // adjust body font size
  function setBodyFontSize () {
    if (document.body) {
      document.body.style.fontSize = (12 * dpr) + 'px'
    }
    else {
      document.addEventListener('DOMContentLoaded', setBodyFontSize)
    }
  }
  setBodyFontSize();

  // set 1rem = viewWidth / 10
  function setRemUnit () {
    var rem = docEl.clientWidth / 10
    docEl.style.fontSize = rem + 'px'
  }

  setRemUnit()

  // reset rem unit on page resize
  window.addEventListener('resize', setRemUnit)
  window.addEventListener('pageshow', function (e) {
    if (e.persisted) {
      setTimeout(() => {
        setRemUnit()
      }, 20);
    }
  })

  // detect 0.5px supports
  if (dpr >= 2) {
    var fakeBody = document.createElement('body')
    var testElement = document.createElement('div')
    testElement.style.border = '.5px solid transparent'
    fakeBody.appendChild(testElement)
    docEl.appendChild(fakeBody)
    if (testElement.offsetHeight === 1) {
      docEl.setAttribute('data-dpr', Math.floor(dpr));
    }
    docEl.removeChild(fakeBody)
  }

   docEl.setAttribute('data-origin-dpr', window.devicePixelRatio);
   // Math.floor后的dpr
   docEl.setAttribute('data-floor-dpr', Math.floor(window.devicePixelRatio));

    // 新版本wk,无需做适配
    // 珍爱app内，没有ZAClientWK标识的，都需要适配
    var needToFit = /client|zhenai/ig.test(navigator.userAgent) && !/ZAClientWK/g.test(navigator.userAgent);
    if(needToFit) {
    //    var oMeta = document.getElementById('__j_viewport_meta_tag__')
    //    /**
    //     * 在IOS APP旧版的UIview中，
    //     * iPhonex/xr/xs/xs max 底部和顶部安全区域不可用，
    //     * 故适配让其顶部和底部安全区域都可以显示HTML页面
    //    */
    //    var iphoneXFixed = (osv = window.navigator.userAgent.match(/(iphone|ipad|ipod)\s+os\s+(\d{2})/i)) && osv.length > 0 && +osv[osv.length - 1] > 10 && (812 == screen.height && 375 == screen.width || 896 == screen.height && 414 == screen.width) ? ", viewport-fit=cover" : "";
    //     oMeta.setAttribute("content", oMeta.getAttribute('content') + iphoneXFixed);

       /**
        * 在IOS APP旧版的UIview中，
        * iPhonex/xr/xs/xs max 顶部安全区域在上面被适配到可显示HTML，但实际应用中并不希望显示，
        * 故适配让其顶部安全区域不显示HTML页面
       */
        // 具体页面无需再添加css适配
        if(getQueryString('fullscreen') != '1') {
          // APP iOS 全屏页的时候不需要适配顶部
          var style = document.createElement('style');
          style.innerHTML = '@media only screen and (device-width:375px) and (device-height:812px) { html { padding-top: 88px!important; } } @media only screen and (device-width:414px) and (device-height:896px) { html { padding-top: 88px!important; } }';
          document.getElementsByTagName('head')[0].appendChild(style);
        }
    }

    // 查询字符串
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg); //获取url中"?"符后的字符串并正则匹配
        var context = "";

        if (r != null)
            context = r[2];
        reg = null;
        r = null;
        return context == null || context == "" || context == "undefined" ? "" : context;
    }
}(window, document));
</script>
