<template>
    <div class="birthday">
        <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(option, name) in birthdayV3"
                :key="name"
            >
                <h4 class="birthday-info-title">
                    {{ name }}后
                </h4>
                <div
                    class="birthday-info-card"
                >
                    <span
                        class="bic-num"
                        :class="{ active: item === curBirthday }"
                        v-for="item in option"
                        :key="item"
                        @click="goNext(item)"
                    >{{ item }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    import {
        setLocalRegisterForm,
        keyToValue
    } from "@/common/business/utils/localRegisterForm.js";
    import { storage as Storage } from "@/common/utils/storage";
    import { birthdayV3 } from "@/common/config/register-dictionary";
    import { PAGE_TYPE } from "../../config";
    export default {
        name: "birthdayV3",
        props: {
            pageType: {
                type: String,
                default: ""
            }
        },
        data() {
            return {
                birthdayV3,
                curBirthday: ''
            };
        },
        created () {
            this.initBirthday()
        },
        methods: {
            initBirthday(){
                const curBirthday = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).birthday || "";
                let curYear = '';
                if(curBirthday){
                    curYear = keyToValue('birthday', curBirthday);
                }
                this.curBirthday = curYear;
            },
            goNext(val) {
                this.curBirthday = val;
                const dateValue = new Date(val + "/" + 1 + "/" + 1).getTime();
                const params = {
                    key: "birthday",
                    value: dateValue,
                    isMark: false,
                    getMarkValue:()=>{
                        // 构造回传给后台的时间格式
                        return {
                            year: keyToValue('birthday', dateValue)+"",
                            month: "1",
                            day: "1"
                        };
                    }
                };
                this.$report(3006, "选择出生年月");
                setLocalRegisterForm(params, '新注册路径手机前置');
                setTimeout(()=>{
                    this.lock = false;
                    this.$emit('go-next');
                },300);
            }
        },
        mounted() {
            // this.$report(4, "出生年份页访问");
            this.$refs.birthScroll.scrollTo(0, 340);
        }
    };
</script>

<style lang="scss" scoped>
    .birthday {
        margin-bottom: 40px;
        .title {
            padding: 48px 0 26px 48px;
            color: #0f1122;
            font-size: 64px;
            font-weight: 500;
        }
        .subtitle {
            padding-left: 48px;
            color: #0f1122;
            font-size: 32px;
        }
        .birthday-scroll {
            overflow-y: scroll;
            height: calc(100vh - 364px);
        }
        &-info {
            &-title {
                margin-left: 74px;
                font-weight: 500;
                font-size: 36px;
                color: #767DFF;
            }
            &-card {
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                padding: 8px 6px;
                margin: 24px 40px 48px;
                background: #fff;
                background-color: #EBEFF1;
                border-radius: 60px;
                > span {
                    flex: 0 0 auto;
                    display: block;
                    color: #26273C;
                    font-size: 36px;
                    line-height: 96px;
                    width: 128px;
                    height: 96px;
                    border-radius: 50px;
                    text-align: center;
                }
                .active {
                    color: #fff;
                    background-color: #8b76f9;
                }
            }
        }
    }
</style>
