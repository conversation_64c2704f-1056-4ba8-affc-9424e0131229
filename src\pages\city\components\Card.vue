<template>
    <div class="card-wrapper">
        <div
            class="card-wrapper__box"
            v-if="cmsConfig.planName !== '同城交友B方案(群聊)'"
            :style="{'background-image' : cmsConfig.questionBodyImg1}"
        >
            <div class="card-wrapper__box-title">
                {{ cmsConfig.planName === '同城交友A方案(活动)' ? '近期活动' : '优质推荐' }}
            </div>
        </div>
        <div class="card-wrapper__box-swiper">
            <za-swiper
                v-if="list.length"
                item-width-mode="custom"
                :list="list"
                :inner-width="this.$utils.pxToRem(750)"
                :inner-height="this.$utils.pxToRem(innerHeight)"
                :span-gap="this.$utils.pxToRem(24)"
                :initial-offset="this.$utils.pxToRem(cmsConfig.planName === '同城交友A方案(活动)' ? 42 : 29)"
                auto-play
            >
                <template
                    slot-scope="{ item }"
                    slot="default"
                >
                    <!-- A方案 -->
                    <div
                        class="card-wrapper__box-swiper-item-a"
                        v-if="cmsConfig.planName === '同城交友A方案(活动)'"
                    >
                        <img
                            class="card-wrapper__box-swiper-item-a-pic"
                            :src="`${item.img}?imageMogr2/thumbnail/284x284`"
                        >
                        <p class="card-wrapper__box-swiper-item-a-title">
                            {{ item.name }}
                        </p>
                        <div
                            class="card-wrapper__box-swiper-item-a-avatar"
                        >
                            <img
                                class="card-wrapper__box-swiper-item-a-avatar-pic"
                                :key="imgIndex"
                                :src="`${img}?imageMogr2/thumbnail/62x62`"
                                v-for="(img, imgIndex) in item.avatarList[0]"
                            >
                        </div>
                    </div>

                    <!-- B方案 -->
                    <div
                        class="card-wrapper__box-swiper-item-b"
                        v-if="cmsConfig.planName === '同城交友B方案(群聊)'"
                        :style="{'background-image' : cmsConfig.questionBodyImg1}"
                    >
                        <div class="card-wrapper__box-swiper-item-b-title">
                            <div
                                class="left"
                            >
                                <div>{{ item.name }}</div>
                                <p class="num">
                                    成员：{{ item.num }}
                                </p>
                            </div>
                            <div
                                class="right"
                                @click="handleJoin"
                            >
                                加入
                                <img
                                    src="https://photo.zastatic.com/images/common-cms/it/20220606/1654503278828_901570_t.png"
                                    class="icon"
                                >
                            </div>
                        </div>
                        <div class="card-wrapper__box-swiper-item-b-group">
                            <div
                                class="item"
                                :key="memberIndex"
                                v-for="(member, memberIndex) in item.userList[0]"
                            >
                                <img :src="`${member.avatar}?imageMogr2/thumbnail/100x100`">
                                <p class="job">
                                    {{ member.occupation }}
                                </p>
                                <p class="age">
                                    {{ member.age }}岁
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- C方案 -->
                    <div
                        class="card-wrapper__box-swiper-item-c"
                        v-if="cmsConfig.planName === '同城交友C方案(单人)'"
                    >
                        <div class="card-wrapper__box-swiper-item-c-avatar">
                            <img
                                class="card-wrapper__box-swiper-item-c-avatar-left"
                                :src="`${item.imgList[0]}?imageMogr2/thumbnail/284x284`"
                            >
                            <div class="card-wrapper__box-swiper-item-c-avatar-right">
                                <img
                                    class="card-wrapper__box-swiper-item-c-avatar-right-sub"
                                    :src="`${item.imgList[1]}?imageMogr2/thumbnail/130x130`"
                                >
                                <img
                                    class="card-wrapper__box-swiper-item-c-avatar-right-sub"
                                    :src="`${item.imgList[2]}?imageMogr2/thumbnail/130x130`"
                                >
                            </div>
                        </div>
                        <div class="card-wrapper__box-swiper-item-c-info">
                            <span class="card-wrapper__box-swiper-item-c-info-name">
                                {{ item.name }}
                            </span>
                            <div class="card-wrapper__box-swiper-item-c-info-tag">
                                <div class="card-wrapper__box-swiper-item-c-info-tag-sub-name">
                                    <img
                                        src="https://photo.zastatic.com/images/common-cms/it/20220606/1654496113842_419193_t.png"
                                    >
                                    <span>已实名</span>
                                </div>
                                <div class="card-wrapper__box-swiper-item-c-info-tag-sub">
                                    已购车
                                </div>
                                <div class="card-wrapper__box-swiper-item-c-info-tag-sub">
                                    已购房
                                </div>
                            </div>
                        </div>
                        <div class="card-wrapper__box-swiper-item-c-des">
                            <!-- 深圳 · 28岁 · 深大博士 · 小富婆 · 十栋楼收租 · 编不下 · 哈哈哈哈哈哈哈哈哈哈哈哈 -->
                            {{ item.labelList && item.labelList.join(' · ') }}
                        </div>
                    </div>
                </template>
            </za-swiper>
        </div>
    </div>
</template>

<script>
import ZaSwiper from '@za/vue-za-swiper';
import '@za/vue-za-swiper/dist/style.css';

export default {
    name: 'Card',
    inject: ['cmsConfig'],
    components: {
        ZaSwiper
    },
    data() {
        return {
            list: [],
            timer: null,
        };
    },
    computed: {
        innerHeight() {
            const heightMap = {
                '同城交友A方案(活动)' : 486,
                '同城交友B方案(群聊)' : 685,
                '同城交友C方案(单人)' : 560
            };
            return heightMap[this.cmsConfig.planName];
        }
    },
    mounted() {
        if (this.cmsConfig.planName === '同城交友A方案(活动)') {
            this.list = this.cmsConfig.planA;
        } else if (this.cmsConfig.planName === '同城交友B方案(群聊)') {
            this.list = this.cmsConfig.planB;
        } else {
            this.list = this.cmsConfig.planC;
        }
 
        if (this.cmsConfig.planName !== '同城交友C方案(单人)') {
            this.timer = setInterval(() => {
                this.handleGenerateData();
            }, 2000);
        }
    },

    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },

    methods: {
        handleJoin() {
            this.$emit('select-join');
        },

        handleGenerateData() {
            if (this.cmsConfig.planName === '同城交友A方案(活动)') {
                this.$z_.each(this.list, o => {
                    o.avatarList.push(o.avatarList[0]);
                    o.avatarList.shift();
                });
            } else {
                this.$z_.each(this.list, o => {
                    o.userList.push(o.userList[0]);
                    o.userList.shift();
                });
            }
        }

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.card-wrapper {
    margin-top: -510px;
    position: relative;
    &__box {
        width: 690px;
        height: 592px;
        margin: 0 auto;
        border-radius: 40px;
        &-title {
            font-weight: 600;
            font-size: 40px;
            color: #FFFFFF;
            padding: 45px 0px 42px 30px;
        }
        &-swiper {
            width: 750px;
            position: relative;
            margin-top: -454px;
            &-item-a {
                width: 320px;
                height: 486px;
                background: #FFFFFF;
                box-shadow: 0 20px 40px 0 rgba(201,203,222,0.10);
                border-radius: 24px;
                padding: 18px 18px 33px;
                &-pic {
                    width: 284px;
                    height: 284px;
                    border-radius: 16px;
                }
                &-title {
                    margin-top: 24px;
                    margin-left: 6px;
                    position: relative;
                    font-weight: 500;
                    font-size: 28px;
                    color: #1A202C;
                }
                &-avatar {
                    margin-top: 25px;
                    overflow: hidden;
                    height: 62px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    &-pic {
                        width: 62px;
                        height: 62px;
                        border-radius: 50%;
                    }
                }
            }

            &-item-b {
                width: 480px;
                opacity: 0.88;
                height: 100%;
                padding: 28px 32px 32px;
                border-radius: 40px;
                display: block;
                &-title {
                    display: flex;
                    justify-content: space-between;
                    .left {
                        font-weight: 600;
                        font-size: 32px;
                        color: #FFFFFF;
                        div:first-child {
                            @include truncate;
                            width: 224px;
                        }
                        .num {
                            font-weight: 400;
                            font-size: 28px;
                            color: #FFFFFF;
                            margin-top: 22px;
                        }
                    }
                    .right {
                        width: 151px;
                        height: 84px;
                        background: #FFFFFF;
                        border-radius: 42px;
                        color: #FF4B90;
                        font-size: 32px;
                        font-weight: 500;
                        padding: 23px 22px 25px 26px;
                        position: relative;
                        .icon {
                            position: absolute;
                            right: 22px;
                            top: 20px;
                            width: 40px;
                            height: 40px;
                        }
                    }
                }

                &-group {
                    width: 416px;
                    height: 496px;
                    background: #FFFFFF;
                    border-radius: 30px;
                    margin-top: 40px;
                    padding: 0px 28px;
                    overflow: hidden;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    .item {
                        width: 160px;
                        height: 212px;
                        display: inline-block;
                        text-align: center;
                        margin-top: 30px;
                        img {
                            width: 100px;
                            height: 100px;
                            border-radius: 50%;
                        }
                                
                        .job {
                            margin-top: 14px;
                            font-weight: 500;
                            font-size: 28px;
                            width: 168px;
                            color: #1A202C;
                            @include truncate;
                        }

                        .age {
                            margin-top: 18px;
                            font-weight: 400;
                            font-size: 24px;
                            color: #9395A4;
                        }
                    }                
                }
            }

            &-item-c {
                width: 518px;
                height: 100%;
                background: #FFFFFF;
                border-radius: 24px;
                padding: 40px;
                position: relative;
                &-avatar {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    &-left {
                        width: 284px;
                        height: 284px;
                        border-radius: 16px;
                    }
                    &-right {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        &-sub {
                            width: 130px;
                            height: 130px;
                            border-radius: 16px;
                        }
                    }
                }

                &-info {
                    margin-top: 30px;
                    display: flex;
                    &-name {
                        font-weight: 600;
                        font-size: 40px;
                        color: #26273C;
                    }
                    &-tag {
                        margin-left: 36px;
                        display: flex;
                        &-sub {
                            width: 72px;
                            height: 40px;
                            text-align: center;
                            font-size: 20px;
                            border-radius: 8px;
                            line-height: 40px;
                            margin-right: 8px;
                            background: rgba(241,241,241,0.70);
                            &-name {
                                position: relative;
                                width: 108px;
                                height: 40px;
                                line-height: 40px;
                                font-size: 20px;
                                margin-right: 8px;
                                background: rgba(241,241,241,0.70);
                                span {
                                    padding-left: 40px;
                                }
                                img {
                                    width: 24px;
                                    height: 24px;
                                    position: absolute;
                                    left: 8px;
                                    top: 6px;
                                }
                            }
                        }
                    }
                }

                &-des {
                    margin-top: 28px;
                    font-weight: 400;
                    font-size: 28px;
                    color: #6C6D75;
                    line-height: 50px;
                    @include ellipse(2)
                }
            }
        }
    }
}
</style>