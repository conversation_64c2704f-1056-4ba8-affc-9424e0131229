<template>
    <div class="blindinfo-wrapper" :style="cmsConfig.pageColor">
        <info-header></info-header>

        <info-blind 
            :has-finished="hasFinished"
            @open-modal="openModal(arguments)"
            @select-blind="selectBlind"
        ></info-blind>

        <info-avatar 
            :has-finished="hasFinished"
            @open-modal="openModal(arguments)"
        ></info-avatar>

        <info-content 
            v-if="hasFinished" 
            @open-modal="openModal(arguments)"
        ></info-content>

        <info-footer
            @open-modal="openModal(arguments)"
        ></info-footer>

        <info-default></info-default>
        <!-- 弹窗 -->
        <modal 
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
        ></modal>        
    </div>
</template>

<script>
import {mapState,mapMutations,mapActions} from 'vuex';
import {Modal} from '../components/common/index.js';
import {InfoHeader,InfoBlind,InfoAvatar,InfoContent,InfoFooter,InfoDefault} from '../components/info/index.js';
export default {
    components:{
        InfoHeader,
        InfoBlind,
        InfoAvatar,
        InfoContent,
        InfoFooter,
        InfoDefault,
        Modal
    },
    data() {
        return {
            hasFinished:false,
            showModal:false,
            modalType:"modalDownload",
            modalParam:{},
        };
    },
    computed:{
        ...mapState([
            'cmsConfig'
        ]),
    },
    async created(){
        let flagFilled = localStorage.getItem("flagFilled");

        // 没有注册态，跳回大表单页
        if(flagFilled !== '1'){
            this.$router.push({
                path:'index'
            })
        }
        // 注册态只能用一次
        localStorage.setItem("flagFilled", "0");

        // 清空注册信息
        localStorage.setItem("localFormInfo", "");
        localStorage.setItem("localRegisterInfo", "");
        localStorage.setItem("defaultBirthday","");
        localStorage.setItem("defaultWorkCity","");

        // 重置主图，从盲盒信息页主动跳普通信息页
        // await this.setModelInfo({mainImg:''});
    },
    mounted(){
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
    },
    methods:{
        ...mapActions([
            'setModelInfo'
        ]),
        selectBlind(){
            this.hasFinished = true;
        },
        openModal(args){
            this.modalType = args[0];
            this.modalParam = args[1];
            this.showModal = true;
        },
        closeModal(){
            this.showModal = false;
        },
    }
};
</script>

<style lang="scss" scoped>
.blindinfo-wrapper{
    padding-bottom: 100px;
}
</style>
