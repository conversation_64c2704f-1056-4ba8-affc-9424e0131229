<template>
    <div
        id="trigger"
        :class="isPlaying ? 'rotate' : ''"
        :style="{
            backgroundImage: `url(${
                isPlaying ? img.player : img.playerDisabled
            })`,
            top: `${top}px`,
            right: `${right}px`
        }"
        @click="switchPlay"
        class="player__wrapper"
    >
        <audio
            id="player"
            loop="loop"
            :src="musicUrl"
            :autoplay="autoplay"
            style="width: 0; height: 0; opacity: 0"
            @play="onPlay"
        ></audio>
    </div>
</template>

<script>
export default {
    name: "Player",
    props: {
        top: {
            type: Number,
            default: 37
        },
        right: {
            type: Number,
            default: 40
        },
        musicUrl: {
            type: String,
        },
        autoplay: {
            type: <PERSON>olean,
            default: true
        }
    },
    data() {
        return {
            img: {
                player:
                    "https://photo.zastatic.com/images/common-cms/it/20211230/1640852879310_306444_t.png",
                playerDisabled:
                    "https://photo.zastatic.com/images/common-cms/it/20211230/1640852876025_620862_t.png"
            },
            player: null,
            isPlaying: false
        };
    },
    mounted() {
       this.$nextTick(() => {
            this.player = document.getElementById("player");

            // 页面隐藏时声音关闭
            document.addEventListener("visibilitychange", this.pause);

            // safari兼容
            document.addEventListener("pagehide", this.pause);
       })
    },
    methods: {
        // 开始播放
        play() {
            if (!this.player.paused) return;
            this.player.play();
            if (!this.player.paused) {
                this.isPlaying = true;
            }
        },
        // 暂停
        pause() {
            if (this.player.paused) return;
            this.player.pause();
            if (this.player.paused) {
                this.isPlaying = false;
            }
        },
        switchPlay() {
            if (this.isPlaying || !this.player.paused) {
                this.pause();
            } else {
                this.play();
            }
        },
        onPlay() {
            this.isPlaying = true;
        }
    }
};
</script>

<style lang="scss" scoped>
@keyframes rotate {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}

.player__wrapper {
    z-index: 9;
    position: fixed;
    width: 60px;
    height: 60px;
    border-radius: 30px;
    right: 0;
    top: 0;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.rotate {
    animation-name: rotate;
    animation-iteration-count: infinite;
    animation-duration: 5s;
    animation-timing-function: linear;
}
</style>
