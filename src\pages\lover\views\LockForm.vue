<template>
    <div class="form-wrapper">
        <div class="main-title"></div>
        <div class="recom-wrapper">
            <ul class="recom-list">
                <li
                    class="recom-item"
                    v-for="item in recomList"
                    :key="item.modelId"
                >
                    <div class="photo-wrapper">
                        <div
                            class="photo" 
                            :style="`background-image:url(${item.avatar})`"
                        ></div>
                        <span class="desc">{{ item.outOfSingleTime }}个月内脱单</span>
                    </div>
                    <div class="name">{{ item.name }}</div>
                    <div class="extra">{{ item.age }}岁·{{ item.height }}cm</div>
                </li>
            </ul>
        </div>
        <div class="title">
            请验证手机号，立即领取
        </div>
        <common-form
            :page-type="cmsConfig.planName"
            class="form"
            :class="{'phone-valid': finished}"
            :filter-config="['phone']"
            :style-config="{
                color: '#4A3BC0',
                selectorColor: '#4A3BC0',
                selectorFontColor: '#FFFFFF',
                labelColor: '#6C6D75',
                valueColor: '#26273C',
                phoneBgColor: '#fff'
            }"
            :is-need-sub-title="false"
            :showLabel="false"
        />
        <common-submit
            :page-type="cmsConfig.planName"
            class="submit"
            :is-need-protocol="false"
            :style-config="{
                modalConfig: {
                    confirmButtonColor: '#FFFFFF',
                    confirmButtonBgColor: 'linear-gradient(154deg, #7566EB 0%, #4A3BC0 100%)',
                    cancleButtonColor: '#6C6D75',
                }
            }"
            :handle-after-regisiter="handleJump"
            :handle-login="handleJump"
        >
            <common-button
                :config="{width: 610, height: 116, fontSize: 36, des: '立即领取'}"
                :class="{'submit-disabled': !finished}"
                @click="handleClick"
            />
        </common-submit>
    </div>
</template>

<script>
import CommonForm from "@/common/business/CommonForm";
import CommonSubmit from "@/common/business/CommonSubmit";
import CommonButton from "../components/CommonButton";
import { _queryIdealLoverModelListNoReg, _saveIdealLoverDressed } from "../api";
import { session, storage as Storage } from "@/common/utils/storage.js";
import { getMiniPathV2 } from "@/common/business/utils/wecom";
import { pageTypeChnMap } from '@/common/config/register-dictionary';

export default {
    name: "Form",
    components: {
        CommonForm,
        CommonSubmit,
        CommonButton
    },
    inject: ["cmsConfig"],
    data() {
        return {
            registerForm: Storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            ) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            recomList: [],
        };
    },
    computed: {
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            const val = this.allFillIn && this.validatedPhone;
            if (val) {
                this.$report(13, "手机验证页-输入11位格式正确的手机号");
            }
            return val;
        }
    },

    mounted() {
        this.handleWatchForm();
        this.handleFetchList();
        this.$report(13, "手机验证页-访问");
    },
    methods: {
        async handleJump() {
            const data = {
                stature: session.getItem("Figure"),
                dressed: session.getItem("Ootd"),
                memberId: session.getItem("reg_memberid")
            };

            try {
                const result = await _saveIdealLoverDressed(data);
                if (result.isError) {
                    this.$toast(result.errorMessage);
                }
            } catch(e) {
                // 
            }

            this.judgeJumpWay();
        },

        async judgeJumpWay() {
            if (["定制恋人（导小程序）", "定制恋人新UI(小程序)"].includes(this.cmsConfig.planName)) {
                const miniPath = await getMiniPathV2(
                    "pages/idealLover/idealLover", {
                        ext30: pageTypeChnMap[this.cmsConfig.planName],
                        pageType: pageTypeChnMap[this.cmsConfig.planName],
                    }
                );
                window.location.href = miniPath;
            } else {
                this.$router.push("/recom");
            }
        },

        handleWatchForm() {
            window.addEventListener("setItemEvent", e => {
                if (e.key === `cachedRegisterForm-${this.cmsConfig.planName}`) {
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        },
        
        async handleFetchList() {
            const {
                gender,
                birthday,
            } = this.registerForm;
            const sendData = {
                gender,
                birthday,
                stature: session.getItem("Figure"),
                dressed: session.getItem("Ootd"),
                count: 3,
            };
            const result = await _queryIdealLoverModelListNoReg(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            this.recomList = result.data.list;
        },

        handleClick() {
            if (this.finished) {
                this.$report(13, "手机验证页-点击按钮（可点状态）");
            } else {
                this.$report(13, "手机验证页-点击按钮（未点亮时）");
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.form-wrapper {
    position: relative;
    z-index: 1;
    padding-top: 120px;
    min-height: 100vh;
    background-color: #F4F5FF;
    // background: linear-gradient(180deg, #f6f6fb 40%, #d4d1ff);
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        top: 0;
        width: 750px;
        height: 550px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20230424/1682323739359_641902_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .main-title {
        background: url(https://photo.zastatic.com/images/common-cms/it/20230423/1682241229241_441164_t.png)
            no-repeat;
        background-size: 100% 100%;
        width: 641px;
        height: 56px;
        margin: 0 auto;
    }
    .title {
        font-weight: 600;
        font-size: 48px;
        color: #26273C;
        line-height: 58px;
        text-align: center;
        margin-top: 88px;
    }
    .form {
        margin: 24px auto 0px;
        width: 610px;

        /deep/ .form-item--phone__clear {
            display: none;
        }
        &.phone-valid {
            /deep/ .form-item--phone__clear {
                display: block;
                width: 60px;
                height: 60px;
                right: 24px;
                top: 50%;
                transform: translateY(-50%);
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20230426/1682481110449_103070_t.png);
            }   
        }
    }
    .submit {
        margin-top: 40px;
        &-disabled {
            opacity: 0.4;
        }
    }
    .recom-wrapper {
        margin: 72px 43px;
        .recom-list {
            display: flex;
            justify-content: space-between;
            height: 288px;
            .recom-item {
                flex: 0 0 auto;
                text-align: center;
                .photo-wrapper {
                    border: 1px solid #5848C0;
                    border-radius: 16px;
                    overflow: hidden;
                    position: relative;
                }
                .photo {
                    width: 198px;
                    height: 198px;
                    position: relative;
                    background-size: cover;
                    filter: blur(4px);
                }
                .desc {
                    display: block;
                    width: 100%;
                    height: 44px;
                    font-size: 24px;
                    color: #fff;
                    line-height: 44px;
                    background: rgba(0, 0, 0, .25);
                    text-align: center;
                    position: absolute;
                    bottom: -1px;
                }
                .name {
                    font-weight: 500;
                    font-size: 28px;
                    color: #000;
                    line-height: 34px;
                    margin-top: 16px;
                }
                .extra {
                    font-size: 24px;
                    color: #959599;
                    line-height: 30px;
                    margin-top: 8px;
                }
            }
        }
    }
}
</style>
