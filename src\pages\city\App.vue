<template>
    <div class="city-root">
        <router-view
            :key="$route.fullPath"
            v-if="canRender"
        />
    </div>
</template>

<script>
import { createRoot } from '@/common/framework';
import Api from '@/common/server/base';
import z_ from "@/common/zdash";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';

export default createRoot({
    name: "App",
    data() {
        return {
            cmsConfig: {
                // 默认配置
                planName: '同城交友A方案(活动)',
                formImg: 'https://photo.zastatic.com/images/cms/banner/20220531/18135307829432722.png',
                pageColor: '#BBE8FF',
                homeButtonText: '立即匹配',
                homeButtonColor: '#FF5E90',
                questionBodyImg1: 'linear-gradient(90deg, #015DFF 0%, #1790FF 100%)',
                progessColor: '#015DFF',
                agreementStatus: 0,
                downloadStatus: 0,
                planA: [],
                planB: [],
                planC: []
            },
            canRender: false,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };        
    },
    mounted() {
        // 加载cms配置
        this.handleInitCmsConfig();
        judgeIfInToutiaoIos();
    },
    methods: {
        async handleInitCmsConfig() {
            const id = Z.getParam('materialId');

            // 没有id的情况下，用默认配置
            if (!id) {
                window._zconfig.resourceKey = this.cmsConfig.planName;
                this.canRender = true;
                return;
            }

            const result = await Api.getMaterial({id});

            if (result.isError) {
                window._zconfig.resourceKey = this.cmsConfig.planName;
                this.canRender = true;
                return;
            }

            // 处理渐变
            function handleColor(color) {
                if (color.indexOf('&') > -1) {
                    // 按钮为渐变色
                    const [leftColor, rightColor] = color.split("&");
                    return `linear-gradient(90deg, ${leftColor} 0%, ${rightColor} 100%)`;

                }
                return color;
            }

            const data = z_.get(result, 'data.materialVo');
            data.progessColor = data.questionBodyImg1 && data.questionBodyImg1.split("&")[0] || '#015DFF';
            data.questionBodyImg1 = handleColor(data.questionBodyImg1) || 'linear-gradient(90deg, #015DFF 0%, #1790FF 100%)';

            window._zconfig.resourceKey = data.planName;
            this.cmsConfig = Object.assign(this.cmsConfig, z_.pick(data, Object.keys(this.cmsConfig)));
            this.canRender = true;
        }
    }
});
</script>

<style lang="scss" scoped>
.city-root {
    // color: #fff;
    font-family: PingFangSC-Regular;
    font-weight: 400;
}
</style>
