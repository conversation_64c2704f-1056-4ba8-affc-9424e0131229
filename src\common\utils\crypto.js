import CryptoJS from "crypto-js";

export class CryptoDes {
    constructor(key) {
        this.keyHex = CryptoJS.enc.Utf8.parse(key);
    }

    encode(word) {
        let srcs = word;
        let encrypted = CryptoJS.DES.encrypt(srcs, this.keyHex, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });

        return encrypted.ciphertext.toString().toUpperCase();
    }

    decode(ciphertext) {
        const decrypted = CryptoJS.DES.decrypt(
            {
                ciphertext: CryptoJS.enc.Hex.parse(ciphertext),
            },
            this.keyHex,
            {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7,
            }
        );

        return decrypted.toString(CryptoJS.enc.Utf8);
    }
}
