<template>
    <div class="wrapper">
        <img
            src="https://photo.zastatic.com/images/common-cms/it/20240731/1722420866633_42469.png"
            alt=""
        >
        <div class="item_container">
            <div
                v-for="(item,index) in list.options"
                :key="index"
                @click="goNext(item.key)"
                class="item"
                :class="(curEducation !== '' && curEducation == item.key)?'active':''"
            >
                {{ item.text }}
            </div>
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Education",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    mounted() {

    },
    data() {
        return {
            lock:false,
            curEducation: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).isSingle
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curEducation = val;
            const params = {
                key: "isSingle",
                value: val
            };
            // this.$report(31, '单身页-按钮点击');
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;

                this.$emit('go-next');

            },300);
            if (val) {

                this.$report(90,`询问单身页-选择单身`);
            } else {
                this.$report(91,`询问单身页-选择非单身`);

            }

        }
    }
};
</script>

<style lang="scss" scoped>@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
        margin-top: 187px;
        width: 200px;
        height: 211px;
    }
    .item_container {
        margin-top: 140px;
    }
    .item{
        width: 610px;
        height: 88px;
        background: #ffff;
        border-radius: 45px;
        display: flex;
        align-items: center;
        justify-content: center;

        line-height: 88px;
        font-size: 30px;
        text-align: center;
        letter-spacing: 4px;
        margin-bottom: 30px;
        border: 2px solid #B8BCCC;
    }
    .active{
        background: #fff0f0;
        border: none;
        color: #D7204A;
    }
}
</style>
