<template>
    <div
        id="trigger"
        :class="isPlaying ? 'rotate' : ''"
        :style="{
            backgroundImage: `url(${
                isPlaying ? img.player : img.playerDisabled
            })`
        }"
        @click="switchPlay"
        class="player__wrapper"
    >
        <audio
            id="player"
            loop="loop"
            :src="musicUrl"
            :autoplay="autoplay"
            style="width: 0; height: 0; opacity: 0"
            @play="onPlay"
        ></audio>
    </div>
</template>

<script>
export default {
    name: "Player",
    props: {
        musicUrl: {
            type: String,
        },
        autoplay: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            img: {
                player:
                    "https://photo.zastatic.com/images/common-cms/it/20230812/1691829504892_433174_t.png",
                playerDisabled:
                    "https://photo.zastatic.com/images/common-cms/it/20230814/1691981542945_722472_t.png"
            },
            player: null,
            isPlaying: false
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.player = document.getElementById("player");

            // 页面隐藏时声音关闭
            document.addEventListener("visibilitychange", this.pause);

            // safari兼容
            document.addEventListener("pagehide", this.pause);
        });
    },
    methods: {
        // 开始播放
        play() {
            if (!this.player.paused) return;
            this.player.play();
            if (!this.player.paused) {
                this.isPlaying = true;
            }
        },
        // 暂停
        pause() {
            if (this.player.paused) return;
            this.player.pause();
            if (this.player.paused) {
                this.isPlaying = false;
            }
        },
        switchPlay() {
            if (this.isPlaying || !this.player.paused) {
                this.pause();
            } else {
                this.play();
            }
        },
        onPlay() {
            this.isPlaying = true;
        }
    }
};
</script>

<style lang="scss" scoped>
@keyframes rotate {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}

.player__wrapper {
    position: relative;
    z-index: 9;
    width: 60px;
    height: 60px;
    border-radius: 30px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.rotate {
    animation-name: rotate;
    animation-iteration-count: infinite;
    animation-duration: 5s;
    animation-timing-function: linear;
}
</style>
