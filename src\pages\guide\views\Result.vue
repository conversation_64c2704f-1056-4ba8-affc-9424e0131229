<template>
    <div
        class="result"
        :class="{ 'is-test': pageType === 79 }"
    >
        <RecomV2
            v-if="pageType === 79 && isShowDownButton"
            class="recom recom-v2"
            @expend="handleExpendModel"
        />

        <div
            class="header"
            :class="{'model-expended': modelExpended}"
        >
            <div class="header-title">
                <span>{{ result.delistingResult }}</span>
            </div>
            <div class="header-rate">
                <div
                    class="header-rate-number"
                    v-if="pageType !== 79"
                >
                    {{ 118 - score.total }}
                </div>
                <div class="header-rate-percent">
                    {{ score.percent }}<span class="subfix">%</span>
                </div>
                <radar
                    class="header-rate-radar"
                    :width="350"
                    :height="295.8"
                    :show-score="true"
                    :radius="{
                        radiusSmall: 97.2/2,
                        radiusMid: 169.4/2,
                        radiusBig: 194.4/2
                    }"
                />
            </div>
            <div class="header-content">
                <div class="common-title">
                    <span>你的单身现状</span>
                </div>
                <div class="header-content-begin">
                    {{ result.singleActuality }}
                </div>
                <div class="header-content-end">
                    大部分人能觉察到自己为什么会是现在这个状态，但却只有很少一部分人能够克服自身的阻碍，实现突破。68%的单身个体，努力想结束自己一个人的生活，主要是受到内心孤单感的驱使，人毕竟是群居动物，很难忍受长久的寂寞。
                </div>
            </div>
        </div>

        <Recom
            v-if="pageType !== 79 && isShowDownButton"
            class="recom common-bg"
        />

        <div class="info common-bg">
            <div class="info-item">
                <div class="common-title">
                    <span>你的亲密关系态度</span>
                </div>
                <div class="info-item-content">
                    {{ result.internalSecurity }}
                </div>
            </div>
            <div class="info-item">
                <div class="common-title">
                    <span>你与异性交往心态</span>
                </div>
                <div class="info-item-content">
                    {{ result.aloneAbility }}
                </div>
            </div>
            <div class="info-item">
                <div class="common-title">
                    <span>你的脱单行动力</span>
                </div>
                <div class="info-item-content">
                    {{ result.delistingAction }}
                </div>
            </div>
        </div>
        <div class="tips common-bg">
            <div class="tips-title"></div>
            <div class="tips-item">
                在脱单的问题上，我们首先需要解决的是自身的问题，但往往很少人能意识到症结在自身，而是不断向外寻找。在亲密关系中感受到的痛苦大多源于自己，婚姻中有的问题必然出现，那是我们自身的特点决定的，而且与不同的人建立亲密关系，就会出现不同的成长课题，每一段感情降临，都是我们成长的绝佳机会。
            </div>
            <div
                v-for="(item,index) in result.delistingAdvice"
                :key="index"
                class="tips-item prefix"
            >
                {{ item }}
            </div>
            <div class="tips-item">
                一般情况下，我们的行为能固化下来通常是通过内部驱动，内部驱动能产生的能量远远大于外部驱动，所以，我们要激发自己的潜在动机，这对于择偶或者以后亲密关系的建立都大有帮助。这种困境是我们自己造成的，也需要我们自己去打破才可以哦！加油！
            </div>
        </div>
    </div>
</template>

<script>
import Recom from '../components/Recom.vue';
import RecomV2 from '../components/RecomV2.vue';
import Radar from "../components/Radar.vue";
import { PAGE_TYPE, countScore } from "../config";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { _getDelistingGuideResult } from "../api.js";

export default {
    name:"Result",
    components:{
        Recom,
        Radar,
        RecomV2
    },
    data(){
        return {
            isShowDownButton: true,
            reportLock:{
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            score:null,
            result:{
                aloneAbility:'',
                delistingAction:'',
                delistingAdvice:'',
                delistingProbability:'',
                delistingResult:'',
                highScoreTitle:'',
                internalSecurity:'',
                singleActuality:'',
            },
            pageType: pageTypeChnMap[PAGE_TYPE],
            modelExpended: false,
        };
    },
    async created(){
        const channelId = Z.getParam('channelId') && Number(Z.getParam('channelId'))
        const arr = []
        if (arr.includes(channelId)) {
            this.isShowDownButton = false
        }
        const score = countScore();
        score.percent = score.percent.replace('%', '');
        this.score = score;
        // 后台预发布解析有问题, 用JSON.stringfy传参
        const res = await _getDelistingGuideResult({
            json: JSON.stringify({
                answerList: this.score.answerList,
                pageType: pageTypeChnMap[PAGE_TYPE],
            })
        }).catch(e=>this.$toast(e.message));
        if(res.isError){
            return this.$toast(res.errorMessage);
        }

        this.result = res.data;
        this.result.delistingAdvice = this.result.delistingAdvice.split("\n");
        this.result.delistingAdvice.forEach((item,index)=>{
            this.result.delistingAdvice[index] = item.replace(/[0-9]|（|）/g,'');
        });

    },
    mounted(){
        this.$report(60, "测试报告页-曝光");
        window.addEventListener('scroll', this.listenScreen);
    },
    methods:{
        // 处理屏幕曝光
        reportScreen(){
            if(document.documentElement.scrollTop > screen.height && !this.reportLock.secondScreen){
                this.$report(60,"测试报告页-曝光2屏");
                this.reportLock.secondScreen = true;
            }
            if(document.documentElement.scrollTop > screen.height * 2 && !this.reportLock.thirdScreen){
                this.$report(60,"测试报告页-曝光3屏");
                this.reportLock.thirdScreen = true;
            }
            if(document.documentElement.scrollTop > screen.height * 3 && !this.reportLock.fourthScreen){
                this.$report(60,"测试报告页-曝光4屏");
                this.reportLock.fourthScreen = true;
            }
            if(document.documentElement.scrollTop > screen.height * 4 && !this.reportLock.fifthScreen){
                this.$report(60,"测试报告页-曝光5屏");
                this.reportLock.fifthScreen = true;
            }

            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(item => item === true).length,
                totalCount = Object.keys(this.reportLock).length;
            if(reportedNum === totalCount){
                window.removeEventListener('scroll',this.listenScreen);
            }
        },
        listenScreen(){
            this.reportScreen();
        },
        handleExpendModel() {
            this.modelExpended = true;
        }

    },
    destroyed(){
        window.removeEventListener('scroll',this.listenScreen);
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.result{
    background-color: #161F38;
    padding: 72px 0 200px;

    &.is-test {
        padding-top: 132px;
        .header {
            background-image: url("https://photo.zastatic.com/images/common-cms/it/20230516/1684231040819_926905_t.png");
            position: relative;
            margin-top: 122px;
            padding-top: 38px;
            height: 1158px;

            &::before {
                display: block;
                content: '';
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20230517/1684293526507_595606_t.png");
                width: 414px;
                height: 124px;
                position: absolute;
                top: -100px;
                left: 50%;
                transform: translateX(-50%);
            }

            &-title {
                letter-spacing: 4px;
                margin-left: 50%;
                transform: translateX(-50%);
                word-break: keep-all;
                margin-top: 0;
            }

            &-rate {
                height: 322px;
                margin-top: 12px;
            }

            &-rate-percent {
                top: 40%;
                transform: translateY(-50%);

                &::before{
                    content: '今年脱单概率';
                    top: auto;
                    bottom: -50px;
                }

                .subfix {
                    font-size: 36px;
                }
            }

            &-content {
                margin-top: 110px;
            }

            &.model-expended::before {
                background-image: url("https://photo.zastatic.com/images/common-cms/it/20230517/1684310108528_101526_t.png");
            }
        }

        .recom-v2 {
            border-radius: 40px;
            position: relative;
            padding: 0 20px 0;
            margin: 0;

            &::before {
                display: block;
                content: '';
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20230517/1684293526693_590529_t.png");
                width: 548px;
                height: 124px;
                position: absolute;
                top: -100px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1;
            }
        }
    }

    .header{
        margin: 0 auto;
        padding: 0 32px;
        width: 702px;
        height: 1156px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668500536085_581294_t.png");

        &-title{
            margin-top: 40px;
            position: relative;
            display: inline-block;

            > span{
                position: relative;
                font-weight: 600;
                font-size: 72px;
                color: #E2F0FE;
                z-index: 2;
            }

            &::after{
                content: '';
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -8px;
                width: 98%;
                height: 16px;
                background-image: linear-gradient(90deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 100%);
                z-index: 1;
            }
        }

        &-rate{
            position: relative;

            &-number{
                position: absolute;
                top: 120px;
                font-weight: 600;
                font-size: 40px;
                background-image: linear-gradient(to top, rgba(22,171,251,0.67), rgba(153,244,255,0.87));
                -webkit-background-clip: text;
                color: transparent;

                &::before{
                    content: '· 脱单指数';
                    position: absolute;
                    left: 0;
                    top: -50px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #FFFFFF;
                    text-align: left;
                    width: 120px;
                }
            }

            &-percent{
                position: absolute;
                top: 240px;
                font-weight: 600;
                font-size: 80px;
                background-image: linear-gradient(to top, rgba(22,171,251,0.67), rgba(153,244,255,0.87));
                -webkit-background-clip: text;
                color: transparent;

                &::before{
                    content: '· 今年脱单概率';
                    position: absolute;
                    left: 0;
                    top: -50px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #FFFFFF;
                    text-align: left;
                    width: 200px;
                }
            }

            &-radar{
                position: absolute;
                top: 24px;
                left: 304px;
                // width: 332px;
                // height: 300px;
            }
        }

        &-content{
            margin: 450px auto 0;

            &-begin{
                margin-top: 24px;
                font-size: 28px;
                color: #FFFFFF;
                text-align: justify;
                line-height: 42px;
            }

            &-end{
                margin-top: 24px;
                padding: 30px;
                width: 638px;
                background: rgba(55,167,207,0.38);
                border-radius: 24px;
                font-size: 28px;
                color: #BEE1FE;
                letter-spacing: 0;
                text-align: justify;
                line-height: 1.4;
            }
        }
    }

    .recom{
        margin: 32px auto 0;
        padding: 20px 20px 32px;
    }

    .info{
        position: relative;
        margin: 32px auto 0;
        padding: 0 24px 32px;
        overflow: hidden;
        width: 686px;
        background-image: linear-gradient(180deg, #123151 0%, #132035 100%);

        &-item{
            margin-top: 60px;

            &-content{
                margin-top: 24px;
                font-size: 28px;
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: justify;
                line-height: 42px;
            }
        }
    }

    .tips{
        margin: 32px auto 0;
        padding: 40px 24px 32px;

        &-title{
            padding-left: 132px;
            width: 608px;
            height: 64px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668504538003_393076_t.png");
        }

        &-content{
            margin-top: 32px;
            width: 638px;
            font-size: 24px;
            color: #FFFFFF;
            text-align: justify;
            line-height: 36px;
        }

        &-item{
            position: relative;
            margin-top: 30px;
            font-size: 28px;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: justify;
            line-height: 36px;
            white-space: wrap;
        }

        .prefix{
            padding-left: 60px;
            &::before{
                content: '';
                position: absolute;
                left: 0;
                top: 4px;
                width: 38px;
                height: 34px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668504625559_36858_t.png");
            }
        }
    }

    .common{
        &-bg{
            // @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668503752704_865876_t.png");
            width: 686px;
            // min-height: 956px;
            // background-position: 0% 0%;
            border: 2px solid #fff;
            border-radius: 40px 86px 40px 40px;
        }

        &-title{
            position: relative;
            font-weight: 600;
            font-size: 40px;
            color: #FFFFFF;
            line-height: 40px;

            &::before{
                content: '';
                position: absolute;
                top: -6px;
                right: 10px;
                width: 28px;
                height: 32px;
                @include set-img('https://photo.zastatic.com/images/common-cms/it/20221115/1668503199368_828458_t.png');
            }

            span{
                position: relative;
                &::before{
                    content: '';
                    position: absolute;
                    top: -10px;
                    right: 0;
                    width: 94px;
                    height: 65px;
                    opacity: 0.52;
                    transform: rotate(-15deg);
                    background-image: linear-gradient(180deg, rgba(148,212,251,0.00) 0%, #94C5FB 100%);
                    border-radius: 50%;
                }
            }


        }
    }
}
</style>
