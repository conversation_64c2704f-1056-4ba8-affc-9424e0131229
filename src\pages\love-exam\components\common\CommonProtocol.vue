<template>
    <div
        class="common-protocol"
        :style="{ color: styleConfig.textColor }"
    >
        <div
            class="common-protocol__checke-box"
            @click="handleToggleCheck"
        >
            <span v-if="isNeed">
                <div
                    v-show="isChecked"
                    :style="{ background: `url(${styleConfig.protocolCheckedUrl}) center center no-repeat`, backgroundSize: 'contain'}"
                    class="common-protocol__checke-box-checked"
                />
                <div
                    v-show="!isChecked"
                    :style="{ borderColor: styleConfig.protocolColor}"
                    class="common-protocol__checke-box-normal"
                />
            </span>
        </div>
        <div class="common-protocol__text">
            已阅读并同意<a
                :style="{ color: styleConfig.protocolColor }"
                href="//i.zhenai.com/m/portal/register/prDeal.html"
            >《珍爱网服务协议》</a>
            和 <a
                :style="{ color: styleConfig.protocolColor }"
                href="//i.zhenai.com/m/portal/register/serverDeal.html"
            >《个人信息保护政策》</a>
        </div>
    </div>
</template>

<script>

export default {
    name: 'CommonProtocol',
    props: {
        agreementStatus: {
            type: Number,
            default: 0,
        },
        isChecked: {
            type: Boolean,
            default: true
        },
        isNeed: {
            type: Boolean,
            default: true
        },
        styleConfig: {
            type: Object,
            default: {
                textColor: "#FFFFFF",
                protocolColor: "#767DFF",
                protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20220512/1652336753832_292583_t.png'
            }
        }
    },
    methods: {
        handleToggleCheck() {
            if (this.agreementStatus === 0) {
                return;
            }

            this.$emit('update:isChecked', !this.isChecked);
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.common-protocol {
    height: 36px;
    @include flex-center(row, center, center);
    font-size: 22px;
    position: relative;
    &__checke-box {
        margin-right: 8px;
        width: 28px;
        height: 28px;
        &-normal {
            width: 100%;
            height: 100%;
            border-width: 2px;
            border-style: solid;
            border-radius: 50%;
        }
        &-checked {
            width: 100%;
            height: 100%;
            border: none;
        }
    }
    &__text {
        text-align: left;
    }
    a {
        font-weight: 600;
        text-decoration: none;
    }
}
</style>
