import { post } from '@/common/utils/ajax.js';
if (process.env.NODE_ENV === 'development' && window.setMonitorOption) {
    window.setMonitorOption({
        open: false
    });
}
// 获取IOS下载配置信息
const getH5WapRegConfig = (data) => post("/loveAnswerBook/getH5WapRegConfig.do", data);

(async ()=>{
    let res = await getH5WapRegConfig().catch(e=>sessionStorage.setItem("IOS_TURN_ON",0));
    if(!res.isError){
        var IOS_TURN_ON = res.data && res.data.applePayEntityCard? 1: 0;
        sessionStorage.setItem("IOS_TURN_ON",IOS_TURN_ON);
    }else{
        sessionStorage.setItem("IOS_TURN_ON",0);
    }
    
})();