<template>
    <van-popup
        class="common-msg-code-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        get-container="#app"
        :close-on-click-overlay="false"
    >
        <div class="common-msg-code-modal">
            <h1 class="common-msg-code-modal__title">
                {{ phone || '' }}
            </h1>

            <p class="common-msg-code-modal__desc">
                验证码已通过短信发到你的手机
            </p>

            <div class="common-msg-code-modal__code">
                <div class="common-msg-code-modal__code-input-items">
                    <div
                        v-for="(item, index) in 4"
                        :key="index"
                        class="common-msg-code-modal__code-input-item"
                    />
                </div>
                <input
                    ref="codeInput"
                    type="tel"
                    class="common-msg-code-modal__code-input"
                    maxlength="4"
                    :value="code"
                    @input="checkCode"
                    autocomplete="new-password"
                >
            </div>

            <div class="common-msg-code-modal__code-error">
                {{ errorMessage }}
            </div>

            <div
                class="common-msg-code-modal__btn"
                :style="{ background: isLock ? styleConfig.confirmButtonBgColor : '#FF5B87' , color: styleConfig.confirmButtonColor}"
                @click="clickSendCode"
            >
                <van-loading v-if="loading" />
                <span v-else> {{ `${btnText}` }}</span>
            </div>

            <div
                class="common-msg-code-modal__cancel"
                @click="closeModal"
                :style="{ color: styleConfig.cancleButtonColor}"
            >
                取消
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup, loading as VanLoading} from 'vant';
import {sendMessageCode} from '../../api';
import Api from '@/common/server/base';
import { reportMagic, reportKibana,reportLoveKibana } from "@/common/utils/report";
import Prototype from "@/common/framework/prototype";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from '../../config';

export default {
    name: 'CommonMsgCodeModal',
    components: {
        VanPopup: Popup,
        VanLoading,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        validateCode: {
            type: Function,
            required: true,
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF',
            }
        }
    },
    data() {
        return {
            errorMessage: '',
            btnText: '',
            btnNum: 0,
            isLock: false,
            code: '',
            leaveTime: null,
            isListenVisibilitychange: null,
            phone: '',
            loading: false,
        };
    },
    watch: {
        value: {
            handler(value) {
                if (!value) {
                    return;
                }

                this.code = '';
                this.errorMessage = '';

                const registerForm = Storage.getItem(
                    `cachedRegisterForm-${this.pageType}`
                );

                this.phone = registerForm.phone;

                this.$report(108, '手机验证码弹窗-曝光');


                Prototype.$gather.setBeforeValidateCodeOCPC();

                this.countDown();

                this.$nextTick().then(() => {
                    this.$refs.codeInput.focus();
                });
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        async checkCode(evt) {
            this.errorMessage = '';
            this.code = evt.target.value.replace(/\D/g, '');
            evt.target.value = this.code;

            if (this.code.length === 4) {
                this.$refs.codeInput.blur();
                const result = await this.validateCode(this.code);

                if (result.code !== 0) {
                    this.code = '';
                    this.$refs.codeInput.focus();
                    reportKibana(this.pageType, 31000, '异常情况处理', {
                        ext17: result && JSON.stringify(result)
                    });
                    // switch (result.errorCode) {
                    // case '-8002005':
                    //     this.errorMessage = '验证码错误，请重新输入';
                    //     break;
                    // case '-8002006':
                    //     this.errorMessage = '验证码已过期';
                    //     break;
                    // case '-8002004':
                    //     this.errorMessage = result.errorMessage;
                    //     break;
                    //  default:
                    //      this.errorMessage = '网络异常'
                    // }
                    this.errorMessage = result.msg;
                    return;
                }

                this.closeModal();
                Prototype.$gather.setValidateCodeSuccessOCPC();
            }
        },
        closeModal() {
            this.$emit('input', false);
        },
        async sendCode() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();

            const sendData = {
                phone: this.phone.replace(/[^(\d)]/g, ""),
                type: 0,
            };

            //【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam('clickid'),
                adid: Z.getParam('adid'),
                creativeid: Z.getParam('creativeid'),
                creativetype: Z.getParam('creativetype')
            };

            for (const key in toutiaoParamlist) {
                if (toutiaoParamlist[key]) {
                    sendData[key] = toutiaoParamlist[key];
                }
            }

            const isSingle =  Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).isSingle;

            const sendMsgResult = await isSingle ? await Api.sendWapMessageCodeV2(sendData): await sendMessageCode(sendData) ;

            const isError = (isSingle && sendMsgResult.isError) || !isSingle && sendMsgResult.code != 0;

            if (isError) {
                this.errorMessage = sendMsgResult.msg || sendMsgResult.errorMessage;
            } else {
                this.$toast('验证码已发送，请注意查收');
            }
        },
        clickSendCode() {
            this.$report(110, '手机号码验证弹窗-点击获取验证码');
            this.countDown();
        },
        async countDown() {
            if (this.isLock) {
                return;
            }

            reportKibana(this.pageType, 3100, '提交手机号（监控）');

            this.isLock = true;
            this.loading = true;
            await this.sendCode();
            this.loading = false;

            // 从60s开始倒计时
            this.btnNum = 60 ;
            this.btnText = this.btnNum + '秒后重新获取';

            this.timer = setInterval(() => {
                this.btnText = --this.btnNum + '秒后重新获取';
                if (this.btnNum <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }, 1000);
        },
    },
    mounted() {
        const callback = () => {
            // 用户进入后台
            if (document.visibilityState === "hidden") {
                this.leaveTime = new Date().getTime();
            } else if (document.visibilityState === "visible") {
                if (!this.isLock) {
                    return;
                }

                this.backTime = new Date().getTime();
                const diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                this.btnText -= diff;

                if (this.btnText <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }
        };

        document.addEventListener('visibilitychange', callback);
        this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('visibilitychange', callback);
        });
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

$width: 622px;

.common-msg-code-modal {
    width: $width;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 72px 0 72px;
    overflow: hidden;
    @include flex-center(column, null, center);

    &.van-popup--center {
        // 某些浏览器出现软键盘后会覆盖输入框，所以将弹窗位置放上点
        top: 40%;
    }

    &__title {
        width: 504px;
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
        padding: 0 26px;
    }

    &__btn {
        @include flex-center(column);
        flex-shrink: 0;
        width: 357px;
        height: 100px;
        font-size: 32px;
        margin-top: 24px;
        margin-bottom: 32px;
        background-image: linear-gradient(180deg, #ff9e8b80 4%, #ff5b8780 100%) !important;
        border-radius: 54px;
        &-continue {
            padding-top: 3px;

            > div:last-child {
                font-size: 24px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        flex-shrink: 0;
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #000000;
        font-weight: 400;
        text-align: center;
        width: 357px;
        height: 100px;
    }

    &__code {
        position: relative;
        color: #26273C;
        width: 100%;
        margin-bottom: 16px;

        &-input-items {
            @include flex-center(row, space-between, center);
        }

        &-input-item {
            width: 90px;
            height: 90px;
            opacity: 0.2;
            background: #979797;
            border-radius: 8px;
        }

        &-error {
            width: 100%;
            text-align: center;
            font-size: 24px;
            margin-top: 48px;
            color: #F04086;
        }

        &-input {
            box-sizing: border-box;
            width: $width;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            height: 60px;
            font-size: 30px;
            line-height: 30px;
            background: transparent;
            letter-spacing: 110px;
            padding-left: 110px;
            overflow: hidden;
        }
    }
}
</style>
