<template>
    <!-- 网络异常时的默认页面 -->
    <div
        v-if="showDefault"
        class="info-default-wrapper"
    >
        <div class="default__logo"></div>
        <div class="default__info">当前网络出小差了，请刷新下页面吧~</div>
        <div class="default__button--refresh" @click="refreshInfoPage">刷新页面</div>
    </div>
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex';
import {_getSpecifyGenderRandomAvatar} from '../../js/api.js';

export default {
    components:{
    },
    data() {
        return {

        };
    },
    computed:{
        ...mapState([
            'showError',
            'registerInfo',
            'showErrorCb'
        ]),
        showDefault(){
            let isInfoPage = this.$route.path === '/info' || this.$route.path === '/blindinfo';

            // 如果在下载页，且没有获取到模特数据
            return isInfoPage && this.showError
        }
    },
    created(){

    },
    mounted(){

    },
    methods:{
        ...mapMutations([
            'setShowError',

        ]),
        ...mapActions([
            'setCmsConfig'
        ]),

        async refreshInfoPage(){
            // 重新获取CMS设置和三个随机头像
            await this.setCmsConfig();
            await this.showErrorCb();
        },
    },

};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.info-default-wrapper{
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    // margin: auto;
    background: #fff;
    z-index: 100;

}

.default__logo{
    margin: 475px auto 0;
    width: 120px;
    height: 120px;
    @include set-img($gifDefault);
}

.default__info{
    margin-top: 80px;
    height: 47px;
    font-size: 32px;
    color: #6C6D75;
    line-height: 47px;
    text-align: center;
}

.default__button--refresh{
    margin: 48px auto 0;
    width: 462px;
    height: 88px;
    background: #4633EF;
    border-radius: 55px;

    font-size: 32px;
    color: #FFFFFF;
    line-height: 88px;
    text-align: center;
}

</style>
