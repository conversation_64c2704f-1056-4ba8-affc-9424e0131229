<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curGender === item.key?'active':''"
        >
            {{ item.text }}
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Gender",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curGender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curGender = val;
            const params = {
                key: "gender",
                value: val
            };

            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                if (sessionStorage.getItem('loveAnswerIsSingle') == '1') {
                    this.$report(23,'性别页-按钮点击', {
                        ext29: 8024
                    });
                    this.$router.push({
                        path:'/register/2'
                    });
                } else {
                    this.$report(23,'性别页-按钮点击');
                    this.$emit('go-next');
                }
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    .item{
        width: 396px;
        height: 89px;
        background-color: #A98CE6;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 45px;
        // font-family: 'love';

        line-height: 48px;
        font-size: 30px;
        text-align: center;
        color: #FFFFFF ;
        letter-spacing: 4px;
        box-shadow: 0 3px 3px 0 #bd92eb66;
    }
    > .active{
        background: linear-gradient(180deg,#5243FE, #9A55F0)
    }
}
</style>
