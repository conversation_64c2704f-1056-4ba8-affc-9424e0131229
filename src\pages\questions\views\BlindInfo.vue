<template>
    <div class="blindinfo">
        <h3 class="blindinfo_title">
            珍爱APP为你推荐以下3位会员
        </h3>
        <div class="info-blind-wrapper">
            <div
                v-for="(item, index) in blindArr"
                :key="index"
                class="blind__item"
            >
                <!-- 头像 -->
                <div
                    v-if="item.avatar"
                    class="blind__item__img"
                    :class="selectedId === index ? '' : 'blur'"
                    :style="{ backgroundImage: `url(${item.avatar})` }"
                ></div>

                <!-- 动效 -->
                <div
                    :id="'itemBlur' + index"
                    class="blind__item__img--svga"
                ></div>

                <!-- 按钮 -->
                <div
                    class="blind__item__block"
                    @click="selectBlind(item, index)"
                >
                    <button
                        class="blind__item__block__button"
                        :class="
                            hasSelected && selectedId === index
                                ? 'blind__item__block__button--disabled'
                                : ''
                        "
                    >
                        我要约Ta
                    </button>
                </div>
            </div>
        </div>
        <div class="info-avatar-wrapper">
            <div
                class="info-avatar"
                :class="modelInfo.mainImg ? '' : 'blur'"
                :style="{
                    backgroundImage: modelInfo.mainImg
                        ? `url(${modelInfo.mainImg})`
                        : `url(${require('../assets/images/default-main.png')})`
                }"
            >
                <div class="avatar__icon--online">
                    APP在线
                </div>
                <div class="avatar__icon--location">
                    在您附近
                </div>
                <div
                    class="avatar__icon--hello"
                    @click="openModal(1)"
                >
                    打个招呼
                </div>
            </div>

            <div
                v-if="!modelInfo.mainImg"
                class="info-avatar--blur"
            >
                <div class="avatar--blur__lock"></div>
                <div class="avatar--blur__text">
                    您有一次约会机会
                    <span>选择约会的对象，可解锁Ta的全部资料</span>
                </div>
            </div>
        </div>
        <div
            class="info-content-wrapper"
            v-if="hasSelected"
        >
            <div class="content__list">
                <div class="list__logo--car"></div>
                <div
                    class="list__logo--phone"
                    @click="openModal(2)"
                >
                    查看完整手机号
                </div>
                <div
                    class="list__logo--wechat"
                    @click="openModal(3)"
                >
                    查看完整微信号
                </div>
                <div class="list__title">
                    Ta的详细资料
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        {{ modelInfo.name }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        年龄
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.ageString }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        工作地
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.workCityString }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        学历
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.educationString }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        月收入
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.salaryString }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        手机号
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.phone }}
                    </div>
                </div>

                <div class="list__item">
                    <div class="list__item__label">
                        微信号
                    </div>
                    <div class="list__item__value">
                        {{ modelInfo.weChat }}
                    </div>
                </div>
            </div>

            <div class="content__photo">
                <div class="photo__title">
                    <div class="photo__title--main">
                        Ta的动态
                    </div>
                    <div
                        class="photo__title--sub"
                        @click="openModal"
                    >
                        查看所有{{ momentCount }}条
                    </div>
                </div>

                <div class="content-block">
                    <div
                        class="photo__block--main"
                        :style="{
                            backgroundImage: `url(${modelInfo['momentImg1']})`
                        }"
                    ></div>
                    <div class="photo__block--sub">
                        <div
                            class="block--sub__item"
                            v-for="(item, index) in photoArr"
                            :key="index"
                            :style="{
                                backgroundImage: `url(${
                                    modelInfo['momentImg' + (index + 2)]
                                })`
                            }"
                        ></div>
                        <div
                            class="block--sub__item--last"
                            @click="openModal"
                        >
                            更多照片
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-footer-wrapper">
            <div class="footer-tips">
                Ta当前APP在线，期待与你相遇
            </div>
            <div
                class="footer-button"
                @click="goDownload"
            >
                下载APP，立即约会
            </div>
        </div>
        <Popup
            :show="popupStatus"
            @handleConfirm="handleConfirm"
            @handleCancel="handleCancel"
        />
    </div>
</template>

<script>
import Popup from "../components/popup.vue";
import { _getSpecifyGenderRandomAvatar, _getModelInfo } from "../api.js";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

import { Toast } from "vant";
import { session as Session} from "@/common/utils/storage";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    components: {
        Popup
    },
    data() {
        const blindArr = [{}, {}, {}];
        const photoArr = ["", "", ""];
        return {
            blindArr,
            buttonText: "我要约Ta",
            selectedId: null,
            hasSelected: false,
            modelInfo: {
                ageString: "",
                avatar: "",
                educationString: "",
                mainImg: "", // 主图
                momentImg1: "", // 动态图
                momentImg2: "",
                momentImg3: "",
                momentImg4: "",
                momentImg5: "",
                name: "",
                phone: "",
                salaryString: "",
                sex: "",
                weCaht: "",
                workCityString: ""
            },
            momentCount: 10 + Math.floor(Math.random() * (50 - 10 + 1)),
            photoArr,
            popupStatus: false
        };
    },
    created() {
        this.getAvatars();
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            11, // 记录点
            "测试结果页曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    methods: {
        async getAvatars() {
            const gender = this.$storage.loadFromStorage(
                "__regInfo__",
                "gender"
            );
            const birthday = this.$storage.loadFromStorage(
                "__regInfo__",
                "birthday"
            );

            const age =
                new Date().getFullYear() - new Date(birthday).getFullYear();
            const res = await _getSpecifyGenderRandomAvatar({
                sex: gender == 1 ? 0 : 1,
                limit: 3,
                age
            });
            if (!res.isError) {
                this.blindArr = res.data.list;
                this.blindArr.forEach(item => {
                    item.avatar += "?imageMogr2/thumbnail/200x200";
                });
            } else {
                this.$toast(res.errorMessage);
            }
        },
        async selectBlind(item, index) {
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                12, // 记录点
                "测试结果页-约ta按钮点击", // 点描述
                {
                    ext1: Z.getParam("materialId")
                }
            );
            if (typeof this.selectedId === "number") {
                // 已选
                if (this.selectedId === index) {
                    return;
                }

                // 下载APP的逻辑

                this.popupStatus = true;
                console.log("下载APP");
                return;
            }

            // 设置当前选中项
            this.selectedId = index;
            this.hasSelected = true;
            let currentBlurId = "#itemBlur" + index;
            this.setSVGA(currentBlurId, item.avatar);
            this.setModelInfo(item.id);
        },
        async setModelInfo(target) {
            // 获取模特信息
            let sendData = {};
            if (typeof target === "number") {
                // 如果已经指定model的ID
                sendData.modelId = target;
            }
            const regInfo = this.$storage.loadRegInfo("__regInfo__");
            sendData.salary = regInfo.salary;
            sendData.salary = regInfo.salary;
            sendData.workCity = regInfo.workCity;
            sendData.education = regInfo.education;
            sendData.sex = regInfo.gender;
            let age =
                new Date().getFullYear() -
                new Date(regInfo.birthday).getFullYear();

            sendData.age = age;

            if (!sendData.salary || !sendData.workCity) {
                return;
            }

            const res = await _getModelInfo({
                ...sendData
            });

            if (res.isError) {
                return this.$toast(res.errorMessage);
            }

            const modelInfoVo = res.data.modelInfoVo;
            modelInfoVo.avatar += "?imageMogr2/thumbnail/100x100";
            modelInfoVo.mainImg += "?imageMogr2/thumbnail/700x700";
            modelInfoVo.momentImg1 += "?imageMogr2/thumbnail/315x315";
            modelInfoVo.momentImg2 += "?imageMogr2/thumbnail/154x154";
            modelInfoVo.momentImg3 += "?imageMogr2/thumbnail/154x154";
            modelInfoVo.momentImg4 += "?imageMogr2/thumbnail/154x154";
            modelInfoVo.momentImg5 += "?imageMogr2/thumbnail/154x154";

            this.modelInfo = modelInfoVo;
        },
        setSVGA(dom, avatar) {
            let player = new SVGA.Player(dom),
                parser = new SVGA.Parser(dom);

            parser.load(
                require("../assets/images/svgaBroken.svga"),
                videoItem => {
                    player.setImage(avatar, "key");
                    player.loops = 1;
                    player.setVideoItem(videoItem);
                    player.startAnimation();
                    // player.startAnimationWithRange({location:0,length:5});
                }
            );
        },
        goDownload() {
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                16, // 记录点
                "测试结果页-底部主按钮点击", // 点描述
                {
                    ext1: Z.getParam("materialId")
                }
            );
            const channelType = sessionStorage.getItem("is_toutiao");

            if (+channelType === 1) {
                this.popupStatus = true;
                return;
            }
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        handleConfirm() {
            this.popupStatus = false;
        },
        handleCancel() {
            this.popupStatus = false;
        },
        openModal(id) {
            this.popupStatus = true;
            let description = ['测试结果页-打招呼按钮点击', '测试结果页-查看手机号按钮点击', '测试结果页-查看微信号按钮点击', '测试结果页-底部主按钮点击'];
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                12+id, // 记录点
                description[id-1], // 点描述
                {
                    ext1: Z.getParam("materialId")
                }
            );
        }
    }
};
</script>
<style lang="scss" scoped>
@import "../assets/css/common.scss";
.blindinfo {
    height: 100%;
    overflow-y: scroll;
    padding-bottom: 74px;
    -webkit-overflow-scrolling: touch;
    &_title {
        padding-top: 110px;
        padding-bottom: 90px;
        color: #fff;
        font-size: 36px;
        font-weight: 500;
        text-align: center;
    }
}
.info-blind-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
}

.blind__item {
    position: relative;
    width: 200px;
    height: 278px;
    // background: #6200AE;
    border-radius: 16px 16px 16px 16px;
    overflow: hidden;
}

.blind__item__img {
    @include set-img("../assets/images/img.png");
    width: 200px;
    height: 200px;
    border-radius: 16px 16px 0px 0px;
    // transition: filter 2s;
}

// .no-blur{
//     filter: blur(0);
// }
.blur {
    filter: blur(4px);
}

.blind__item__img--svga {
    position: absolute;
    left: 0;
    top: 0;
    width: 200px;
    height: 200px;
    z-index: 2;
}

.blind__item__block {
    position: absolute;
    top: 200px;
    width: 200px;
    height: 80px;
    background: #6200ae;
}

.blind__item__block__button {
    position: relative;
    display: block;
    margin: 15px auto 0;
    width: 150px;
    height: 50px;
    background: linear-gradient(0deg, #ffc334, #ff8f02);
    border-radius: 25px;
    font-size: 24px;
    font-weight: 700;
    color: #fdfdfd;
    border: none;
    line-height: 50px;
    text-shadow: 0px 0px 6px rgba(255, 150, 9, 0.6);
    box-shadow: inset 0 0 6px 1px #fff;
    z-index: 3;
}

.blind__item__block__button--disabled {
    background: linear-gradient(0deg, #979797, #888888) !important;
}

.info-avatar-wrapper {
    position: relative;
    margin: 40px auto 0;
    width: 702px;
    height: 702px;
    overflow: hidden;
    border-radius: 32px;
    z-index: 3;
}

.info-avatar {
    width: 100%;
    height: 100%;
    border-radius: 32px;
    font-size: 26px;
    color: #fdfdfd;
    @include set-img("../assets/images/default-main.png");
}

.avatar__icon--online {
    position: absolute;
    top: 550px;
    left: 32px;
    padding-left: 54px;
    width: 194px;
    height: 48px;
    background: rgba($color: #26273c, $alpha: 0.5);
    border-radius: 24px;
    line-height: 48px;
}

.avatar__icon--online::before {
    content: "";
    position: absolute;
    left: 22px;
    top: 50%;
    transform: translateY(-50%);
    width: 22px;
    height: 22px;
    background-image: url(../assets/images/icon-online.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.avatar__icon--location {
    position: absolute;
    top: 622px;
    left: 32px;
    padding-left: 54px;
    width: 194px;
    height: 48px;
    background: rgba($color: #26273c, $alpha: 0.5);
    border-radius: 24px;
    line-height: 48px;
}

.avatar__icon--location::before {
    content: "";
    position: absolute;
    left: 22px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 24px;
    background-image: url(../assets/images/icon-location.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.avatar__icon--hello {
    position: absolute;
    top: 582px;
    left: 468px;
    width: 201px;
    height: 88px;
    background: #787cff;
    border-radius: 44px;
    box-shadow: 3px 5px 36px 7px rgba(120, 124, 255, 0.4);

    font-size: 32px;
    font-weight: 700;
    color: #fdfdfd;
    line-height: 88px;
    text-align: center;
}

.info-avatar--blur {
    position: absolute;
    left: 0;
    top: 0;
    background: rgba($color: #464849, $alpha: 0.5);

    width: 100%;
    height: 100%;
    border-radius: 32px;
}

.avatar--blur__lock {
    position: absolute;
    left: 50%;
    top: 153px;
    transform: translateX(-50%);
    @include set-img("../assets/images/lock.png");
    width: 32px;
    height: 38px;
}

.avatar--blur__text {
    margin: 220px auto 0;
    width: 600px;

    font-size: 36px;
    font-weight: 700;
    color: #ffffff;
    line-height: 53px;
    text-align: center;
    span {
        display: block;
        font-size: 32px;
    }
}

.info-footer-wrapper {
    min-height: 300px;
    // background-color: red;
}

.footer-tips {
    font-size: 30px;
    font-weight: 700;
    color: #ffffff;
    line-height: 38px;
    text-align: center;
    padding-top: 50px;
    padding-bottom: 40px;
}

.footer-button {
    display: table !important;
    margin: 0 auto;
    width: 686px;
    height: 110px;
    background: linear-gradient(0deg, #ffc334, #ff8f02);
    border-radius: 56px;
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    line-height: 110px;
    text-align: center;
    text-shadow: 0px 6px 13px rgba(255, 125, 199, 0.7);
    box-shadow: inset 0 0 16px 2px #ffffff;
}

.info-content-wrapper {
    position: relative;
    margin: -50px auto 0;
    width: 702px;
}

.content__list {
    // 重合部分高度为50px
    padding-top: 50px;
    padding-left: 32px;
    padding: 50px 32px 80px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
    border-radius: 32px;
}

.list__logo--car {
    position: absolute;
    top: 102px;
    right: -20px;
    width: 198px;
    height: 198px;
    @include set-img("../assets/images/car-logo.png");
}

.list__logo--phone {
    position: absolute;
    top: 606px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787cff;
    border-radius: 24px;

    font-size: 24px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
}

.list__logo--wechat {
    position: absolute;
    top: 686px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787cff;
    border-radius: 24px;

    font-size: 24px;
    color: #ffffff;
    line-height: 48px;
    text-align: center;
}

.list__title {
    margin-top: 52px;
    font-size: 36px;
    font-family: Source Han Sans SC;
    font-weight: 700;
    color: #26273c;
    line-height: 63px;
}

.list__item {
    @include set-flex(flex-start, center);
    margin-top: 50px;
    height: 30px;
    font-size: 32px;
    color: #26273c;
    line-height: 30px;
}

.list__item__label {
    position: relative;
    top: 50%;
    // transform: translateY(-50%);
    display: inline-block;
    text-align: justify;
    text-align-last: justify;
    width: 160px;
}

.list__item__label::after {
    content: "";
    width: 100%;
    display: inline-block;
    height: 0;
}

.list__item__value {
    position: relative;
    padding-left: 32px;
}

.list__item__value::before {
    content: ":";
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

// 昵称单独处理
.list__item:nth-child(5) {
    .list__item__label {
        text-align: left;
        text-align-last: left;
    }

    .list__item__value::before {
        content: "";
    }
}

.content__photo {
    margin: 39px auto 0;
    width: 702px;
    height: 518px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
    border-radius: 32px;
}

.photo__title {
    @include set-flex(space-between, center);
    padding: 0 33px;
    height: 140px;
}

.photo__title--main {
    font-size: 36px;
    font-weight: 700;
    color: #26273c;
    line-height: 140px;
}

.photo__title--sub {
    position: relative;
    padding-right: 28px;
    font-size: 26px;
    font-weight: 400;
    color: #6c6d75;
    line-height: 140px;
}

.photo__title--sub::after {
    content: "";
    @include set-img("../assets/images/right-arrow.png");
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-52%);
    width: 32px;
    height: 32px;
    font-weight: 400;
    color: #6c6d75;
    line-height: 140px;
}

.content-block {
    @include set-flex(space-between, center);
    margin: 0 auto;
    width: 640px;
}

.photo__block--main {
    width: 315px;
    height: 315px;
    border-radius: 16px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.photo__block--sub {
    @include set-flex(space-between, center);
    flex-wrap: wrap;
    align-content: space-between;
    width: 315px;
    height: 315px;
}

.block--sub__item {
    width: 154px;
    height: 154px;
    border-radius: 16px;
    // background: grey;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.block--sub__item--last {
    width: 154px;
    height: 154px;
    background-color: rgba($color: #26273c, $alpha: 0.1);
    border-radius: 16px;

    font-size: 26px;
    font-weight: 400;
    color: rgba($color: #26273c, $alpha: 0.5);
    line-height: 154px;
    text-align: center;
}
</style>
