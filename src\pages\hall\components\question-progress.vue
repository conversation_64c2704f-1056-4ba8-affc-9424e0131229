<template>
    <div class="question-progress">
        <z-image
            :src="srcMap[stepIndex]"
            :width="618"
            :height="92"/>
        <div class="question-progress__text">
            {{ stepIndex + 1 }}/4
        </div>
    </div>
</template>

<script>
export default {
    name: "question-progress",
    props: {
        stepIndex: {
            type: Number,
            default: 0,
        }
    },
    data() {
        return {
            srcMap: {
                0: require('../assets/images/progress-1.png'),
                1: require('../assets/images/progress-2.png'),
                2: require('../assets/images/progress-3.png'),
                3: require('../assets/images/progress-4.png'),
            }
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.question-progress {
    @include flex-center(row, space-between, center);
    width: 672px;

    &__text {
        font-size: 28px;
    }
}
</style>
