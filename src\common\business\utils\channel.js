import Api from '@/common/server/base';
import { session } from '@/common/utils/storage.js';

export const judgeIfInToutiaoIos = async () => {
    const channelId = Z.getParam("channelId"), subChannelId = Z.getParam("subChannelId");

    let result = {};
    const params = {
        channel: channelId,
        subChannel: subChannelId
    };

    try {
        result = await Api.getChannelConfig(params);
    } catch (err) {
        console.log(err);
    }

    if (result.isError) {
        session.setItem('isToutiaoIos', false);
        session.setItem('isMaiMai', false);
    }

    if (result.data.trafficChannel && Z.platform.isIos) {
        session.setItem('isToutiaoIos', true);
    } else {
        session.setItem('isToutiaoIos', false);
    }
    if(result.data.mediaGroupType&& result.data.mediaGroupType.includes(9)){
        session.setItem('isMaiMai', true);
    }else{
        session.setItem('isMaiMai', false);
    }
};