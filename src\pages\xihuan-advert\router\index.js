import VueRouter from "vue-router";

import Collection from "../views/Collection.vue";
import Info from "../views/Info.vue";
import SuccessResult from "../views/SuccessResult.vue";
import YoulianActivity from "../views/YoulianActivity.vue";
import DownApp from "../views/downApp.vue";
import MateResult from "../views/mateResult.vue";
import MateSelection from "../views/mateSelection.vue";
import YoulianMakefriends from "../views/YoulianMakefriends.vue";
import zaPrivacy from "../views/zaPrivacy";



const router = new VueRouter({
    mode: "hash",
    routes: [{
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            component: Collection
        },
        {
            path: "/info",
            component: Info
        },
        {
            path: "/successResult",
            component: SuccessResult
        },
        {
            path: "/youlianActivity",
            component: YoulianActivity
        },
        {
            path: "/downApp",
            component: DownApp
        },
        {
            path: "/mateResult",
            component: MateResult
        },
        {
            path: "/mateSelection/:id",
            component: MateSelection
        },
        {
            path: "/youlianMakefriends",
            component: YoulianMakefriends
        },
        {
            path: "/zaPrivacy",
            component: zaPrivacy
        },
    ],
    scrollBehavior(to, from, savedPosition) {
        return { x: 0, y: 0 }
    }
});

export default router;
