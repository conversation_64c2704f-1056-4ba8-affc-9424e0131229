// 不同机型缩放比例
let percent = window.innerWidth / 375;
const v = (d) => d * percent;

export default class Radar {
    constructor(size, context, types) {
        this.size = v(size);
        this.types = types;
        this.endPoints = null;
        this.context = context;
        this.centerPoint = [this.size / 2 + v(60), this.size / 2 + v(60)];
    }

    get r() {
        return this.size / 2;
    }

    setLineStyle(color, width) {
        this.context.lineWidth = width;
        this.context.strokeStyle = color;
        this.context.lineCap = "round";
    }

    line(start, end) {
        this.context.beginPath();
        this.context.moveTo(start[0], start[1]);
        this.context.lineTo(end[0], end[1]);
        this.context.stroke();
    }

    baseRender(r) {
        const {
            types,
            context,
            centerPoint: [x, y],
        } = this;

        const num = types.length;

        context.beginPath();

        const points = types.map((v, i) => {
            const px = x + r * Math.sin((2 * Math.PI * i) / num);
            const py = y - r * Math.cos((2 * Math.PI * i) / num);
            context[i ? "lineTo" : "moveTo"](px, py);
            return [px, py];
        });

        context.closePath();
        context.stroke();

        return points;
    }

    // 绘制基本的图形
    render() {
        this.setLineStyle("#524984", 1.2);
        this.baseRender(this.r * 0.5);
        this.baseRender(this.r * 0.75);
        this.endPoints = this.baseRender(this.r);
    }

    // 绘制线条
    renderLine(active = [], isOnlyText) {
        const {
            types,
            context,
            endPoints,
            centerPoint: [x, y],
        } = this;

        if (types) {
            const r = this.r + v(10);
            const num = types.length;
            // context.setFontSize(v(12))
            context.font = `${v(13)}px PingFangSC-Regular`;

            types.forEach((v, i) => {
                const text = types[i];
                const px = x + r * Math.sin((2 * Math.PI * (i + 1)) / num);
                const py = y - r * Math.cos((2 * Math.PI * (i + 1)) / num);
                const isActive = active.includes(i);

                if (isActive && !isOnlyText) {
                    context.strokeStyle = "#ff41f3";
                    context.fillStyle = "#ff41f3";
                } else {
                    context.strokeStyle = "#524984";
                    context.fillStyle = "#D5CEFF";
                }

                const [fx, fy] = this.fixTextPosition(i, px, py);

                if (isActive && isOnlyText) {
                    // context.setFillStyle('#ff41f3')
                    context.fillStyle = "#ff41f3";
                    context.fillText(text, fx, fy);
                } else {
                    // context.strokeText(text, fx, fy)
                    context.fillText(text, fx, fy);
                }

                this.line([x, y], endPoints[i]);
            });
        }
    }

    // 修正 text 的位置
    fixTextPosition(i, x, y) {
        switch (i) {
        case 3:
            return [x - v(10), y + v(15)];
        case 4:
            return [x - v(25), y + v(15)];
        case 5:
            return [x - v(50), y + v(5)];
        case 6:
            return [x - v(45), y + v(5)];
        case 7:
            return [x - v(55), y];
        case 8:
            return [x - v(25), y - v(5)];
        default:
            return [x, y];
        }
    }

    // 绘制选中的区域
    renderActualValue(vals) {
        if (!Array.isArray(vals)) return;

        const {
            r,
            size,
            types,
            context,
            centerPoint: [x, y],
        } = this;

        if (vals.length !== types.length) {
            console.error(`应该有${this.types.length}个值，但得到${vals.length}个`);
            return;
        }

        const num = types.length;

        vals.forEach((v, i) => {
            const precent = v > 1 ? 1 : v;
            const px = x + 5 * r * precent * Math.sin((2 * Math.PI * (i + 1)) / num);
            const py = y - 5 * r * precent * Math.cos((2 * Math.PI * (i + 1)) / num);

            context[i ? "lineTo" : "moveTo"](px, py);
        });

        // 渐变颜色
        const grd = context.createLinearGradient(
            size / 2,
            size / 2,
            size / 2,
            size
        );
        grd.addColorStop(0, "#FF4DF6");
        grd.addColorStop(1, "#935AFF");

        // context.setFillStyle(grd)
        context.fillStyle = grd;
        context.fill();
    }

    draw() {
        this.context.fill();
        this.context.stroke();
    }
}
