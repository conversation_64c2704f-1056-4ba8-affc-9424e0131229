import VueToast from './toast';
let queue = [];

const defaultOptions = {
    type: 'text',
    mask: false,
    message: '',
    isVisible: true,
    duration: 2000,
    position: 'middle',
    loadingType: 'circular',
    forbidClick: false,
    style: {},
    classes: []
};

function createInstance() {
    if (!queue.length) {
        const toast = new (Vue.extend(VueToast))({
            el: document.createElement('div')
        });
        document.body.appendChild(toast.$el);
        queue.push(toast);
    }
    return queue[queue.length - 1];
}


export default {
    name: 'toast',
    install() {
        Vue.prototype.$toast = function (message, opts = {}) {
            if (!message) {
                throw new Error('attribute message is not allowed empty.');
            }

            const toast = createInstance();
            const finalOpts = Object.assign(defaultOptions, opts, {
                message,
                clear() {
                    this.isVisible = false;
                }
            });

            Object.assign(toast, finalOpts);
            clearTimeout(toast.timer);

            if (toast.duration > 0) {
                toast.timer = setTimeout(() => {
                    toast.clear();
                }, toast.duration);
            }

            return toast;
        };

        Vue.use(VueToast);

    }
};
