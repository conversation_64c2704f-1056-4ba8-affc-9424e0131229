import VueRouter from "vue-router";

const Fulfill = () => import('./views/Fulfill.vue')
const Home = () => import('./views/Home.vue')
const Quiz = () => import('./views/Quiz.vue')
const Result = () => import('./views/Result.vue')
const Single = () => import('./views/Single.vue')
const Form = () => import('./views/Form.vue')
import { storage } from "./lib/utils.js";



const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            component: Home
        },
        {
            path: "/single",
            component: Single
        },
        {
            path: "/quiz/:id",
            component: Quiz
        },
        {
            path: "/fulfill",
            component: Fulfill
        },
        {
            path: "/result",
            component: Result
        },
        {
            path: '/form',
            component: Form
        },
    ],
});

router.beforeEach((to, from, next) => {
    let userPath = storage.getItem('userPath');
    if (!userPath) {
        userPath = [];
    }

    if (userPath.indexOf(to.path) > -1) {
        userPath.splice(userPath.indexOf(to.path) + 1);
    } else {
        userPath.push(to.path);
    }
    storage.setItem('userPath', userPath);
    next();
});

// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
