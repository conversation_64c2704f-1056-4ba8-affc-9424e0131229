<template>
    <van-popup
        class="mutual-like-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        closeable
    >
        <h1 class="mutual-like-modal__title">
            配对成功
        </h1>

        <p class="mutual-like-modal__desc">
            你和<span>{{ currentModel && currentModel.name }}</span>互相喜欢了对方
        </p>

        <div class="mutual-like-modal__like">
            <div
                class="mutual-like-modal__like-member-avatar"
                :class="{'mutual-like-modal__like-member-avatar-male': currentModel.sex === 0, 'mutual-like-modal__like-member-avatar-female' : currentModel.sex === 1}"
            >
                <img :src="currentModel && (currentModel.mainImg || currentModel.avatar)">
            </div>
            <div class="mutual-like-modal__like-icon"></div>
            <div
                class="mutual-like-modal__like-default-avatar"
                :class="{
                    'mutual-like-modal__like-default-avatar-male': currentModel.sex === 1,
                    'mutual-like-modal__like-default-avatar-female': currentModel.sex === 0
                }"
            >
            </div>
        </div>

        <div
            class="mutual-like-modal__btn"
            @click="closeModal"
        >
            立即约会
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from "vuex";

export default {
    name: 'MutualLikeModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        currentModel: {
            type: Object,
            default: () => {}
        },
        handleGoDownload: {
            type: Function,
            default: () => {}
        }
    },

    computed: {
        ...mapState([
            'resourceKey',
        ]),
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$reportKibana(this.resourceKey, 97, '报告页-配对成功弹窗曝光');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },


        closeModal() {
            this.$reportKibana(this.resourceKey, 98, '报告页-配对成功弹窗-约会按钮点击');
            this.$emit('input', false);
            this.handleGoDownload();
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';
.mutual-like-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 60px 48px 48px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 16px;
    }

    &__desc {
        width: 462px;
        font-size: 28px;
        font-weight: 400;
        color: #6C6D75;
        line-height: 42px;
        text-align: center;
        margin-bottom: 48px;
        span {
            color: #F591F9;
        }
    }

    &__like {
        display: flex;
        margin-bottom: 48px;
        @include flex-center(row, center, center);
        &-member-avatar {
            width: 170px;
            height: 168px;
            @include flex-center(column, center, center);
            background-size: 100% 100%;
            position: relative;
            left: 12px;
            &-male {
                background: url("../../assets/images/modal-member-circle-male.png") no-repeat;
                background-size: 100% 100%;
            }
            &-female {
                background: url("../../assets/images/modal-member-circle-female.png") no-repeat;
                background-size: 100% 100%;
            }
            img {
                width: 140px;
                height: 140px;
                border-radius: 50%;
            }
        }
        &-icon {
            width: 72px;
            height: 68px;
            background: url("../../assets/images/modal-love.png") no-repeat;
            position: absolute;
            z-index: 1;
            background-size: 100% 100%;
        }
        &-default-avatar {
            width: 170px;
            height: 168px;
            position: relative;
            right: 12px;
            &-female {
                background: url("../../assets/images/modal-female-avatar.png") no-repeat;
                background-size: 100% 100%;
            }
            &-male {
                background: url("../../assets/images/modal-male-avatar.png") no-repeat;
                background-size: 100% 100%;
            }
        }
    }


    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
