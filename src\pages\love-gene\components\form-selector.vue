<template>
    <div
        v-if="visible"
        class="select-wrapper"
        ref="refWrapper">

        <div class="select-mask" @click="closeSelect"></div>

        <div :class="['select-panel', selectType === 'selectSlide' ? 'select-panel--picker' : '']">
            <z-image
                class="form-selector__icon-close"
                :src="require('../assets/images/icon-x.png')"
                :img-size="this.$utils.pxToRem(32)"
                :width="72"
                :height="72"
                @click="closeSelect"/>

            <div class="panel__title">{{ selectParam.label }}</div>
            <div class="panel__tips">完善资料，立即匹配对象</div>

            <!-- 滑动组件：工作地、出生年份-->
            <van-picker
                v-if="selectType === 'selectSlide'"
                ref="refPicker"
                show-toolbar
                :columns="columns"
                @confirm="onConfirm"
                @cancel="onCancel"
                toolbar-position="bottom"
                item-height=1.25rem
                visible-item-count=5
                confirm-button-text="确定"
                cancel-button-text="取消"/>

            <!-- 平铺组件：婚姻状况、学历、收入 -->
            <template v-if="selectType === 'selectBoard'">
                <div class="panel__board" ref="refBoard">
                    <div
                        v-for="(item,index) in selectParam.options"
                        :key="index"
                        @click="onBoardClick($event,item)"
                        class="panel__board__item"
                        :class="item.key === registerForm[currentKey]?'panel__board__item--selected':''">
                        {{ item.text }}
                    </div>
                </div>
                <button class="panel__board__cancel" @click="closeSelect">取消</button>
            </template>
        </div>
    </div>
</template>

<script>
import { Picker } from 'vant';
import { mapState, mapMutations } from 'vuex';
import borderStopScroll from '@/common/utils/borderStopScroll.js';

export default {
    name: 'FormSelector',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        selectType: {
            type: String,
            required: true,
        },
        selectParam: {
            type: Object,
        },
        nextCb: {
            type: Function,
        }
    },
    components: {
        VanPicker: Picker,
    },
    data() {
        return {
            columns: [],
            loopLock: false
        };
    },
    computed: {
        ...mapState([
            'formItems',
            'registerForm',
        ]),
        currentKey() {
            return this.selectParam.key;
        }
    },
    watch: {
        visible (show) {
            if (show) {
                this.setDefault();
            }
        },
    },
    methods: {
        ...mapMutations([
            'setRegisterForm',
            'setFormItems'
        ]),
        setDefault() {
            this.columns = this.selectParam.options;

            this.$nextTick(() => {
                switch (this.selectParam.key) {
                    case 'workCity': {
                        let arr = this.$storage.getItem("cachedWorkCity");

                        if (arr) {
                            this.$refs.refPicker.setIndexes(arr);
                        } else {
                            this.$refs.refPicker.setIndexes([ 2, 5, 0 ]) // 广东 肇庆 端州区
                        }
                        break;
                    }
                    case 'birthday' : {
                        let index = this.$storage.getItem("cachedBirthday");

                        if (typeof index === 'number') {
                            this.$refs.refPicker.setIndexes([ index ]);
                        } else {
                            this.$refs.refPicker.setIndexes([ 38 ]) // 1990
                        }
                        break;
                    }
                    case 'education': {
                        this.$refs.refBoard.scrollTo(0, 200);
                        break;
                    }
                    case 'salary': {
                        //可能需要根据屏幕尺寸调整
                        this.$refs.refBoard.scrollTo(0, 52);
                        break;
                    }
                }

                if ([ 'education', 'salary' ].includes(this.selectParam.key)) {
                    // 处理滚动穿透
                    borderStopScroll({
                        wrapEle: this.$refs.refBoard
                    });
                }
            });
        },
        closeSelect() {
            this.$emit('update:visible', false)
        },
        onBoardClick(event, item) {
            // 设置数据
            this.setFormItems({ [this.selectParam.key]: item.text });
            this.setRegisterForm({
                key: this.selectParam.key,
                value: item.key
            });

            if (this.loopLock) {
                return;
            }

            this.loopLock = true;

            // 获取未填项，自动跳转,会导致漏点击的bug
            setTimeout(() => {
                this.loopLock = false;
                this.autoLoop();
            }, 300)

        },
        autoLoop() {
            let needSelectArr = this.formItems;

            // 获取当前位置
            let position = needSelectArr.findIndex((item) => {
                return item.key === this.selectParam.key;
            })

            // 记录是否已经全部填写
            let hasDone = true,
                nextItem = null;

            for (let i = 0; i < needSelectArr.length; i++) {
                // 从当前位置的下一个开始正循环遍历，寻找下一个需要调用selectPanel组件的未填项
                position = position === (needSelectArr.length - 1) ? 0 : (position + 1);

                if (needSelectArr[position].value) {
                    // 已填写则跳过
                    continue;
                } else {
                    // 未填写则存储要打开的注册项信息
                    nextItem = needSelectArr[position];
                    hasDone = false;
                    break;
                }
            }

            // 如果需要调用selectPanel组件的注册项都填写，则关闭selectPanel组件
            if (hasDone) {
                return this.closeSelect();
            }

            // 否则自动跳转至下一个需要调用selectPanel组件的注册项
            this.nextCb(nextItem);
            this.$nextTick(() => {
                this.setDefault();
            });
        },
        onConfirm(value, index) {
            let picker = this.$refs.refPicker;
            let key = '';
            let text = '';

            if (this.selectParam.key === "birthday") {
                // 生日picker
                let year = picker.getColumnValue(0).key;

                key = new Date(year + "/" + 1 + "/" + 1).getTime(); //实际传给后台的数据为 年/1/1 对应的毫秒值
                text = picker.getColumnValue(0).text;

                this.$storage.setItem("cachedBirthday", index);  // 用于初始化select
            } else if (this.selectParam.key === "workCity") {
                // 工作地picker
                let currentCity = picker.getColumnValue(0).text;

                if ([ "北京", "上海", "重庆", "天津" ].includes(currentCity)) {
                    key = picker.getColumnValue(1).key;
                    text = value.slice(0, 2).join("/");
                } else {
                    key = picker.getColumnValue(2).key;
                    text = value.slice(0, 3).join("/");
                }

                this.$storage.setItem("cachedWorkCity", index);  // 用于初始化select
            }

            // 设置数据
            this.setFormItems({ [this.selectParam.key]: text });

            const formItem = {
                key: this.selectParam.key,
                value: key,
            }

            if (this.selectParam.key === 'birthday') {
                formItem.getMarkValue = () => {
                    return {
                        year: String(text),
                        month: '1',
                        day: '1',
                    }
                }
            }

            this.setRegisterForm(formItem);

            if (this.loopLock) {
                return;
            }

            this.loopLock = true;

            // 获取未填项，自动跳转
            setTimeout(() => {
                this.loopLock = false;
                this.autoLoop();
            }, 300)
        },
        onCancel() {
            this.closeSelect();
        }
    },
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.form-selector {
    &__icon-close {
        position: absolute;
        top: 40px;
        right: 24px;
    }
}

.select-mask {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    background: rgba($color: #26273C, $alpha: 0.6);
    z-index: 100;
}

.select-panel {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 750px;
    background: #FFFFFF;
    border-radius: 60px 60px 0 0;
    z-index: 999;

    &--picker {
        height: 990px;
    }
}

.panel__tips {
    margin-top: 8px;
    font-size: 29px;
    color: #92939D;
    text-align: center;
    line-height: 43px;
}

.panel__title {
    margin-top: 48px;
    font-size: 36px;
    font-weight: 700;
    color: #26273C;
    text-align: center;
    line-height: 54px;
}

.panel__board {
    padding: 0 48px;
    position: relative;
    @include flex-center(row, null, center);
    flex-direction: column;
    margin-top: 48px;
    width: 750px;
    max-height: 50vh;
    overflow: scroll;
}

.panel__board--blur {
    position: absolute;
    width: 560px;
    height: 0px;
    box-shadow: 0 0 40px 20px #ffffff;
    z-index: 3;
}

.top {
    top: 260px;
    left: 95px;
}

.bottom {
    bottom: 164px;
    left: 95px;
}

.panel__board__item {
    width: 100%;
    height: 88px;
    margin-top: 32px;
    flex-shrink: 0;
    border-radius: 55px;
    border: 2px solid #767DFF;
    font-size: 32px;
    font-weight: 400;
    color: #767DFF;
    text-align: center;
    line-height: 88px;
}

.panel__board__item:nth-child(1) {
    margin-top: 0;
}

.panel__board__item--selected {
    color: #FFFFFF;
    background: #767DFF;
}

.panel__board__cancel {
    display: block;
    margin: 48px auto 62px;
    font-size: 32px;
    font-weight: 400;
    color: #767DFF;
    text-align: center;
}

</style>

<style lang="scss">
/* 覆盖vant样式 */
.van-picker {
    margin-top: 77px;
}

.van-picker__columns {
    margin-top: -30px;
}

.van-picker-column {
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
}

.van-picker-column__item {
    font-size: 30px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-picker-column__item--selected {
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-picker__toolbar {
    margin-top: 60px;
    flex-direction: column;
    align-content: flex-start;

}

.van-picker__cancel {
    flex-shrink: 0;
    order: 1;
    width: 654px;
    height: 110px;
    font-size: 32px;
    font-weight: 400;
    color: #767DFF;
    line-height: 47px;
}

.van-picker__confirm {
    flex-shrink: 0;
    order: 0;
    margin: 0 auto;
    width: 654px;
    height: 110px;
    background: #767DFF;
    border-radius: 55px;
    font-size: 32px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 47px;
}
</style>
