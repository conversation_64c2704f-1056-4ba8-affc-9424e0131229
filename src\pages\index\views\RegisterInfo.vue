<!-- 老注册页AB测试方案测试组对应的下载页 -->
<template>
    <div class="registerinfo-wrapper">
        <!-- 头图 -->
        <img
            class="info-header-wrapper"
            src="https://photo.zastatic.com/images/common-cms/it/20220607/1654595675438_582500_t.png"
        />

        <!-- 翻牌选择 -->
        <div class="info-blind-wrapper">
            <div
                v-for="(item, index) in blindArr"
                :key="index"
                class="blind__item"
            >
                <!-- 头像 -->
                <div
                    v-if="item.avatar"
                    class="blind__item__img"
                    :class="selectedId === index ? '' : 'blur'"
                    :style="{
                        backgroundImage: `url(${
                            item.avatar
                        }?imageMogr2/thumbnail/200x200)`
                    }"
                ></div>

                <!-- 动效 -->
                <div
                    :id="'itemBlur' + index"
                    class="blind__item__img--svga"
                ></div>

                <!-- 按钮 -->
                <div
                    class="blind__item__block"
                    @click="selectBlind(item, index)"
                >
                    <button
                        class="blind__item__block__button"
                        :class="
                            selectedId === index
                                ? 'blind__item__block__button--disabled'
                                : ''
                        "
                    >
                        我要约Ta
                    </button>
                </div>
            </div>
        </div>

        <!-- 主图 -->
        <div class="info-avatar-wrapper">
            <div
                class="info-avatar"
                :class="modelInfo.mainImg ? '' : 'blur'"
                :style="{
                    backgroundImage: modelInfo.mainImg
                        ? `url(${
                            modelInfo.mainImg
                        }?imageMogr2/thumbnail/700x700)`
                        : `url(${require('../assets/imgs/default-main.png')})`
                }"
            >
                <div class="avatar__icon--online">
                    APP在线
                </div>
                <div class="avatar__icon--location">
                    在您附近
                </div>
                <div
                    class="avatar__icon--hello"
                    @click="openDownloadModal('modalDownload', {})"
                >
                    打个招呼
                </div>
            </div>

            <div
                v-if="!modelInfo.mainImg"
                class="info-avatar--blur"
            >
                <div class="avatar--blur__lock"></div>
                <div class="avatar--blur__text">
                    您有一次约会机会
                    <span>选择约会的对象，可解锁Ta的全部资料</span>
                </div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div
            v-if="selectedId !== ''"
            class="info-content-wrapper"
        >
            <div class="content__list">
                <!-- 基本信息 -->
                <div class="list__title">
                    Ta的详细资料
                </div>
                <div
                    v-for="(item, index) in contentList"
                    :key="index"
                    class="list__item"
                >
                    <div
                        v-if="item.key === 'name'"
                        class="list__item__label"
                    >
                        {{ modelInfo[item.key] }}
                    </div>
                    <template v-else>
                        <div class="list__item__label">
                            {{ item.label }}
                        </div>
                        <div class="list__item__value">
                            {{ modelInfo[item.key] }}
                        </div>
                    </template>
                </div>

                <!-- logo和按钮 -->
                <div class="list__logo--car"></div>
                <div
                    class="list__logo--phone"
                    @click="openDownloadModal()"
                >
                    查看完整手机号
                </div>
                <div
                    class="list__logo--wechat"
                    @click="openDownloadModal()"
                >
                    查看完整微信号
                </div>
            </div>

            <div class="content__photo">
                <div class="photo__title">
                    <div class="photo__title--main">
                        Ta的动态
                    </div>
                    <div
                        class="photo__title--sub"
                        @click="openDownloadModal()"
                    >
                        查看所有{{ momentCount }}条
                    </div>
                </div>

                <div class="content-block">
                    <div
                        class="photo__block--main"
                        :style="{
                            backgroundImage: `url(${
                                modelInfo['momentImg1']
                            }?imageMogr2/thumbnail/315x315)`
                        }"
                    ></div>
                    <div class="photo__block--sub">
                        <div
                            class="block--sub__item"
                            v-for="index in 3"
                            :key="index"
                            :style="{
                                backgroundImage: `url(${
                                    modelInfo['momentImg' + (index + 1)]
                                }?imageMogr2/thumbnail/154x154)`
                            }"
                        ></div>
                        <div
                            class="block--sub__item--last"
                            @click="openDownloadModal()"
                        >
                            更多照片
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="info-footer-wrapper">
            <div class="footer-tips">
                请尽快下载APP去跟Ta约会吧~
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="info-bottom-wrapper">
            <div
                class="bottom-button"
                @click="openDownloadModal()"
            >
                下载APP，立即约会
            </div>
            <a
                @click="handleGoBack"
                class="bottom-link"
            >访问网页版可查看更多优质异性>></a>
        </div>

        <!-- 弹窗 -->
        <download-modal
            v-if="downloadModalVisible"
            @close-modal="closeModal"
            :open-download="openDownload"
        />
    </div>
</template>

<script>
import DownloadModal from "../components/common/DownloadModal.vue";
import { _getSpecifyGenderRandomAvatar, _getModelInfo } from "../js/api.js";
import { getRandomInt } from "@/common/utils/tools";
import { reportKibana } from "@/common/utils/report.js";

export default {
    components: {
        DownloadModal
    },
    data() {
        return {
            registerInfo: {},
            blindArr: [{}, {}, {}],
            selectedId: "",
            modelInfo: {
                ageString: "",
                avatar: "",
                educationString: "",
                mainImg: "", // 主图
                momentImg1: "", // 动态图
                momentImg2: "",
                momentImg3: "",
                momentImg4: "",
                momentImg5: "",
                name: "",
                phone: "",
                salaryString: "",
                sex: "",
                weCaht: "",
                workCityString: ""
            },
            contentList: [
                {
                    key: "name"
                },
                {
                    label: "年龄",
                    key: "ageString"
                },
                {
                    label: "工作地",
                    key: "workCityString"
                },
                {
                    label: "学历",
                    key: "educationString"
                },
                {
                    label: "月收入",
                    key: "salaryString"
                },
                {
                    label: "手机号",
                    key: "phone"
                },
                {
                    label: "微信号",
                    key: "weChat"
                }
            ],
            momentCount: getRandomInt(10, 50),
            downloadModalVisible: false,
            openDownload: true
        };
    },
    mounted() {
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
        reportKibana("旧落地页(翻牌)", 60, "翻牌页访问");
        const oRegister = JSON.parse(localStorage.getItem("oRegister"));
        // 限访问一次
        // localStorage.removeItem('oRegister');
        // if(!oRegister){
        //     window.location.replace('//i.zhenai.com/m/portal/register/index.html');
        // }
        Object.assign(this.registerInfo, oRegister);
        this.getAvatars();

        // 监听返回事件，强制跳转至 wap 站
        window.addEventListener("popstate", () => {
            reportKibana("旧落地页(翻牌)", 60, "翻牌页-返回按钮点击");
            this.handleGoBack();
        });
        window.history.pushState(null, "", "");
    },
    destroyed() {
        window.removeEventListener("popstate", this.handleGoBack);
    },
    methods: {
        // 获取随机的三个翻牌模特
        async getAvatars() {
            let age = new Date().getFullYear() - Number(this.registerInfo.year);
            if (age === "" || this.registerInfo.gender === "") {
                return;
            }
            let sendData = {
                sex: +this.registerInfo.gender === 0 ? 1 : 0, // 当前性别为男(0),则传女(1)
                limit: 3, // 随机头像个数
                age
            };
            let resData = await _getSpecifyGenderRandomAvatar(sendData);
            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }
            this.blindArr = resData.data.list;
        },
        // 选择翻牌模特
        async selectBlind(item, index) {
            if (typeof this.selectedId === "number") {
                if (this.selectedId === index) {
                    return;
                }
                this.openDownloadModal();
                return;
            }
            reportKibana("旧落地页(翻牌)", 60, "翻牌页-“我要约ta”按钮点击");
            this.selectedId = index;
            let currentBlurId = "#itemBlur" + index;
            this.setSVGA(currentBlurId, item.avatar);
            await this.setModelInfo(item.id);
        },
        setSVGA(dom, avatar) {
            let player = new SVGA.Player(dom),
                parser = new SVGA.Parser(dom);
            parser.load(
                require("../assets/imgs/svgaBroken.svga"),
                videoItem => {
                    player.setImage(avatar, "key");
                    player.loops = 1;
                    player.setVideoItem(videoItem);
                    player.startAnimation();
                    // player.startAnimationWithRange({location:0,length:5});
                }
            );
        },
        // 获取翻牌模特信息
        async setModelInfo(target) {
            const age =
                new Date().getFullYear() - Number(this.registerInfo.year);
            let sendData = {
                age,
                modelId: target,
                salary: this.registerInfo.salary,
                workCity: this.registerInfo.workCity,
                education: this.registerInfo.education,
                sex: this.registerInfo.gender
            };
            if (!sendData.salary || !sendData.workCity) {
                return;
            }

            let resData = await _getModelInfo(sendData);
            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }

            Object.assign(this.modelInfo, resData.data.modelInfoVo);
        },
        openDownloadModal() {
            reportKibana("旧落地页(翻牌)", 60, "翻牌页-“下载”按钮点击");
            this.downloadModalVisible = true;
        },
        closeModal() {
            this.downloadModalVisible = false;
        },
        handleGoBack() {
            const channelId = Z.getParam("channelId");
            const subChannelId = Z.getParam("subChannelId");
            location.href = `${
                location.protocol
            }//i.zhenai.com/m/wap/index/index.html?channelId=${channelId}&subChannelId=${subChannelId}`;
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.registerinfo-wrapper {
    background: #6f6bfd;
    overflow: auto;
}
.info-header-wrapper {
    width: 750px;
}
.info-blind-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;

    .blind__item {
        position: relative;
        width: 200px;
        height: 278px;
        // background: #6200AE;
        border-radius: 16px 16px 16px 16px;
        overflow: hidden;
    }

    .blind__item__img {
        @include set-img("../assets/imgs/img.png");
        width: 200px;
        height: 200px;
        border-radius: 16px 16px 0px 0px;
    }

    .blind__item__img--svga {
        position: absolute;
        left: 0;
        top: 0;
        width: 200px;
        height: 200px;
        z-index: 2;
    }
    .blind__item__block {
        position: absolute;
        top: 200px;
        width: 200px;
        height: 80px;
        background: #6200ae;
    }
    .blind__item__block__button {
        position: relative;
        display: block;
        margin: 15px auto 0;
        width: 150px;
        height: 50px;
        background: linear-gradient(0deg, #ffc334, #ff8f02);
        border-radius: 25px;
        font-size: 24px;
        font-weight: 700;
        color: #fdfdfd;
        line-height: 50px;
        text-shadow: 0px 5px 13px rgba(255, 125, 199, 0.7);
        box-shadow: inset 0 0 6px 1px #fff;
        z-index: 3;
    }

    .blind__item__block__button--disabled {
        background: linear-gradient(0deg, #979797, #888888) !important;
    }
}
.info-avatar-wrapper {
    position: relative;
    margin: 40px auto 0;
    width: 702px;
    height: 702px;
    overflow: hidden;
    border-radius: 32px;
    z-index: 3;

    .info-avatar {
        width: 100%;
        height: 100%;
        border-radius: 32px;
        font-size: 26px;
        color: #fdfdfd;
        @include set-img("../assets/imgs/default-main.png");
    }

    .avatar__icon--online {
        position: absolute;
        top: 550px;
        left: 32px;
        padding-left: 54px;
        width: 194px;
        height: 48px;
        background: rgba($color: #26273c, $alpha: 0.5);
        border-radius: 24px;
        line-height: 48px;
    }

    .avatar__icon--online::before {
        content: "";
        position: absolute;
        left: 22px;
        top: 50%;
        transform: translateY(-50%);
        width: 22px;
        height: 22px;
        background-image: url(../assets/imgs/icon-online.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .avatar__icon--location {
        position: absolute;
        top: 622px;
        left: 32px;
        padding-left: 54px;
        width: 194px;
        height: 48px;
        background: rgba($color: #26273c, $alpha: 0.5);
        border-radius: 24px;
        line-height: 48px;
    }

    .avatar__icon--location::before {
        content: "";
        position: absolute;
        left: 22px;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 24px;
        background-image: url(../assets/imgs/icon-location.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .avatar__icon--hello {
        position: absolute;
        top: 582px;
        left: 468px;
        width: 201px;
        height: 88px;
        background: #787cff;
        border-radius: 44px;
        box-shadow: 3px 5px 36px 7px rgba(120, 124, 255, 0.4);

        font-size: 32px;
        font-weight: 700;
        color: #fdfdfd;
        line-height: 88px;
        text-align: center;
    }

    .info-avatar--blur {
        position: absolute;
        left: 0;
        top: 0;
        background: rgba($color: #464849, $alpha: 0.5);

        width: 100%;
        height: 100%;
        border-radius: 32px;
    }

    .avatar--blur__lock {
        position: absolute;
        left: 50%;
        top: 153px;
        transform: translateX(-50%);
        @include set-img("../assets/imgs/lock.png");
        width: 32px;
        height: 38px;
    }

    .avatar--blur__text {
        margin: 220px auto 0;
        width: 600px;

        font-size: 36px;
        font-weight: 700;
        color: #ffffff;
        line-height: 53px;
        text-align: center;
        span {
            display: block;
            font-size: 32px;
        }
    }
}
.info-content-wrapper {
    position: relative;
    margin: -50px auto 0;
    width: 702px;

    .content__list {
        // 重合部分高度为50px
        padding-top: 50px;
        padding-left: 32px;
        padding: 50px 32px 80px;
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
        border-radius: 32px;
    }
    .list__logo--car {
        position: absolute;
        top: 102px;
        right: -20px;
        width: 198px;
        height: 198px;
        @include set-img("../assets/imgs/car-logo.png");
    }

    .list__logo--phone {
        position: absolute;
        top: 606px;
        right: 49px;
        width: 234px;
        height: 48px;
        background: #787cff;
        border-radius: 24px;

        font-size: 24px;
        color: #ffffff;
        line-height: 48px;
        text-align: center;
    }

    .list__logo--wechat {
        position: absolute;
        top: 686px;
        right: 49px;
        width: 234px;
        height: 48px;
        background: #787cff;
        border-radius: 24px;
        font-size: 24px;
        color: #ffffff;
        line-height: 48px;
        text-align: center;
    }

    .list__title {
        margin-top: 52px;
        font-size: 36px;
        font-family: Source Han Sans SC;
        font-weight: 700;
        color: #26273c;
        line-height: 63px;
    }

    .list__item {
        @include set-flex(flex-start, center);
        margin-top: 50px;
        height: 30px;
        font-size: 32px;
        color: #26273c;
        line-height: 30px;
    }

    .list__item__label {
        position: relative;
        top: 50%;
        // transform: translateY(-50%);
        display: inline-block;
        text-align: justify;
        text-align-last: justify;
        width: 160px;
    }

    .list__item__label::after {
        content: "";
        width: 100%;
        display: inline-block;
        height: 0;
    }

    .list__item__value {
        position: relative;
        padding-left: 32px;
    }

    .list__item__value::before {
        content: ":";
        position: absolute;
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
    }

    // 昵称单独处理
    .list__item:nth-child(2) {
        .list__item__label {
            text-align: left;
            text-align-last: left;
        }

        .list__item__value::before {
            content: "";
        }
    }

    .content__photo {
        margin: 39px auto 0;
        width: 702px;
        height: 518px;
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
        border-radius: 32px;
    }

    .photo__title {
        @include set-flex(space-between, center);
        padding: 0 33px;
        height: 140px;
    }

    .photo__title--main {
        font-size: 36px;
        font-weight: 700;
        color: #26273c;
        line-height: 140px;
    }

    .photo__title--sub {
        position: relative;
        padding-right: 28px;
        font-size: 26px;
        font-weight: 400;
        color: #6c6d75;
        line-height: 140px;
    }

    .photo__title--sub::after {
        content: "";
        @include set-img("../assets/imgs/right-arrow.png");
        position: absolute;
        right: -6px;
        top: 50%;
        transform: translateY(-52%);
        width: 32px;
        height: 32px;
        font-weight: 400;
        color: #6c6d75;
        line-height: 140px;
    }

    .content-block {
        @include set-flex(space-between, center);
        margin: 0 auto;
        width: 640px;
    }

    .photo__block--main {
        width: 315px;
        height: 315px;
        border-radius: 16px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .photo__block--sub {
        @include set-flex(space-between, center);
        flex-wrap: wrap;
        align-content: space-between;
        width: 315px;
        height: 315px;
    }

    .block--sub__item {
        width: 154px;
        height: 154px;
        border-radius: 16px;
        // background: grey;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .block--sub__item--last {
        width: 154px;
        height: 154px;
        background-color: rgba($color: #26273c, $alpha: 0.1);
        border-radius: 16px;
        font-size: 26px;
        font-weight: 400;
        color: rgba($color: #26273c, $alpha: 0.5);
        line-height: 154px;
        text-align: center;
    }
}
.info-footer-wrapper {
    margin-top: 50px;
    padding-bottom: 250px;
    .footer-tips {
        font-size: 30px;
        font-weight: 700;
        color: #ffffff;
        line-height: 38px;
        text-align: center;
    }
}
.info-bottom-wrapper {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: center;
    z-index: 4;
    padding-top: 32px;
    background-color: rgba(255, 255, 255, .65);
    border-radius: 32px 32px 0 0;
    .bottom-button {
        margin: 0 auto;
        width: 686px;
        height: 110px;
        background: linear-gradient(0deg, #ffc334, #ff8f02);
        border-radius: 55px;
        font-size: 32px;
        font-weight: 700;
        color: #ffffff;
        line-height: 110px;
        text-align: center;
        text-shadow: 0px 5px 13px rgba(255, 125, 199, 0.7);
        box-shadow: inset 0 0 16px 2px #ffffff;
        z-index: 4;
    }
    .bottom-link {
        display: inline-block;
        margin: 16px 0 32px;
        font-size: 28px;
        color: #888b8a;
    }
}
</style>
