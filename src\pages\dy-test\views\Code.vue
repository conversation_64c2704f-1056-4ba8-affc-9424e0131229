<template>
    <div class="code">
        <div class="header">
            <div
                class="back"
                @click="goBack"
            ></div>
        </div>

        <h1 class="code__title">
            验证码已通过短信发送至你手机
        </h1>

        <p class="code__desc">
            {{ phone || '' }}
        </p>

        <div class="code__code">
            <div class="code__code-input-items">
                <div
                    v-for="(item, index) in 4"
                    :key="index"
                    class="code__code-input-item"
                />
            </div>
            <input
                ref="codeInput"
                type="tel"
                class="code__code-input"
                maxlength="4"
                :value="code"
                @input="checkCode"
                autocomplete="new-password"
            >
        </div>

        <div class="code__code-error">
            {{ errorMessage }}
        </div>

        <div
            class="code__btn"
            :class="{'disabled': isLock}"
            @click="clickSendCode"
        >
            {{ isLock ? `重新获取（${btnText}s）` : btnText }}
        </div>

        <!-- 覆盖注册弹窗 -->
        <common-register-overwrite-modal
            :page-type="planName"
            :validate-account-result="validateAccountResult"
            :overwrite-member-info="overwriteMemberInfo"
            v-model="overwriteModal"
            @select-overwrite="handleOverwriteAccount"
            @select-origin="handleLogin"
        />
    </div>
</template>

<script>
import Api from '@/common/server/base';
import { reportMagic } from "@/common/utils/report";
import Prototype from "@/common/framework/prototype";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import { registerResult } from "@/common/config/register-dictionary.js";
import CommonRegisterOverwriteModal from '../components/RegisterOverwriteModal.vue';
import oUserSelect from '@/common/ocpx/huichuan';
import z_ from '@/common/zdash';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { Toast } from "vant";

export default {
    name: 'Code',
    components: {
        CommonRegisterOverwriteModal
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            errorMessage: '',
            btnText: '60',
            isLock: false,
            code: '',
            leaveTime: null,
            isListenVisibilitychange: null,
            phone: '',
            planName: Session.getItem('planName'),
            pageType: Session.getItem('ext30'),
            
            // 校验账号结果
            validateAccountResult: {},
            overwriteModal: false,
            overwriteMemberInfo: null,
        };
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        async checkCode(evt) {
            this.errorMessage = '';
            this.code = evt.target.value.replace(/\D/g, '');
            evt.target.value = this.code;

            if (this.code.length === 4) {
                this.$refs.codeInput.blur();
                const result = await this.handleValidateCode(this.code);

                if (result.isError) {
                    // this.code = '';
                    // this.$refs.codeInput.focus();

                    switch (result.errorCode) {
                    case '-8002005':
                        this.errorMessage = '验证码错误，请重新输入';
                        break;
                    case '-8002006':
                        this.errorMessage = '验证码已过期';
                        break;
                    case '-8002004':
                        this.errorMessage = result.errorMessage;
                        break;
                    }

                    return;
                }
                Prototype.$gather.setValidateCodeSuccessOCPC();
            }
        },
        async sendCode() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();
            
            const sendData = {
                phone: this.phone.replace(/[^(\d)]/g, ""),
                type: 0,
            };

            //【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam('clickid'),
                adid: Z.getParam('adid'),
                creativeid: Z.getParam('creativeid'),
                creativetype: Z.getParam('creativetype')
            };

            for (const key in toutiaoParamlist) {
                if (toutiaoParamlist[key]) {
                    sendData[key] = toutiaoParamlist[key];
                }
            }

            const sendMsgResult = await Api.sendWapMessageCodeV2(sendData);

            if (sendMsgResult.isError) {
                this.errorMessage = sendMsgResult.errorMessage;
            } else {
                this.$toast('验证码已发送，请注意查收');
            }
        },
        clickSendCode() {
            this.$report(11, "短信验证页-重获验证码按钮点击");
            this.countDown();
        },
        async countDown() {
            if (this.isLock) {
                return;
            }

            // this.$report(3100, "提交手机号（监控）");

            this.isLock = true;

            await this.sendCode();

            // 从60s开始倒计时
            this.btnText = 60;

            this.timer = setInterval(() => {
                this.btnText -= 1;
                if (this.btnText <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }, 1000);
        },

        // 处理校验验证码相关
        async handleValidateCode(messageCode) {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.planName}`
            ) || this.registerForm;

            Prototype.$gather.setAbtZaTTTCookie();

            const sendData = Prototype.$gather.getValidateCodeParams(registerForm.phone.replace(/[^(\d)]/g, ""), messageCode, this.pageType);
            const result = await Api.submitWapRegNoPasswordInfoV2(sendData);

            if (!result.isError) {
                if(result.data.memberID) {
                    this.handleSetRegMemberId(result.data.memberID);
                }
                setTimeout(() => {
                    this.messageCode = messageCode;
                    this.handleCheckOverrideAccount(result.data);
                }, 0);
            }

            return result;
        },
        goBack() {
            this.$router.back();
        },
        // 根据类型展示不同弹窗
        handleCheckOverrideAccount(validateAccountResult) {
            const { type, oldMemberID, memberInfoVo } = validateAccountResult;

            console.log(validateAccountResult);

            this.validateAccountResult = validateAccountResult;

            switch (type) {
            case registerResult.LOGIN_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.overwriteModal = true;
                break;
            case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.overwriteMemberInfo = memberInfoVo;
                this.overwriteModal = true;
                break;
            case registerResult.NEW_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.NEW_ACCOUNT.label);
                break;
            case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.AUTO_OVERWRITE_ACCOUNT.label);
                break;
            }

            const resultType = z_.find(registerResult, {
                value: type
            });

            if (z_.get(resultType, 'label')) {
                this.$report(50, "手机号验证成功", {
                    ext17: resultType.label,
                });
            }
        },
        // 处理覆盖注册
        async handleOverwriteAccount() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = Prototype.$gather.getOverwriteAccountParams(this.messageCode, this.pageType);
            const result = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            if(result.data.memberID) {
                this.handleSetRegMemberId(result.data.memberID);
            }

            this.overwriteModal = false;
            this.handleFinishedRegister(registerResult.MANUAL_OVERWRITE_ACCOUNT.label);
        },
        handleFinishedRegister(ext) {
            this.$report(51, "注册成功并生成ID", {
                ext17: ext
            });
            this.$toast('注册成功');
            Storage.setItem('registerFinished', true);
            this.handleAfterRegisiter();
        },
        // reg_id存session，原因是ocpx从session里取的值是reg_memberid
        handleSetRegMemberId(id) {
            Session.setItem('reg_memberid', id);
            oUserSelect.mark({
                msgValid: true
            });
        },
        handleAfterRegisiter() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.planName}`
            ) || this.registerForm;
            console.log('oRegister', registerForm);
            Storage.setItem('oRegister', registerForm);
            // 注册完成
            const channelId = Z.getParam('channelId');
            const subChannelId = Z.getParam('subChannelId');
            const urlParams = `?channelId=${channelId}&subChannelId=${subChannelId}&source=register&ext30=${this.pageType}`;
            if (this.pageType === '86') {
                // 新全屏下载页测试
                window.location = `//i.zhenai.com/m/portal/newmisc/welcome/index.html${urlParams}`;
            } else {
                window.location = `//i.zhenai.com/m/portal/newregister/index/index.html${urlParams}#/registerinfo`;
            }
        },
        handleLogin() {
            visibilityChangeDelay(function() {
                const isTTWebview = /(TTWebView|BytedanceWebview)/.test(navigator.userAgent);
                if (isTTWebview && Z.platform.isIos) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();

            // const channelId = Z.getParam('channelId');
            // const subChannelId = Z.getParam('subChannelId');
            // const urlParams = `?channelId=${channelId}&subChannelId=${subChannelId}&source=register`;
            // window.location = `//i.zhenai.com/m/portal/login.html${urlParams}`;
        }
    },
    created() {
        this.code = '';
        this.errorMessage = '';
                
        const registerForm = Storage.getItem(
            `cachedRegisterForm-${this.planName}`
        );

        this.phone = registerForm.phone;

        this.$report(11, "验证短信页访问");

        Prototype.$gather.setBeforeValidateCodeOCPC();
        this.countDown();
        this.$nextTick().then(() => {
            this.$refs.codeInput.focus();
        });
    },
    mounted() {
        const callback = () => {
            // 用户进入后台
            if (document.visibilityState === "hidden") {
                this.leaveTime = new Date().getTime();
            } else if (document.visibilityState === "visible") {
                if (!this.isLock) {
                    return;
                }

                this.backTime = new Date().getTime();
                const diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                this.btnText -= diff;

                if (this.btnText <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }
        };

        document.addEventListener('visibilitychange', callback);
        this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('visibilitychange', callback);
        });
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.code {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    background-color: #F3F5F6;
    
    &::before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0;
        right: 0;
        width: 750px;
        height: 686px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684724562516_581755_t.png');
    }

    .header {
        padding: 20px 0 0;
        .back {
            width: 72px;
            height: 72px;
            margin-left: 48px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684492059170_707059_t.png');
            background-position: center center;
        }
    }

    &__title {
        font-weight: 400;
        font-size: 36px;
        color: #26273C;
        line-height: 54px;
        text-align: center;
        margin: 28px auto 0;
    }

    &__desc {
        font-weight: 400;
        font-size: 36px;
        color: #26273C;
        line-height: 54px;
        text-align: center;
        margin: 0 auto 72px;
    }

    &__btn {
        @include flex-center(column);
        flex-shrink: 0;
        width: 654px;
        height: 110px;
        font-size: 32px;
        margin: 66px auto 0;
        border-radius: 80px;
        text-align: center;
        background-color: #191C32;
        color: #fff;
        &.disabled {
            background-color: #AEB1B6;
        }
    }

    &__code {
        position: relative;
        color: #26273C;
        width: 490px;
        margin: 72px auto 24px;

        &-input-items {
            @include flex-center(row, space-between, center);
            box-sizing: border-box;
        }

        &-input-item {
            width: 110px;
            height: 144px;
            background: #b1b4bb33;
            border-radius: 50px;
        }

        &-error {
            width: 100%;
            text-align: center;
            font-size: 28px;
            margin-top: 24px;
            color: #F04086;
        }

        &-input {
            box-sizing: border-box;
            width: 580px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 60px;
            font-size: 42px;
            line-height: 52px;
            background: transparent;
            letter-spacing: 104px;
            padding-left: 42px;
            overflow: hidden;
            color: #26273C;
            caret-color: #8C7AFE;
        }
    }

    /deep/ .van-overlay {
        background-color: rgba(0, 0, 0, .6);
    }
}
</style>
