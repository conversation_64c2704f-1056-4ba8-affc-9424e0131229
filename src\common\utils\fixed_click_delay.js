import { env } from './platform.js';

let hasInit = false
export default function fixedClickDelay() {
    // 因为只有UIWebview下有这个问题,所以只在ios 的 app中才加载，其他情况已通过 touch-action: manipulation;
    if(!hasInit && env.isApp && env.isIos){
        let url = '/common/m/base/js/fastclick-1.0.6.min.js'
        if(process.env.NODE_ENV==='development'){
            // ~~
            url = `${window.location.protocol}//i.zhenai.com/common/m/base/js/fastclick-1.0.6.min.js`
        }
        Z.loadUrl(url, function () {
            if(!hasInit){
                FastClick.attach(document.body);
                hasInit = true
            }
        })
    }
}