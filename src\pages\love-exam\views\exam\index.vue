<template>
    <div class="box">
        <div
            class="contentBox"
            @click="goToExam"
        >
        <!-- <img src="https://photo.zastatic.com/images/common-cms/it/20240528/1716865215320_862941.png" alt="" class="examBtn" @click="goToExam"> -->
        </div>
    </div>
</template>

<script>

export default {
    name: 'LoveExamIndex',
    data() {
        return {

        };
    },
    created() {
        this.getCompleteExamResult();
        this.$report(1,'首页访问', {ext1: location.href});
        this.$report(3000,'首页访问（监控）');

    },
    methods: {
        getCompleteExamResult(){
            Z.ajax({
                type: 'GET', url: 'https://api.zhenai.com/game/loveExam/examInfo.do'
            }, res => {
                if(res.data && res.data.completeExam){
                    this.$router.push('/exam-result');
                }
            }, error => {
                console.log(error);
            });
        },
        goToExam(){
            this.$router.push('/register/0');
            this.$report(2,'首页-按钮点击');
        }
    },
    components: {
    },
};
</script>

<style scoped>
.box{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.contentBox{
    flex: 1;
    /* background: url('https://photo.zastatic.com/images/common-cms/it/20240528/1716865302116_693971.png') no-repeat; */
    background: url('https://photo.zastatic.com/images/common-cms/it/20240530/1717041338778_901813.png') no-repeat;
    background-size: 100% 100%;
}
.examBtn{
    width: 197px;
    height: 66.5px;
    position: absolute;
    top: 66%;
    left: 100px;
}
</style>
