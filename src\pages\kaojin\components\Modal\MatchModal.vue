<template>
    <van-popup
        class="match-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="title">
            恭喜!配对成功
        </div>

        <div class="subtitle">
            请在应用市场搜索“珍爱”下载APP
        </div>

        <div class="avatar">
            <div class="avatar-female">
                <div 
                    v-if="!isMale"
                    :style="{backgroundImage:`url(${this.avatar})`}"
                ></div>
            </div>
            <div class="avatar-male">
                <div
                    v-if="isMale"
                    :style="{backgroundImage:`url(${this.avatar})`}"
                >
                </div>
            </div>
            <div class="avatar-love"></div>
        </div>

        <div class="content">
            对方已接受你的好友请求<br />
            在珍爱等你回复中
        </div>

        <div
            class="btn"
            @click="goNext"
        >
            立即聊天
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { PAGE_TYPE } from "../../config";

export default {
    name: 'MatchModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        isMale:{
            type: Boolean,
        },
        avatar:{
            type: String,
            required: true
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                        this.$report(62, "测试报告页-app弹窗曝光");
                    } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                        this.$report(63, "测试报告页-引导小程序弹窗曝光");
                    }
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$emit('input', false);
        },
        goNext(){
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.$emit('go-download');
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.$emit('go-mini');
            }
        },
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.match-modal {
    width: 558px;
    height: 680px;
    background-color: transparent;
    @include flex-center(column, flex-start, center);
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668569392958_9446_t.png");

    .title {
        margin-top: 60px;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
        text-align: center;
    }

    .subtitle{
        margin-top: 16px;
        font-size: 28px;
        color: #FFFFFF;
        text-align: center;
        line-height: 36px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.50);
        opacity: 0.7;
    }

    .avatar{
        margin-top: 44px;
        position: relative;
        width: 310px;
        height: 168px;
        &-female{
            padding: 10px;
            position: absolute;
            left: 0;
            top: 0;
            width: 168px;
            height: 168px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668569411476_195388_t.png");

            > div{
                width: 148px;
                height: 148px;
                border-radius: 50%;
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
        }

        &-male{
            padding: 10px;
            position: absolute;
            right: 0;
            top: 0;
            width: 168px;
            height: 168px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668569038506_10602_t.png");

            > div{
                width: 148px;
                height: 148px;
                border-radius: 50%;
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
        }

        &-love{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            width: 72px;
            height: 68px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668579291473_71867_t.png");

        }

    }

    .content{
        margin-top: 48px;
        opacity: 0.7;
        font-size: 28px;
        color: #FFFFFF;
        text-align: center;
        line-height: 36px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.50);
    }

    .btn{
        margin-top: 56px;
        width: 462px;
        height: 88px;
        background-image: linear-gradient(104deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 81%);
        box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
        border-radius: 44px;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        line-height: 88px;
    }
}
</style>
