<template>
    <van-popup
        class="invite-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        closeable
    >
        <h1 class="invite-modal__title">
            配对成功
        </h1>

        <div class="invite-modal__desc">
            <img :src="currentModel && currentModel.mainImg">
            <div>
                <p>{{ currentModel && currentModel.name }}<span></span></p>
                <p>Ta已关注你的资料，并给你留言，<span>请尽快前往APP查看</span></p>
            </div>
        </div>

        <div class="invite-modal__word">
            请前往应用市场搜索下载珍爱APP
        </div>

        <div
            class="invite-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

export default {
    name: 'InviteModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        currentModel: {
            type: Object,
            default: () => {}
        }
    },

    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.invite-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 26px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        img {
            width: 128px;
            height: 128px;
            border-radius: 24px;
        }
        display: flex;
        border: 2px solid #F4F4F4;
        border-radius: 24px;
        margin-bottom: 40px;
        padding: 20px;
        width: 506px;
        >div {
            font-size: 26px;
            margin-left: 16px;
            >p:nth-child(1) {
                font-weight: 500;
                font-size: 32px;
                color: #26273C;
                position: relative;
                span {
                    display: inline-block;
                    width: 146px;
                    height: 42px;
                    background: url('https://photo.zastatic.com/images/common-cms/it/20220701/1656667013009_162025_t.png') no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    top: -2px;
                }
            }
            >p:nth-child(2) {
                color: #6C6D75;
                margin-top: 20px;
                line-height: 38px;
                span {
                    color: #2CCEFF;
                }
            }
        }
    }

    &__word {
        font-weight: 500;
        font-size: 28px;
        color: #26273C;
        margin: 0 auto 48px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
