<template>
    <div>
        <!-- 表单 -->
        <div
            class="form-wrapper"
            :class="styleConfig.theme"
        >
            <div
                v-for="(item, index) in formItems"
                :key="index"
                :class="
                    item.type === 'phone'
                        ? 'form-item--phone'
                        : 'form-item--normal'
                "
                @click="openSelector(item)"
            >
                <!-- label -->
                <div
                    :class="
                        item.type === 'phone'
                            ? 'form-item--phone__label'
                            : 'form-item--normal__label'
                    "
                    :style="{color:styleConfig.labelColor}"
                    v-if="showLabel"
                >
                    {{ item.label }}
                </div>

                <!-- value：性别 -->
                <div
                    v-if="item.type === 'gender'"
                    class="form-item--gender__value"
                >
                    <button
                        v-for="(optionItem, optionIndex) in formItems.find(
                            item => item.type === 'gender'
                        ).options"
                        :key="optionIndex"
                        class="form-item--gender__value__item"
                        @click="setGender(optionItem)"
                        :class="
                            optionItem.key === registerForm.gender
                                ? 'form-item--gender__value__item--selected'
                                : ''
                        "
                        :style="
                            optionItem.key === registerForm.gender
                                ? {
                                    background:styleConfig.color,
                                    color:styleConfig.fontColor,

                                }: {
                                    background:styleConfig.fontColor,
                                    color:styleConfig.color,
                                    border:`2px solid ${styleConfig.color}`
                                }
                        "
                    >
                        {{ optionItem.text }}
                    </button>
                </div>

                <!-- value：手机号 -->
                <div
                    v-else-if="item.type === 'phone'"
                    class="form-item--phone__value__wrapper"
                >
                    <div
                        class="form-item--phone__value"
                        :style="{background:styleConfig.phoneBgColor}"
                    >
                        <input
                            class="form-item--phone__value__input"
                            ref="refPhoneInput"
                            type="tel"
                            :value="registerForm.phone"
                            @input="inputPhone"
                            placeholder="请输入11位手机号"
                            maxlength="13"
                            :style="{color:styleConfig.valueColor}"
                        />
                    </div>
                    <div
                        class="form-item--phone__clear"
                        @click="clearPhone"
                    ></div>
                </div>

                <!-- value：工作地、出生年份、学历、婚况、月收入 -->
                <div
                    v-else
                    class="form-item--normal__value"
                    :class="registerForm[item.type] ? 'filled':''"
                >
                    <span
                        v-if="registerForm[item.type]"
                        :style="{color:styleConfig.valueColor}"
                    >
                        {{ keyToValue(item.type, registerForm[item.type]) }}
                    </span>
                    <span
                        v-else
                        :style="{color:styleConfig.color}"
                    >
                        <template v-if="styleConfig.theme === 'orange'">{{ item.placeholder }}</template>
                        <template v-if="!styleConfig.theme">待完善</template>
                    </span>
                </div>
            </div>
        </div>

        <!-- 上拉选择器 -->
        <common-form-selector
            v-model="showSelector"
            :page-type="pageType"
            :style-config="styleConfig"
            :selector-type="selectorType"
            :selector-param="selectorParam"
            @autoloop="handleAutoloop"
            :isNeedSubTitle="isNeedSubTitle"
        />
    </div>
</template>

<script>
import * as dict from "@/common/config/register-dictionary";
import { storage as Storage } from "@/common/utils/storage";
import CommonFormSelector from "./components/CommonFormSelector.vue";
import { formatPhone } from "@/common/utils/tools.js";
import {setLocalRegisterForm, keyToValue} from "@/common/business/utils/localRegisterForm.js";
import { reportKibana } from "@/common/utils/report";

export default {
    name: "CommonForm",
    components: {
        CommonFormSelector
    },
    props: {
        pageType: {
            type: String,
            default: "导量H5大表单翻牌"
        },
        filterConfig: {
            type: Array,
            default() {
                return [
                    "gender",
                    "workCity",
                    "birthday",
                    "education",
                    "marriage",
                    "salary",
                    "phone"
                ];
            }
        },
        styleConfig: {
            type: Object,
            default() {
                return {
                    theme: "",
                    color: "#787bff", // 主色
                    fontColor: "#fff", // 表单字体色
                    selectorColor: "#26273C",// 选择器主色
                    selectorFontColor: "#fff", // 选择器字体色
                    labelColor: "#26273c", // label颜色
                    valueColor: "#26273c", // value默认颜色
                    phoneBgColor: "rgba($color: #26273c, $alpha: 0.05)", //input背景色
                };
            }
        },
        isNeedSubTitle: {
            type: Boolean,
            default: true
        },
        showLabel: {
            type: Boolean,
            default: true,
        }
    },
    data() {
        return {
            registerForm:{
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            originformItems: [
                {
                    type: "gender",
                    label: "您的性别",
                    options: dict.genderV2,
                    index: 0
                },
                {
                    type: "workCity",
                    label: "您的工作地",
                    options: Z.workCity,
                    placeholder: "请选择省",
                    index: 1
                },
                {
                    type: "birthday",
                    label: "您的出生年份",
                    options: dict.birthday,
                    placeholder: "请选择出生年份",
                    index: 2
                },
                {
                    type: "education",
                    label: "您的学历",
                    options: dict.education,
                    placeholder: "请选择您的学历",
                    index: 3
                },
                {
                    type: "marriage",
                    label: "您的婚姻状况",
                    options: dict.marriage,
                    placeholder: "请选择您的婚姻状况",
                    index: 4
                },
                {
                    type: "salary",
                    label: "您的月收入",
                    options: dict.salary,
                    placeholder: "请选择您的月收入",
                    index: 5
                },
                {
                    type: "phone",
                    label: "您的手机号",
                    index: 6
                }
            ],
            formItems: [],
            showSelector: false,
            selectorType: "",
            selectorParam: {},
            // 手机号输入款使用
            isReport: false
        };
    },
    mounted() {
        this.initRegisterForm();
        this.initFormItems();
        this.initFormTheme();
    },
    methods: {
        keyToValue,
        openSelector(formItem) {
            if (["gender", "phone"].includes(formItem.type)) {
                return;
            } else if (["workCity", "birthday"].includes(formItem.type)) {
                this.selectorType = "selectorPicker";
            } else if (
                ["marriage", "education", "salary"].includes(formItem.type)
            ) {
                this.selectorType = "selectorBoard";
            }

            this.selectorParam = formItem;
            this.showSelector = true;
        },
        handleAutoloop() {
            // 构造需要autoloop的注册项数组（即剔除 性别项、手机号项）
            let autoloopItems = this.formItems.filter(item => {
                return item.type !== "gender" && item.type !== "phone";
            });

            // 获取当前位置
            let currentIndex = autoloopItems.findIndex(item => {
                return item.index === this.selectorParam.index;
            });

            // 记录是否已经全部填写
            let hasDone = true,
                nextItem = null;

            for (let i = 0; i < autoloopItems.length; i++) {
                // 从当前位置的下一个开始正循环遍历，寻找下一个需要调用selector组件的未填项
                currentIndex =
                    currentIndex === autoloopItems.length - 1
                        ? 0
                        : currentIndex + 1;

                if (this.registerForm[autoloopItems[currentIndex].type]){
                    // 已填写则跳过
                    continue;
                } else {
                    // 未填写则存储要打开的注册项信息
                    nextItem = autoloopItems[currentIndex];
                    hasDone = false;
                    break;
                }
            }
            // 如果需要调用selectPanel组件的注册项都填写，则关闭selector组件
            if (hasDone) {
                return this.showSelector = false;
            }
            // 否则自动跳转至下一个需要调用selector组件的注册项
            this.openSelector(nextItem);
        },
        setGender(genderItem) {
            this.setRegisterForm({
                key: "gender",
                value: genderItem.key
            });
        },
        formatPhone,
        inputPhone(e) {
            if (e.target.value.length === 13 && !this.isReport) {
                reportKibana(this.pageType, 49, '大表单注册页-输入11位手机号');
                this.isReport = true;
            }

            e.target.value = e.target.value.replace(/[^(\d|\s)]/g, "");
            this.setRegisterForm({
                key: "phone",
                value: this.formatPhone(e),
                isMark: false
            });
        },
        clearPhone() {
            this.setRegisterForm({
                key: "phone",
                value: "",
                isMark: false
            });

        },
        setRegisterForm(target) {
            // 同步至缓存
            const cachedRegisterForm = setLocalRegisterForm(target, this.pageType);
            // 并更新data
            Object.assign(
                this.registerForm,
                cachedRegisterForm
            );
        },
        initRegisterForm() {
            // 处理回显
            const cachedRegisterForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            );
            if (cachedRegisterForm) {
                Object.assign(this.registerForm, cachedRegisterForm);
            }

            window.addEventListener("setItemEvent", (e) => {
                if(e.key === `cachedRegisterForm-${this.pageType}`){
                    let oldValue = JSON.stringify(cachedRegisterForm);
                    if(oldValue === e.newValue){
                        return;
                    }
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        },
        initFormItems() {
            // 筛选显示项
            this.formItems = this.originformItems.filter(item => {
                return this.filterConfig.includes(item.type);
            });
        },
        initFormTheme() {
            if(this.styleConfig.theme){
                return import(`./styles/form-theme/${this.styleConfig.theme}.scss`);
            }
            import(`./styles/form-theme/default.scss`);
        }
    },
};
</script>
