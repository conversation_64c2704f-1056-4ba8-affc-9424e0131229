import VueLoading from './loading'
let queue = []

const defaultOptions = {
    loadingText: '加载中...',
    isLoading: false,
    timer: null,
    delay: 0,
    delayTimer: null,
    overrideStyle: {}
}

function createInstance() {
    if (!queue.length) {
        const loading = new (Vue.extend(VueLoading))({
            el: document.createElement('div')
        })
        document.body.appendChild(loading.$el)
        queue.push(loading)
    }
    return queue[queue.length - 1]
}


export default {
    name: 'loading',
    install() {
        Vue.prototype.$loading = function (opts = {}) {
            if (typeof opts === 'boolean') {
                opts = {
                    isLoading: opts
                }
            }
            const loading = createInstance()
            const finalOpts = Object.assign(defaultOptions, opts, {
                clear() {
                    this.isLoading = false
                    if (this.delay) {
                        clearTimeout(loading.delayTimer)
                    }
                }
            })

            function init() {
                Object.assign(loading, finalOpts)
                clearTimeout(loading.timer)

                // 如果有延迟，则关闭timer
                if (loading.delay) {
                    clearTimeout(loading.delayTimer)
                }

                if (loading.duration > 0) {
                    loading.timer = setTimeout(() => {
                        loading.clear()
                    }, loading.duration)
                }
            }

            // 如果有延迟时间，则延迟后再展示
            // 中途调了关闭，则取消timer
            if (finalOpts.delay) {
                loading.delayTimer = setTimeout(init, finalOpts.delay)
            } else {
                init()
            }

            return loading
        }

        Vue.use(VueLoading)

    }
}
