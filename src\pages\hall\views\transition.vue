<template>
    <love-page>
        <div class="transition">
            <div id="svgaPlanet" class="transition-bg"></div>
            <div class="transition-title">系统匹配中…</div>
            <div class="transition-subtitle">
                已有<span>{{ number }}</span>位{{registerForm.gender === 0 ? "女生选择了你的约会" : "男生想邀请你一起约会"}}
            </div>
        </div>
    </love-page>
</template>

<script>
import { mapState } from "vuex";
import { createPage } from "@/common/framework";

export default createPage({
    name: "Transition",
    visitReport: {
        accessPoint: 6,
        accessPointDesc: "中转页访问"
    },
    computed: {
        ...mapState(["isDefaultCmsConfig", "cmsConfig", "registerForm"])
    },
    data() {
        return {
            number: 1,
            timer: null,
            this: null
        };
    },
    methods: {
        setSVGA() {
            const player = new SVGA.Player("#svgaPlanet");
            const parser = new SVGA.Parser("#svgaPlanet");

            parser.load(require("../assets/svga-planet.svga"), item => {
                player.setVideoItem(item);
                player.startAnimation();
            });
        },
        countNumber() {
            this.timer = setInterval(() => {
                if (this.number === 8) {
                    clearInterval(this.timer)
                    this.goNext();
                    return;
                }
                this.number++;
            }, 350);
        },
        goNext(){
            this.delay = setTimeout(() => {
                clearTimeout(this.delay);
                this.$router.push({
                    path: "/form"
                });
            },500);
        }
    },
    mounted() {
        this.setSVGA();
        this.countNumber();
    },
    destroyed() {
        clearTimeout(this.delay);
        clearTimeout(this.timer);
    }
});
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.transition {
    &-bg {
        width: 750px;
        height: 1334px;
        overflow: hidden;
    }

    &-title {
        position: absolute;
        top: 732px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 32px;
        color: #fff;
        text-align: center;
    }

    &-subtitle {
        position: absolute;
        top: 800px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 26px;
        text-align: center;
        color: #fff;

        span {
            color: #ffb900;
        }
    }
}
</style>
