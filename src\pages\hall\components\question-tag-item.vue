<template>
    <z-image
        @click="$emit('click',$event)"
        :border-radius="42"
        :height="80"
        :src="getTagImg(item).src"
        :width="getTagImg(item).width"
        img-size="cover"
        class="tag-item"
        >
        <z-image
            class="tag-item__icon"
            :width="20"
            :height="20"
            :src="item.check ? require('../assets/images/icon-check.png') : require('../assets/images/icon-add.png')"/>
        <span>{{ item.text }}</span>
    </z-image>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
    name: "question-tag-item",
    props: {
        item:{
            type: Object,
            required: true
        }
    },
    computed: {
        ...mapState([
            'cmsConfig',
            'requirement',
            'registerForm'
        ]),
        text(){
            if(this.requirement.driftTag){
                return this.requirement.driftTag.text;
            }else{
                return ''
            }
        }
    },
    methods: {
        getTagImg({text}){
            if (text.length < 4){
                return {
                    src:require('../assets/images/tag-small.png'),
                    width:174
                };
            } else if (text.length > 5) {
                return {
                    src:require('../assets/images/tag-big.png'),
                    width:348
                };
            } else{
                return {
                    src:require('../assets/images/tag-middle.png'),
                    width:234
                };
            }   
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.tag {
    &-item {
        @include flex-center();
        font-weight: 500;
        font-size: 28px;
        margin-right: 16px;

        &__icon {
            top: -1px;
            margin-right: 8px;
        }
    }
}
</style>
