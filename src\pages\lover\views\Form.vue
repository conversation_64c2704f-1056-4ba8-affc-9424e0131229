<template>
    <div class="form-wrapper">
        <div class="title">
            完善您的信息<br />
            即可遇见心动的Ta
        </div>
        <common-form
            :page-type="cmsConfig.planName"
            class="form"
            :filter-config="['birthday', 'marriage', 'phone']"
            :style-config="{
                color: '#4A3BC0',
                selectorColor: '#4A3BC0',
                selectorFontColor: '#FFFFFF',
                labelColor: '#6C6D75',
                valueColor: '#26273C'
            }"
            :is-need-sub-title="false"
        />
        <common-submit
            :page-type="cmsConfig.planName"
            class="submit"
            :is-need-protocol="false"
            :style-config="{
                modalConfig: {
                    confirmButtonColor: '#FFFFFF',
                    confirmButtonBgColor: 'linear-gradient(154deg, #7566EB 0%, #4A3BC0 100%)',
                    cancleButtonColor: '#6C6D75',
                }
            }"
            :handle-after-regisiter="handleJump"
            :handle-login="handleJump"
        >
            <common-button
                :config="{width: 610, height: 116, fontSize: 36, des: '一键领取'}"
                :class="{'submit-disabled': !finished}"
            />
        </common-submit>
    </div>
</template>

<script>
import CommonForm from "@/common/business/CommonForm";
import CommonSubmit from "@/common/business/CommonSubmit";
import CommonButton from "../components/CommonButton";
import { _saveIdealLoverDressed } from "../api";
import { session, storage as Storage } from "@/common/utils/storage.js";
import { getMiniPathV2 } from "@/common/business/utils/wecom";
import { pageTypeChnMap } from '@/common/config/register-dictionary';

export default {
    name: "Form",
    components: {
        CommonForm,
        CommonSubmit,
        CommonButton
    },
    inject: ["cmsConfig"],
    data() {
        return {
            registerForm: Storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            ) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            }
        };
    },
    computed: {
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            return this.allFillIn && this.validatedPhone;
        }
    },

    mounted() {
        this.handleWatchForm();
        this.$report(13, "留资页-访问");
    },
    methods: {
        async handleJump() {
            const data = {
                stature: session.getItem("Figure"),
                dressed: session.getItem("Ootd"),
                memberId: session.getItem("reg_memberid")
            };

            try {
                const result = await _saveIdealLoverDressed(data);
                if (result.isError) {
                    this.$toast(result.errorMessage);
                }
            } catch(e) {
                //
            }

            this.judgeJumpWay();
        },

        async judgeJumpWay() {
            if (["定制恋人（导小程序）", "定制恋人新UI(小程序)"].includes(this.cmsConfig.planName)) {
                const miniPath = await getMiniPathV2(
                    "pages/idealLover/idealLover", {
                        ext30: pageTypeChnMap[this.cmsConfig.planName],
                        pageType: pageTypeChnMap[this.cmsConfig.planName],
                    }
                );
                window.location.href = miniPath;
            } else {
                this.$router.push("/recom");
            }
        },

        handleWatchForm() {
            window.addEventListener("setItemEvent", e => {
                if (e.key === `cachedRegisterForm-${this.cmsConfig.planName}`) {
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.form-wrapper {
    position: relative;
    z-index: 1;
    padding-top: 56px;
    min-height: 100vh;
    background: linear-gradient(180deg, #f6f6fb 40%, #d4d1ff);
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        bottom: 0;
        width: 750px;
        height: 550px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220905/1662349717190_46242_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .title {
        position: relative;
        text-align: center;
        line-height: 84px;
        font-size: 56px;
        font-weight: 500;
        &::after {
            position: absolute;
            right: 24px;
            top: 2px;
            content: "";
            width: 56px;
            height: 151px;
            @include set-img(
                "https://photo.zastatic.com/images/common-cms/it/20220902/1662086128700_651928_t.png"
            );
        }
    }
    .form {
        margin: 24px auto 0px;
        width: 610px;
    }
    .submit {
        margin-top: 100px;
        &-disabled {
            opacity: 0.4;
        }
    }
}
</style>
