<template>
    <div class="marriage">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}
        </div>
        <div class="marriage-info">
            <div
                class="marriage-info-btn"
                :class="{ active: curMarriage === option.key }"
                v-for="option in list.options"
                @click="goNext(option.key)"
            >
                {{ option.text }}
            </div>
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    inject: ["cmsConfig"],
    data() {
        return {
            curMarriage: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) && Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).marriage || ''
        };
    },
    mounted() {
        this.$report(8, '婚况页访问');
    },
    methods: {
        goNext(val) {
            this.curMarriage = val;
            const params = {
                key: "marriage",
                value: val
            };
            this.$report(8, '婚况页-具体婚况点击');
            setLocalRegisterForm(params, this.cmsConfig.planName);
            setTimeout(() => {
                this.$emit("val-updated", val);
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
.marriage {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    &-info {
        margin: 50px 48px 0;
        &-btn {
            width: 100%;
            height: 110px;
            margin-bottom: 20px;
            color: #26273c;
            font-size: 32px;
            line-height: 110px;
            text-align: center;
            border-radius: 80px;
            background: #fff;
            &.active {
                color: #fff;
                background: #5368f0;
            }
        }
    }
}
</style>
