import { getOcpxConf } from '../js/api.js';
import { ocpxApi } from './ocpx_api'
import { ocpxJS } from './ocpx_js';
import { IS_SPRING_CHANNEL } from '../js/const.js';

var ocpxConfig; // ocpx配置条件
var conditionList; // 特定条件 数组
var reportFn; // 上报
var reportHadMap = {}; // 已经上报
var CHANNEL_ID = Z.getParam('channelId');
var SUB_CHANNEL_ID = Z.getParam('subChannelId');

// 限制条件，为true则不走ocpx逻辑
function limit() {
    return IS_SPRING_CHANNEL; // 2020春晚注册流程不走此步骤
}

// 填充数字，1, 10 => [1,2,3,4,5,6,7,8,9,10]
function fillNumber(min, max){
    var res = [];
    if(typeof min!=='number' || min!==min || typeof max!=='number' || max!==max){
        return res;
    }
    if(min>max){
        var tmp = min
        min = max
        max = tmp
    }
    for(var i=min; i<=max; i++){
        res.push(i)
    }
    return res;
}

// 处理条件json字符串，白名单过滤，对字段数据转换，以方便后续判断
function parseConditions(jsonConditionString){
    var conditions = null;
    try{
        conditions = JSON.parse(jsonConditionString)
    }catch(e){
        Z.tj.error('ocpx解析规则json错误' + e + jsonConditionString, 'ocpx.js/parseConditions', 0);
        return null;
    }
    if(!conditions || !conditions.length){
        return null;
    }
    var res = [];
    // 合法Key
    var validKeys = ['gender', 'year', 'education', 'marriage', 'salary', 'submitPhone', 'msgValid', 'convertId', 'workCity'];
    for(var i=0; i<conditions.length; i++){
        var cond = conditions[i];
        var tmp = null;
        for(var key in cond){
            if(validKeys.indexOf(key)===-1){
                break;
            }
            // 转换为数组，后续统一通过 数组的 indexOf 判断
            // gender在cms试单选， 0 | 1,
            // submitPhone, msgValid在cms是radio,   true | false
            // 其他字段都为数组，如果cms有改动，这里也需要对应变化
            if(['gender', 'submitPhone', 'msgValid'].indexOf(key)!==-1){
                tmp = tmp ? tmp : {};
                tmp[key] = [cond[key]]
                // 校验是数组格式才合法
            }else if(Object.prototype.toString.call(cond[key]).toLocaleLowerCase().indexOf('array')!==-1){
                tmp = tmp ? tmp : {};
                // 转换为数组，后续统一通过 数组的 indexOf 判断
                // year: [1990, 1994] 转为 ：   [1990,1991,1992,1993,1994]
                if(key==='year'){
                    tmp[key] = fillNumber(cond[key][0], cond[key][1])
                }else{
                    tmp[key] = cond[key]
                }
                // 转换id特殊处理，非回传条件，是回传参数
            }else if(key==='convertId'){
                tmp = tmp ? tmp : {};
                tmp[key] = cond[key]
            }
        }
        if(tmp){
            res.push(tmp)
        }
    }
    if(res.length){
        return res;
    }
    return null;
}

/**
 * 初始化，获得ocpx配置、设置回传方式
 */
function ocpxInit() {
    if (limit()) return;
    getOcpxConf(function (config) {
        if (!config) return;
        conditionList = parseConditions(config.conditions);
        if (!conditionList) return;
        ocpxConfig = config;

        if (document.referrer && document.referrer.indexOf('a.zhenai.com/abt/') !== -1) {
            config.adUrl = document.referrer;
        } else if (!config.adUrl) {
            config.adUrl = 'https://a.zhenai.com/abt/link.do?channelId='+ CHANNEL_ID +'&subChannelId='+ SUB_CHANNEL_ID +'&pageKey=za_m_landing&za_ttt='+ Z.getParam('za_ttt');
        }
        if (config.returnType === 0) {
            // JS回传
            reportFn = ocpxJS(config);
        } else {
            // API回传
            reportFn = ocpxApi(config);
        }
    })
}

/**
 * 校验是否符合上报条件
 * @param oSelectedData 选择的条件上报
 * @param conditionListItem  后台配置的条件，单个规则
 * @returns {string}
 */
function verifyCondition(oSelectedData, conditionListItem) {
    var reportResult = true; // 是否符合上报条件，默认是
    var otherKeys = ['convertId']; //后台设置非用户选择的条件值
    var appiontCondition = JSON.parse(JSON.stringify(conditionListItem));
    otherKeys.forEach(function(item){
        delete appiontCondition[item]
    })
    for (var key in appiontCondition) {
        var conditionValue = appiontCondition[key]; // 后台设置的条件值，conditionValue是一个数组
        var selectValue = oSelectedData[key]; // 匹配前端选择的值
        if (typeof selectValue === 'undefined') {
            reportResult = false;
            break;
        }
        // 把数据都转成数字
        if(['msgValid', 'submitPhone'].indexOf(key) === -1){
            selectValue = key == 'workCity' ? selectValue.toString() : +selectValue
        }
        if(conditionValue.indexOf(selectValue) === -1 ){
            reportResult = false; // 不符合上报
            break
        }
    }

    return reportResult;
}

/**
 * 上报回传
 * @param oSelectedData
 */
function ocpxReport (oSelectedData) {
    try {
        if (!conditionList || !conditionList.length) return null;
        for(var i = 0; i < conditionList.length; i += 1) { // 多个规则组
            var conditionListItem = conditionList[i];
            var verifyResult = verifyCondition(oSelectedData, conditionListItem); // 规则中条件完全匹配
            if (
                verifyResult
                && !reportHadMap[i]
                && (typeof reportFn === 'function')
            ) {
                var appointConvertId = conditionListItem.hasOwnProperty('convertId') ? conditionListItem['convertId'] : '';
                var reportSuccess = reportFn(appointConvertId);
                if (reportSuccess) {
                    reportHadMap[i] = true;
                }
            }
        }
    } catch (e) {
        Z.tj.error('ocpx上报错误' + e, 'ocpx/report', 0);
    }
}

try {
    ocpxInit();
} catch (e) {
    Z.tj.error('ocpx初始化错误' + e, 'ocpx/init', 0);
}

export default ocpxReport;
