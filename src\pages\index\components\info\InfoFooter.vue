<template>
    <div class="info-footer-wrapper">
        <div class="footer-tips">
            请尽快下载APP去跟Ta约会吧~
        </div>
        <div
            class="footer-button"
            :style="cmsConfig.buttonColor"
            @click="openModal('modalDownload', {})"
        >
            {{ cmsConfig.downloadButtonText }}
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

export default {
    components: {},
    data() {
        return {};
    },
    computed: {
        ...mapState(["cmsConfig", "overwriteRegistrationSwitch"])
    },
    created() {},
    mounted() {
    // this.jump()
    },
    methods: {
        openModal(modalType, modalParam) {
            this.$report(6, "翻牌下载页-主按钮点击", {
                ext16: 1, // 1 投放版 2 达人版
                ext18: this.$route.path === "/info" ? 1 : 2 // 1 无盲盒 2 有盲盒
            });

            this.$emit("open-modal", modalType, modalParam);
        }
    }
};
</script>

<style lang="scss" scoped>
.info-footer-wrapper {
  margin-top: 50px;
}

.footer-tips {
  font-size: 30px;
  font-weight: 700;
  color: #ffffff;
  line-height: 38px;
  text-align: center;
}

.footer-button {
  margin: 40px auto 0;
  width: 686px;
  height: 110px;
  background: linear-gradient(0deg, #ffc334, #ff8f02);
  border-radius: 55px;

  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  line-height: 110px;
  text-align: center;
  text-shadow: 0px 5px 13px rgba(255, 125, 199, 0.7);
  box-shadow: inset 0 0 16px 2px #ffffff;
}
</style>
