<template>
    <div
        class="select-wrapper"
        ref="refWrapper"
    >
        <div
            class="select-mask"
            @click="closeSelect"
        ></div>
        
        <div class="select-panel">
            <div class="panel__tips">
                完善资料，立即匹配对象
            </div>
            <div class="panel__title">
                {{ selectParam.label }}
            </div>

            <!-- 滑动组件：工作地、出生年份-->
            <van-picker
                v-if="selectType === 'selectSlide'"
                ref="refPicker"
                show-toolbar
                :columns="columns"
                @confirm="onConfirm"
                @cancel="onCancel"
                @change="onChange"
                toolbar-position="bottom"
                item-height="1.25rem"
                visible-item-count="5"
                confirm-button-text="提交"
                cancel-button-text="取消"
            />

            <!-- 平铺组件：婚姻状况、学历、收入 -->
            <template v-if="selectType === 'selectBoard'">
                <!-- <div class="panel__board--blur top" v-if="selectParam.selectArr.length>5"></div> -->
                <div
                    class="panel__board"
                    ref="refBoard"
                >
                    <div 
                        v-for="(item,index) in selectParam.selectArr"
                        :key="index"
                        @click="onBoardClick($event,item)"
                        class="panel__board__item"
                        :class="item.key === registerInfo[currentIndex]?'panel__board__item--selected':''"
                    >   
                        {{ item.text }}
                    </div>
                </div>
                <!-- <div class="panel__board--blur bottom" v-if="selectParam.selectArr.length>5"></div> -->
                <button
                    class="panel__board__cancel"
                    @click="closeSelect"
                >
                    取消
                </button>
            </template>
        </div>
    </div>
</template>

<script>

import { Picker } from 'vant';
import {mapState,mapMutations} from 'vuex';
import oUserSelect from '@/common/ocpx/huichuan.js';
import borderStopScroll from '@/common/utils/borderStopScroll.js';

export default {
    props:{
        selectType:{
            type:String,
            required: true,
        },
        selectParam:{
            type:Object,
        }
    },
    components:{
        [Picker.name]:Picker
    },
    data() {
        return {
            columns: [],
            loopLock: false
        };
    },
    computed:{
        ...mapState([
            'formInfo',
            'registerInfo',
        ]),
        currentIndex(){
            return this.selectParam.index;
        }
    },
    created(){
        
    },
    mounted(){
        this.columns = this.selectParam.selectArr;

        this.$nextTick(() => {
            this.setDefault();
        });

        borderStopScroll({
            wrapEle: this.$refs.refWrapper
        });
        
        if(['education','salary'].includes(this.selectParam.index)){
            // 处理滚动穿透
            borderStopScroll({
                wrapEle: this.$refs.refBoard
            });
        }
    },
    watch:{
        selectParam(){
            this.columns = this.selectParam.selectArr;

            // 设置缺省,视觉中心
            this.$nextTick(() => {
                this.setDefault();
            });

            this.$nextTick(()=>{
                if(['education','salary'].includes(this.selectParam.index)){
                    borderStopScroll({
                        wrapEle: this.$refs.refBoard
                    });
                }
            });
        }
    },
    methods:{
        ...mapMutations([
            "setFormInfo",
            "setRegisterInfo",
            "setInfo"
        ]),
        setDefault(){
            if(this.selectParam.index === "workCity"){
                let arr = localStorage.getItem("defaultWorkCity");
                if(arr){
                    this.$refs.refPicker.setIndexes(JSON.parse(arr)); 
                } else {
                    this.$refs.refPicker.setIndexes([2,5,0]); // 广东 肇庆 端州区
                }
            } else if(this.selectParam.index === "birthday"){
                let index = localStorage.getItem("defaultBirthday");
                if(index){
                    this.$refs.refPicker.setIndexes([JSON.parse(index)]); 
                } else {
                    this.$refs.refPicker.setIndexes([ 38 ]);
                }
            } else if(this.selectParam.index === "education"){
                this.$refs.refBoard.scrollTo(0,200);
            } else if(this.selectParam.index === "salary"){
                //可能需要根据屏幕尺寸调整
                this.$refs.refBoard.scrollTo(0,52);
            }
        },
        closeSelect(){
            this.$emit("close-select");
        },
        onBoardClick(event,item){
            // 设置数据
            this.setFormInfo({[this.selectParam.index]:item.text});
            this.setRegisterInfo({[this.selectParam.index]:item.key});

            // 处理回传
            oUserSelect.mark({[this.selectParam.index]:item.key});

            if(this.loopLock){
                return;
            }

            this.loopLock = true;
            // 获取未填项，自动跳转
            setTimeout(()=>{
                this.loopLock = false;
                this.autoLoop();
            },300);
            
        },
        autoLoop(){
            // 构造需要select组件的注册项数组（剔除 性别项）
            let needSelectArr = this.formInfo.slice(1);

            // 获取当前位置
            let position = needSelectArr.findIndex((item,index)=>{
                return item.index === this.selectParam.index;
            });

            // 记录是否已经全部填写
            let hasDone = true,
                nextItem = null;

            for(let i = 0; i<needSelectArr.length; i++){
                // 从当前位置的下一个开始正循环遍历，寻找下一个需要调用selectPanel组件的未填项
                position =  position === (needSelectArr.length-1) ? 0 : (position + 1);

                if(needSelectArr[position].value){
                    // 已填写则跳过
                    continue;
                } else {
                    // 未填写则存储要打开的注册项信息
                    nextItem = needSelectArr[position];
                    hasDone = false;
                    break;
                }
            }


            // 如果需要调用selectPanel组件的注册项都填写，则关闭selectPanel组件
            if(hasDone){
                return this.closeSelect();
            }

            // 否则自动跳转至下一个需要调用selectPanel组件的注册项
            this.$parent.openSelect(nextItem);

            
        },
        
        onConfirm(value, index) {
            let picker = this.$refs.refPicker,
                key = '',
                text = '';

            // 
            if(this.selectParam.index === "birthday"){
                // 生日picker
                let year = picker.getColumnValue(0).key;
                key = new Date(year + "/" + 1 + "/" + 1).getTime(); //实际传给后台的数据为 年/1/1 对应的毫秒值
                text = picker.getColumnValue(0).text;
                localStorage.setItem("defaultBirthday",JSON.stringify(index));  // 用于初始化select
            }else if(this.selectParam.index === "workCity"){
                // 工作地picker
                let currentCity = picker.getColumnValue(0).text;
                if(["北京","上海","重庆","天津"].includes(currentCity)){
                    key = picker.getColumnValue(1).key;
                    text = value.slice(0,2).join("/");
                } else {
                    key = picker.getColumnValue(2).key;
                    text = value.slice(0,3).join("/");
                }
                localStorage.setItem("defaultWorkCity",JSON.stringify(index));  // 用于初始化select
            }


            // 设置数据
            this.setFormInfo({[this.selectParam.index]:text});
            this.setRegisterInfo({[this.selectParam.index]:key});

            // 处理回传
            if(this.selectParam.index === "birthday"){
                oUserSelect.mark({
                    year: ""+picker.getColumnValue(0).key,
                    month: '1',
                    day: '1',
                });
            } else if(this.selectParam.index === "workCity") {
                oUserSelect.mark({
                    workCity: key
                });
            }

            if(this.loopLock){
                return;
            }

            this.loopLock = true;
            // 获取未填项，自动跳转
            setTimeout(()=>{
                this.loopLock = false;
                this.autoLoop();
            },300);
        },
        onChange(picker, value, index) {
        },
        onCancel() {
            this.closeSelect();
        }        
    },
    // beforeRouteUpdate(to, from, next) {
    //     console.log(111,to);
    //     console.log(222,from);
    //     next(true)
    // }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.select-wrapper{
    // position: fixed;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    
}

.select-mask{
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    // margin: auto;
    background: rgba($color: #26273C, $alpha: 0.6);
    z-index: 100;
}

.select-panel{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 750px;
    height: 998px;
    background: #FFFFFF;
    border-radius: 60px 60px 0px 0px;
    z-index: 999;
}

.panel__tips{
    margin-top: 60px;
    font-size: 29px;
    color: #92939D;
    text-align: center;
    line-height: 43px;
}

.panel__title{
    margin-top: 16px;
    font-size: 36px;
    font-weight: 700;
    color: #26273C;
    text-align: center;
    line-height: 54px;
}

.panel__board{
    padding: 4px;
    position: relative;
    @include set-flex(flex-start,center);
    flex-direction: column;
    margin-top: 87px;
    width: 750px;
    height: 575px;
    overflow: scroll;
    // border: 1px solid red;
}

.panel__board--blur{
    position: absolute;
    width: 560px;
    height: 0px;
    box-shadow: 0 0 40px 20px #ffffff;
    z-index: 3;
}

.top{
    top: 260px;
    left: 95px;
}

.bottom{
    bottom: 164px;
    left: 95px;
}

.panel__board__item{
    width: 560px;
    height: 88px;
    
    margin-top: 30px;
    flex-shrink: 0;
    border-radius: 55px;
    border: 2px solid #FC77A8;
    font-size: 32px;
    font-weight: 400;
    color: #FC77A8;
    text-align: center;
    line-height: 88px;
}

.panel__board__item:nth-child(1){
    margin-top: 0;
}

.panel__board__item--selected{
    color: #FFFFFF;
    background: #FC77A8;
}

.panel__board__cancel{
    display: block;
    margin: 53px auto 0;
    font-size: 32px;
    font-weight: 400;
    color: #FC77A8;
    text-align: center;
}

</style>

<style lang="scss">
/* 覆盖vant样式 */
.van-picker{
    margin-top: 77px;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}

.van-picker__columns{
    margin-top: -30px;
    // height: 430px;
}

.van-picker-column{
    // height: 475px;
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
    // background: yellow;
}

.van-picker-column__wrapper{

}

.van-picker-column__item{
    // height: 95px !important;
    
    font-size: 30px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-picker-column__item--selected{
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-hairline-unset--top-bottom{
    // height: 95px !important;
}

.van-picker__toolbar{
    margin-top: 60px;
    flex-direction: column;
    align-content: flex-start;
    
}

.van-picker__cancel{
    flex-shrink: 0;
    order: 1;
    width: 654px;
    height: 110px;
    // background: #767DFF;
    // border-radius: 55px;
    font-size: 32px;
    font-weight: 400;
    color: #FC77A8 ;
    line-height: 47px;
}

.van-picker__confirm{
    flex-shrink: 0;
    order: 0;
    margin: 0 auto;
    width: 654px;
    height: 110px;
    background: #FC77A8;
    border-radius: 55px;

    font-size: 32px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 47px;
}
</style>
