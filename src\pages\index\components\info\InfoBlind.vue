<template>
    <div class="info-blind-wrapper">
        <div v-for="(item, index) in blindArr" :key="index" class="blind__item">
            <!-- 头像 -->
            <div
                v-if="item.avatar"
                class="blind__item__img"
                :class="selectedId === index ? '' : 'blur'"
                :style="{ backgroundImage: `url(${item.avatar})` }"
            ></div>

            <!-- 动效 -->
            <div :id="'itemBlur' + index" class="blind__item__img--svga"></div>

            <!-- 按钮 -->
            <div class="blind__item__block" @click="selectBlind(item, index)">
                <button
                    class="blind__item__block__button"
                    :style="cmsConfig.buttonColor"
                    :class="
                        hasSelected && selectedId === index
                            ? 'blind__item__block__button--disabled'
                            : ''
                    "
                >
                    {{ cmsConfig.flopButtonText }}
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { _getSpecifyGenderRandomAvatar } from "../../js/api.js";

export default {
    props: {
        hasSelected: {
            type: Boolean
            // required: true,
        }
    },
    components: {},
    data() {
        const blindArr = [{}, {}, {}];
        return {
            blindArr,
            buttonText: "我要约Ta",
            selectedId: null
        };
    },
    computed: {
        ...mapState(["cmsConfig", "registerInfo", "showError"])
    },
    created() {
        this.getAvatars();
    },
    mounted() {},
    methods: {
        ...mapMutations(["setShowError"]),
        ...mapActions(["setModelInfo"]),
        async getAvatars() {
            // 计算用户的年龄
            let age =
                new Date().getFullYear() -
                new Date(this.registerInfo.birthday).getFullYear();

            if (age === "" || this.registerInfo.gender === "") {
                return;
            }

            let sendData = {
                sex: +this.registerInfo.gender === 0 ? 1 : 0, // 当前性别为男(0),则传女(1)
                limit: 3, // 随机头像个数为3
                age
            };
            let resData = await _getSpecifyGenderRandomAvatar(sendData);
            if (resData.isError) {
                if (resData.errorMessage === "当前网络异常") {
                    return this.setShowError({
                        showError: true,
                        showErrorCb: this.getAvatars
                    });
                }

                return this.$toast(resData.errorMessage);
            }

            // 请求数据正常，关闭引导刷新
            this.setShowError({
                showError: false,
                showErrorCb: () => {}
            });

            this.blindArr = resData.data.list;

            // 图片压缩
            this.blindArr.forEach(item => {
                item.avatar += "?imageMogr2/thumbnail/200x200";
            });
        },
        async selectBlind(item, index) {
            // 打桩
            this.$report(7, "翻牌下载页-翻牌按钮点击", {
                ext16: 1, // 1 投放版 2 达人版
                ext18: this.$route.path === "/info" ? 1 : 2 // 1 无盲盒 2 有盲盒,
            });

            if (typeof this.selectedId === "number") {
                // 已选
                if (this.selectedId === index) {
                    return;
                }

                // 下载APP的逻辑
                this.$emit("open-modal", "modalDownload", {});
                console.log("下载APP");
                return;
            }

            // 设置当前选中项
            this.selectedId = index;
            this.$emit("select-blind");
            let currentBlurId = "#itemBlur" + index;
            this.setSVGA(currentBlurId, item.avatar);

            // 请求详细信息
            await this.setModelInfo(item.id);
        },
        setSVGA(dom, avatar) {
            let player = new SVGA.Player(dom),
                parser = new SVGA.Parser(dom);

            parser.load(
                require("../../assets/imgs/svgaBroken.svga"),
                videoItem => {
                    player.setImage(avatar, "key");
                    player.loops = 1;
                    player.setVideoItem(videoItem);
                    player.startAnimation();
                    // player.startAnimationWithRange({location:0,length:5});
                }
            );
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";

.info-blind-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
}

.blind__item {
    position: relative;
    width: 200px;
    height: 278px;
    // background: #6200AE;
    border-radius: 16px 16px 16px 16px;
    overflow: hidden;
}

.blind__item__img {
    @include set-img("../../assets/imgs/img.png");
    width: 200px;
    height: 200px;
    border-radius: 16px 16px 0px 0px;
    // transition: filter 2s;
}

// .no-blur{
//     filter: blur(0);
// }

.blind__item__img--svga {
    position: absolute;
    left: 0;
    top: 0;
    width: 200px;
    height: 200px;
    z-index: 2;
}

.blind__item__block {
    position: absolute;
    top: 200px;
    width: 200px;
    height: 80px;
    background: #6200ae;
}

.blind__item__block__button {
    position: relative;
    display: block;
    margin: 15px auto 0;
    width: 150px;
    height: 50px;
    background: linear-gradient(0deg, #ffc334, #ff8f02);
    border-radius: 25px;

    font-size: 24px;
    font-weight: 700;
    color: #fdfdfd;
    line-height: 50px;
    text-shadow: 0px 0px 6px rgba(255, 150, 9, 0.6);
    box-shadow: inset 0 0 6px 1px #fff;
    z-index: 3;
}

.blind__item__block__button--disabled {
    background: linear-gradient(0deg, #979797, #888888) !important;
}
</style>
