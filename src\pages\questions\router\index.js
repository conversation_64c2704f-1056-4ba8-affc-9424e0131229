import VueRouter from "vue-router";
import index from "../views/index.vue";
import Question from "../views/question.vue";
import Login from "../views/login.vue";
import BlindInfo from "../views/BlindInfo.vue";

const router = new VueRouter({
    mode: "hash",
    routes: [
        {
            path: "/index",
            component: index
        },
        {
            path: "/ques/:id",
            component: Question
        },
        {
            path: "/login",
            component: Login
        },
        {
            path: "/blindinfo",
            component: BlindInfo
        },
        {
            path: "/",
            redirect: "/index"
        }
    ]
})

export default router;
