<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="common-download-guide-modal-v2"
    >
        <h1 class="common-download-guide-modal-v2__title">
            请先下载 [珍爱APP]
        </h1>

        <p class="common-download-guide-modal-v2__desc">
            方法1: 前往各大应用市场搜索“珍爱网”并下载APP <br>
            方法2: 复制下载链接前往浏览器打开，可直接下载APP
        </p>

        <div
            class="common-download-guide-modal-v2__btn"
            @click="closeModal"
        >
            复制下载链接
        </div>
    </van-popup>
</template>

<script>
import { Popup, Toast } from 'vant';

export default {
    name: 'CommonDownloadGuideModalV2',
    components: {
        VanPopup: Popup,
    },
    data() {
        return {
            value: false
        };
    },
    methods: {
        handleVisibleChange() {
            this.value = false;
        },

        closeModal() {
            const input = document.createElement("input"); //
            input.value = 'https://i.zhenai.com/m/portal/welcome.html'; // 设置复制内容
            document.body.appendChild(input); //
            input.select();
            document.execCommand("Copy");
            document.body.removeChild(input);
            Toast('复制链接成功，快去浏览器打开下载吧~');
            this.handleVisibleChange();
        },

    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.common-download-guide-modal-v2 {
    width: 588px;
    height: 458px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 600;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
    }

    &__btn {
        width: 462px;
        height: 98px;
        font-size: 32px;
        border-radius: 44px;
        background: #767dff;
        color: #ffffff;
        @include flex-center();
    }
}
</style>
