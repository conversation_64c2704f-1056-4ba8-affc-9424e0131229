<template>
    <div class="ques">
        <template v-for="(option, index) in options">
            <button
                class="ques_btn"
                :class="{ active: current === option }"
                :key="option"
                @click="goNext(option, index)"
                v-if="option"
            >
                {{ option }}
            </button>
        </template>
    </div>
</template>

<script>
export default {
    name: "ques",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            options: this.list.options,
            current: -1
        };
    },
    activated() {
        this.options = this.list.options;
        const option = this.$storage.loadFromStorage(
            "__ques__",
            this.$route.params.id,
            ""
        );
        if (option) {
            this.current = option;
        }
    },
    methods: {
        goNext(option) {
            this.current = option;
            this.$storage.saveToStorage(
                "__ques__",
                this.$route.params.id,
                option
            );
            setTimeout(() => {
                this.current = -1;
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
