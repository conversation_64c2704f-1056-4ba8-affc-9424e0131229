<template>
    <van-popup
        class="common-msg-code-modal"
        v-if="!sendError && value"
        :value="value"
        @input="handleVisibleChange"
        :close-on-click-overlay="false"
    >
        <div class="common-msg-code-modal">
            <h1 class="common-msg-code-modal__title">
                验证码已通过短信发到你的手机
            </h1>

            <p class="common-msg-code-modal__desc">
                {{ phone || '' }}
            </p>

            <div class="common-msg-code-modal__code">
                <div class="common-msg-code-modal__code-input-items">
                    <div
                        v-for="(item, index) in 4"
                        :key="index"
                        class="common-msg-code-modal__code-input-item"
                    />
                </div>
                <input
                    ref="codeInput"
                    type="tel"
                    class="common-msg-code-modal__code-input"
                    maxlength="4"
                    :value="code"
                    @input="checkCode"
                    autocomplete="new-password"
                >
            </div>

            <div class="common-msg-code-modal__code-error">
                {{ errorMessage }}
            </div>

            <div
                class="common-msg-code-modal__btn"
                :style="{ background: isLock ?'#6C6D75': styleConfig.confirmButtonBgColor, color: styleConfig.confirmButtonColor}"
                @click="clickSendCode"
            >
                {{ `${isLock ? `${cutdown}”` : btnText}` }}
            </div>

            <div
                class="common-msg-code-modal__cancel"
                @click="closeModal"
                :style="{ color: styleConfig.cancleButtonColor}"
            >
                取消
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import Api from '@/common/server/base';
import { reportMagic, reportKibana,reportLoveKibana } from "@/common/utils/report";
import Prototype from "@/common/framework/prototype";
import { storage as Storage } from "@/common/utils/storage";
import { getCode } from '../../nearApi';

export default {
    name: 'RegisterMsgCodeModal',
    components: {
        VanPopup: Popup,
    },

    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        validateCode: {
            type: Function,
            required: true,
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF',
            }
        }
    },

    data() {
        return {
            errorMessage: '',
            sendError: true,
            btnText: '',
            cutdown: 60,
            isLock: false,
            code: '',
            leaveTime: null,
            isListenVisibilitychange: null,
            phone: ''
        };
    },
    watch: {
        value: {
            handler(value) {
                if (!value) {
                    return;
                }

                this.code = '';
                this.errorMessage = '';

                const registerForm = Storage.getItem(
                    `cachedRegisterForm-${this.pageType}`
                );

                this.phone = registerForm.phone;


                reportKibana(this.pageType, 300, '验证短信弹窗访问');

                Prototype.$gather.setBeforeValidateCodeOCPC();

                this.countDown();


            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        async checkCode(evt) {
            this.errorMessage = '';
            this.code = evt.target.value.replace(/\D/g, '');
            evt.target.value = this.code;
            if (this.code.length === 4) {
                this.$refs.codeInput.blur();
                this.errorMessage = '';
                const result = await this.validateCode(this.code);

                if (!result.isError) {
                    this.code = '';

                    return;
                }

                if (result.errorCode !== '-00017') {
                    // 验证码错误
                    this.closeModal();
                }
            }
        },
        closeModal() {
            this.$emit('input', false);
            this.isLock = false;
            this.sendError = true;
            clearInterval(this.timer);
        },
        async sendCode() {

            const sendData = {
                phone: this.phone.replace(/[^(\d)]/g, ""),
                type: 0,
            };

            const sendMsgResult = await getCode(sendData);

            if (sendMsgResult.isError) {
                this.sendError = true;
                this.closeModal();
            } else {
                this.sendError = false;
                this.$toast('验证码已发送，请注意查收');
            }
        },
        clickSendCode() {
            this.errorMessage = '';
            reportKibana(this.pageType, 301, '验证短信弹窗-重新获取短信按钮点击');
            reportLoveKibana('新注册路径手机前置',39,'手机验证页验证码弹窗-点击获取验证码');
            this.countDown();
        },
        async countDown() {
            if (this.isLock) {
                return;
            }

            reportKibana(this.pageType, 3100, '提交手机号（监控）');

            this.isLock = true;

            await this.sendCode();

            // 从60s开始倒计时
            this.btnText = '';
            this.cutdown = 60;
            this.timer = setInterval(() => {
                this.cutdown -= 1;
                if (this.cutdown <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }, 1000);
            setTimeout(() => {
                this.$refs.codeInput.focus();
            }, 300);
        },
    },
    mounted() {
        const callback = () => {
            // 用户进入后台
            if (document.visibilityState === "hidden") {
                this.leaveTime = new Date().getTime();
            } else if (document.visibilityState === "visible") {
                if (!this.isLock) {
                    return;
                }

                this.backTime = new Date().getTime();
                const diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                this.cutdown -= diff;

                if (this.cutdown <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }
        };

        document.addEventListener('visibilitychange', callback);
        this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('visibilitychange', callback);
        });
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

$width: 622px;

.common-msg-code-modal {
    width: $width;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 72px;
    overflow: hidden;
    @include flex-center(column, null, center);

    &.van-popup--center {
        // 某些浏览器出现软键盘后会覆盖输入框，所以将弹窗位置放上点
        top: 40%;
    }

    &__title {
        width: 504px;
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
        padding: 0 26px;
    }

    &__btn {
        @include flex-center(column);
        flex-shrink: 0;
        width: 462px;
        height: 88px;
        font-size: 32px;
        border-radius: 44px;
        margin-top: 24px;
        margin-bottom: 32px;

        &-continue {
            padding-top: 3px;

            > div:last-child {
                font-size: 24px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        flex-shrink: 0;
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #000000;
        font-weight: 400;
        text-align: center;
    }

    &__code {
        position: relative;
        color: #26273C;
        width: 100%;
        margin-bottom: 16px;

        &-input-items {
            @include flex-center(row, space-between, center);
        }

        &-input-item {
            width: 90px;
            height: 90px;
            opacity: 0.2;
            background: #6C6D75;
            border-radius: 20px;
        }

        &-error {
            width: 100%;
            text-align: center;
            font-size: 24px;
            margin-top: 48px;
            color: #F04086;
        }

        &-input {
            box-sizing: border-box;
            width: $width;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            height: 60px;
            font-size: 30px;
            line-height: 30px;
            background: transparent;
            letter-spacing: 110px;
            padding-left: 110px;
            overflow: hidden;
        }
    }
}
</style>
