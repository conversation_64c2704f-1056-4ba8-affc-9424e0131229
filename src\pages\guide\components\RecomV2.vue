<template>
    <div
        v-if="canRender && recList.length"
        ref="refRecom"
        class="recom"
    >
        <div class="model">
            <div
                class="model-card"
                v-for="item, index in recList"
                :key="index"
            >
                <div
                    class="model-card-avatar"
                    :class="(currentIndex === index) && isHidden ? 'modal-card-hide' : ''"
                >
                    <!-- <img
                        :class="{'model-card-hide' :currentIndex === index && isHidden, 'model-card-blur': currentIndex !== index }"
                        :src="item.avatar"
                    /> -->
                    <img
                        :src="item.avatar"
                    />
                    <div class="model-card-info">
                        {{ item.name }}·{{ item.age }}岁
                    </div>
                </div>

                <!-- 动效 -->
                <div
                    :id="'blur'+index"
                    class="model-card-svga"
                ></div>

                <common-button
                    :config="{ width: 160, height: 64, fontSize: 28, des: '解锁资料' }"
                    :class="{ 'button-disabled': currentIndex === index }"
                    @click="handleSelect(item, index)"
                />
            </div>
        </div>

        <div
            class="model-detail"
            v-if="currentIndex >= 0"
        >
            <div class="info">
                <div class="header">
                    <!-- <img :src="modelDetail.avatar"> -->
                    <div class="main-avatar" :style="`background-image:url(${modelDetail.avatar})`"></div>
                    <div>
                        <img :src="modelDetail.mainImg">
                        <img
                            :src="modelDetail.momentImg1"
                        >
                    </div>
                </div>
                <div class="des">
                    {{ modelDetail.name }} · {{ modelDetail.ageString }} · {{ modelDetail.educationString }}
                </div>
                <div class="detail">
                    <div
                        v-for="(item, key) in detailMap"
                        :key="key"
                    >
                        <span>{{ item }}</span>
                        <span>{{ modelDetail[key] }}</span>
                    </div>
                </div>
                <div class="online">
                    APP在线
                </div>
                <div class="icon" />
            </div>
            <div
                class="activity"
                ref="activity"
            >
                <div class="header">
                    <span>Ta的动态</span>
                    <span @click="handleDownload(1)">查看所有{{ modelId % 10 + 10 }}条</span>
                </div>
                <div class="bottom">
                    <img :src="modelDetail.momentImg2" />
                    <img :src="modelDetail.momentImg3" />
                    <img :src="modelDetail.momentImg4 || modelDetail.momentImg4 || modelDetail.momentImg3" />
                </div>
            </div>
            <div class="btn-wrapper">
                <div
                    class="btn"
                    @click="handleDownload(2)"
                >获取{{ modelDetail.sex === 1 ? '她' : '他' }}的微信</div>
                <div class="btn-tips">请下载APP与{{ modelDetail.sex === 1 ? '她' : '他' }}畅聊，加{{ modelDetail.sex === 1 ? '她' : '他' }}微信</div>
            </div>
        </div>

        <!-- 主按钮，修改为下载 -->
        <div
            class="main"
            @click="goDownload"
        ></div>

        <!-- 模态框-->
        <match-modal
            v-if="canRender"
            v-model="showModal.matchModal"
            :is-Male="isMale"
            :avatar="`${modelDetail.avatar}?imageMogr2/format/jpg`"
            @go-download="goDownload"
            @go-mini="goMini"
        />

        <guide-modal
            v-model="showModal.guideModal"
            :guide-config="guideConfig"
            @go-download="goDownload"
            @go-mini="goMini"
        />
    </div>
</template>
<script>
import CommonButton from './CommonButton';
import { PAGE_TYPE } from "../config";
import MatchModal from "./Modal/MatchModal.vue";
import GuideModal from "./Modal/GuideModal.vue";
import {
    _queryIdealLoverModelList,
    _getModelInfo,
    _signDelistingGuideStatus
} from '../api.js';
import { getMiniPathV2 } from "@/common/business/utils/wecom";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { session as Session, storage as Storage } from "@/common/utils/storage";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { Toast } from "vant";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "Recom",
    components:{
        MatchModal,
        GuideModal,
        CommonButton,
    },
    data(){
        return {
            recList: [],
            modelDetail: {},
            showModal:{
                matchModal: false,
                guideModal: false,
            },
            showMain: false,
            guideConfig: {
                title:'',
                subtitle:'',
                submitText:'',
                cancelText:''
            },
            canRender: false,

            currentIndex: -1,
            isHidden: false,
            detailMap: {
                workCityString: '工作地：',
                // job: '职业：',
                salaryString: '月收入：'
            },
            modelId: null,
        };
    },
    async created(){
        await this.getRecList();
        this.canRender = true;
    },
    mounted(){
    },
    computed:{
        isMale() {
            // 当前用户性别
            // 推荐的若是女(1)，说明当前用户为男(0)
            return this.modelDetail.gender === 1;
        }
    },
    methods: {
        async getRecList(){
            const res = await _queryIdealLoverModelList({
                memberId: Session.getItem("reg_memberid")
            }).catch(e=>this.$toast(e.message));
        
            if(res.isError){
                return this.$toast(res.errorMessage);
            }        
            this.recList = res.data.list;
        },
        async getDetail(modelId, age){
            const registerForm = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            };
            // const age = new Date().getFullYear() - new Date(registerForm.birthday).getFullYear();
            const sendData = {
                sex: registerForm.gender,
                age,
                education: registerForm.education,
                salary: registerForm.salary,
                workCity: registerForm.workCity,
                modelId: modelId
            };
            const res = await _getModelInfo(sendData).catch(e=>this.$toast(e.message));
        
            if(res.isError){
                return this.$toast(res.errorMessage);
            }        
            this.modelDetail = {
                ...res.data.modelInfoVo,
                ageString: `${age}岁`
            }; 
        },
        async handleWeixin(){
            this.$report(61, "测试报告页-人物刺激-点击交换微信");
            await this.markStatus(this.modelDetail.objectId, 1);

            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.guideConfig = {
                    title:'下载珍爱网APP',
                    subtitle:`搜索Ta的昵称【${this.modelDetail.nickName}】即可申请交换微信哦！`,
                    submitText:'交换',
                    cancelText:'再看看'
                };
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.guideConfig = {
                    title:'她对你也有好感',
                    subtitle:'是否交换微信呢？',
                    submitText:'交换',
                    cancelText:'再看看'
                };
            }

            this.showModal.guideModal = true;
        },
        async handleLike(){
            this.$report(61, "测试报告页-人物刺激-点击喜欢");
            await this.markStatus(this.modelDetail.objectId,1);
            
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.guideConfig = {
                    title:'下载珍爱网APP',
                    subtitle:`搜索Ta的昵称【${this.modelDetail.nickName}】即可申请交换微信哦！`,
                    submitText:'交换',
                    cancelText:'再看看'
                };
                this.showModal.guideModal = true;
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.showModal.matchModal = true;
            }
        },
        async markStatus(objectId, status){
            const res = await _signDelistingGuideStatus({
                objectId,
                status
            }).catch(e=>this.$toast(e.message));  

            if(res.isError){
                this.$toast(res.errorMessage);
            }
        },
        scrollToRecom(){
            this.$report(61, "测试报告页-人物刺激-点击悬浮按钮");
            const target = this.$refs.refRecom;
            try{
                // oneplus 6不兼容
                window.scrollTo({
                    top: target.offsetTop,   
                    behavior: "smooth"         
                });
            } catch(e){
                window.scrollTo(0, target.offsetTop);
            }
        },
        goDownload(){
            Toast({
                message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                duration: 5000
            });
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        async goMini(){
            const queryObj = {
                mid: Session.getItem('reg_memberid'),
                recommendId: this.idList[0],
                ext30: pageTypeChnMap[PAGE_TYPE]
            };

            const miniPath = await getMiniPathV2('pages/recommend/recommend',queryObj);
            window.location.href = miniPath;
        },
        
        handleSelect(item, index) {
            if (this.currentIndex > -1) {
                this.$report(61, "测试报告页-翻牌人物（已解锁）-点击解锁资料");
                this.handleDownload();
                return;
            }

            if (this.currentIndex < 0) {
                this.$report(61, "测试报告页-翻牌人物（未解锁）-点击解锁资料");
                this.currentIndex = index;
                this.isHidden = true;
                setTimeout(() => {
                    this.isHidden = false;
                }, 1000);
                // this.handlesetSVGA(item.avatar);
                this.modelId = item.modelId;
                this.getDetail(item.modelId, item.age);
                this.$emit('expend');
            }
        },
        handlesetSVGA(avatar){
            let player = new window.SVGA.Player(`#blur${this.currentIndex}`),
                parser = new window.SVGA.Parser(`#blur${this.currentIndex}`);

            parser.load(require('../assets/images/svgaBroken.svga'), (videoItem)=>{
                player.setImage(avatar, 'key');
                player.loops = 1;
                player.setVideoItem(videoItem);
                player.startAnimation();
            });
        },
        handleDownload(type) {
            if (type === 1) {
                this.$report(61, "测试报告页-翻牌人物（已解锁）-点击查看所有动态");
            } else if (type === 2) {
                this.$report(61, "测试报告页-翻牌人物（已解锁）-点击获取微信");
            }
            this.goDownload();
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.recom {
    .main{
        position: fixed;
        bottom: 36px;
        left: 50%;
        transform: translateX(-50%);
        width: 616px;
        height: 126px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20230517/1684294103367_518672_t.png");
        z-index: 9;
    }

    .model {
        @include flex-center(row, space-between, null);
        width: 100%;
        border-radius: 40px;
        padding: 54px 22px 30px;
        position: relative;
        background-image: linear-gradient(180deg, #fff 0%, #00000020 100%);

        &::before {
            content: "";
            display: block;
            background-image: linear-gradient(180deg, #123151 0%, #132035 100%);
            border: 2px solid transparent;
            border-radius: 40px;
            position: absolute;
            top: 2px;
            right: 2px;
            bottom: 2px;
            left: 2px;
        }

        &-card {
            width: 206px;
            position: relative;
            @include flex-center(column, null, center);
            >div:nth-child(1) {
                height: 206px;
                border-radius: 16px;
                margin-bottom: 16px;
                img {
                    margin-bottom: 24px;
                    width: 206px;
                    height: 206px;
                    border-radius: 16px;
                }
            }

            &-blur {
                filter: blur(4px);
            }

            &-hide {
                opacity: 0;
            }

            &-svga {
                position: absolute;
                left: 0;
                top: 0;
                width: 202px;
                height: 202px;
                z-index: 2;
            }

            &-avatar {
                position: relative;
            }

            &-info {
                position: absolute;
                font-weight: 400;
                font-size: 28px;
                line-height: 40px;
                color: #fff;
                text-align: center;
                top: 124px;
                right: 0;
                bottom: 0;
                left: 0;
                padding: 32px 0 10px;
                background-image: linear-gradient(180deg, #0000000a 0%, #000000cc 100%);
                border-radius: 0 0 16px 16px;
            }


        }
    }

    .model-detail {
        width: 100%;
        border: 2px solid #fff;
        border-radius: 80px 40px 40px 40px;
        padding: 22px 22px 38px;
        margin-top: 32px;
        .info {
            position: relative;
            width: 100%;
            margin: 0 auto;
            border-radius: 32px;
            .header {
                @include flex-center(row, space-between, null);
                .main-avatar {
                    width: 450px;
                    height: 404px;
                    border-radius: 36px;
                    background-size: cover;
                    background-repeat: no-repeat;
                    background-position: center;
                }
                >div:nth-child(2) {
                    @include flex-center(column, space-between, null);
                    img {
                        width: 192px;
                        height: 192px;
                    }
                    >img:nth-child(1) {
                        border-radius: 32px;
                    }
                    >img:nth-child(2) {
                        border-radius: 32px;
                    }
                }
            }
            .des {
               font-weight: 600;
                font-size: 40px;
                color: #fff;
                line-height: 56px;
                margin: 32px 0 24px;
            }
            .detail {
                div {
                    margin-bottom: 24px;
                    height: 32px;
                    line-height: 32px;
                    >span:nth-child(1) {
                        font-weight: 400;
                        font-size: 26px;
                        color: #ffffff99;
                        line-height: 32px;
                    }
                    >span:nth-child(2) {
                        font-weight: 400;
                        font-size: 28px;
                        color: #FFFFFF;
                        line-height: 32px;
                    }
                }
            }
            .online {
                position: absolute;
                left: 0;
                top: 340px;
                background-image: linear-gradient(to left, #30B4E8 11%, #00000000 100%);
                border-radius: 0 31px 31px 36px;
                font-weight: 500;
                font-size: 24px;
                color: #fff;
                letter-spacing: 0;
                line-height: 60px;
                text-align: center;
                width: 148px;
                height: 60px;
            }
            .icon {
               position: absolute;
               left: -26px;
               top: -24px;
               width: 158px;
               height: 158px;
               @include set-img("https://photo.zastatic.com/images/common-cms/it/20230516/1684220210769_314993_t.png");
            }
        }
        .activity {
            margin: 52px 0 24px;
            .header {
                position: relative;
                >span:nth-child(1) {
                    font-weight: 600;
                    font-size: 40px;
                    color: #FFFFFF;
                    line-height: 56px;
                }
                >span:nth-child(2) {
                    font-weight: 400;
                    font-size: 28px;
                    color: #FFFFFF;
                    line-height: 56px;
                    height: 56px;
                    position: absolute;
                    right: 24px;
                    top: 50%;
                    transform: translateY(-50%);
                    margin-right: 8px;
                    &::after {
                        position: absolute;
                        content: "";
                        width: 32px;
                        height: 32px;
                        @include set-img("https://photo.zastatic.com/images/common-cms/it/20230517/1684306764863_989277_t.png");
                        top: 12px;
                    }
                }
            }
            .bottom {
                margin-top: 32px;
                @include flex-center(row, space-between, null);
                img {
                    width: 206px;
                    height: 206px;
                    border-radius: 16px;
                }
            }
        }
        .btn-wrapper {
            margin: 32px 0 0;
            .btn {
                margin: 0 auto;
                width: 638px;
                height: 100px;
                font-weight: 500;
                font-size: 32px;
                line-height: 100px;
                text-align: center;
                color: #FFFFFF;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20230517/1684306765001_490399_t.png");
            }
            .btn-tips {
                margin-top: 24px;
                font-weight: 400;
                font-size: 26px;
                color: #ffffff99;
                line-height: 32px;
                text-align: center;
            }
        }
    }
}
</style>
