<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        get-container="#app"
        class="common-protocol-modal"
    >
        <div class="common-protocol-modal__headtitle">
            服务协议及隐私要求
        </div>
        <div class="common-protocol-modal__desc">
            此账号将用于登录珍爱网旗下产品，查看报告及专业老师致电解读，为了更好的保障您的合法权益。
            <br />
            请您阅读并同意以下协议<span
                class="protocol_text"
                @click="goServiceProtocol"
            >《珍爱网服务协议》</span>和<span
                class="protocol_text"
                @click="goPersonDescription"
            >《个人信息保护政策》</span>
        </div>

        <div class="btns">
            <div
                class="cancel"
                @click="handleCloseModal"
            >
                取消
            </div>
            <div
                class="confirm"
                @click="confirm"
            >
                同意
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { reportKibana,reportLoveKibana } from "@/common/utils/report";

export default {
    name: 'CommonProtocolModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF',
            }
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(112,'手机号码验证-用户协议弹窗曝光');
                }
            },
            immediate: true,
        }
    },
    methods: {
        goServiceProtocol() {
            window.location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
        },
        goPersonDescription() {
            window.location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
        },
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirm() {
            this.$report(113,'手机号码验证-用户协议弹窗曝光-同意');
            this.$emit('confirm');
        },
        handleCloseModal() {
            reportKibana(this.pageType, 602, '同意协议弹窗-取消按钮点击');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss" scoped>

.common-protocol-modal {
    width: 558px;
    background-color: #FFFFFF;
    border-radius: 32px;
    padding: 43px 32px 43px;

    &__headtitle {
        font-size: 36px;
        color: #26273C;
        font-weight: 600;
        margin-bottom: 20px;
    }

    &__desc {
        width: 462px;
        font-weight: 400;
        font-size: 28px;
        color: #666666;
        text-align: left !important;
        line-height: 42px;
        margin-bottom: 48px;
        // padding: 0 26px;
        letter-spacing: 0;
    }
    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
    .protocol_text {
        color: rgb(215, 32, 74);
    }

    .confirm {
      width: 227px;
      height: 100px;
      background-image: linear-gradient(180deg, #FF9E8B 4%, #FF5B87 100%);
      border-radius: 54px;
      font-size: 32px;
      color: #FFFFFF;
      text-align: center;
      line-height: 100px;
    }

    .cancel {
        width: 227px;
        height: 100px;
        background: #0000001a;
        border-radius: 54px;
        line-height: 100px;
        text-align: center;
        font-size: 32px !important;
        color: #FFFFFF;
    }
}
</style>
