<template>
    <div class="fulfill">
        <template v-if="cmsConfig.reportViewType === 4">
            <div class="title">恭喜，完成测试！</div>
            <div class="subtitle">
                你的<strong>【MBTI恋爱人格报告】</strong>已生成
            </div>
            <div class="panels">
                <div class="panels-title">从测试报告中，你能获取以下信息：</div>
                <p>自己是怎样的人？</p>
                <p>在爱情中你是怎样的角色？</p>
                <p>最适合你的Ta会是怎样的？</p>
                <!--<p>系统已为你推荐几<span></span>位对象，或许有你喜欢的</p>-->
            </div>
            <div class="unlocks" @click="unLock">
                去解锁
            </div>
        </template>
        <template v-else>
            <h3>恭喜，完成测试！</h3>
            <h4>你的<strong>专业版MBTI报告</strong>已生成</h4>
            <div class="panel">
                <div class="panel-title">从测试报告中，你能获取以下信息：</div>
                <p>自己是怎样的人？</p>
                <p>在爱情中你是怎样的角色？</p>
                <p>最适合你的Ta会是怎样的？</p>
                <!--<p>系统已为你推荐几<span></span>位对象，或许有你喜欢的</p>-->
            </div>
            <div class="unlock" @click="unLock">
                <anim-svga><span>去解锁</span></anim-svga>
            </div>
        </template>
    </div>
</template>

<script>
import { storage } from "../lib/utils.js";
import { mapState } from "vuex";
import AnimSvga from "../components/AnimSvga.vue";
export default {
    name: "Fulfill",
    mounted() {
        this.$reportKibana(this.resourceKey, 89, "完成答题页访问");
    },
    computed: {
        ...mapState(["cmsConfig", "resourceKey"])
    },
    components: {
        AnimSvga
    },
    methods: {
        unLock() {
            const single = storage.getItem("single");
            if (single === 1) {
                this.$router.push({ path: "/form" });
            } else {
                this.$router.push({ path: "/result" });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.fulfill {
    position: relative;
    z-index: 1;
    padding: 0 32px 32px;
    > h3 {
        width: 592px;
        height: 72px;
        font-size: 0;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650512031243_255894_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    > h4 {
        margin-top: 96px;
        width: 630px;
        height: 60px;
        font-size: 0;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650512036662_482562_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .panel {
        width: 686px;
        height: 372px;
        margin-top: 48px;
        color: #fff;
        font-size: 32px;
        line-height: 48px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650512159217_124537_t.png)
            no-repeat;
        background-size: 100% 100%;
        &-title {
            padding: 14px 0 60px;
            text-align: center;
        }
        > p {
            padding-left: 32px;
            > span {
                color: #00ffff;
            }
        }
    }
    .unlock {
        margin: -40px auto 0;
    }
    .title {
        height: 56px;
        padding-left: 76px;
        color: #fff;
        font-size: 36px;
        font-weight: 500;
        line-height: 56px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577029972_719710_t.png)
            no-repeat;
        background-size: 60px 56px;
    }
    .subtitle {
        margin-top: 36px;
        color: #fff;
        font-size: 36px;
        > strong {
            color: #5ad9ff;
            font-weight: 600;
        }
    }
    .panels {
        margin-top: 64px;
        padding-bottom: 40px;
        background: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.55) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        border-radius: 0 0 32px 32px;
        &-title {
            width: 686px;
            height: 64px;
            padding-left: 112px;
            margin-bottom: 40px;
            color: #fff;
            font-size: 26px;
            line-height: 64px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579479727_582408_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        > p {
            color: #fff;
            font-size: 30px;
            padding-left: 32px;
            line-height: 44px;
            > span {
                color: #5ad9ff;
                font-weight: 600;
            }
        }
    }
    .unlocks {
        width: 476px;
        height: 140px;
        margin: 160px auto 70px;
        line-height: 140px;
        color: #fff;
        font-size: 46px;
        text-align: center;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656931632682_459807_t.png)
            no-repeat;
        background-size: 100% 100%;
        &:active {
            opacity: .7;
        }
    }
}
</style>
