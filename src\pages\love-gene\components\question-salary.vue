<template>
    <question-panel
        title="您理想对象的收入水平区间"
        :src="cmsConfig.questionBodyImg3"
        :height="640">
        <radio-list
            :list="list"
            :value="requirement.salaryRange"
            @change="handleChange"/>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import RadioList from './radio-list';
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-salary",
    components: {
        QuestionPanel,
        RadioList,
    },
    data() {
        return {
            list: [
                {
                    label: '3000~5000元',
                    accessPoint: 13,
                    accessPointDesc: '问题页3-点击“选项1”',
                },
                {
                    label: '5001~8000元',
                    accessPoint: 14,
                    accessPointDesc: '问题页3-点击“选项2”',
                },
                {
                    label: '8001~12000元',
                    accessPoint: 15,
                    accessPointDesc: '问题页3-点击“选项3”',
                },
                {
                    label: '12001~20000元',
                    accessPoint: 16,
                    accessPointDesc: '问题页3-点击“选项4”',
                },
                {
                    label: '20001~50000元',
                    accessPoint: 17,
                    accessPointDesc: '问题页3-点击“选项5”',
                },
            ]
        }
    },
    computed: {
        ...mapState([
            'cmsConfig',
            'requirement'
        ]),
    },
    methods: {
        ...mapMutations([
            'setRequirement',
        ]),
        handleChange(item) {
            this.$report(item.accessPoint, item.accessPointDesc);

            if (this.lock) {
                return;
            }

            this.lock = true;

            this.setRequirement({
                salaryRange: item.label,
            });

            setTimeout(() => {
                this.lock = false;

                this.$router.push({
                    path: "/questions/3",
                });
            }, 1000);
        }
    }
}
</script>
