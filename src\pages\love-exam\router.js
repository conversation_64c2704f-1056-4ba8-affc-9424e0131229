import VueRouter from "vue-router";
import Quiz from './views/Quiz.vue';
import Result from './views/Result.vue';
import ResultSingle from './views/ResultSingle.vue';
import ResultSingleQr from './views/ResultSingleQr.vue';
import FinishV2 from './views/FinishV2.vue';
import FinishV3 from './views/FinishV3.vue';
import TestResult from './views/testResult.vue';
import Register from './views/register.vue';
import LoveExamIndex from './views/exam/index.vue';
import exam from './views/exam/exam.vue';
import ExamResultBefore from './views/exam-result-before.vue';



const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/loveExamIndex",
            
        },
        {
            path: "/loveExamIndex",
            name: 'loveExamIndex',
            component: LoveExamIndex
        },
        {
            path: "/examResultBefore",
            name: 'examResultBefore',
            component: ExamResultBefore
        },
        {
            path: "/exam/:id",
            component: exam,
        },
        {
            path: '/quiz',
            name: 'quiz',
            component: Quiz
        },
        {
            path: '/result',
            name: 'Result',
            component: Result
        },
        {
            path: '/finish-v2',
            name: 'finishV2',
            component: FinishV2
        },
        {
            path: '/testresult',
            name: 'TestResult',
            component: TestResult
        },
        {
            path: '/register/:id',
            name: 'Register',
            component: Register
        },
        {
            path: '/finish-v3',
            name: 'finishV3',
            component: FinishV3
        },
        {
            path: '/resultSingle',
            name: 'resultSingle',
            component: ResultSingle
        },
        {
            path: '/resultSingleQr',
            name: 'resultSingleQr',
            component: ResultSingleQr
        },
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0, 0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
