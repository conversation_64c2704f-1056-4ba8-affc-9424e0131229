<template>
    <div class="salary">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}
        </div>
        <div class="salary-info">
            <div
                class="salary-info-btn"
                :class="{ active: curSalary === option.key }"
                v-for="option in list.options"
                @click="goNext(option.key)"
            >
                {{ option.text }}
            </div>
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
export default {
    name: "Salary",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    inject: ["cmsConfig"],
    mounted() {
        this.$report(9, '收入页访问');
    },
    data() {
        return {
            curSalary: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) && Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).salary || ''
        };
    },
    methods: {
        goNext(val) {
            this.curSalary = val;
            const params = {
                key: "salary",
                value: val
            };
            this.$report(9, '收入页-具体收入点击');
            setLocalRegisterForm(params, this.cmsConfig.planName);
            setTimeout(() => {
                this.$emit("val-updated", val, true);
            }, 300);

        }
    }
};
</script>

<style lang="scss" scoped>
.salary {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    &-info {
        margin: 50px 48px 0;
        &-btn {
            width: 100%;
            height: 110px;
            margin-bottom: 20px;
            color: #26273c;
            font-size: 32px;
            line-height: 110px;
            text-align: center;
            border-radius: 80px;
            background: #fff;
            &.active {
                color: #fff;
                background: #5368f0;
            }
        }
    }
}
</style>
