<template>
    <div class="home" :class="{ spe: cmsConfig.isNewUI }">
        <template v-if="!cmsConfig.isNewUI">
            <div class="home-title"></div>
            <div class="home-info">
                <div class="home-info-title">
                    根据您的偏好定制异性的声音、身材、穿搭、性格<br />
                    以及个人条件，你将得到一个符合您喜好的：
                </div>
                <div class="home-info-card">
                    <div
                        class="hic-item"
                        v-for="gender in genders"
                        :key="gender.key"
                        @click="goNext(gender.key)"
                    >
                        <img
                            :src="
                                gender.key === selectIndex
                                    ? gender.activeUrl
                                    : gender.url
                            "
                            :class="{
                                'hic-item-ok': gender.key === selectIndex
                            }"
                            class="hic-item-normal"
                        />
                        <div class="hic-item-btn">
                            {{ gender.des }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="home-desc"></div>
            <div class="home-pure">
                <div
                    class="home-pure-item"
                    v-for="gender in genders"
                    :key="gender.key"
                    @click="goNext(gender.key)"
                >
                    <img
                        :src="gender.normalUrl"
                        class="hpi-img"
                    />
                    <div class="hpi-btn">
                        {{ gender.des }}
                    </div>
                </div>
            </div>
        </template>
        <div class="home-foot">
            <common-protocol
                class="home-wrapper__protocol"
                :is-checked.sync="isCheckProtocol"
                :agreement-status="cmsConfig.agreementStatus"
                :style-config="{
                    textColor: '#6C7175',
                    protocolColor: '#4A3BC0',
                    protocolCheckedUrl:
                        'https://photo.zastatic.com/images/common-cms/it/20220905/1662362153461_207491_t.png'
                }"
            />
            <p class="copyright" v-if="isShowCopyright">
                4001-520-520 粤ICP备09157619号-1
                <br />深圳市珍爱网信息技术有限公司
            </p>
        </div>
        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :page-type="cmsConfig.planName"
            :style-config="{
                confirmButtonColor: '#fff',
                confirmButtonBgColor: '#4A3BC0',
                cancleButtonColor: '#4A3BC0'
            }"
        />
    </div>
</template>

<script>
import CommonProtocol from "@/common/business/CommonProtocol.vue";
import CommonProtocolModal from "@/common/business/components/CommonProtocolModal.vue";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";

export default {
    name: "Home",
    inject: ["cmsConfig"],
    data() {
        return {
            selectIndex: [0, 1].includes(Session.getItem("gender"))
                ? Session.getItem("gender")
                : -1,
            genders: [
                {
                    text: "女生",
                    key: 1,
                    des: "男朋友",
                    url:
                        "https://photo.zastatic.com/images/common-cms/it/20220927/1664247718782_542248.png",
                    activeUrl:
                        "https://photo.zastatic.com/images/common-cms/it/20220927/1664251206459_995612.png",
                    normalUrl: "https://photo.zastatic.com/images/common-cms/it/20221102/1667387852781_77290_t.png"
                },
                {
                    text: "男生",
                    key: 0,
                    des: "女朋友",
                    url:
                        "https://photo.zastatic.com/images/common-cms/it/20220927/1664247790257_300072.png",
                    activeUrl:
                        "https://photo.zastatic.com/images/common-cms/it/20220927/1664251221623_526342.png",
                    normalUrl: "https://photo.zastatic.com/images/common-cms/it/20221102/1667387856521_52477_t.png"
                }
            ],
            isCheckProtocol: false,
            isShowModal: false,
            isShowCopyright: Session.getItem("mediaGroupType") == "12"
        };
    },
    components: {
        CommonProtocol,
        CommonProtocolModal
    },
    mounted() {
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                this.isCheckProtocol = value === 0;
            },
            {
                immediate: true
            }
        );
        this.$report(1, "首页-访问", {ext1: location.href});
        this.$report(3000, "首页访问（监控）");
        Storage.removeItem(`cachedRegisterForm-${this.cmsConfig.planName}`);
    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.isShowModal = true;
            setTimeout(() => {
                this.$router.push({
                    path: "/about/0"
                });
            }, 200);
        },
        goNext(val) {
            const desMap = {
                0: "首页-性别女点击",
                1: "首页-性别男点击"
            };
            console.log(desMap[val]);
            this.$report(2, desMap[val]);
            this.selectIndex = val;
            const params = {
                key: "gender",
                value: val
            };
            Session.setItem("gender", val);
            setLocalRegisterForm(params, this.cmsConfig.planName);

            if (!this.isCheckProtocol) {
                this.isShowModal = true;
            } else {
                setTimeout(() => {
                    this.$router.push({
                        path: "/about/0"
                    });
                }, 200);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.home-foot {
    position: fixed;
    left: 50px;
    bottom: 20px;
    .copyright {
        padding-top: 10px;
        color: #6c7175;
        font-size: 12px;
        line-height: 30px;
        text-align: center;
    }
}
.home {
    position: relative;
    min-height: 100vh;
    padding-top: -14px;
    background: url(https://photo.zastatic.com/images/common-cms/it/20220927/1664247345274_953465.jpeg)
        no-repeat;
    background-size: 100% 1624px;
    background-position: center center;
    &.spe {
        position: relative;
        padding-top: 120px;
        background: #F4F4F9
            url(https://photo.zastatic.com/images/common-cms/it/20221103/1667447328651_409067_t.png)
            no-repeat;
        background-size: 100% 1448px;
        background-position: top center;
    }
    &-title {
        height: 356px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220927/1664247508632_962012.png)
            no-repeat;
        background-size: 100% 100%;
    }
    &-info {
        margin-top: 128px;
        &-title {
            color: #4a3bc0;
            font-size: 28px;
            font-weight: 400;
            line-height: 42px;
            text-align: center;
        }
        &-card {
            display: flex;
            margin: 55px 105px 0;
        }
        .hic-item {
            position: relative;
            text-align: center;
        }
        .hic-item:nth-of-type(1) {
            margin-right: auto;
        }

        .hic-item-btn {
            font-weight: 500;
            font-size: 36px;
            color: #5243c8;
        }
        .hic-item-normal {
            width: 222px;
            height: 222px;
            margin-bottom: 24px;
        }
        .hic-item-ok {
            margin-top: -30px;
            width: 279px;
            height: 279px;
            margin-bottom: 0px;
        }
    }
    &-desc {
        margin: 0 78px;
        width: 592px;
        height: 372px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20221103/1667447159822_890936_t.png) no-repeat;
        background-size: 100% 100%;
    }
    &-pure {
        display: flex;
        justify-content: space-between;
        margin: 0 54px;
        padding-top: 64px;
        &-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            .hpi-img {
                width: 290px;
                height: 404px;
            }
            .hpi-btn {
                width: 268px;
                height: 78px;
                margin-top: 30px;
                color: #000000;
                font-size: 32px;
                font-weight: 600;
                line-height: 78px;
                text-align: center;
                background: url(https://photo.zastatic.com/images/common-cms/it/20221102/1667387873146_442714_t.png) no-repeat;
                background-size: 100% 100%;
                &:active {
                    opacity: .8;
                }
            }
        }
        &-item:nth-of-type(2) {
             .hpi-btn {
                background: url(https://photo.zastatic.com/images/common-cms/it/20221102/1667387870405_33587_t.png) no-repeat;
                background-size: 100% 100%;
            }
        }
    }
}
</style>
