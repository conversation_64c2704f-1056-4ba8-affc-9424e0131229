<template>
    <div
        class="common-submit"
    >
        <div
            @click="handleSubmit"
            class="common-submit__button"
        >
            <slot></slot>
        </div>

        <common-protocol
            class="common-submit__protocol"
            v-if="isNeedProtocol"
            :is-checked.sync="isCheckProtocol"
            :is-need.sync="isNeedCheck"
            :agreement-status="agreementStatus"
            :style-config="styleConfig.protocolConfig"
        />

        <!-- 协议弹窗 -->
        <common-protocol-modal
            v-model="modalVisible.protocolModal"
            @confirm="handleConfirmProtocol"
            :page-type="pageType"
            :style-config="styleConfig.modalConfig"
        />

        <!-- 短信验证码弹窗 -->
        <RegisterMgsCodeModal
            v-model="modalVisible.msgCodeModal"
            :page-type="pageType"
            :validate-code="handleValidateCode"
            :style-config="styleConfig.modalConfig"
        />

    </div>
</template>

<script>
import CommonProtocol from '@/common/business/CommonProtocol.vue';
import CommonProtocolModal from '@/common/business/components/CommonProtocolModal.vue';
import RegisterMgsCodeModal from './RegisterMgsCodeModal.vue';
import { Toast } from "vant";

import z_ from '@/common/zdash';
import Api from '@/common/server/base';
import Prototype from "@/common/framework/prototype";
import oUserSelect from '@/common/ocpx/huichuan';
import { storage as Storage, session as Session } from "@/common/utils/storage";

import { reportError, reportKibana, reportLoveKibana } from "@/common/utils/report";
import { pageTypeChnMap, registerResult } from "@/common/config/register-dictionary.js";

export default {
    name: 'RegisterSubmit',
    components: {
        CommonProtocol,
        CommonProtocolModal,
        RegisterMgsCodeModal,
    },
    props: {
        // 勾子类型
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        agreementStatus: {
            type: Number,
            default: 1
        },
        // 是否需要协议
        isNeedProtocol: {
            type: Boolean,
            default: true
        },
        styleConfig: {
            type: Object,
            default: {
                modalConfig: {
                    confirmButtonColor: '#ffffff',
                    confirmButtonBgColor: '#000000',
                    cancleButtonColor: '#000000',
                },
                protocolConfig: {
                    textColor: "#FFFFFF",
                    protocolColor: "#767DFF",
                    protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20220512/1652336753832_292583_t.png'
                }
            }
        },
        // 注册成功之后的回调
        handleAfterRegisiter: {
            type: Function,
            default: () => {}
        },

        handleLogin: {
            type: Function,
            default: () => {}
        },

        handleCancle: {
            type: Function,
            default: () => {}
        },

    },
    mounted() {
        this.$watch('agreementStatus', (value) => {
            this.isCheckProtocol = value === 0;
        }, {
            immediate: true
        });
    },
    data() {
        return {
            isNeedCheck: false,
            // 控制弹窗是否展示
            modalVisible: {
                protocolModal: false,
                registerOverwriteModal: false,
                msgCodeModal: false
            },
            isCheckProtocol: false,
            // 校验账号结果
            validateAccountResult: {},
            // 防止多次触发
            lockSubmit: false,
            lockOverwrite: false,
            registerForm:{
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },
    computed: {

    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.modalVisible.protocolModal = false;
            this.handleSubmit(false);
        },

        // reg_id存session，原因是ocpx从session里取的值是reg_memberid
        handleSetRegMemberId(id) {
            Session.setItem('reg_memberid', id);
            oUserSelect.mark({
                msgValid: false,
                isMark: false,
            });
        },

        handleAllFillIn() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            return z_.every(registerForm , value => {
                return !z_.isNil(value) && value !== "";
            });
        },

        handleValidatedPhone() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },


        // 处理提交基本信息
        async handleSubmit(isReport = true) {
            if (isReport) {
                reportKibana(this.pageType, 49, '大表单注册页-提交按钮点击');
            }


            if (this.lockSubmit) {
                return;
            }

            // if (!this.handleAllFillIn()) {
            //     Toast('请完善资料再提交');
            //     return;
            // }

            if (!this.handleValidatedPhone()) {
                Toast('请输入正确的手机号');
                return;
            }

            // if (this.isNeedProtocol && !this.isCheckProtocol) {
            //     this.modalVisible.protocolModal = true;
            //     return;
            // }
            this.modalVisible.msgCodeModal = true;
            reportKibana(this.pageType, 3002, '获取验证码(监控)');
            if (isReport) {
                reportKibana(this.pageType, 49, '提交按钮点击（有效点击）');
            }

            // const registerForm = Storage.getItem(
            //     `cachedRegisterForm-${this.pageType}`
            // ) || this.registerForm;

            // const sendData = {
            //     height: -1, // 表单没有身高项，为兼容老注册页直接传-1
            //     ...registerForm
            // };
            // delete sendData.phone;
            //
            // this.lockSubmit = true;
            //
            // try {
            //     const baseInfoPostResult = await Api.submitWapRegBaseInfo(sendData);
            //
            //     if (baseInfoPostResult.isError) {
            //         this.$toast(baseInfoPostResult.errorMessage);
            //         // 老注册页qms上报逻辑
            //         reportError('新注册页(大表单H5),注册信息提交失败 |' + baseInfoPostResult.errorMessage + ' | ' + JSON.stringify(registerForm));
            //         return;
            //     }
            //
            //     this.modalVisible.msgCodeModal = true;
            //     reportLoveKibana('新注册路径手机前置',38,'手机验证页验证码弹窗-曝光');
            //
            // } finally {
            //     this.lockSubmit = false;
            // }
        },

        // 处理校验验证码相关
        async handleValidateCode(messageCode) {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;

            // Prototype.$gather.setAbtZaTTTCookie();
            // const sendData = Prototype.$gather.getValidateCodeParams(registerForm.phone.replace(/[^(\d)]/g, ""), messageCode, pageTypeChnMap[this.pageType]);
            const sendData = {
                phone: registerForm.phone.replace(/[^(\d)]/g, ""),
                messageCode,
                pageType: pageTypeChnMap[this.pageType],
                clickId: localStorage.getItem('AD_URL')
            }
            const result = await Api.checkMessageCodeAndReg(sendData);

            if (result.code == 0) {
                if(result.data.memberId == '-1') {
                    // this.handleSetRegMemberId(result.data.memberID);
                    this.$router.push({
                        path:`/register/0`
                    });
                    reportLoveKibana('新注册路径手机前置',3004,'验证码校验通过未生成珍爱ID');
                } else {
                    oCookie.set('token', result.data.token, '.zhenai.com' )
                    this.$router.push({
                        path:`/downApp`
                    });
                    reportLoveKibana('新注册路径手机前置',3003,'验证码校验通过生成珍爱ID');
                }
            } else {
                reportKibana(this.pageType, 31001, '异常情况处理', {
                    ext17: result && JSON.stringify(result),
                    ext18: sendData && JSON.stringify(sendData),
                    ext19: location.href,
                });
            }
            return result;
        },

        // 根据类型展示不同弹窗
        handleCheckOverrideAccount(validateAccountResult) {
            const { type, oldMemberID } = validateAccountResult;

            console.log(validateAccountResult);

            this.validateAccountResult = validateAccountResult;

            switch (type) {
            case registerResult.LOGIN_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.modalVisible.registerOverwriteModal = true;
                break;
            case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.modalVisible.registerOverwriteModal = true;
                break;
            case registerResult.NEW_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.NEW_ACCOUNT.label);
                break;
            case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.AUTO_OVERWRITE_ACCOUNT.label);
                break;
            }

            const resultType = z_.find(registerResult, {
                value: type
            });

            if (z_.get(resultType, 'label')) {
                reportKibana(this.pageType, 50, '手机号验证成功', {
                    ext17: resultType.label,
                });
            }
        },

        // 处理覆盖注册
        async handleOverwriteAccount() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = Prototype.$gather.getOverwriteAccountParams(this.messageCode, pageTypeChnMap[this.pageType]);
            const result = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            if(result.data.memberID) {
                this.handleSetRegMemberId(result.data.memberID);
            }

            this.modalVisible.registerOverwriteModal = false;
            this.handleFinishedRegister(registerResult.MANUAL_OVERWRITE_ACCOUNT.label);

        },

        handleFinishedRegister(ext) {
            reportKibana(this.pageType, 51, '注册成功并生成ID', {
                ext17: ext
            });
            this.$toast('注册成功');
            Storage.setItem('registerFinished', true);
            this.handleAfterRegisiter();
        },

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';


.common-submit {
    &__button {
        @include flex-center(row, center, center);
    }
    &__protocol {
        margin-top: 49px;
    }
    &__app-info {
        color: #FFFFFF;
        opacity: 0.6;
        font-size: 22px;
        line-height: 40px;
        padding: 40px 39px 0;
        text-align: center;
    }
}
</style>
