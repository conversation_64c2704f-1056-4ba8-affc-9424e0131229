import '@/common/bootstrap.js';
import Vue from 'vue';
import App from './app';
import router from "./router/index.js";
import store from "./store/index.js";
import Prototype from "@/common/framework/prototype";
import { reportKibana } from "@/common/utils/report";
import { storage as Storage } from "@/common/utils/storage";


Prototype.$gather.setAbtPageKeyCookie();
Vue.prototype.$report = (accessPoint, accessPointDesc, options) => {
    const resourceKey = Storage.getItem('resourceKey');
    return reportKibana(resourceKey, accessPoint, accessPointDesc, options);
};

new Vue({
    el: '#app',
    store,
    router,
    render: h => h(App)
});
