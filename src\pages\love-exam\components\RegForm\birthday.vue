<template>
    <div class="birthday">
        <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(name) in names"
                :key="name"
            >
                <h4 class="birthday-info-title">
                    {{ name }}后
                </h4>
                <div class="birthday-info-card">
                    <div
                        class="bic-num"
                        :class="{ active: item === curBirthday }"
                        v-for="item in birthdayV3[name]"
                        :key="item"
                        @click="goNext(item)"
                    >
                        <img
                            src="https://photo.zastatic.com/images/common-cms/it/20240521/1716282359883_359550.png"
                            alt=""
                            class="active_img"
                        >
                        {{ item }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>

import { storage as Storage } from "@/common/utils/storage";
import { getBirthdayDict } from "@/common/config/register-dictionary";
import { PAGE_TYPE } from "../../config";
import { keyToValue } from "../../utils/index";
import { setLocalRegisterForm } from '@/common/business/utils/localRegisterForm';
export default {
    name: "BirthdayV3",
    props: {
        pageType: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            birthdayV3: {},
            names: [],
            bannerMap: {
                0: 'https://photo.zastatic.com/images/common-cms/it/20240521/1716283591128_543653.png',
                1: 'https://photo.zastatic.com/images/common-cms/it/20240523/1716462021197_955963.png'
            },
            gender: 0,
            curBirthday: ''
        };
    },
    activated () {
        this.gender = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender;
    },
    created() {
        this.initBirthday();
        const {names, birthday} = getBirthdayDict({ bottom: 18, top: 99 });
        this.names = names;
        this.birthdayV3 = birthday;
    },
    methods: {
        initBirthday() {
            const curBirthday = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).birthday || "";
            let curYear = '';
            if (curBirthday) {
                curYear = keyToValue('birthday', curBirthday);
            }
            this.curBirthday = curYear;
        },
        goNext(val) {
            this.curBirthday = val;
            const dateValue = `${val}-01-01`;
            const params = {
                key: "birthday",
                value: dateValue,
                isMark: false,

            };
            this.$report(97, "出生年份页-按钮点击", {
                ext28: dateValue
            });
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(() => {
                this.lock = false;
                this.$emit('go-next');
            }, 300);
        }
    },
    mounted() {
        // this.$report(4, "出生年份页访问");
        this.$refs.birthScroll.scrollTo(0, 700);
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    margin-bottom: 40px;
    position: relative;

    .banner {
        width: 690px;
        height: 120px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

    }

    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }

    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }

    .birthday-scroll {
      overflow-y: scroll;
      margin-top: 90px;
      height: calc(100vh - 364px);
    }

    &-info {
        display: flex;
        &-title {
            text-wrap: nowrap;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            margin-left: 26px;
            font-weight: 600;
            font-size: 40px;
            color: #222833;

            &::after {
                content: " ";
                margin-top: 5px;
                height: 8px;
                width: 100%;
                background: #D7204A;
                border-radius: 6px;
            }
        }

        &-card {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding: 8px 6px;
            margin: 0 0px 40px 21px;
            border-radius: 60px;

            .bic-num {
                position: relative;
                border-radius: 45px;
                flex: 0 0 auto;
                display: block;
                color: #26273C;
                font-size: 28px;
                line-height: 62px;
                width: 128px;
                height: 60px;
                border-radius: 50px;
                text-align: center;
                margin-right: 12px;
                margin-bottom: 24px;
                border: 2px solid #B8BCCC;
            }

            .bic-num:nth-of-type(4n) {

                margin-right: 0;
            }

            .active_img {
                position: absolute;
                right: 29px;
                top: 0;
                transform: translateY(-50%);
                width: 28px;
                height: 28px;
                display: none;
            }

            .active {
                // background-image: linear-gradient(90deg, #D7204A 0%, #f06787 100%);
                background: #f06787;

                .active_img {
                }
            }
        }
    }
}
</style>
