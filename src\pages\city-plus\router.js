import VueRouter from "vue-router";
import Home from "./views/Home.vue";
import About from "./views/About.vue";
import Anim from "./views/Anim.vue";
import Success from "./views/Success.vue";
import Rcommd from "./views/Rcommd.vue";

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/home"
        },
        {
            path: "/home",
            name: 'home',
            component: Home
        },
        {
            path: '/about',
            name: 'about',
            component: About,
        },
        {
            path: '/anim',
            name: 'anim',
            component: Anim
        },
        {
            path: '/success',
            name: 'success',
            component: Success
        },
        {
            path: '/rcommd',
            name: 'rcommd',
            component: Rcommd
        }
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0, 0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
