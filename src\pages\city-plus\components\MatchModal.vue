<template>
    <van-popup
        class="match-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="match-modal__model">
            <img
                v-for="(item, index) in modelList"
                :key="index"
                :src="item.avatar"
            >
        </div>

        <div class="match-modal__title">
            <div>{{gender === 1 ? '他' : '她'}}们与你的匹配度高达：<span>{{ num }}%</span></div>
            <div>请前往应用市场搜索下载【珍爱APP】</div>
        </div>

        <div
            class="match-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

export default {
    name: 'MatchModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        modelList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        gender: {
            type: Number,
        }
    },

    computed: {
        num() {
            return (Math.random() + 98.5).toFixed(1);
        }
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.match-modal {
    padding-top: 303px;
    @include flex-center(column, null, center);
    width: 586px;
    height: 752px;
    background: url("https://photo.zastatic.com/images/common-cms/it/20220808/1659925772976_868094_t.png") no-repeat;
    background-size: 100% 100%;

    &__model {
        width: 504px;
        height: 88px;
        @include flex-center(row, space-between, null);
        img {
            width: 88px;
            height: 88px;
            border-radius: 44px;
            border: 3.2px solid #FFFFFF;
        }
    }

    &__title {
        margin-top: 30px;
        padding-top: 36px;
        width: 504px;
        height: 160px;
        background: rgba(255,255,255,0.77);
        color: #4828A2;
        text-align: center;
        border-radius: 22px;
        >div:nth-child(1) {
            font-weight: 500;
            font-size: 32px;
            span {
                color: #F16598;
            }
        }
        >div:nth-child(2) {
            margin-top: 26px;
            font-size: 24px;
        }
    }


    &__btn {
        margin-top: 32px;
        @include flex-center();
        width: 504px;
        height: 88px;
        font-size: 32px;
        background: #5368F0;
        color: #FFFFFF;
        border-radius: 77px;
    }
}
</style>
