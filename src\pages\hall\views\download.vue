<template>
    <love-page>
        <div class="download">
            <!-- 头图 -->
            <z-image
                :src="require('../assets/images/download-banner.png')"
                block
                :height="364"
            />
            
            <!-- 脱单执照 -->
            <div class="download-licence">
                <!-- html转的image -->
                <div
                    ref="refMask"
                    v-if="showMask"
                    class="download-licence__mask"
                    @click.self="zoomOut"
                />
                <img
                    ref="refLicenceImg"
                    v-show="isImgReady"
                    :src="dataUrl"
                    alt="脱单许可证"
                    class="download-licence__img"
                    :class="showMask ? 'download-licence__img-zoom' : ''"
                    @click="zoomIn"
                />
                
                <!-- 原html -->
                <div
                    v-show="!isImgReady"
                    ref="refLicenceHTML"
                    class="download-licence__content"
                >
                    <!-- 此部分需使用img标签，因为使用z-image的background生成图片时会模糊 -->
                    <img
                        class="download-licence__content__bg"
                        :src="require('../assets/images/download-licence.png')"
                        alt="脱单许可证"
                    />
                    <div class="download-licence__content__items">
                        <div class="download-licence__content__item">
                            恋爱体质：<span>{{ licenceData.label }}</span>
                        </div>
                        <div class="download-licence__content__item">
                            年纪轻轻，就：<span>{{ licenceData.money }}</span>
                        </div>
                        <div class="download-licence__content__item">
                            偏爱这样的Ta：<span>{{ licenceData.adore }}</span>
                        </div>
                        <div class="download-licence__content__item">
                            主营脱单业务：提供<span>{{licenceData.major}}</span>，不浪不渣一心爱Ta
                        </div>
                    </div>
                </div>

                <!-- 右上角label -->
                <z-image
                    class="download-licence__label"
                    :src="require('../assets/images/download-label.png')"
                    block
                    :width="176"
                    :height="176"
                />
            </div>

            <!-- 消息区域,底部渐现 -->
            <div 
                class="download-transition"
                ref="refTransition"
            >
                <z-image
                    class="download-message"
                    :src="require('../assets/images/download-message.png')"
                    :width="558"
                    :height="126"
                >
                    <random-avatar
                        :width="76"
                        :height="76"
                        :gender="registerForm.gender"
                    />
                    <div class="download-message__content">
                        距离你<span>1.3公里</span>的{{
                            (registerForm.gender === 0 ? "小姐姐" : "小哥哥")
                        }}想与你约会 <br />
                        并跟你打了招呼
                    </div>
                </z-image>
                <!-- 下载按钮 -->
                <z-image
                    class="download-button"
                    :src="require('../assets/images/download-button.png')"
                    :width="574"
                    :height="136"
                    block
                    @click="buttonClickDownload"
                />
            </div>
        </div>

        <download-guide-modal v-model="modalVisible" />
    </love-page>
</template>

<script>
import { mapState } from "vuex";
import { createPage } from "@/common/framework";
import html2canvas from "html2canvas";
import RandomAvatar from "../components/random-avatar.vue";
import DownloadGuideModal from "../components/modal/download-guide-modal";

const labelMap = [
        {
            key: "A",
            value: "高阶恋爱信号收藏家"
        },
        {
            key: "B",
            value: "直球率真的佛系恋人"
        },
        {
            key: "C",
            value: "勇敢稳重的轻熟伴侣"
        },
        {
            key: "D",
            value: "绝对靠谱的结婚人选"
        }
    ],
    moneyMap = ["收入潜力无限", "经济独立", "衣食无忧", "财富自由"],
    majorMap = [
        { key: "一起打球", value: "打球运动组局" },
        { key: "一起健身", value: "健身监督" },
        { key: "一起开黑", value: "开黑陪玩" },
        { key: "一起爬山", value: "爬山组局" },
        { key: "一起看电影", value: "看电影陪伴" },
        { key: "一起线上剧本杀", value: "线上剧本杀陪玩" },
        { key: "一起线上狼人杀", value: "线上狼人杀陪玩" },
        { key: "一起打卡咖啡店", value: "打卡咖啡店陪伴" },
        { key: "一起聊天半小时", value: "线上聊天" },
        { key: "一起玩线上游戏", value: "线上游戏陪玩" }
    ];

export default createPage({
    name: "Download",
    components: {
        RandomAvatar,
        DownloadGuideModal
    },
    visitReport: {
        accessPoint: 10,
        accessPointDesc: '结果页访问',
    },
    data() {
        return {
            isImgReady: false,
            lock: false,
            dataUrl: "",
            showMask: false,
            modalVisible: false,
            duration: 0
        };
    },
    computed: {
        ...mapState(["requirement", "registerForm", "cmsConfig"]),
        licenceData() {
            let moneyIndex = -1,
                label,
                major;

            switch (this.registerForm.salary) {
                case 3:
                case 4:
                    moneyIndex = 0;
                    break;
                case 5:
                case 6:
                    moneyIndex = 1;
                    break;
                case 7:
                case 8:
                    moneyIndex = 2;
                    break;
                case 9:
                    moneyIndex = 3;
            }

            labelMap.forEach(item => {
                if (item.key === this.requirement.selection) {
                    label = item.value;
                }
            });

            majorMap.forEach(item => {
                if (item.key === this.requirement.driftTag.text) {
                    major = item.value;
                }
            });

            return {
                label,
                money: moneyMap[moneyIndex],
                adore: this.requirement.tags.join("、"),
                major
            };
        }
    },
    methods: {
        buttonClickDownload() {
            this.$report(11, "结果页访问-回应按钮点击");
            this.goDownload();
        },
        goDownload() {
            if (this.cmsConfig.downloadStatus === 1) {
                this.$utils.handleDownload();
                return;
            }

            this.modalVisible = true;
        },
        start() {
            this.$router.push({
                path: "/questions/0"
            });
        },
        html2Image(dom) {
            const width = dom.offsetWidth,
                height = dom.offsetHeight,
                scale = window.devicePixelRatio;

            // scale默认为devicePixelRatio，扩大绘图区域以消除模糊
            html2canvas(dom, {
                width,
                height,
                scale,
                useCORS: true,
                logging: true
            }).then(canvas => {
                // 关闭抗锯齿形
                const context = canvas.getContext("2d");
                context.mozImageSmoothingEnabled = false;
                context.webkitImageSmoothingEnabled = false;
                context.msImageSmoothingEnabled = false;
                context.imageSmoothingEnabled = false;

                // canvas转化为图片
                this.dataUrl = this.canvas2Image(
                    canvas,
                    canvas.width,
                    canvas.height
                );
            });
        },
        canvas2Image(canvas, width, height) {
            const retCanvas = document.createElement("canvas");
            const retCtx = retCanvas.getContext("2d");
            retCanvas.width = width;
            retCanvas.height = height;
            retCtx.drawImage(canvas, 0, 0, width, height, 0, 0, width, height);
            const src = retCanvas.toDataURL("image/png"); // 可以根据需要更改格式
            return src;
        },
        zoomIn() {
            if(!this.showMask){
                this.$report(12, "结果页访问-证书点击");
            }
            
            this.showMask = true;
            document.body.addEventListener("touchmove", this.preventTouchmove, {
                passive: false
            });
        },
        zoomOut() {
            this.showMask = false;
            document.body.removeEventListener(
                "touchmove",
                this.preventTouchmove,
                { passive: false }
            );
        },
        preventTouchmove(e) {
            e.preventDefault();
        },
        checkRedirect() {
            const registerFinished = this.$storage.getItem("registerFinished");

            if (!registerFinished) {
                this.$router.push({
                    path: "/"
                });
                return;
            }

            this.$storage.removeItem("registerFinished");
            this.$storage.removeItem("cachedRequirement");
            this.$storage.removeItem("cachedHallFormItems");
            this.$storage.removeItem("cachedHallRegisterForm");
            this.$storage.removeItem("cachedBirthday");
            this.$storage.removeItem("cachedWorkCity");
        },
        handleLongPress(dom) {
            dom.addEventListener('touchstart',()=>{
                this.duration = new Date().getTime();
            });

            // 移动端使用touchcancel，因为touchend会被原生的图片下载调用给打断
            dom.addEventListener('touchcancel',()=>{
                this.duration = new Date().getTime() - this.duration;
                if(this.duration > 500){
                    this.$report(13, "结果页访问-证书-长按（调起保存）");
                }
            });
        },
    },
    mounted() {
        this.checkRedirect();

        // dom就绪后将dom转成img
        let licenceHTMLDom = this.$refs.refLicenceHTML,
            licenceImgDom = this.$refs.refLicenceImg;
        
        this.html2Image(licenceHTMLDom);

        // img就绪后替换dom
        licenceImgDom.onload = () => {
            // 渐变动效
            const timer = setTimeout(() =>{
                this.$refs.refTransition.style.opacity = 1;
                this.$refs.refTransition.style.left = 0;
                clearTimeout(timer);
            },300)
            this.isImgReady = true;
        };

        // 监听长按 打桩
        this.handleLongPress(licenceImgDom);
    }
});
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";
.download {
    padding-bottom: 70px;

    &-licence {
        position: relative;
        margin: -122px auto 0;
        width: 658px;
        height: 868px;

        &__mask {
            position: fixed;
            top: 0;
            left: 0;
            @include flex-center();
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: za-index($base-index, mask);
        }

        &__img {
            position: relative;
            width: 100%;
            background-size: contain;
            border-radius: 40px;
        }

        &__img-zoom {
            position: fixed;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 100vw;
            background-size: contain;
            z-index: za-index($base-index, modal);
        }

        &__content {
            width: 100%;
            height: 100%;
            &__bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

            &__items {
                position: relative;
                padding: 200px 80px 0;
                font-size: 26px;
                color: #504f8b;
            }

            &__item {
                margin-top: 38px;
                height: 36px;
                line-height: 36px;

                span {
                    padding: 0 8px;
                    font-weight: 700;
                    color: #fe4f06;
                    text-decoration: underline;
                }
            }
        }

        &__label {
            position: absolute;
            top: -102px;
            right: -26px;
        }
    }

    &-transition {
        position: relative;
        left: -750px;
        opacity: 0;
        transition: all 2s;
    }


    &-message {
        margin: 30px auto 0;
        width: 554px;
        height: 122px;
        border: 14px;
        @include flex-center();

        &__content {
            margin-left: 28px;
            height: 84px;
            font-size: 26px;
            line-height: 42px;

            span {
                text-decoration: underline;
            }
        }
    }

    &-button {
        position: relative;
        margin: 0 auto;
        animation: shake 1s 3s linear infinite;

        @keyframes shake {
            0%{
                left: 0;
            }
            25% {
                left: -20px;
            }
            50% {
                left: 0;
            }
            75% {
                left: 20px;
            }
            100% {
                left: 0;
            }
        }

        
    }
}
</style>

