<template>
    <div class="gender-picker-wrapper">
        <div class="gender-picker-wrapper__panel">
            <div
                v-for="(item, index) in list"
                :key="index"
                class="gender-picker-wrapper__panel-item"
                @click="handleSelect(item.value)"
            >
                <z-image
                    :src="item.url"
                    :width="200"
                    :height="200"
                />
                <z-image
                    v-show="gender === item.value"
                    class="gender-picker-wrapper__panel-item-active"
                    src="https://photo.zastatic.com/images/common-cms/it/20220530/1653905818700_47123_t.png"
                    :width="200"
                    :height="200"
                />
                <div class="gender-picker-wrapper__panel-item-text">
                    {{ item.label }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { storage as Storage } from "@/common/utils/storage";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";

export default {
    name: 'GenderPicker',
    inject: ['cmsConfig'],
    data() {
        return {
            list: [
                {
                    active: false,
                    label: "女生",
                    value: 1,
                    url: 'https://photo.zastatic.com/images/common-cms/it/20220530/1653905865925_553953_t.png'
                },
                {
                    active: false,
                    label: "男生",
                    value: 0,
                    url: 'https://photo.zastatic.com/images/common-cms/it/20220530/1653905875252_682561_t.png'
                }
            ],
            gender: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) ? Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).gender : ''
        };
    },
    mounted() {
        this.$report(5, '性别页访问');
    },
    methods: {
        handleSelect(value) {
            const params = {
                key: "gender",
                value
            };
            // 回显
            this.gender = value;
            this.$report(6, '性别页-按钮点击');
            setLocalRegisterForm(params, this.cmsConfig.planName);
            setTimeout(() => {
                this.$router.push({
                    path: "/collection/2"
                });
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.gender-picker-wrapper {
    width: 690px;
    height: 887px;
    margin: 80px auto 0px;
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20220530/1653892927104_524494_t.png");
    &__panel {
        width: 540px;
        margin: 0 auto;
        padding-top: 240px;
        @include flex-center(row, space-between, center);
        &-item {
            position: relative;

            &-active {
                position: absolute !important;
                left: 0px;
                top: 0px;
            }

            &-text {
                margin-top: 52px;
                font-weight: 600;
                font-size: 48px;
                text-align: center;
                color: #26273C;
            }
        }
    }
}
</style>

