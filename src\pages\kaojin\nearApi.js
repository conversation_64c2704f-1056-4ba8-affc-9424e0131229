import { formatNearAxios } from "./lib/index";

export const registerDict = (data) => {
    return formatNearAxios("/system/getDictionary.do",  data, 'post', true, false,{'Content-Type': 'application/x-www-form-urlencoded'});
};
export const getCode = (data) => {
    return formatNearAxios("/sms/getCode.do",  data, 'post', true, false,{'Content-Type': 'application/x-www-form-urlencoded'});
};
export const login = (data) => {
    return formatNearAxios("/user/login.do",  data, 'post', true, false,{'Content-Type': 'application/x-www-form-urlencoded'});
};
export const register = (data) => {
    return formatNearAxios("/user/register.do",  data, 'post', true, false,{'Content-Type': 'application/x-www-form-urlencoded'});
};