import VueRouter from "vue-router";

import BlindInfo from "../views/BlindInfo.vue";
import WeixinInfo from "../views/WeixinInfo.vue";
import RegisterInfo from "../views/RegisterInfo.vue";
import EndorsementInfo from "../views/EndorsementInfo.vue";
import RecomInfo from "../views/RecomInfo.vue";
import Collection from "../views/Collection.vue";
import Home from "../views/Home.vue";

const router = new VueRouter({
    mode: "hash",
    routes: [
        // {
        //     path: "/",
        //     redirect: "/index"
        // },
        {
            path: "/index",
            component: Collection
        },
        {
            path: "/blindinfo",
            component: BlindInfo
        },
        {
            path: "/weixininfo",
            component: WeixinInfo
        },
        {
            path: "/registerinfo",
            component: RegisterInfo
        },
        {
            path: '/endorsementinfo',
            component: EndorsementInfo
        },
        {
            path: '/recominfo',
            component: RecomInfo
        },
        {
            path: "/home",
            component: Home
        }
    ],
    scrollBehavior() {
        return { x: 0, y: 0 };
    }
});

export default router;
