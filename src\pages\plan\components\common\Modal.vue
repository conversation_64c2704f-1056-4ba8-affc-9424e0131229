<template>
    <div
        class="modal-wrapper"
        ref="refWrapper"
    >
        <div
            class="modal-mask"
            @click="closeModal"
        ></div>

        <!-- 验证码弹窗 -->
        <div
            class="modal-content modal-validate"
            v-if="modalType === 'modalValidate'"
            ref="refValidate"
        >
            <div class="modal-content__title">
                验证码已通过短信发到您的手机
            </div>
            <div class="modal-validate__phone">
                {{ registerInfo.phone }}
            </div>
            <div class="modal-validate__row">
                <!-- UI为4个框 -->
                <div class="modal-validate__inputs">
                    <div
                        v-for="(item, index) in 4"
                        :key="index"
                        class="modal-validate__inputs__item"
                    ></div>
                </div>
                <!-- 实际为1个框 -->
                <input
                    ref="refCode"
                    type="tel"
                    v-model="code"
                    class="modal-validate__input"
                    maxlength="4"
                    @input="checkCode"
                    pattern="[0-9]*"
                    autocomplete="new-password"
                />
            </div>

            <div
                v-if="showWarning"
                class="modal-validate__warning"
            >
                {{ warningMessage }}
            </div>
            <button
                class="modal-content__button--submit"
                :style="{ background: isValidating ? '#B5B6BA' : '#767dff' }"
                @click="resendValidateCode"
            >
                {{ validateSubmit }}
            </button>
            <button
                class="modal-content__button--cancel"
                @click="closeModal"
            >
                取消
            </button>
        </div>

        <!-- 引导下载弹窗 -->
        <div
            class="modal-content modal-download"
            v-if="modalType === 'modalDownload'"
            ref="refDownload"
        >
            <div
                class="modal-content__title"
                :style="{
                    width: +this.cmsConfig.channelType === 1 ? '5.43rem' : ''
                }"
            >
                {{ downloadTitle }}
            </div>
            <div class="modal-download__subtitle">
                快来与Ta相遇，收获您的爱情吧！
            </div>
            <button
                v-if="+this.cmsConfig.channelType === 1"
                class="modal-content__button--submit"
                @click="closeModal(401, '引导去市场的弹窗-按钮点击')"
            >
                好的
            </button>
            <template v-else>
                <button
                    class="modal-content__button--submit"
                    @click="goDownloadAnyway(401, '引导去市场的弹窗-按钮点击')"
                >
                    立即约会
                </button>
                <button
                    class="modal-content__button--cancel"
                    @click="closeModal()"
                >
                    取消
                </button>
            </template>
        </div>

        <!-- 引导弹窗 -->
        <div
            class="modal-content modal-protocol modal-guide"
            v-if="modalType === 'modalGuide'"
            ref="refGuide"
        >
            <div class="modal-protocol__subtitle">
                <div class="modal-protocol__headtitle modal-guide_title">
                    下载珍爱APP
                </div>
            </div>
            <button
                class="modal-content__button--submit"
                @click="confirmProtocol"
            >
                复制珍爱网
            </button>
            <button
                class="modal-content__button--cancel"
                @click="closeModal(602, '同意协议弹窗-取消按钮点击')"
            >
                取消
            </button>
        </div>

        <!-- 协议弹窗 -->
        <div
            class="modal-content modal-protocol"
            v-if="modalType === 'modalProtocol'"
            ref="refProtocol"
        >
            <div class="modal-protocol__subtitle">
                <div class="modal-protocol__headtitle">
                    温馨提示
                </div>
                <div class="modal-protocol__title">
                    已阅读并同意
                    <span @click="goUrl(1)">《珍爱网服务协议》</span>
                    和
                    <span @click="goUrl(2)">《个人信息保护政策》</span>
                </div>
            </div>
            <button
                class="modal-content__button--submit"
                @click="confirmProtocol"
            >
                确认
            </button>
            <button
                class="modal-content__button--cancel"
                @click="closeModal(602, '同意协议弹窗-取消按钮点击')"
            >
                取消
            </button>
        </div>

        <!-- 计算方法弹窗 -->
        <div
            class="modal-content modal-method"
            v-if="modalType === 'modalMethod'"
        >
            <div class="modal-method__title">
                脱单计划数据37%法制计算方法
            </div>
            <ul
                class="modal-method__list"
                ref="refMethod"
            >
                <li>
                    <i class="list_number1"></i>
                    <span class="list_text_type1">
                        对于某个固定的 k，如果最适合的人出现在了第 i 个位置（k &lt; i ≤
                        n），要想让他有幸正好被 MM 选中，就必须得满足前i-1
                        个人中的最好的人在前 k 个人里，这有 k/(i-1) 的可能。
                    </span>
                </li>
                <li>
                    <i class="list_number2"></i>
                    <span class="list_text_type2">
                        考虑所有可能的 i，我们便得到了试探前 k
                        个男生之后能选中最佳男生的总概率 P(k)：
                    </span>
                    <i class="list_number2_formula2"></i>
                </li>
                <li>
                    <i class="list_number3"></i>
                    <span class="list_text_type2">
                        用 x 来表示 k/n 的值，并且假设 n 充分大，则上述公式可以写成：
                    </span>
                    <i class="list_number3_formula3"></i>
                </li>
                <li>
                    <i class="list_number4"></i>
                    <span class="list_text_type1">
                        对&nbsp;-x&nbsp;&nbsp;·&nbsp;&nbsp;ln x
                        求导，并令这个导数为0，可以解出 x
                        的最优值，它就是欧拉研究的神秘常数的倒数—— 1/e ！
                        <i class="textBr"></i>
                        也就是说，如果你预计求爱者有 n 个人，你应该先拒绝掉前 n/e
                        个人，静候下一个比这些人都好的人。假设你一共会遇到大概 30
                        个求爱者，就应该拒绝掉前 30/e ≈ 30/2.718 ≈ 11 个求爱者，然后从第 12
                        个求爱者开始，一旦发现比前面 11 个求爱者都好的人，就果断接受他。由于
                        1/e 大约等于 37%，因此这条爱情大法也叫做 37% 法则。
                    </span>
                </li>
            </ul>
            <div class="button_mask"></div>
            <button
                class="modal-content__button--confirm"
                @click="closeModal(201, '脱单计算公式弹窗点击知道按钮')"
            >
                知道了
            </button>
        </div>

        <!-- 已注册弹窗 -->
        <div
            class="modal-content modal-registered"
            v-if="modalType === 'modalRegistered'"
            ref="refRegistered"
        >
            <div class="modal-registered__banner"></div>
            <div class="modal-content__title">
                该手机已在珍爱网APP注册过！
            </div>
            <button
                v-if="modalParam.showCoverButton"
                class="modal-registered__button"
                @click="goCoverRegister"
            >
                继续<span>(原有账号的资料将被覆盖)</span>
            </button>
            <button
                class="modal-content__button--submit"
                @click="goDownload"
            >
                登录原有账号
            </button>
            <button
                class="modal-content__button--cancel"
                @click="closeModal"
            >
                取消
            </button>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from "vuex";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { _sendWapMessageCode } from "../../js/api.js";
import { ABT_NEED_BLIND } from "../../js/const.js";
import {
    IS_SAPP,
    channelId,
    subChannelId,
    PROTOCOL
} from "@/common/js/const.js";
import { reportKibana } from "@/common/utils/report.js";
import { ajax } from "@/common/utils/ajax.js";
import oUserSelect from "@/common/ocpx/huichuan.js";
import borderStopScroll from "@/common/utils/borderStopScroll.js";

export default {
    props: {
        modalType: {
            type: String,
            required: true
        },
        modalParam: {
            type: Object
        }
    },
    components: {},
    data() {
        return {
            inputValueList: ["", "", "", ""],
            code: "",
            showWarning: false,
            warningMessage: "",
            validateSubmit: "获取验证码",
            isValidating: false,
            downloadTitle: "",
            backTimer: null,
            leaveTime: null,
            backTime: null,
            lockValidate: false
        };
    },
    computed: {
        ...mapState([
            "registerInfo",
            "isCover",
            "cmsConfig",
            "overwriteRegistrationSwitch",
            "materialId"
        ]),
        ...mapGetters(["getNormalPhone"])
    },
    created() {
        if (this.modalType === "modalMethod") {
            reportKibana("脱单计划H5", 200, "脱单计算公式弹窗访问", {
                ext16: this.materialId
            });
        } else if (this.modalType === "modalDownload") {
            reportKibana("脱单计划H5", 400, "引导去市场的弹窗访问", {
                ext16: this.materialId
            });
        } else if (this.modalType === "modalProtocol") {
            reportKibana("脱单计划H5", 600, "同意协议弹窗访问", {
                ext16: this.materialId
            });
        }
    },
    mounted() {
        this.downloadTitle =
            +this.cmsConfig.channelType === 1
                ? "请前往各大应用市场搜索 “珍爱网”下载珍爱APP"
                : "下载珍爱APP使用此功能";

        // 处理滚动穿透
        borderStopScroll({
            wrapEle: this.$refs.refWrapper
        });

        // 所有弹窗此处统一处理，新增弹窗类型时此处需补充
        borderStopScroll({
            wrapEle: this.$refs.refValidate
        });
        borderStopScroll({
            wrapEle: this.$refs.refDownload
        });
        borderStopScroll({
            wrapEle: this.$refs.refProtocol
        });
        borderStopScroll({
            wrapEle: this.$refs.refRegistered
        });
        borderStopScroll({
            wrapEle: this.$refs.refMethod
        });
    },
    methods: {
        ...mapMutations(["setRegMemberId"]),
        // 限制滚动, 有bug
        limitScroll() {
            document.documentElement.style.overflow = "hidden";
            document.body.style.overflow = "hidden";
        },
        // 取消限制滚动
        unlimitScroll() {
            document.documentElement.style.overflow = "visible";
            document.body.style.overflow = "auto";
        },
        countDown() {
            // 从60s开始倒计时
            this.validateSubmit = 60;

            // 锁，倒计时期间不能再点击
            this.isValidating = true;

            let timer = setInterval(() => {
                this.validateSubmit -= 1;
                if (this.validateSubmit <= 0) {
                    this.validateSubmit = "获取验证码";
                    this.isValidating = false;
                    clearInterval(timer);
                }
            }, 1000);

            // 如果已经有监听，无需重复绑定
            if (this.backTimer) {
                return;
            }

            this.backTimer = document.addEventListener("visibilitychange", () => {
                // 用户进入后台
                if (document.visibilityState === "hidden") {
                    this.leaveTime = new Date().getTime();
                } else if (document.visibilityState === "visible") {
                    this.backTime = new Date().getTime();
                    let diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                    // 在后台期间已经超过当前的剩余秒数
                    if (
                        diff > this.validateSubmit ||
                        this.validateSubmit === "获取验证码"
                    ) {
                        this.validateSubmit = "获取验证码";
                        this.isValidating = false;
                        clearInterval(timer);
                    } else {
                        // 否则将后台期间的差值计入倒计时
                        this.validateSubmit -= diff;
                    }
                }
            });
        },
        confirmProtocol() {
            reportKibana("脱单计划H5", 601, "同意协议弹窗-同意按钮点击", {
                ext16: this.materialId
            });
            this.$emit("ok");
        },
        checkCode() {
            this.code = this.code.replace(/[^\d]/g, "");

            if (this.code.length === 4) {
                this.$refs.refCode.blur();
                this.getValidate(this.code);
            }
        },
        checkInputValue(event) {
            // 获取当前input框
            let currentTarget = event.target,
                currentIndex = +currentTarget.dataset.index;

            // 限制只能输入数字
            this.inputValueList[currentIndex] = this.inputValueList[
                currentIndex
            ].replace(/[^\d]/g, "");

            // 非最后一个框,且当前框填了数字，输入后自动跳转下一个框
            if (currentIndex !== 3 && this.inputValueList[currentIndex] !== "") {
                let nextTarget = currentTarget.parentNode.nextSibling.firstChild;
                if (!nextTarget.value) {
                    nextTarget.focus();
                }
            }

            // 非第一个框，删除时自动回退到上一个框
            if (currentIndex !== 0 && event.key === "Backspace") {
                let preTarget = currentTarget.parentNode.previousSibling.firstChild;
                // if(!nextTarget.value && currentIndex !== 0){
                //     preTarget = preTarget.parentNode.previousSibling.firstChild;
                // }
                preTarget.focus();
            }

            if (this.inputValueList.findIndex(item => item === "") === -1) {
                // 确保输完四个数字，即遍历数字数组找不到''时(findIndex返回-1)，调用验证码接口
                let fullCode = "";
                this.inputValueList.forEach(item => {
                    fullCode = fullCode + item;
                });

                this.getValidate(fullCode);
            }
        },
        async resendValidateCode() {
            if (this.lockValidate) {
                return;
            }

            if (this.isValidating) {
                console.log(`已发送验证请求，请${this.validateSubmit}秒后重试`);
                return;
            }

            // 清空警告
            this.showWarning = false;

            // 锁
            this.lockValidate = true;
            // 重新发送验证码
            let resData = await _sendWapMessageCode({
                phone: this.getNormalPhone,
                type: 0
            });
            this.lockValidate = false;

            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }
            this.$toast(resData.data.msg);
            this.countDown();
        },
        jump(path) {
            this.$router.push({
                path,
                query: {
                    // plan:123
                }
            });
        },
        // 核对验证码
        async getValidate(fullCode) {
            let bd_vid = "";
            let uc_vid = "";
            let tt_vid = "";
            let qh_vid = "";
            // 爱奇艺投放，需要传给后端的参数
            let iqiyi_id = "";
            let iqiyi_params = "";

            try {
                bd_vid = Z.getParam("bd_vid");
                uc_vid = Z.getParam("uctrackid");
                tt_vid = Z.getParam("adid");
                qh_vid = Z.getParam("qhclickid");
                iqiyi_id = Z.getParam("impress_id");
            } catch (e) {
                console.log(e);
            }

            // 新注册页没有落地页，此处的landingUrl为由新注册页生成的abt URL
            // let landingUrl = document.referrer || undefined;   // 旧逻辑
            let landingUrl = location.href.replace(location.hash, ""); // 新逻辑

            if (bd_vid || uc_vid || tt_vid) {
                try {
                    // 当拿不到referrer时，手动拼接落地页Url，以减少后台数据与注册数据的误差
                    if (!landingUrl) {
                        var pageKey = Z.getParam("pageKey");
                        var za_ttt = encodeURIComponent(Z.getParam("za_ttt"));
                        // link.do情况
                        if (pageKey && za_ttt) {
                            if (bd_vid) {
                                landingUrl =
                                    PROTOCOL +
                                    "//a.zhenai.com/abt/link.do?channelId=" +
                                    channelId +
                                    "&subChannelId=" +
                                    subChannelId +
                                    "&pageKey=" +
                                    pageKey +
                                    "&za_ttt=" +
                                    za_ttt +
                                    "&bd_vid=" +
                                    encodeURIComponent(bd_vid);
                            }
                            if (uc_vid) {
                                landingUrl =
                                    PROTOCOL +
                                    "//a.zhenai.com/abt/link.do?channelId=" +
                                    channelId +
                                    "&subChannelId=" +
                                    subChannelId +
                                    "&pageKey=" +
                                    pageKey +
                                    "&za_ttt=" +
                                    za_ttt +
                                    "&uctrackid=" +
                                    encodeURIComponent(uc_vid);
                            }
                            if (tt_vid) {
                                var ttParams = {
                                    adid: tt_vid,
                                    creativeid: Z.getParam("creativeid"),
                                    creativetype: Z.getParam("creativetype"),
                                    clickid: Z.getParam("clickid")
                                };
                                landingUrl =
                                    PROTOCOL +
                                    "//a.zhenai.com/abt/link.do?channelId=" +
                                    channelId +
                                    "&subChannelId=" +
                                    subChannelId +
                                    "&pageKey=" +
                                    pageKey +
                                    "&za_ttt=" +
                                    za_ttt +
                                    "&adid=" +
                                    ttParams.adid +
                                    "&creativeid=" +
                                    ttParams.creativeid +
                                    "&creativetype=" +
                                    ttParams.creativetype +
                                    "&clickid=" +
                                    ttParams.clickid;
                            }
                            // from.do情况,什么情况会生成from.do？
                        } else {
                            if (bd_vid) {
                                landingUrl =
                                    PROTOCOL +
                                    "//a.zhenai.com/abt/from.do?channelId=" +
                                    channelId +
                                    "&subChannelId=" +
                                    subChannelId +
                                    "&pageKey=" +
                                    pageKey +
                                    "&bd_vid=" +
                                    encodeURIComponent(bd_vid);
                            }
                        }
                    }
                } catch (e) {
                    console.error(e);
                }
            }

            let postUrl = PROTOCOL + "//api.zhenai.com/register/validatePhoneCode.do";
            if (this.isCover) {
                // 覆盖注册
                postUrl = PROTOCOL + "//api.zhenai.com/register/validAndRegNew.do";
            }

            if (landingUrl) {
                if (bd_vid) {
                    // 原逻辑
                    // var params = '?' + $.param({ landingUrl: landingUrl });
                    // postUrl += params;

                    // 修改后
                    postUrl += `?landingUrl=${encodeURIComponent(landingUrl)}`;

                    Z.cookie.set(
                        "bd_url",
                        encodeURIComponent(landingUrl),
                        ".zhenai.com",
                        "",
                        1
                    );
                }
                if (uc_vid) {
                    Z.cookie.set(
                        "uc_link",
                        encodeURIComponent(landingUrl),
                        ".zhenai.com",
                        "",
                        1
                    ); // 文档要求落地页链接进行url编码
                }
                if (tt_vid) {
                    Z.cookie.set(
                        "toutiao_url",
                        encodeURIComponent(landingUrl),
                        ".zhenai.com",
                        "",
                        1
                    );
                }
            }

            if (qh_vid) {
                // 奇虎只需传广告id，不需要传url
                Z.cookie.set("qhclickid", qh_vid, ".zhenai.com", "", 1);
            }

            // 关于提交验证并上报给后端的接口信息
            let sendData = {},
                sendType = "",
                sendUrl = "";

            // 当为爱奇艺投放落地页进来时，给予后端不同的数据(注意：覆盖时不用给予)
            // 接口地址：http://yapi.zhenaioa.com/project/171/interface/api/7511
            if (iqiyi_id && this.isCover === false) {
                sendType = "POST";
                sendUrl = PROTOCOL + "//api.zhenai.com/register/validatePhoneCode.do";
                sendData.messageCode = fullCode;
                iqiyi_params = this.parseQueryString(location.href);
                sendData.advReportJsonString = JSON.stringify(iqiyi_params);
            } else {
                sendType = "GET";
                sendUrl = postUrl;
                sendData.messageCode = fullCode;
                sendData.bdVid = bd_vid;
                sendData.qywxId = Z.getParam("qywxId"); // 企业微信渠道
                // 添加爱奇艺，覆盖注册时候的上报
                if (iqiyi_id) {
                    iqiyi_params = this.parseQueryString(location.href);
                    sendData.advReportJsonString = JSON.stringify(iqiyi_params);
                }
            }

            let resData = await ajax({
                type: sendType,
                data: sendData,
                customUrl: sendUrl
            });

            if (resData.isError) {
                // 显示错误信息
                if (resData.errorCode === "-8002004") {
                    this.warningMessage = resData.errorMessage;
                } else if (resData.errorCode === "-8002005") {
                    this.warningMessage = "验证码错误，请重新输入";
                } else if (resData.errorCode === "-8002006") {
                    this.warningMessage = "验证码已过期";
                }
                this.showWarning = true;

                // 清空输入框，优化前
                // this.inputValueList.forEach((item,index)=>{
                //     Vue.set(this.inputValueList,index,'')
                // })

                // 优化后
                this.code = "";

                //定位到第一个输入框，优化前
                // document.getElementsByClassName("modal-validate__inputs__item")[0].firstChild.focus();

                // 优化后
                this.$refs.refCode.focus();

                return;
            }

            // 核对成功
            this.$toast(resData.data.msg);

            // 设置注册态
            localStorage.setItem("flagFilled", "1");

            // 新注册页打桩
            // if (this.isCover) {
            //     reportKibana("导量H5大表单翻牌", 4, "注册成功并生成ID", {
            //         ext16: 1, // 1 投放版 2 达人版
            //         ext17: 2 //覆盖注册
            //     });
            // } else {
            //     reportKibana("导量H5大表单翻牌", 4, "注册成功并生成ID", {
            //         ext16: 1, // 1 投放版 2 达人版
            //         ext17: 1 //新注册
            //     });
            // }

            // 老注册页打桩
            // let EXT5 = 1;
            // if (this.isCover) {
            //     tongji(43, "注册成功并生成ID", {
            //         ext2: resData.data.memberID,
            //         ext5: EXT5
            //     });
            //     tongji(44, "验证成功(覆盖资料)", {
            //         ext2: resData.data.memberID,
            //         ext5: EXT5
            //     });
            // } else {
            //     tongji(44, "验证成功", {
            //         ext2: resData.data.memberID,
            //         ext5: EXT5
            //     });
            // }

            // OCPX相关
            // 统计百度信息流回传数据，以排查数据误差
            if (bd_vid) {
                Z.tj.kibana({
                    resourceKey: "注册页百度信息流api回传",
                    ext10: bd_vid,
                    ext11: document.referrer || "0",
                    ext12: landingUrl,
                    ext13: Z.platform.isIos ? "ios" : "android",
                    ext14: window.navigator.userAgent
                });
            }

            // 统计uc信息流回传数据，以排查数据误差
            if (uc_vid) {
                Z.tj.kibana({
                    resourceKey: "注册页uc汇川api回传",
                    ext10: uc_vid,
                    ext11: document.referrer || "0",
                    ext12: landingUrl,
                    ext13: Z.platform.isIos ? "ios" : "android",
                    ext14: window.navigator.userAgent
                });
            }

            let memberID = resData.data.memberID;

            if (IS_SAPP) {
                // 清除注册相关的信息
                // oUserInfo.clear();

                // Z.client.invoke('ui', 'login', {
                //     id: memberID,
                //     password: userLogin.globalPwd
                // });
                return false;
            }

            if (memberID) {
                this.setRegMemberId(memberID);
                // 记录ocpc回传状态
                oUserSelect.mark({
                    msgValid: true
                });
            }

            // 微博ocpc回传 - start
            var reportIdMap = {
                "913271": "207911",
                "913272": "207912",
                "913273": "207922",
                "913274": "207924",
                "913275": "207925"
            };
            try {
                if (reportIdMap[channelId]) {
                    window.wbadmt &&
                        typeof window.wbadmt.send === "function" &&
                        window.wbadmt.send({
                            eventid: reportIdMap[channelId],
                            eventtype: "form",
                            eventvalue: ""
                        });
                }
            } catch (e) {
                console.log(e);
            }

            this.closeModal();

            // 跳转详情页（AB测试 1普通流程 2盲盒流程）
            if (ABT_NEED_BLIND === 1) {
                this.jump("info");
            } else if (ABT_NEED_BLIND === 2) {
                this.jump("blindinfo");
            }
        },
        parseQueryString(url) {
            if (url.indexOf("#") !== -1) {
                url = url.split("#")[0];
            }
            var obj = {};
            var keyvalue = [];
            var key = "",
                value = "";
            var paraString = url
                .substring(url.indexOf("?") + 1, url.length)
                .split("&");
            for (var i in paraString) {
                keyvalue = paraString[i].split("=");
                key = keyvalue[0];
                value = keyvalue[1];
                if (key) {
                    obj[key] = value;
                }
            }
            return obj;
        },
        closeModal(accessPoint, description) {
            // this.unlimitScroll();
            if (accessPoint && description) {
                reportKibana("脱单计划H5", accessPoint, description, {
                    ext16: this.materialId
                });
            }

            this.$emit("close-modal");
        },
        goUrl(type) {
            if (type === 1) {
                location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
            } else if (type === 2) {
                location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
            }
        },
        // 跳转/下载APP
        goDownload() {
            // SAPP
            if (IS_SAPP) {
                this.closeModal();
                Z.client.invoke("ui", "logout", { canBack: true });
                return;
            }

            if (this.isCover) {
                reportKibana("脱单计划H5", 311, "询问覆盖弹窗-登录按钮", {
                    ext16: this.materialId
                });
                // tongji(311, '询问覆盖弹窗登录按钮点击');
            } else {
                reportKibana("脱单计划H5", 314, "引导登录弹窗-登录按钮", {
                    ext16: this.materialId
                });
                // tongji(314, '引导登录弹窗登录按钮点击');
            }

            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            if (this.overwriteRegistrationSwitch) {
                visibilityChangeDelay(function() {
                    downloadApp();
                }, 500);
                openApp();
                return;
            }

            // 【兜底】：跳wap登录页
            window.location.href = `${
                location.protocol
            }//i.zhenai.com/m/portal/login.html`;
        },
        // 跳转/下载APP
        goDownloadAnyway(accessPoint, description) {
            // 打桩
            if (accessPoint && description) {
                reportKibana("脱单计划H5", accessPoint, description, {
                    ext16: this.materialId
                });
            }

            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                downloadApp();
            }, 500);
            openApp();
        },
        // 覆盖注册
        goCoverRegister() {
            reportKibana("脱单计划H5", 312, "询问覆盖弹窗-覆盖注册按钮", {
                ext16: this.materialId
            });
            // tongji(312, "询问覆盖弹窗重新注册按钮点击");
            this.closeModal();
            this.$parent.coverRegister();
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
// 弹窗共享样式
.modal-mask {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  // margin: auto;
  background: rgba($color: #26273c, $alpha: 0.6);
  z-index: 100;
}

.modal-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32px;
  z-index: 999;
}

.modal-content__title {
  margin: 48px auto 0;
  font-size: 36px;
  font-weight: 700;
  color: #26273c;
  text-align: center;
  line-height: 54px;
}

.modal-content__button--disabled {
  background: #6c6d75;
  opacity: 0.5;
}

.modal-content__button--submit {
  display: block;
  margin: 48px auto 0;
  width: 462px;
  height: 88px;
  background: #279bae;
  border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 88px;
}

.modal-content__button--cancel {
  display: block;
  margin: 20px auto 0;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  text-align: center;
  line-height: 47px;
}

// 验证弹窗
.modal-validate {
  width: 600px;
  height: 625px;
}

.modal-validate__phone {
  margin-top: 16px;
  font-size: 32px;
  font-weight: 400;
  color: #92939d;
  text-align: center;
  line-height: 47px;
}

.modal-validate__row {
  position: relative;
}

.modal-validate__inputs {
  @include set-flex(space-between, center);
  margin-top: 46px;
  margin-bottom: 81px;
  padding: 0 72px;
}

.modal-validate__inputs__item {
  position: relative;
  width: 102px;
  height: 130px;
  background: #eaebec;
  border-radius: 55px;

  // input{
  //     position: absolute;
  //     left: 50%;
  //     top: 50%;
  //     transform: translate(-50%,-50%);
  //     width: 22px;
  //     height: 43px;
  //     font-size: 29px;
  //     font-weight: 400;
  //     background: #EAEBEC;
  //     color: #26273C;
  //     line-height: 43px;
  // }
}

.modal-validate__input {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding-left: 116px;
  height: 60px;
  width: 600px;
  font-size: 30px;
  line-height: 30px;
  background: transparent;
  letter-spacing: 101px;
  // -webkit-appearance:none;
  // border: 1px solid ;
}

// input:-webkit-autofill {
// background-color: transparent;
// background-image: none;

// color: #000;
// box-shadow:0 0 0px 1000px transparent inset !important;
// -webkit-text-fill-color: #333333;
// }

// iphone12 自动输入验证码黄色背景的兼容
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  // -webkit-transition: background-color 99999s ease-out;
}

.modal-validate__warning {
  position: absolute;
  top: 377px;
  font-size: 28px;
  font-weight: 400;
  color: #f04086;
  width: 100%;
  text-align: center;
  line-height: 41px;
}

// 引导下载弹窗
.modal-download {
  width: 602px;
  height: 424px;
}

.modal-download__subtitle {
  margin-top: 28px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  line-height: 47px;
  text-align: center;
}

// 计算方法弹窗
.modal-method {
  width: 558px;
  height: 848px;
  overflow: hidden;
}

.modal-method__title {
  margin: 0 auto;
  width: 558px;
  height: 194px;
  line-height: 130px;
  font-size: 32px;
  font-weight: 500;
  text-align: center;
  margin-bottom: -65px;
  color: #26273c;
  @include set-img("../../assets/images/banner-modal.png");
}

.modal-method__list {
  position: relative;
  width: 100%;
  height: 703px;
  padding-bottom: 106px;
  overflow-y: scroll;

  li {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 25px;
    margin-bottom: 32px;

    i {
      width: 32px;
      height: 32px;
      transform: translateY(5px);
    }

    .list_number1 {
      @include set-img("../../assets/images/icon-num1.png");
    }

    .list_number2 {
      @include set-img("../../assets/images/icon-num2.png");
    }

    .list_number3 {
      @include set-img("../../assets/images/icon-num3.png");
    }

    .list_number4 {
      @include set-img("../../assets/images/icon-num4.png");
    }

    .list_text_type1 {
      width: 466px;
      font-size: 28px;
      color: #6c6d75;
      line-height: 42px;

      .textBr {
        display: block;
        height: 0;
        margin-bottom: 32px;
      }
    }

    .list_text_type2 {
      width: 466px;
      font-size: 28px;
      font-weight: 500;
      color: #191c32;
      line-height: 42px;
    }

    .list_number2_formula2 {
      margin: 0 auto;
      width: 458px;
      height: 110px;
      @include set-img("../../assets/images/formula1.png");
    }

    .list_number3_formula3 {
      margin: 0 auto;
      width: 364px;
      height: 88px;
      @include set-img("../../assets/images/formula2.png");
    }
  }
}

.button_mask {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 510px;
  height: 136px;
  background: #ffffff;
  opacity: 0.6;
  filter: blur(7.128205128205128px);
}

.modal-content__button--confirm {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 462px;
  height: 88px;
  background: #279bae;
  border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 88px;
  opacity: 1;
  z-index: 99;
}

// 确认协议弹窗
.modal-protocol {
  width: 558px;
  height: 458px;
}

.modal-protocol__subtitle {
  margin: 0 auto;
  padding-top: 48px;
  width: 558px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  line-height: 47px;
  border-radius: 32px 32px 0 0;
  @include set-img("../../assets/images/banner-modal.png");

  .modal-protocol__headtitle {
    margin: 0 auto 16px;
    height: 50px;
    line-height: 50px;
    font-size: 36px;
    font-weight: 500;
    text-align: center;
    color: #26273c;
  }

  .modal-protocol__title {
    margin: 0 auto;
    width: 462px;
    height: 80px;
    line-height: 40px;
    font-size: 28px;
    color: #6c6d75;
  }
}

// 引导弹窗
.modal-guide {
  height: 400px;
}

.modal-guide_title {
  padding-bottom: 80px;
}

// 已注册弹窗
.modal-registered {
  width: 602px;
  padding: 44px;
  // height: 437px;
}

.modal-registered__banner {
  position: relative;
  top: -100px;
  @include set-img("../../assets/images/modal-registered-banner.png");
  margin: 0 auto -100px;
  width: 262px;
  height: 264px;
}

.modal-registered__button {
  @include set-flex(space-between, center);
  flex-direction: column;
  padding-bottom: 6px;
  margin: 20px auto 0;
  width: 462px;
  height: 88px;
  background: #279bae;
  border-radius: 55px;

  font-size: 32px;
  color: #ffffff;
  line-height: 47px;

  span {
    font-size: 24px;
    color: #ffffff;
    line-height: 36px;
  }
}
</style>
