<template>
    <div
        class="collection-wrapper"
        :style="cmsConfig.pageColor"
    >
        <!-- CMS配置的头图 -->
        <img
            class="collection-banner"
            :src="cmsConfig.formImg"
            :style="{
                visibility:cmsConfig.formImg?'visible':'hidden',
                height:cmsConfig.formImg?'auto':'0px'
            }"
        />
        <!-- <div class="test"></div> -->
        <div class="collection-header">
            <div
                class="header__avatars"
                id="svgaAvatars"
            ></div>
            <div class="header__online">
                <img src="../assets/imgs/new/top-text.png" alt="">
            </div>
        </div>

        <!-- 表单部分 -->
        <collection-form />
    </div>
</template>

<script>

import {mapState,mapMutations,mapActions} from 'vuex';
import {CollectionForm} from '../components/collection/index.js';
import {getRandomInt} from "@/common/utils/tools.js";
import {_getMaterial,_getRandomAvatar} from "../js/api.js";
import {reportKibana} from '@/common/utils/report.js';

export default {
    components:{
        CollectionForm
    },
    data() {
        return {
            onlineNumber:null,
            player:null,
            testPhone:''
        };
    },
    computed:{
        ...mapState([
            'formInfo',
            'registerInfo',
            'cmsConfig'
        ]),
        formAvatarText(){
            let formAvatarText = this.cmsConfig.formAvatarText;

            // 运营配置了[]
            if(formAvatarText.indexOf('[')>-1 && formAvatarText.indexOf(']')>-1){
                let part1 = formAvatarText.split('[')[0],
                    part2 = formAvatarText.split('[')[1].split(']')[0],
                    part3 = formAvatarText.split('[')[1].split(']')[1];

                return `${part1}<span>${part2}</span>${part3}`;
            }
            // 运营未配置，使用缺省
            else if(formAvatarText == "缺省"){
                // return `已经有<span>${this.onlineNumber}</span>位铲屎官匹配到心仪对象`;
                return `已为你匹配到多位同城异性朋友`;
            }

            // 运营没有配置[]
            return `${formAvatarText}`;
        }

    },
    async created(){
        // 随机生成在线人数
        this.setOnlineNumber();
        // 获取CMS配置
        await this.setCmsConfig();
        // this.$route.push('/info')

    },
    mounted(){
        // 打桩
        reportKibana("优恋大表单", 1, "首页访问", {});

        // 定位到页面顶部
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
        window.scrollTo(0, 0);

        // banner动效
        this.setSVGA();
    },
    methods:{

        ...mapMutations([
            "setFormInfo"
        ]),
        ...mapActions([
            "setCmsConfig",

        ]),
        setOnlineNumber(){
            this.onlineNumber = getRandomInt(360000,499997);
            // let timer = setInterval(()=>{
            //     let count = getRandomInt(1,3)
            //     this.onlineNumber += count;
            //     if(this.onlineNumber > 499997 ){
            //             clearInterval(timer);
            //         }
            // },2000);
        },
        async setSVGA(){
            let player = new SVGA.Player('#svgaAvatars'),
                parser = new SVGA.Parser('#svgaAvatars');

            let resData = await _getRandomAvatar({});
            if(resData.isError){
                return this.$toast(resData.errorMessage);
            }

            // 后台给的是200x200，压缩至100x100
            let avatarList = resData.data.list;
            avatarList.forEach((item)=>{
                item.avatar += '?imageMogr2/blur/18x35/thumbnail/100x100';
            });

            parser.load(require('../assets/imgs/svgaAvatar1.svga'), (videoItem)=>{
                // 设置头像
                for(let i=0;i<avatarList.length;i++){
                    player.setImage(avatarList[i].avatar,`key${i+1}`);
                }
                player.setVideoItem(videoItem);
                player.loops = 1;
                player.startAnimation();
                player.onFinished(()=>{
                    // 动画执行一次，然后只循环48帧之后的上下浮动部分
                    player.startAnimationWithRange({location:48,length:48});
                });
            });
        },
        closeModal(){
            this.showModal = false;
        },
        closeSelect(){
            this.showSelect = false;
        },
        checkRegisterInfo(){
            return true;
        }
    }

    // beforeRouteUpdate(to, from, next) {
    //     console.log(111,to);
    //     console.log(222,from);
    //     next(true)
    // }
};
</script>

<style lang="scss" scoped>


.collection-wrapper{
    font-family: Source Han Sans SC;
    width: 750px;
    background-color: #ffffff;
    // height: ;
}
.collection-banner{
    width: 750px;
    visibility: hidden;
    margin-bottom: -230px;
}

.collection-header{
    // margin-top: -5px;
    position: relative;
    width: 750px;
    height: 264px;
    overflow: hidden;
    text-align: center;
    top: 4px;
}

.header__avatars{
    position: absolute;
    top: -20px;
    left: 0px;
    height: 240px;
    width: 750px;
    overflow: hidden;
    z-index: 1;
}

.header__online{
    display: inline-block;
    position:relative;
    margin-top: 150px;
    padding: 0 14px;
    z-index: 2;

    // position: absolute;
    // top: 140px;
    // left: 35px;
    // width: 680px;
    text-align: center;
    // letter-spacing:50;
    img {
        width: 583px;
        height: 54px;
    }

}

</style>
