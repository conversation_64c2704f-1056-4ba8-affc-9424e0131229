<template>
    <question-panel
        title="您理想对象的标签"
        :src="cmsConfig.questionBodyImg4"
        :height="544">
        <div class="question-tag">
            <z-image
                v-for="(item, index) in list"
                @click="handleCheck(item)"
                :border-radius="42"
                :key="index"
                :height="80"
                :src="item.text.length < 4 ? require('../assets/images/question-tag-bg-normal.png') : require('../assets/images/question-tag-bg-big.png')"
                :width="item.text.length < 4 ? 190 : 216"
                img-size="cover"
                :class="[ 'question-tag__item', item.check ? 'question-tag__item--active' : '']">
                <z-image
                    class="question-tag__icon"
                    :width="20"
                    :height="20"
                    :src="item.check ? require('../assets/images/icon-check.png') : require('../assets/images/icon-add.png')"/>
                <span>{{ item.text }}</span>
            </z-image>
        </div>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-tag",
    components: {
        QuestionPanel,
    },
    computed: {
        ...mapState([
            'cmsConfig',
            'requirement',
            'registerForm'
        ]),
        list() {
            const tagList = this.registerForm.gender === 0 ? [
                '萌妹子',
                '御姐',
                '高颜值',
                '有趣灵魂',
                '邻家女孩',
                '吃货',
                '女强人',
                '甜妹',
                '清纯妹妹',
                '搞笑女',
            ] : [
                '小奶狗',
                '1米8',
                '高颜值',
                '风趣幽默',
                '霸道总裁',
                '暖男',
                '小狼狗',
                '厨艺好',
                '温柔顾家',
                '斜杠青年',
            ]

            return tagList.map((item) => {
                return {
                    text: item,
                    check: this.$z_.includes(this.requirement.tags, item)
                }
            })
        },
    },
    methods: {
        ...mapMutations([
            'setRequirement',
        ]),
        handleCheck(item) {
            const tags = this.$z_.clone(this.requirement.tags);

            if (item.check) {
                const index = this.$z_.findIndex(tags, (text) => {
                    return text === item.text;
                });

                tags.splice(index, 1);
            } else {
                if (tags.length === 3) {
                    this.$toast('最多选择3个标签');
                    return;
                }

                tags.push(item.text);
            }

            this.setRequirement({
                tags,
            });
        }
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.question-tag {
    padding: 16px 32px 0;
    width: 100%;
    @include flex-center(row, null, null, wrap);

    &__icon {
        top: -1px;
        margin-right: 8px;
    }

    &__item {
        @include flex-center();
        font-weight: 500;
        font-size: 28px;
        margin: 16px 16px 0 0;

        &:nth-child(3), &:nth-child(8) {
            margin-right: 0;
        }

        &--active {
            background-image: none !important;
            background: #767DFF;
        }
    }
}
</style>
