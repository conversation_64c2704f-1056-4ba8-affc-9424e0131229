<template>
    <div class="gender">
        <common-btn
            v-for="(option, index) in options"
            :btnText="option.text"
            :btnStyle="[
                selectIndex === option.text
                    ? btnStyleObj.active
                    : btnStyleObj.normal
            ]"
            :key="index"
            @goNext="goNext(option)"
        />

        <div class="male">
            <div
                class="avatar"
                @click="goNext(0)"
            ></div>
            <div class="text">
                男
            </div>
        </div>
        <div class="female">
            <div
                class="avatar"
                @click="goNext(1)"
            ></div>
            <div class="text">
                女
            </div>
        </div>
    </div>
</template>

<script>
import CommonBtn from "@/common/businessV2/components/CommonBtn.vue";
import { session as Session } from "@/common/utils/storage";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
// import { reportKibana } from "@/common/utils/report";
export default {
    name: "Gender",
    props: {
        type: {
            type: String
        },
        options: {
            type: Array,
            default: () => []
        },
        btnStyleObj: {
            type: Object,
            default: () => {}
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    components: {
        CommonBtn
    },
    data() {
        return {
            selectGender: Session.getItem(this.type) || "",
        };
    },
    methods: {
        goNext(val) {
            this.$report(2, `性别页-${val === 0 ? '选择男' : '选择女'}`);
            const params = {
                key: 'gender',
                value: val
            };
            setLocalRegisterForm(params, this.pageType);
            Session.setItem('gender', val);

            this.selectGender = val;
            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';
.gender {
    display: flex;

    & > div {
        flex: 1;
        text-align: center;
        position: relative;

        .avatar {
            width: 340px;
            height: 360px;
        }

        .text {
            font-weight: 400;
            font-size: 36px;
            color: #26273C;
            position: absolute;
            right: 0;
            bottom: -16px;
            left: 0;
        }
    }

    .male {
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20230522/1684744963536_842183_t.png");
    }

    .female {
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20230522/1684744963541_27058_t.png");
    }

    .btn {
        margin: 52px auto 0;
        width: 654px;
        height: 110px;
        color: #ffffff;
        font-size: 32px;
        text-align: center;
        line-height: 110px;
        background: #5368f0;
        border-radius: 80px;
    }
}
</style>
