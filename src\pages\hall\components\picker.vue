<template>
    <div
        v-if="visible"
        class="picker-wrapper"
        ref="refWrapper"
    >
        <div
            class="select-mask"
            @click.self="close"
        ></div>

        <div class="select-panel">
            <z-image
                class="form-selector__icon-close"
                :src="require('../assets/images/icon-x.png')"
                :img-size="this.$utils.pxToRem(32)"
                :width="72"
                :height="72"
                @click="close"
            />

            <div class="panel__title">
                {{ param.label }}
            </div>
            <div class="panel__tips">
                完善资料，立即匹配对象
            </div>

            <!-- 滑动组件：工作地、出生年份-->
            <van-picker
                ref="refPicker"
                show-toolbar
                :columns="columns"
                @confirm="onConfirm"
                @cancel="onCancel"
                toolbar-position="bottom"
                item-height="1.25rem"
                visible-item-count="5"
                confirm-button-text="确定"
                cancel-button-text="取消"
            />
        </div>
    </div>
</template>

<script>
import { Picker } from 'vant';
import { mapMutations } from "vuex";
import borderStopScroll from '@/common/utils/borderStopScroll.js';

export default {
    name: "Picker",
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        param: {
            type: Object,
        }
    },
    components: {
        VanPicker: Picker,
    },
    data(){
        return {
            columns: [],
        };
    },
    watch: {
        visible (show) {
            if (show) {
                this.setDefault();
            }
        },
    },
    methods:{
        ...mapMutations([
            'setRegisterForm',
            'setFormItems'
        ]),
        setDefault() {
            this.columns = this.param.options;

            this.$nextTick(() => {
                let arr = this.$storage.getItem("cachedWorkCity");

                borderStopScroll({
                    wrapEle: this.$refs.refWrapper
                });

                if (arr) {
                    this.$refs.refPicker.setIndexes(arr);
                } else {
                    this.$refs.refPicker.setIndexes([ 2, 5, 0 ]); // 广东 肇庆 端州区
                }
            });
        },
        close(){
            this.$emit('update:visible', false);
        },
        onConfirm(value, index) {
            let picker = this.$refs.refPicker;
            let key = '';
            let text = '';

            // if (this.selectParam.key === "workCity") {
            // 工作地picker
            let currentCity = picker.getColumnValue(0).text;

            if ([ "北京", "上海", "重庆", "天津" ].includes(currentCity)) {
                key = picker.getColumnValue(1).key;
                text = value.slice(0, 2).join("/");
            } else {
                key = picker.getColumnValue(2).key;
                text = value.slice(0, 3).join("/");
            }

            this.$storage.setItem("cachedWorkCity", index);  // 用于初始化select
            // }

            // 设置数据
            this.setFormItems({ [this.param.key]: text });

            const formItem = {
                key: this.param.key,
                value: key,
            };

            // if (this.param.key === 'birthday') {
            //     formItem.getMarkValue = () => {
            //         return {
            //             year: String(text),
            //             month: '1',
            //             day: '1',
            //         }
            //     }
            // }

            this.setRegisterForm(formItem);

            setTimeout(()=>{
                this.close();
            }, 300);
            
        },
        onCancel() {
            this.close();
        }
    }
};
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.picker-wrapper {
    .form-selector {
        &__icon-close {
            position: absolute;
            top: 40px;
            right: 24px;
        }
    }

    .select-mask {
        position: fixed;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        background: rgba($color: #26273C, $alpha: 0.6);
        z-index: 100;
    }

    .select-panel {
        position: fixed;
        left: 0;
        bottom: 0;
        height: 990px;
        width: 750px;
        background: #FFFFFF;
        border-radius: 60px 60px 0 0;
        z-index: 999;

        &--picker {
            height: 990px;
        }
    }

    .panel__title {
        margin-top: 48px;
        font-size: 36px;
        font-weight: 700;
        color: #26273C;
        text-align: center;
        line-height: 54px;
    }

    .panel__tips {
        margin-top: 8px;
        font-size: 29px;
        color: #92939D;
        text-align: center;
        line-height: 43px;
    }
}

</style>


<style lang="scss">
/* 覆盖vant样式 */
.van-picker {
    margin-top: 77px;
}

.van-picker__columns {
    margin-top: -30px;
}

.van-picker-column {
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
}

.van-picker-column__item {
    font-size: 30px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-picker-column__item--selected {
    font-size: 36px;
    font-weight: 400;
    color: #26273C;
    line-height: 95px;
}

.van-picker__toolbar {
    margin-top: 60px;
    flex-direction: column;
    align-content: flex-start;

}

.van-picker__cancel {
    flex-shrink: 0;
    order: 1;
    // width: 654px;
    height: 110px;
    font-size: 32px;
    font-weight: 400;
    color: #6C6D75;
    line-height: 47px;
}

.van-picker__confirm {
    flex-shrink: 0;
    order: 0;
    margin: 0 auto;
    width: 654px;
    height: 110px;
    background: #FFB900;
    border-radius: 55px;
    font-size: 32px;
    font-weight: 400;
    color: #26273C;
    line-height: 47px;
}
</style>
