<template>
    <div class="regform">
        <div :class="curRegItem.type">
            <h3 class="com-title">{{ curRegItem.label }}</h3>
            <p class="com-subtitle">{{ curRegItem.desc }}</p>
            <div :class="`${curRegItem.type}-info`">
                <template v-if="curRegItem.type === 'gender'">
                    <div class="gender-info-item" @click="goNext(0)">
                        <div class="gii-img"></div>
                        <p>男</p>
                    </div>
                    <div class="gender-info-item" @click="goNext(1)">
                        <div class="gii-img"></div>
                        <p>女</p>
                    </div>
                </template>
                <template v-if="curRegItem.type === 'workCity'">
                    <van-picker
                        ref="refPicker"
                        :columns="curRegItem.options"
                        item-height="1.25rem"
                        visible-item-count="5"
                    ></van-picker>
                </template>
                <template v-if="curRegItem.type === 'birthday'"></template>
                <template v-if="curRegItem.type === 'education'"></template>
                <template v-if="curRegItem.type === 'marriage'"></template>
                <template v-if="curRegItem.type === 'salary'"></template>
            </div>
        </div>
    </div>
</template>

<script>
import * as dict from "@/common/config/register-dictionary";
import { Popup, Picker } from "vant";
export default {
    name: "RegFrom",
    components: {
        [Picker.name]: Picker,
        [Popup.name]: Popup
    },
    data() {
        return {
            regForm: {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            curIndex: 0
        };
    },
    props: {
        regItems: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    computed: {
        curRegItem() {
            return this.regItems[this.curIndex];
        }
    },
    methods: {
        goNext(val) {
            this.curIndex++;
        }
    }
};
</script>

<style lang="scss" scoped>
.regform {
    height: 100vh;
    overflow: hidden;
    > div {
        height: 100%;
        background: linear-gradient(
            -30deg,
            rgba(183, 191, 255, 0.8) 46%,
            rgba(250, 251, 255, 0.8) 86%
        );
    }
    .com-title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .com-subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    .gender-info {
        margin: 86px 86px 0;
        display: flex;
        justify-content: space-around;
        &-item {
            width: 234px;
            > p {
                margin-top: 32px;
                color: #26273c;
                font-size: 36px;
                text-align: center;
            }
            &:active {
                opacity: 0.8;
            }
        }
        .gii-img {
            width: 100%;
            height: 234px;
            background: url(https://i.zhenai.com/m/portal/register/index/images/ic_man.cf95782.png)
                no-repeat;
            background-size: 100% 100%;
        }
        &-item:nth-of-type(1) {
            margin-right: 112px;
        }
    }
}
</style>
