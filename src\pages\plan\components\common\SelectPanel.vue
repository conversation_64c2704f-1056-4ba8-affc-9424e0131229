<template>
    <div
        class="select-wrapper"
        ref="refWrapper"
    >
        <div
            class="select-mask"
            @click="closeSelect"
        ></div>

        <div
            :class="
                selectType === 'messagesType'
                    ? isIOS && opened
                        ? 'select-panel message-panel-ios'
                        : 'select-panel message-panel'
                    : 'select-panel'
            "
        >
            <div class="panel__title">
                {{ selectParam.label }}
                <div
                    class="close-btn"
                    @click="closeSelect"
                ></div>
            </div>

            <!-- 滑动组件：婚姻状况、学历、收入、工作地、出生年份-->
            <van-picker
                v-if="selectType === 'selectSlide'"
                ref="refPicker"
                show-toolbar
                :columns="columns"
                @confirm="onConfirm"
                @cancel="onCancel"
                @change="onChange"
                toolbar-position="bottom"
                item-height="1.25rem"
                visible-item-count="5"
                confirm-button-text="提交"
                cancel-button-text="取消"
            />

            <!-- 平铺组件 -->
            <template v-if="selectType === 'selectBoard'">
                <div
                    class="panel__board"
                    ref="refBoard"
                >
                    <div
                        v-for="(item, index) in selectParam.selectArr"
                        :key="index"
                        @click="onBoardClick($event, item)"
                        class="panel__board__item"
                        :class="
                            item.key === registerInfo[currentIndex]
                                ? 'panel__board__item--selected'
                                : ''
                        "
                    >
                        {{ item.text }}
                    </div>
                </div>
                <button
                    class="panel__board__cancel"
                    @click="closeSelect"
                >
                    取消
                </button>
            </template>

            <template>
                <div
                    class="modal-content modal-validate"
                    v-if="selectType === 'messagesType'"
                    ref="refValidate"
                >
                    <div class="modal-validate__phone">
                        {{ registerInfo.phone }}
                    </div>
                    <div class="modal-validate__row">
                        <!-- UI为4个框 -->
                        <div class="modal-validate__inputs">
                            <div
                                v-for="(item, index) in 4"
                                :key="index"
                                class="modal-validate__inputs__item"
                                :style="{
                                    background:
                                        code.length > index ? '#279BAE' : 'rgba(177, 180, 187, 0.2)'
                                }"
                            >
                                <i
                                    class="inputs__item_cursor"
                                    :style="{ display: code.length === index ? 'block' : 'none' }"
                                    :ref="'inputs__item_cursor' + index"
                                ></i>
                                {{ messageCodeList[index] }}
                            </div>
                        </div>
                        <!-- 实际为1个框 -->
                        <input
                            ref="refCode"
                            type="tel"
                            v-model="code"
                            class="modal-validate__input"
                            maxlength="4"
                            @input="checkCode"
                            @click="renewCode"
                            @blur="closeCode"
                            pattern="[0-9]*"
                            autocomplete="new-password"
                        />
                    </div>

                    <div
                        v-if="showWarning"
                        class="modal-validate__warning"
                    >
                        {{ warningMessage }}
                    </div>
                    <button
                        class="modal-content__button--submit"
                        :style="{ background: isValidating ? '#AEB1B6' : '#279BAE' }"
                        @click="resendValidateCode"
                    >
                        {{ validateText }}
                    </button>
                    <button
                        class="modal-content__button--cancel"
                        @click="closeSelect"
                    >
                        取消
                    </button>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import { Picker } from "vant";
import { mapState, mapGetters, mapMutations } from "vuex";
import { reportKibana } from "@/common/utils/report.js";
import oUserSelect from "@/common/ocpx/huichuan.js";
import borderStopScroll from "@/common/utils/borderStopScroll.js";
import Prototype from "@/common/framework/prototype";
import Api from "@/common/server/base";

export default {
    props: {
        selectType: {
            type: String,
            required: true
        },
        selectParam: {
            type: Object
        },
        validateCode: {
            type: Function,
            required: true
        }
    },
    components: {
        [Picker.name]: Picker
    },
    data() {
        return {
            columns: [],
            boardItems: [
                "高中及以下",
                "中专",
                "大专",
                "大学本科",
                "硕士",
                "1212",
                "大学本科",
                "硕士",
                "1212"
            ],
            messageCodeList: [],
            code: "",
            showWarning: false,
            warningMessage: "",
            isValidating: false,
            lockValidate: false,
            validateSubmit: "重新获取",
            backTimer: null,
            leaveTime: null,
            backTime: null,
            // 控制解决ios键盘遮挡问题
            opened: false
        };
    },
    computed: {
        ...mapState(["isCover", "formInfo", "registerInfo", "materialId"]),
        ...mapGetters(["getNormalPhone"]),
        currentIndex() {
            return this.selectParam.index;
        },
        validateText() {
            if (this.validateSubmit === "重新获取") {
                return this.validateSubmit;
            } else {
                return `重新获取（${this.validateSubmit}s）`;
            }
        },
        isIOS() {
            if (/(iphone|ipod|ipad)/gi.test(navigator.userAgent)) {
                return true;
            } else {
                return false;
            }
        }
    },
    created() {
        if (this.selectType === "messagesType") {
            // 打桩
            reportKibana("脱单计划H5", 300, "验证短信弹窗访问", {
                ext16: this.materialId
            });

            this.countDown();
            this.$nextTick(() => {
                this.$refs.refCode.focus();
            });
        }
    },
    mounted() {
        this.columns = this.selectParam.selectArr;

        this.$nextTick(() => {
            this.setDefault();
        });

        borderStopScroll({
            wrapEle: this.$refs.refWrapper
        });

        if (["education", "salary"].includes(this.selectParam.index)) {
            // 处理滚动穿透
            borderStopScroll({
                wrapEle: this.$refs.refBoard
            });
        }
    },
    watch: {
        selectParam() {
            this.columns = this.selectParam.selectArr;

            // 设置缺省,视觉中心
            this.$nextTick(() => {
                this.setDefault();
            });

            this.$nextTick(() => {
                if (["education", "salary"].includes(this.selectParam.index)) {
                    console.log("watch监听");
                    borderStopScroll({
                        wrapEle: this.$refs.refBoard
                    });
                }
            });
        }
    },
    methods: {
        ...mapMutations([
            "setFormInfo",
            "setRegisterInfo",
            "setInfo",
            "setRegMemberId"
        ]),
        countDown() {
            // 从60s开始倒计时
            this.validateSubmit = 60;

            // 锁，倒计时期间不能再点击
            this.isValidating = true;

            let timer = setInterval(() => {
                this.validateSubmit -= 1;
                if (this.validateSubmit <= 0) {
                    this.validateSubmit = "重新获取";
                    this.isValidating = false;
                    clearInterval(timer);
                }
            }, 1000);

            // 如果已经有监听，无需重复绑定
            if (this.backTimer) {
                return;
            }

            this.backTimer = document.addEventListener("visibilitychange", () => {
                // 用户进入后台
                if (document.visibilityState === "hidden") {
                    this.leaveTime = new Date().getTime();
                } else if (document.visibilityState === "visible") {
                    this.backTime = new Date().getTime();
                    let diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                    // 在后台期间已经超过当前的剩余秒数
                    if (
                        diff > this.validateSubmit ||
                        this.validateSubmit === "重新获取"
                    ) {
                        this.validateSubmit = "重新获取";
                        this.isValidating = false;
                        clearInterval(timer);
                    } else {
                        // 否则将后台期间的差值计入倒计时
                        this.validateSubmit -= diff;
                    }
                }
            });
        },
        async resendValidateCode() {
            reportKibana("脱单计划H5", 301, "验证短信弹窗-重新获取短信按钮点击", {
                ext16: this.materialId
            });

            if (this.lockValidate) {
                return;
            }

            if (this.isValidating) {
                console.log(`已发送验证请求，请${this.validateSubmit}秒后重试`);
                return;
            }

            // 清空警告
            this.showWarning = false;

            // 锁
            this.lockValidate = true;

            const sendData = {
                phone: this.getNormalPhone,
                type: 0
            };
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype")
            };

            for (const v in toutiaoParamlist) {
                if (toutiaoParamlist[v]) {
                    sendData[v] = toutiaoParamlist[v];
                }
            }
            let resData = await Api.sendWapMessageCodeV2(sendData);
            this.lockValidate = false;

            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }
            this.$toast(resData.data.msg);
            this.countDown();
        },
        modalClick(index) {
            this.$refs[`inputs__item_cursor${index}`].style = "block";
        },
        closeCode() {
            this.opened = false;
        },
        renewCode() {
            this.opened = true;
            let code = this.$refs.refCode.value;
            this.$refs.refCode.value = "";
            this.$refs.refCode.value = code;
        },
        async checkCode() {
            this.messageCodeList = this.code.split("");
            this.code = this.code.replace(/[^\d]/g, "");

            if (this.code.length === 4) {
                this.$refs.refCode.blur();

                const result = await this.validateCode(this.code);

                if (result.isError) {
                    this.code = "";
                    this.$refs.codeInput.focus();

                    switch (result.errorCode) {
                    case "-8002005":
                        this.errorMessage = "验证码错误，请重新输入";
                        break;
                    case "-8002006":
                        this.errorMessage = "验证码已过期";
                        break;
                    case "-8002004":
                        this.errorMessage = result.errorMessage;
                        break;
                    }

                    return;
                }

                this.closeSelect();
                Prototype.$gather.setValidateCodeSuccessOCPC();
            }
        },
        clearCache() {
            // 清空注册信息
            localStorage.setItem("localFormInfo", "");
            localStorage.setItem("localRegisterInfo", "");
            localStorage.setItem("defaultBirthday", "");
            localStorage.setItem("defaultWorkCity", "");
            localStorage.setItem("defaultEducation", "");
            localStorage.setItem("defaultSalary", "");
            localStorage.setItem("defaultMarriage", "");
            // 清空脱单计划测试题信息
            localStorage.setItem("za_localPlanInfo", "");
            // 清空协议勾选状态
            localStorage.removeItem("protocolStatus");
        },
        setDefault() {
            if (this.selectParam.index === "workCity") {
                let arr = localStorage.getItem("defaultWorkCity");
                if (arr) {
                    this.$refs.refPicker.setIndexes(JSON.parse(arr));
                } else {
                    this.$refs.refPicker.setIndexes([2, 5, 0]); // 广东 肇庆 端州区
                }
            } else if (this.selectParam.index === "birthday") {
                let index = localStorage.getItem("defaultBirthday");
                if (index) {
                    this.$refs.refPicker.setIndexes([JSON.parse(index)]);
                } else {
                    this.$refs.refPicker.setIndexes([38]);
                }
            } else if (this.selectParam.index === "education") {
                let index = localStorage.getItem("defaultEducation");
                if (index) {
                    this.$refs.refPicker.setIndexes([JSON.parse(index)]);
                } else {
                    this.$refs.refPicker.setIndexes([0]);
                }
            } else if (this.selectParam.index === "salary") {
                let index = localStorage.getItem("defaultSalary");
                if (index) {
                    this.$refs.refPicker.setIndexes([JSON.parse(index)]);
                } else {
                    this.$refs.refPicker.setIndexes([0]);
                }
            } else if (this.selectParam.index === "marriage") {
                let index = localStorage.getItem("defaultMarriage");
                if (index) {
                    this.$refs.refPicker.setIndexes([JSON.parse(index)]);
                } else {
                    this.$refs.refPicker.setIndexes([0]);
                }
            }
        },
        closeSelect() {
            this.$emit("close-select");
        },
        onBoardClick(event, item) {
            // 设置数据
            this.setFormInfo({ [this.selectParam.index]: item.text });
            this.setRegisterInfo({ [this.selectParam.index]: item.key });

            // 处理回传
            oUserSelect.mark({ [this.selectParam.index]: item.key });

            // 防止在panel切换之间漏掉点击
            // this.clickable = false;

            // 获取未填项，自动跳转,会导致漏点击的bug
            // setTimeout(()=>{
            this.autoLoop();
            // },200)
        },
        autoLoop() {
            // 构造需要select组件的注册项数组（剔除 性别项）
            let needSelectArr = this.formInfo.slice(1);

            // 获取当前位置
            let position = needSelectArr.findIndex(item => {
                return item.index === this.selectParam.index;
            });

            // 记录是否已经全部填写
            let hasDone = true,
                nextItem = null;

            for (let i = 0; i < needSelectArr.length; i++) {
                // 从当前位置的下一个开始正循环遍历，寻找下一个需要调用selectPanel组件的未填项
                position = position === needSelectArr.length - 1 ? 0 : position + 1;

                if (needSelectArr[position].value) {
                    // 已填写则跳过
                    continue;
                } else {
                    // 未填写则存储要打开的注册项信息
                    nextItem = needSelectArr[position];
                    hasDone = false;
                    break;
                }
            }

            // 如果需要调用selectPanel组件的注册项都填写，则关闭selectPanel组件
            if (hasDone) {
                return this.closeSelect();
            }

            // 否则自动跳转至下一个需要调用selectPanel组件的注册项
            this.$parent.openSelect(nextItem);
        },

        onConfirm(value, index) {
            let picker = this.$refs.refPicker,
                key = "",
                text = "";

            //
            if (this.selectParam.index === "birthday") {
                // 生日picker
                let year = picker.getColumnValue(0).key;
                key = new Date(year + "/" + 1 + "/" + 1).getTime(); //实际传给后台的数据为 年/1/1 对应的毫秒值
                text = picker.getColumnValue(0).text;
                localStorage.setItem("defaultBirthday", JSON.stringify(index)); // 用于初始化select
            } else if (this.selectParam.index === "workCity") {
                // 工作地picker
                let currentCity = picker.getColumnValue(0).text;
                if (["北京", "上海", "重庆", "天津"].includes(currentCity)) {
                    key = picker.getColumnValue(1).key;
                    text = value.slice(0, 2).join(" ");
                } else {
                    key = picker.getColumnValue(2).key;
                    text = value.slice(0, 3).join(" ");
                }
                localStorage.setItem("defaultWorkCity", JSON.stringify(index)); // 用于初始化select
            } else if (this.selectParam.index === "marriage") {
                key = picker.getColumnValue(0).key;
                text = picker.getColumnValue(0).text;
                localStorage.setItem("defaultMarriage", JSON.stringify(index));
            } else if (this.selectParam.index === "education") {
                key = picker.getColumnValue(0).key;
                text = picker.getColumnValue(0).text;
                localStorage.setItem("defaultEducation", JSON.stringify(index));
            } else if (this.selectParam.index === "salary") {
                key = picker.getColumnValue(0).key;
                text = picker.getColumnValue(0).text;
                localStorage.setItem("defaultSalary", JSON.stringify(index));
            }

            // 设置数据
            this.setFormInfo({ [this.selectParam.index]: text });
            this.setRegisterInfo({ [this.selectParam.index]: key });

            // 处理回传
            if (this.selectParam.index === "birthday") {
                oUserSelect.mark({
                    year: "" + picker.getColumnValue(0).key,
                    month: "1",
                    day: "1"
                });
            } else if (this.selectParam.index === "workCity") {
                oUserSelect.mark({
                    workCity: key
                });
            } else {
                oUserSelect.mark({ [this.selectParam.index]: key });
            }

            // 获取未填项，自动跳转
            setTimeout(() => {
                this.autoLoop();
            }, 200);
        },
        onChange() {},
        onCancel() {
            this.closeSelect();
        }
    }
    // beforeRouteUpdate(to, from, next) {
    //     console.log(111,to);
    //     console.log(222,from);
    //     next(true)
    // }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.select-wrapper {
  // position: fixed;
  font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}

.select-mask {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  // margin: auto;
  background: rgba($color: #26273c, $alpha: 0.6);
  z-index: 100;
}

.select-panel {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750px;
  height: 998px;
  background: #ffffff;
  border-radius: 60px 60px 0px 0px;
  z-index: 999;
}

.message-panel {
  height: 718px;
}

.message-panel-ios {
  height: 1100px;
}

.panel__tips {
  margin-top: 60px;
  font-size: 29px;
  color: #92939d;
  text-align: center;
  line-height: 43px;
}

.panel__title {
  position: relative;
  margin-top: 58px;
  font-size: 36px;
  font-weight: 400;
  color: #26273c;
  text-align: center;
  height: 72px;
  line-height: 72px;
}

.close-btn {
  position: absolute;
  right: 24px;
  top: 0;
  width: 72px;
  height: 72px;
  @include set-img("../../assets/images/panel-close.png");
}

.panel__board {
  padding: 4px;
  position: relative;
  @include set-flex(flex-start, center);
  flex-direction: column;
  margin-top: 87px;
  width: 750px;
  height: 575px;
  overflow: scroll;
  // border: 1px solid red;
}

.panel__board--blur {
  position: absolute;
  width: 560px;
  height: 0px;
  box-shadow: 0 0 40px 20px #ffffff;
  z-index: 3;
}

.top {
  top: 260px;
  left: 95px;
}

.bottom {
  bottom: 164px;
  left: 95px;
}

.panel__board__item {
  width: 654px;
  height: 110px;
  line-height: 110px;
  margin-top: 32px;
  flex-shrink: 0;
  border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #103954;
  text-align: center;
  border: 1px solid #e6e6e6;
  background-color: #f6f5f8;
}

.panel__board__item:nth-child(1) {
  margin-top: 0;
}

.panel__board__item--selected {
  color: #ffffff;
  border: 0;
  background: #279bae;
}

.panel__board__cancel {
  display: block;
  margin: 53px auto 0;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  text-align: center;
}

// 验证弹窗
.modal-validate {
  width: 100%;
  height: 625px;
}

.modal-validate__phone {
  margin-top: 16px;
  font-size: 32px;
  font-weight: 400;
  color: #92939d;
  text-align: center;
  line-height: 47px;
}

.modal-validate__row {
  position: relative;
  width: 600px;
  margin: 0 70px;
}

@keyframes Blink {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.modal-validate__inputs {
  display: flex;
  justify-content: center;
  width: 600px;
  margin-top: 46px;
  margin-bottom: 81px;
  padding: 0 72px;
}

.modal-validate__inputs__item {
  position: relative;
  width: 110px;
  height: 144px;
  line-height: 144px;
  border-radius: 50px;
  margin-right: 18px;
  font-size: 42px;
  text-align: center;
  color: #ffffff;
}

.inputs__item_cursor {
  display: none;
  animation: Blink 1s ease-in 0s infinite;
  content: "";
  position: absolute;
  left: 50px;
  top: 50%;
  width: 4px;
  height: 34px;
  background-color: #279bae;
  border-radius: 2px;
  transform: translateY(-50%);
  z-index: 99;
}

.modal-validate__inputs__item:nth-last-child(1) {
  margin-right: 0;
}

.modal-validate__input {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 60px;
  padding-left: 106px;
  font-size: 32px;
  line-height: 52px;
  color: transparent;
  background: transparent;
  letter-spacing: 98px;
  opacity: 0;
}

// iphone12 自动输入验证码黄色背景的兼容
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  // -webkit-transition: background-color 99999s ease-out;
}

.modal-validate__warning {
  position: absolute;
  top: 397px;
  font-size: 28px;
  font-weight: 400;
  color: #ff7599;
  width: 100%;
  text-align: center;
  line-height: 41px;
}

.modal-content__button--submit {
  display: block;
  margin: 0 auto;
  width: 654px;
  height: 110px;
  line-height: 110px;
  background: #279bae;
  border-radius: 80px;
  font-size: 32px;
  color: #ffffff;
  text-align: center;
}

.modal-content__button--cancel {
  display: block;
  margin: 53px auto 0;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  text-align: center;
}
</style>

<style lang="scss">
/* 覆盖vant样式 */
.van-picker {
  margin-top: 77px;
  font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}

.van-picker__columns {
  margin-top: -30px;
  // height: 430px;
}

.van-picker-column {
  // height: 475px;
  font-size: 36px;
  font-weight: 400;
  color: #26273c;
  // background: yellow;
}

.van-picker-column__wrapper {
}

.van-picker-column__item {
  // height: 95px !important;

  font-size: 30px;
  font-weight: 400;
  color: #26273c;
  line-height: 95px;
}

.van-picker-column__item--selected {
  font-size: 36px;
  font-weight: 400;
  color: #26273c;
  line-height: 95px;
}

.van-hairline-unset--top-bottom {
  // height: 95px !important;
}

.van-picker__toolbar {
  margin-top: 60px;
  flex-direction: column;
  align-content: flex-start;
}

.van-picker__cancel {
  flex-shrink: 0;
  order: 1;
  width: 654px;
  height: 110px;
  // background: #767DFF;
  // border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  line-height: 47px;
}

.van-picker__confirm {
  flex-shrink: 0;
  order: 0;
  margin: 0 auto;
  width: 654px;
  height: 110px;
  background: #279bae;
  border-radius: 80px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
  line-height: 47px;
}
</style>
