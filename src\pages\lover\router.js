import VueRouter from "vue-router";
const Lock = () => import("./views/Lock");
const Form = () => import("./views/Form");
const Recom = () => import("./views/Recom");
import Index from "./views/Index.vue";
const About = () => import("./views/About.vue");
const LockForm = () => import("./views/LockForm.vue");

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            name: "index",
            component: Index
        },
        {
            path: "/lock",
            name: "Lock",
            component: Lock
        },
        {
            path: "/form",
            name: "Form",
            component: Form
        },
        {
            path: "/about/:id",
            name: 'about',
            component: About
        },
        {
            path: '/recom',
            name: 'Recom',
            component: Recom
        },
        {
            path: '/lockForm',
            name: 'LockForm',
            component: LockForm
        }
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
