import VueRouter from "vue-router";
import Index from './views/Index.vue';
import PreGender from './views/PreGender.vue';
import Quiz from './views/Quiz.vue';
import Finish from "./views/Finish.vue";
import Result from './views/Result.vue';
import FinishV2 from './views/FinishV2.vue';

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            name: 'index',
            component: Index
        },
        {
            path: "/pre-gender",
            name: 'preGender',
            component: PreGender
        },
        {
            path: '/quiz/:id',
            name: 'quiz',
            component: Quiz
        },
        {
            path: '/finish',
            name: 'finish',
            component: Finish
        },
        {
            path: '/result',
            name: 'result',
            component: Result
        },
        {
            path: '/finish-v2',
            name: 'finishV2',
            component: FinishV2
        },
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0, 0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
