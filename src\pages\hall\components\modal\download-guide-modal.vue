<template>
    <van-popup
        class="download-guide-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange">
        <h1 class="download-guide-modal__title">
            请前往各大应用市场<br>搜索“珍爱网”下载珍爱APP
        </h1>

        <p class="download-guide-modal__desc">
            Ta给你发了消息，在珍爱APP上等你回复
        </p>

        <div class="download-guide-modal__btn"
             @click="closeModal">
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

export default {
    name: 'download-guide-modal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        }
    }
}
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.download-guide-modal {
    width: 558px;
    height: 428px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
        padding: 0 26px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #FFB900;
        border-radius: 44px;
        color: #26273C;
        @include flex-center();
    }
}
</style>
