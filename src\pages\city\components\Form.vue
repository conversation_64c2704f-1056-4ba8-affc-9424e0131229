<template>
    <div>
        <div class="form-wrapper">
            <!-- 标题 -->
            <div
                class="form-wrapper__top"
                :style="{'background-image' : cmsConfig.questionBodyImg1}"
            >
                <div class="form-wrapper__top-title">
                    -请完善以下信息-
                </div>
                <div class="form-wrapper__top-subtitle">
                    {{ subTitle }}
                </div>
            </div>
            <!-- 公共表单 -->
            <common-form
                class="form-wrapper__form"
                :page-type="cmsConfig.planName"
                :filter-config="['birthday','education','marriage','salary','phone']"
                :style-config="{
                    color: cmsConfig.homeButtonColor, 
                    fontColor:'#FFFFFF',
                    selectorColor: cmsConfig.homeButtonColor,
                    selectorFontColor: '#fff',
                }"
            />
        </div>
        <common-submit
            :page-type="cmsConfig.planName"
            :is-need-protocol="false"
            :style-config="{
                modalConfig: {
                    confirmButtonColor: '#FFFFFF',
                    confirmButtonBgColor: cmsConfig.homeButtonColor,
                    cancleButtonColor: cmsConfig.homeButtonColor,
                }
            }"
            :handle-after-regisiter="handleJump"
            :handle-login="handleJump"
        >
            <div
                class="submit"
                :class="{'submit-disabled': !finished}"
                :style="{ background: cmsConfig.homeButtonColor }"
            >
                立即加入活动
            </div>
        </common-submit>
    </div>
</template>

<script>
import CommonForm from '@/common/business/CommonForm';
import CommonSubmit from '@/common/business/CommonSubmit';
import { storage as Storage } from "@/common/utils/storage";

export default {
    name: 'Form',
    components: {
        CommonForm,
        CommonSubmit
    },
    inject: ['cmsConfig'],
    data() {
        return {
            registerForm: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },

    computed: {
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            return this.allFillIn && this.validatedPhone;
        },

        subTitle() {
            const genderMatch = {
                0: '女生',
                1: '男生'
            };
            const thirdPersonMatch = {
                0: '她',
                1: '他'
            };
            
            const subTitleMap = {
                '同城交友A方案(活动)': `立即查看 活动详情&${genderMatch[this.registerForm.gender]}详细资料`,
                '同城交友B方案(群聊)': `立即查看 群聊详情&${genderMatch[this.registerForm.gender]}详细资料`,
                '同城交友C方案(单人)': `立即查看${genderMatch[this.registerForm.gender]}们的详细资料，与${thirdPersonMatch[this.registerForm.gender]}约会`
            };
            return subTitleMap[this.cmsConfig.planName];
        }
    },

    mounted() {
        this.handleWatchForm();
        this.$report(7, '大表单页访问');
    },

    methods: {
        handleWatchForm() {
            window.addEventListener("setItemEvent", (e) => {
                if(e.key === `cachedRegisterForm-${this.cmsConfig.planName}`){
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        },

        handleJump() {
            this.$router.push('/rcommd');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.form-wrapper {
    width: 645px;
    height: 780px;
    margin: 80px auto 157px;
    &__top {
        height: 160px;
        border-radius: 32px 32px 0px 0px;
        @include flex-center(column, null, center);
        &-title {
            font-weight: 500;
            font-size: 30px;
            color: #FFFFFF;
            margin-top: 30px;
        }
        &-subtitle {
            width: 605px;
            text-align: center;
            font-weight: 400;
            font-size: 28px;
            color: #FFFFFF;
            margin-top: 24px;
        }
    }
    &__form {
        background: #FFFFFF;
        border-radius: 32px;
        position: relative;
        top: -20px;
        padding: 22px 22px 32px 22px;
    }
}
.submit {
    width: 658px;
    height: 110px;
    opacity: 0.88;
    background: #FF4881;
    border: 4px solid #FFFFFF;
    border-radius: 55px;
    text-align: center;
    line-height: 110px;
    font-size: 40px;
    color: #FFFFFF;
    font-weight: 500;
    margin-bottom: 102px;
    &-disabled {
        opacity: 0.5;
    }
}
</style>

