<template>
    <div
        v-if="canRender && idList.length"
        ref="refRecom"
        class="recom"
    >
        <div
            class="avatar"
            :style="{backgroundImage:`url(${curDetail.avatar}?imageMogr2/format/jpg)`}"
        >
            <div class="avatar-percent">
                <span>{{ curDetail.percent }}%</span>
                <span>匹配度</span>
            </div>
            
            <div v-if="!isMale">
                <div
                    
                    class="avatar-name male"
                >
                    已完成腾讯实名认证用户
                </div>
                <div
                    v-if="curDetail.hasHouse"
                    class="avatar-house"
                >
                    有房认证
                </div>
                <div
                    v-if="curDetail.hasCar"
                    class="avatar-car"
                >
                    有车认证
                </div>
            </div>
            <div
                v-else
                class="avatar-name female"
            >
                已完成腾讯实名认证用户
            </div>
        </div>

        <div
            v-if="isMale"
            class="moment"
        >
            <div
                v-if="curDetail.photo1"
                :style="{backgroundImage:`url(${curDetail.photo1}?imageMogr2/format/jpg)`}"
                class="moment-item"
            ></div>
            <div
                v-if="curDetail.photo2"
                :style="{backgroundImage:`url(${curDetail.photo2}?imageMogr2/format/jpg)`}"
                class="moment-item"
            ></div>
        </div>

        <div class="info">
            <div class="info-basic">
                <span>{{ curDetail.nickName }}</span>
                <span>· {{ curDetail.age }}</span>
                <span v-if="curDetail.sameCity && curDetail.workCity !== '未填写'">· {{ curDetail.workCity }} </span>
            </div>
            <div 
                v-if="curDetail.education" 
                class="info-edu common-text"
            >
                {{ curDetail.education }}
            </div>
            <div
                v-if="curDetail.profession && curDetail.profession !== '未填写'" 
                class="info-job common-text"
            >
                {{ curDetail.profession }}
            </div>
            <div
                v-if="!isMale && curDetail.salary" 
                class="info-salary common-text"
            >
                {{ curDetail.salary }}
            </div>
            <div 
                class="info-weixin common-text"
            >
                xxxxxxxx
                <div
                    @click="handleWeixin"
                    class="info-weixin-button"
                >
                    交换微信号
                </div>
            </div>
        </div>

        <div
            v-if="!isMale && curDetail.introduceContent"
            class="confess"
        >
            {{ curDetail.introduceContent }}
        </div>

        <div
            class="button"
            :class="isMale?'female':'male'"
        >
            <div
                @click="handleNext"
                class="button-next"
            >
                {{ idList.length }}个打招呼待查看
            </div>
            <div
                @click="handleLike"
                class="button-like"
            >
                喜欢
            </div>
        </div>

        <!-- 主按钮，修改为下载 -->
        <div
            class="main"
            @click="goDownload"
        ></div>

        <!-- 模态框-->
        <match-modal
            v-if="canRender"
            v-model="showModal.matchModal"
            :is-Male="isMale"
            :avatar="`${curDetail.avatar}?imageMogr2/format/jpg`"
            @go-download="goDownload"
            @go-mini="goMini"
        />

        <guide-modal
            v-model="showModal.guideModal"
            :guide-config="guideConfig"
            @go-download="goDownload"
            @go-mini="goMini"
        />
    </div>
</template>
<script>
import { PAGE_TYPE } from "../config";
import MatchModal from "./Modal/MatchModal.vue";
import GuideModal from "./Modal/GuideModal.vue";
import {
    _queryIdealLoverRecList,
    _queryIdealLoverRecDetail,
    _signDelistingGuideStatus
} from '../api.js';
import { getMiniPathV2 } from "@/common/business/utils/wecom";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { session as Session } from "@/common/utils/storage";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { Toast } from "vant";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "Recom",
    components:{
        MatchModal,
        GuideModal,
    },
    data(){
        return {
            idList:[],
            curDetail:null,
            showModal:{
                matchModal: false,
                guideModal: false,
            },
            showMain:false,
            guideConfig:{
                title:'',
                subtitle:'',
                submitText:'',
                cancelText:''
            },
            canRender:false
        };
    },
    async created(){
        await this.getIdList();
        await this.getDetail();
        this.canRender = true;
    },
    mounted(){
        //window.addEventListener("scroll", this.onScroll);
    },
    computed:{
        isMale(){
            // 推荐的若是女(1)，说明当前用户为男(0)
            return this.curDetail.gender === 1;
        }
    },
    methods: {
        async getIdList(){
            const res = await _queryIdealLoverRecList({
                pageType: pageTypeChnMap[PAGE_TYPE],
                limit:3
            }).catch(e=>this.$toast(e.message));
        
            if(res.isError){
                return this.$toast(res.errorMessage);
            }        
            this.idList = res.data.list;
        },
        async getDetail(){
            const res = await _queryIdealLoverRecDetail({
                pageType: pageTypeChnMap[PAGE_TYPE],
                objectId:this.idList[0]
            }).catch(e=>this.$toast(e.message));
        
            if(res.isError){
                return this.$toast(res.errorMessage);
            }        
            this.curDetail = res.data;   
            this.curDetail.percent = this.getRandom(94,99); 
        },
        async handleWeixin(){
            this.$report(61, "测试报告页-人物刺激-点击交换微信");
            await this.markStatus(this.curDetail.objectId,1);

            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.guideConfig = {
                    title:'下载珍爱网APP',
                    subtitle:`搜索Ta的昵称【${this.curDetail.nickName}】即可申请交换微信哦！`,
                    submitText:'交换',
                    cancelText:'再看看'
                };
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.guideConfig = {
                    title:'她对你也有好感',
                    subtitle:'是否交换微信呢？',
                    submitText:'交换',
                    cancelText:'再看看'
                };
            }

            this.showModal.guideModal = true;
        },
        async handleLike(){
            this.$report(61, "测试报告页-人物刺激-点击喜欢");
            await this.markStatus(this.curDetail.objectId,1);
            
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.guideConfig = {
                    title:'下载珍爱网APP',
                    subtitle:`搜索Ta的昵称【${this.curDetail.nickName}】即可申请交换微信哦！`,
                    submitText:'交换',
                    cancelText:'再看看'
                };
                this.showModal.guideModal = true;
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.showModal.matchModal = true;
            }
        },
        async handleNext(){
            this.$report(61, "测试报告页-人物刺激-点击下一个");
            await this.markStatus(this.curDetail.objectId,0);

            if(this.idList.length > 1){
                this.idList.shift();
                return await this.getDetail();
            }

            // 三个模特均已查看
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.guideConfig = {
                    title:'下载珍爱网APP，可获得10次定制推荐',
                    subtitle:`定制你的脱单计划！`,
                    submitText:'现在下载',
                    cancelText:'再看看'
                };
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.guideConfig = {
                    title:'恭喜你！获得10次定制推荐',
                    subtitle:`定制你的脱单计划！`,
                    submitText:'去微信定制',
                    cancelText:'再看看'
                };
            }
            this.showModal.guideModal = true;
        },
        async markStatus(objectId, status){
            const res = await _signDelistingGuideStatus({
                objectId,
                status
            }).catch(e=>this.$toast(e.message));  

            if(res.isError){
                this.$toast(res.errorMessage);
            }
        },
        getRandom(min, max){
            return (min + Math.random() * (max - min)).toFixed(2); 
        },
        scrollToRecom(){
            this.$report(61, "测试报告页-人物刺激-点击悬浮按钮");
            const target = this.$refs.refRecom;
            try{
                // oneplus 6不兼容
                window.scrollTo({
                    top: target.offsetTop,   
                    behavior: "smooth"         
                });
            } catch(e){
                window.scrollTo(0, target.offsetTop);
            }
        },
        // onScroll() {
        //     const target = this.$refs.refRecom;
        //     if(!target){
        //         return;
        //     }
        //     const { bottom } = target.getBoundingClientRect();
        //     if (bottom < 0) {
        //         window.removeEventListener("scroll", this.onScroll);
        //         this.showMain = true;
        //     }
        // },
        goDownload(){
            Toast({
                message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                duration: 5000
            });
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        async goMini(){
            const queryObj = {
                mid: Session.getItem('reg_memberid'),
                recommendId: this.idList[0],
                ext30: pageTypeChnMap[PAGE_TYPE]
            };

            const miniPath = await getMiniPathV2('pages/recommend/recommend',queryObj);
            window.location.href = miniPath;
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.recom {
    .avatar{
        position: relative;
        width: 644px;
        height: 644px;
        border-radius: 24px;
        background-repeat: no-repeat;
        background-size: 100% 100%;

        &-percent{
            @include flex-center(column, center, center);
            position: absolute;
            top: -18px;
            right: -18px;
            width: 158px;
            height: 158px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668510712042_892113_t.png");
            color: rgba(255, 255, 255, 1);
            font-size: 20px;
            font-weight: 600;
            line-height: 24px;
            text-align: center;
            transform: rotate(-20deg);
        }

        &-name{
            position: absolute;
            left: 0;
            width: 312px;
            height: 60px;
            background-image: linear-gradient(-90deg, #8EAEFF 11%, rgba(0,0,0,0.00) 100%);
            border-radius: 0 31px 31px 0;
            font-weight: 500;
            font-size: 24px;
            color: #FFFFFF;
            text-align: center;
            line-height: 60px;

            &.male{
                top: 0;
            }

            &.female{
                bottom: 0;
            }
        }

        &-car{
            padding-left: 53px;
            position: absolute;
            left: 271px;
            top: 548px;
            width: 233px;
            height: 80px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20221121/1669020365468_507686_t.png');
            border-radius: 40px;
            font-weight: 500;
            font-size: 34px;
            color: #FFFFFF;
            text-align: center;
            line-height: 80px;

            &::before{
                content: '';
                position: absolute;
                left: 10px;
                top: 50%;
                transform: translateY(-50%);
                width: 60px;
                height: 60px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668566877042_412172_t.png");
            }
        }

        &-house{
            padding-left: 53px;
            position: absolute;
            left: 16px;
            top: 548px;
            width: 233px;
            height: 80px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20221121/1669020365468_507686_t.png');
            border-radius: 40px;
            font-weight: 500;
            font-size: 34px;
            color: #FFFFFF;
            text-align: center;
            line-height: 80px;

            &::before{
                content: '';
                position: absolute;
                left: 10px;
                top: 50%;
                transform: translateY(-50%);
                width: 60px;
                height: 60px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668566860301_565331_t.png");
            }
        }
    }

    .moment{
        margin-top: 16px;
        @include flex-center(row, space-between, center);
        &-item{
            width: 308px;
            height: 308px;
            border-radius: 16px;
            background-repeat: no-repeat;
            background-size: cover;
        }
    }

    .info{
        position: relative;
        &-basic{
            margin-top: 32px;
            font-weight: 600;
            font-size: 40px;
            color: #FFFFFF;
            line-height: 56px;
        }

        &-edu{
            display: inline-block;
            position: relative;
            margin-left: 66px;

            &::before{
                content: "学历：";
                position: absolute;
                left: -68px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 26px;
                color: rgba(255,255,255,0.60);
                line-height: 32px;
            }
        }

        &-job{
            display: inline-block;
            position: relative;
            margin-left: 200px;

            &::before{
                content: "职业：";
                position: absolute;
                left: -68px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 26px;
                color: rgba(255,255,255,0.60);
            }
        }

        &-salary{
            position: relative;
            margin-left: 66px;

            &::before{
                content: "收入：";
                position: absolute;
                left: -68px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 26px;
                color: rgba(255,255,255,0.60);
            }
        }

        &-weixin{
            position: relative;
            margin-left: 92px;
            @include flex-center(row, flex-start, center);

            &::before{
                content: "微信号：";
                position: absolute;
                left: -96px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 26px;
                color: rgba(255,255,255,0.60);
            }

            &-button{
                margin-left: 18px;
                font-size: 28px;
                color: #28CA73;
            }
        }
    }

    .confess{
        margin-top: 24px;
        padding: 24px;
        width: 638px;
        background: rgba(55,167,207,0.38);
        border-radius: 24px;
        font-size: 28px;
        color: #BEE1FE;
        text-align: justify;
        line-height: 1.4;
    }

    .button{
        @include flex-center(row, space-between, center);
        margin: 48px auto 0;

        &-next{
            position: relative;
            padding-top: 62px;
            width: 312px;
            height: 100px;
            background-image: linear-gradient(104deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 81%);
            border-radius: 50px;
            font-size: 24px;
            color: rgba(255,255,255,0.60);
            text-align: center;

            &::after{
                content: "下一个";
                position: absolute;
                top: 18px;
                left: 108px;
                font-weight: 600;
                font-size: 32px;
                color: #FFFFFF;
            }
        }

        &-like{
            position: relative;
            padding-top: 34px;
            padding-left: 49px;
            width: 308px;
            height: 100px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668512777411_24234_t.png");
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;

            &::before{
                content: "";
                position: absolute;
                top: 34px;
                left: 90px;
                width: 45px;
                height: 36px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668513145564_495040_t.png");
                font-weight: 600;
                font-size: 32px;
            }
        }
    }

    .main{
        position: fixed;
        bottom: 36px;
        left: 50%;
        transform: translateX(-50%);
        width: 600px;
        height: 110px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221117/1668651623695_957084_t.png");
        z-index: 9;
    }

    .common{
        &-text{
            font-size: 28px;
            color: #FFFFFF;
            line-height: 32px;
            line-height: 1.6;
        }
        
            
    }
}
</style>
