<template>
    <div
        class="index"
        :class="{'is-test': page_type === 79}"
    >
        <template v-if="page_type !== 79">
            <div 
                class="btn"
                @click="goNext"
            >
            </div>
            <div
                class="tips"
            >
                *此测试题由珍爱网专业情感团队提供
            </div>
        </template>

        <template v-else>
            <div class="title"></div>
            <div class="btn">
                <div
                    class="item male"
                    @click="goNextByGender(1)"
                >
                    <img src="https://photo.zastatic.com/images/common-cms/it/20230516/1684220211393_684394_t.png" />
                    <div class="text-wrap">
                        <span
                            class="icon"
                            :class="{active: gender === 1}"
                        ></span>我是女生
                    </div>
                </div>
                <div
                    class="item female"
                    @click="goNextByGender(0)"
                >
                    <img src="https://photo.zastatic.com/images/common-cms/it/20230516/1684220211392_185972_t.png" />
                    <div class="text-wrap">
                        <span
                            class="icon"
                            :class="{active: gender === 0}"
                        ></span>我是男生
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../config";
export default {
    name:"Index",
    data(){
        return {
            gender: "",
            page_type: pageTypeChnMap[PAGE_TYPE],
        };
    },
    mounted(){
        this.$report(1, "首页访问");
        this.$report(3000, "首页访问（监控）");

        const registerForm = storage.getItem(
            `cachedRegisterForm-${PAGE_TYPE}`
        );
        if (registerForm.gender !== undefined) {
            this.gender = registerForm.gender;
        }
    },
    methods:{
        goNext() {
            this.$report(2, "首页-按钮点击");
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                return this.$router.push({
                    path:'/pre-gender'
                });
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.$router.push({
                    path:'/quiz/0'
                });
            }
        },
        goNextByGender(val) {
            const ext17 = val === 0 ? '2' : '1';
            this.$report(2, "首页-选择性别点击", {
                ext17
            });
            const params = {
                key: "gender",
                value: val
            };
            setLocalRegisterForm(params, PAGE_TYPE);

            this.$router.push({
                path:'/quiz/0'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.index{
    width: 750px;
    height: 100vh;
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668154065256_863260_t.png");
    background-size: cover;

    > .btn {
        position: fixed;
        bottom: 150px;
        left: 129px;
        width: 492px;
        height: 112px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668154077330_310768_t.png");
    }

    > .tips{
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        color: #BBBBBB;
        font-size: 22px;
        white-space: nowrap;
    }

    &.is-test {
        background-image: url(https://photo.zastatic.com/images/common-cms/it/20230516/1684220215696_870363_t.png);
        background-position: 0 0 ;
        padding-top: 112px;

        .title {
            width: 750px;
            height: 594px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20230516/1684220215824_553688_t.png");
            background-size: cover;
        }

        > .btn {
            background-image: none;
            @include flex-center(row, space-between, center);
            position: fixed;
            top: 1016px;
            left: 50%;
            transform: translateX(-50%);

            .icon {
                display: inline-block;
                margin-right: 16px;
                width: 22px;
                height: 22px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20230516/1684220211270_11346_t.png");
                background-size: cover;

                &.active {
                    background-image: url(https://photo.zastatic.com/images/common-cms/it/20230516/1684220210896_758809_t.png);
                }
            }
        }

        .item{
            font-weight: 400;
            font-size: 30px;
            color: #fff;
            letter-spacing: 0;
            text-align: center;
            img {
                width: 192px;
                height: 192px;
            }
        }
        .text-wrap {
            margin-top: 24px;
            text-align: left;
            text-indent: 4px;
        }
        > .female{
            margin-left: 120px;
        }
    }
}


</style>