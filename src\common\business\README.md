# CommonForm 组件
## 概述
表单组件,用于收集性别、工作地、出生年份、学历、婚况、月收入、手机号等信息。
![avatar](https://photo.zastatic.com/images/common-cms/it/20220610/1654846483623_523624_t.png)

## 代码示例
```vue
import CommonModal from '@/common/business/CommonForm.vue';

<common-form
    class="form-wrapper__form"
    page-type="导量H5大表单翻牌"
    :filter-config="['birthday','education','marriage','salary','phone']"
    :style-config="{color:'#F75FA1',fontColor:'#FFFFFF'}"
/>
```

### Props （以下为通用的props）
| 参数           | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| page-type          | H5注册勾子类型，用于打桩                  | _string_  | 导量H5大表单翻牌 |
| filter-config      | 用于配置展示哪些表单项，用于兼容部分表单项在表单页之前已被填写的情况，展示顺序是固定的与配置顺序无关   | _array_  | `["gender""workCity","birthday","education","marriage","salary","phone"]` |
| style-config       | 样式配置   | _object_  |    |

### style-config说明
| 参数          | 说明                                       | 类型        | 默认值       |
|--------------|------------------------------------------|-----------|-----------|
| color        | 按钮、边框、字体高亮后颜色          | _string_  | `#787bff` |
| font-color   | 按钮高亮时的字体颜色               | _string_  | `#fff` |


# CommonFormSelector 组件
## 概述
表单选择器组件, 一般用于和表单组件联动，为表单项赋值，分为两种类型：
（1）滑动式选择器：selectorPicker
![avatar](https://photo.zastatic.com/images/common-cms/it/20220610/1654848608880_778911_t.png)
（2）平铺式选择器 selectorBoard
![avatar](https://photo.zastatic.com/images/common-cms/it/20220610/1654848955434_729175_t.png)

## 代码示例
```vue
import CommonFormSelector from '@/common/business/components/CommonFormSelector.vue';

<common-form-selector
    v-model="showSelector"
    :page-type="pageType"
    :style-config="styleConfig"
    :selector-type="selectorType"
    :selector-param="selectorParam"
    @autoloop="handleAutoloop"
/>
```

### Props （以下为通用的props）
| 参数           | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| v-model (value)      | 弹窗是否展示                           | _boolean_  | `false` |
| page-type          | H5注册勾子类型，用于打桩                  | _string_  | 导量H5大表单翻牌 |
| style-config       | 样式配置   | _object_  |    |
| selector-type      | 选择器类型，分为概述中selectorPicker和selectorBoard两种，必填 | _string_  |  |
| selector-param     | 选择器参数对象，用于构建选择器，必填                  | _object_  |  |


### style-config说明
| 参数          | 说明                                       | 类型        | 默认值       |
|--------------|------------------------------------------|-----------|-----------|
| color        | 按钮、边框、字体高亮后颜色          | _string_  | `#787bff` |
| font-color   | 按钮高亮时的字体颜色               | _string_  | `#fff` |

### selector-param说明，具体可参考CommonForm中formItems数组中的对象
| 参数       | 说明                                       | 类型        | 默认值       |
|-----------|------------------------------------------|-----------|-----------|
| type      | 当前选择器对应的表单项，必填        | _string_  | |
| label     | 当前选择器面板的标题文案，必填      | _string_  | |
| options   | 当前表单项对应的数据字典，必填      | _array_  | |

### Events
| 名称           | 说明   | 参数                                           |
|---------------|------|----------------------------------------------|
| autoloop  | 填写完表单项后触发 |  |


# CommonModal 组件

## 概述
modal弹窗组件比较多，这里只是举例一般的类型，详情见components里的组件，带有强业务逻辑的就是覆盖注册弹窗和验证码弹窗，其他都是一些引导式的弹窗（如下载APP弹窗、协议弹窗）

## 代码示例
```vue
import CommonModal from '@/common/business/components/CommonXXXXModal.vue';

<common-modal v-modal="true"/>
```

### Props （以下为通用的props）
| 参数            | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| page-type           | H5注册勾子类型，用于打桩                  | _string_  | 导量H5大表单         |
| v-model (value)      | 弹窗是否展示                                   | _boolean_  | `false` |
| style-config         | 样式配置   | _Object_  |    |

### styleConfig说明
| 参数            | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| confirm-button-color        | 确认按钮字体的颜色                  | _string_  | `#FFFFFF`         |
| confirm-button-bg-color     | 确认按钮背景的颜色                                   | _string_  | `#767DFF` |
| cancle-button-color         | 取消按钮字体的颜色   | _string_  | `#767DFF`   |

# CommonProtocol 组件

## 概述
CommonProtocol协议组件，用于置于表单底部，但有时会也会放在别的页面

## 代码示例
```vue
import CommonProtocol from '@/common/business/CommonProtocol.vue';

<common-protocol />
```

### Props
| 参数            | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| agreement-status           | 该字段主要是控制是否可以勾选协议，和cms配置的一致                  | _number_  | 0         |
| is-checked      | 协议是否已勾选                                   | _boolean_  | `true` |
| style-config         | 样式配置   | _Object_  | 可以参考model的styleConfig   |

### styleConfig说明
| 参数            | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| text-color           | 普通字体的颜色                  | _string_  | `#FFFFFF`         |
| protocol-color       | 协议的颜色                                   | _string_  | `#767DFF` |
| protocol-checked-url   | 协议选中的打钩背景图icon   | _string_  | `https://photo.zastatic.com/images/common-cms/it/20220512/1652336753832_292583_t.png`   |


# CommonSubmit 组件

## 概述
CommonSubmit封装了注册逻辑，以及注册相关会唤起的短信验证码弹窗、注册覆盖弹窗、以及协议弹窗

## 代码示例
```vue
import CommonSubmit from '@/common/business/CommonSubmit.vue';

<common-submit>
    <div>自定义button</div>
</common-submit>
```

### Props
| 参数            | 说明                                       | 类型        | 默认值       |
|---------------|------------------------------------------|-----------|-----------|
| page-type           | H5注册勾子类型，用于打桩、注册上报类型                  | _string_  | 导量H5大表单翻牌         |
| is-need-protocol      | 注册表单是否需要协议                                   | _boolean_  | `true` |
| style-config         | 样式配置，主要是弹窗的样式和协议的样式   | _Object_  | 详细见组件代码   |
| handle-after-regisiter         |注册成功之后的回调函数，可以处理跳转等等  | _Function_  | -         |
| handle-login         |覆盖注册弹窗，点击登录触发  | _Function_  | -         |

### Slots
| 名称           | 说明   | 参数                                           |
|---------------|------|----------------------------------------------|
| -             | Body | - |


