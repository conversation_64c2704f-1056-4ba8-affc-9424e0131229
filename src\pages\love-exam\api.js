
import { getDeviceSign } from '@/common/utils/ajax';
import { formatAxios } from './lib/index';
export const getList = (data) => {
    return formatAxios("/api/common/evaluate/start-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const answerResultId = (data) => {
    return formatAxios("/api/common/evaluate/answer-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const getAnswerResult = (data) => {
    return formatAxios("/api/common/evaluate/get-evaluate-by-type.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const bindEvaluate = (data) => {
    return formatAxios("/api/common/evaluate/bind-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const sendMessageCode = async(data) => {
    data.data = await getDeviceSign();
    return formatAxios("/api/advertisement/phoneFrontReg/sendMessageCode.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};

export const checkMsgCodeAndExamResult = async (data) => {
    data.data = await getDeviceSign();
    return formatAxios("/api/advertisement/phoneFrontReg/checkMsgCodeAndExamResult.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const regFailGetExamResult = (data) => {
    return formatAxios("/api/advertisement/phoneFrontReg/regFailGetExamResult.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const complementRegInfo = (data) => {
    return formatAxios("/api/advertisement/phoneFrontReg/complementRegInfo.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const registeredH5CheckReport = async(data) => {
    data.data = await getDeviceSign();

    return formatAxios("/api/common/ad/account/report/registeredH5CheckReport.do",  data, 'post', true, false,{'Content-Type': 'application/x-www-form-urlencoded'});
};
