<template>
    <van-popup
        class="proposal-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="proposal-modal__title">
            恭喜收到 [升级锦囊]
        </h1>

        <img src="https://photo.zastatic.com/images/common-cms/it/20220701/1656670209076_768618_t.png">

        <p class="proposal-modal__desc">
            <span></span>珍爱APP红娘老师将给你来电
        </p>

        <p class="proposal-modal__word">
            为你提供专业<span>爱情答疑</span>，剖析<span>爱情优势</span>，助你早日脱单
        </p>

        <div
            class="proposal-modal__btn"
            @click="confirmModal"
        >
            收下此锦囊
        </div>
        <div
            class="proposal-modal__cancel"
            @click="closeModal"
        >
            暂时放弃
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { _recordMBTIClickBehavior } from "../../api";
import { session } from '@/common/utils/storage.js';
import {  mapState } from "vuex";

export default {
    name: 'ShareModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        isWechat: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState([
            "regMemberId",
            'registerForm',
            'resourceKey'
        ])
    },

    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirmModal() {
            this.handleDealClick(1);
            this.$emit('input', false);
            this.$reportKibana(this.resourceKey, 97, "报告页-锦囊弹窗-接受锦囊按钮点击");
        },
        closeModal() {
            this.handleDealClick(2);
            this.$emit('input', false);
            this.$reportKibana(this.resourceKey, 97, "报告页-锦囊弹窗-放弃锦囊按钮点击");
        },

        async handleDealClick(behavior) {
            const sendData = {
                memberId: this.regMemberId || session.getItem("reg_memberid"),
                behavior,
            };

            const result = await _recordMBTIClickBehavior(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.proposal-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 60px 48px 44px;
    @include flex-center(column, null, center);

    &__title {
        width: 360px;
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    img {
        width: 296px;
        height: 218px;
    }

    &__desc {
        font-size: 28px;
        font-weight: 500;
        color: #26273C;
        margin-bottom: 20px;
        position: relative;
        right: 14px;
        span {
            display: inline-block;
            width: 52px;
            height: 52px;
            background: url('https://photo.zastatic.com/images/common-cms/it/20220701/1656670417356_897129_t.png') no-repeat;
            background-size: 100% 100%;
            position: relative;
            top: 15px;
            margin-right: 16px;
        }
    }

    &__word {
        font-weight: 400;
        font-size: 28px;
        color: #6C6D75;
        line-height: 40px;
        margin-bottom: 28px;
        span {
            color: #00D1FF;
        }
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }

    &__cancel {
        font-weight: 400;
        font-size: 32px;
        color: #000000;
        margin-top: 28px;
    }
}
</style>
