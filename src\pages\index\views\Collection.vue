<template>
    <div
        class="collection-wrapper"
        :class="`scheme-type-${cmsConfig.schemeType}`"
    >
        <!-- <div
            class="collection-notice-bar"
            v-if="isMaiMai"
        >
            <div
                class="collection-notice-bar-content"
                id="noticeContent"
            >
                <img :src="avatar" />
                <span>{{ noticeText }}</span>
            </div>
        </div> -->
        <!-- CMS配置的头图 -->
        <img
            v-if="cmsConfig.formImg"
            class="collection-banner"
            :src="cmsConfig.formImg"
        />
        <div class="collection-header">
            <div
                class="collection-header__avatars"
                id="svgaAvatars"
            ></div>
            <!-- 头条合规修改，不能展示随机数字 -->
            <div
                v-if="isMaiMai"
                class="collection-header__online collection-header__maimai"
            >
                海量实名优质单身会员大数据智能匹配
            </div>
            <section v-else>
                <div
                    v-if="showToutiao"
                    class="collection-header__online"
                >
                    以上为<span>同城在线</span>会员
                </div>
                <div
                    v-else
                    class="collection-header__online"
                >
                    当前在线<span>{{ onlineNumber }}</span>位珍爱会员
                </div>
            </section>
        </div>

        <!-- 表单部分 -->
        <collection-form
            :isHideProtocol="+$route.query.s"
            :isMaiMai="isMaiMai"
        />
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { CollectionForm } from "../components/collection/index.js";
import { getRandomInt } from "@/common/utils/tools.js";
import { _getRandomAvatar } from "../js/api.js";
// import { imgList } from "../assets/imgs/base64";
import { session } from "@/common/utils/storage.js";
export default {
    components: {
        CollectionForm
    },
    data() {
        return {
            onlineNumber: null,
            player: null,
            isMaiMai: false,
            noticeText: "霏霏@平安集团 刚领取成功",
            // avatar: imgList[0],
            // noticeList: [
            //     {
            //         text: "霏霏@平安集团 刚领取成功",
            //         avatar: imgList[0]
            //     },
            //     {
            //         text: "甘露@美团 刚领取成功",
            //         avatar: imgList[1]
            //     },
            //     {
            //         text: "Grace@腾讯教育 刚领取成功",
            //         avatar: imgList[2]
            //     },
            //     {
            //         text: "Carol@京东 刚领取成功",
            //         avatar: imgList[3]
            //     },
            //     {
            //         text: "吴铭@滴滴出行 刚领取成功",
            //         avatar: imgList[4]
            //     },
            //     {
            //         text: "陈微微@字节跳动 刚领取成功",
            //         avatar: imgList[5]
            //     },
            //     {
            //         text: "郑皓@OPPO 刚领取成功",
            //         avatar: imgList[6]
            //     }
            // ]
        };
    },
    computed: {
        ...mapState(["formInfo", "registerInfo", "cmsConfig"]),
        showToutiao() {
            return Z.getParam("platform") === "1";
        }
    },
    async created() {
        // 随机生成在线人数
        this.setOnlineNumber();
        let isMaiMai = session.getItem("isMaiMai");
        this.isMaiMai = isMaiMai;
    },
    mounted() {
        // banner动效
        this.$report(1, "大表单页访问", {
            ext16: 1
        });
        this.$report(3000, "首页访问（监控）");
        this.setSVGA();
        // if (this.isMaiMai) {
        //     this.setNoticeBar();
        // }
    },
    methods: {
        ...mapMutations(["setFormInfo"]),
        ...mapActions(["setCmsConfig"]),
        setOnlineNumber() {
            this.onlineNumber = getRandomInt(360000, 499997);
            // let timer = setInterval(()=>{
            //     let count = getRandomInt(1,3)
            //     this.onlineNumber += count;
            //     if(this.onlineNumber > 499997 ){
            //             clearInterval(timer);
            //         }
            // },2000);
        },
        // async setNoticeBar() {
        //     let element = document.getElementById("noticeContent");
        //     this.avatar = this.noticeList[0].avatar;
        //     this.noticeText = this.noticeList[0].text;
        //     element.setAttribute("style","transition-duration: 0s;transform: translateY(20px);");
        //     setTimeout(()=>{
        //         element.setAttribute("style","transition-duration: 5000ms;transform: translateY(-40px);");
        //     },200);
        //     let i=1;
        //     setInterval(()=>{
        //         this.avatar = this.noticeList[i].avatar;
        //         this.noticeText = this.noticeList[i].text;
        //         element.setAttribute("style","transition-duration: 0s;transform: translateY(20px);");
        //         setTimeout(()=>{
        //             element.setAttribute("style","transition-duration: 5000ms;transform: translateY(-40px);");
        //             i++;
        //             if (i > 6) {
        //                 i = 0;
        //             }
        //         }, 200);
        //     }, 2000);
        // },
        async setSVGA() {
            let player = new SVGA.Player("#svgaAvatars"),
                parser = new SVGA.Parser("#svgaAvatars");

            let resData = await _getRandomAvatar({});
            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }

            // 后台给的是200x200，压缩至100x100
            let avatarList = resData.data.list.concat([]);
            avatarList.forEach(item => {
                item.avatar += "?imageMogr2/thumbnail/100x100";
            });

            parser.load(
                require("../assets/imgs/svgaAvatar1.svga"),
                videoItem => {
                    // 设置头像
                    for (let i = 0; i < avatarList.length; i++) {
                        player.setImage(avatarList[i].avatar, `key${i + 1}`);
                    }
                    player.setVideoItem(videoItem);
                    player.loops = 1;
                    player.startAnimation();
                    player.onFinished(() => {
                        // 动画执行一次，然后只循环48帧之后的上下浮动部分
                        player.startAnimationWithRange({
                            location: 48,
                            length: 48
                        });
                    });
                }
            );
        },
        closeModal() {
            this.showModal = false;
        },
        closeSelect() {
            this.showSelect = false;
        },
        checkRegisterInfo() {
            return true;
        }
    }
};
</script>

<style lang="scss" scoped>
.collection {
    &-notice-bar{
        display: flex;
        align-items: center;
        justify-content: left;
        height: 40px;
        vertical-align: middle;
        font-size: 24px;
        color: #FFFFFF;
        padding-left: 17px;
        overflow-y: hidden;
        &-content {
            display: flex;
            align-items: center;
        }
        img {
            width: 37px;
            height: 37px;
            border-radius: 100%;
            margin-right: 6px;
        }
    }
    &-wrapper{
        font-family: Source Han Sans SC;
        width: 750px;
    }

    &-banner {
        width: 750px;
    }

    &-header {
        position: relative;
        width: 750px;
        height: 294px;

        &__avatars {
            position: absolute;
            top: -20px;
            left: 0px;
            height: 240px;
            width: 750px;
            overflow: hidden;
        }

        &__online {
            position: absolute;
            top: 190px;
            left: 123px;
            padding-top: 14px;
            width: 482px;
            height: 54px;
            border-radius: 27px;
            font-size: 28px;
            text-align: center;

            > span {
                margin: 0 6px;
                font-weight: 700;
            }
        }
        &__maimai {
            left: 100px !important;
            width: 542px !important;
        }
    }
}

// 方案1 样式
.scheme-type-1,
.scheme-type-3 {
    .collection {
        &-header {
            &__online {
                background: #6200ae;
                color: #ffffff;

                > span {
                    color: #ff6dbe;
                }
            }
        }
    }
}

// 方案2 样式
.scheme-type-2,
.scheme-type-10,
.scheme-type-11 {
    .collection {
        &-header {
            &__online {
                background: #fe8c9a;
                color: #0d0d0d;

                > span {
                    color: #0d0d0d;
                }
            }
        }
    }
}
</style>
