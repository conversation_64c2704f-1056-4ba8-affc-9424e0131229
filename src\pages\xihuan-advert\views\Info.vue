<template>
    <div
        class="info-wrapper"
        :style="cmsConfig.pageColor"
    >
        <!-- 头图 -->
        <img
            class="info-header-wrapper"
            :src="cmsConfig.downloadImg"
            :style="{
                visibility:cmsConfig.downloadImg?'visible':'hidden',
                height:cmsConfig.downloadImg?'auto':'0px'
            }"
        />

        <!-- 圈子宣传区域 -->
        <img
            class="info-content-wrapper"
            :src="cmsConfig.downloadContentImg"
            :style="{
                visibility:cmsConfig.downloadContentImg?'visible':'hidden',
                height:cmsConfig.downloadContentImg?'auto':'0px'
            }"
        />

        <!-- 来信用户区域 -->
        <div class="info-user-wrapper">
            <div
                class="info-user__avatar"
                :style="{backgroundImage:`url(${avatarUrl})`}"
            >
                <div class="info-user__avatar__circle"></div>
            </div>

            <div class="info-user__tips">
                <div
                    class="info-user__tips__message"
                    v-for="(item,index) in tips"
                    :key="index"
                >
                    <span>{{ item }}</span>
                </div>
            </div>

            <!--<button
                @click="goDownloadAnyway"
                class="info-user__btn"
                :style="cmsConfig.buttonColor"
            >
                <div class="info-user__btn__border"></div>
                {{ cmsConfig.downloadButtonText }}
            </button>-->
            <!--<div
                @click="goDownloadAnyway"
                class="info-user__btn"
                :style="cmsConfig.buttonColor"
            >
                <img src="../assets/imgs/new/button-re.png" alt="" style="width: 100%">
            </div>-->
        </div>

        <modal
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
        />
    </div>
</template>

<script>
import {
    mapState,
    mapMutations,
    mapActions
} from 'vuex';

import {
    Modal
} from '../components/common/index.js';

import {reportKibana} from '@/common/utils/report.js';
import {getRandomInt} from "@/common/utils/tools.js";
import {openApp, visibilityChangeDelay, downloadApp} from "@/common/utils/download_app.js";


export default {
    components: {
        Modal
    },
    data() {
        return {
            showModal: false,
            modalType: "modalValidate",
            modalParam: {},
        };
    },
    computed: {
        ...mapState([
            'registerInfo',
            'cmsConfig'
        ]),
        tips() {
            let tipString = this.cmsConfig.downloadUserText;

            // 如果运营未配置
            if (tipString === "缺省"){
                return [
                    `有位${this.registerInfo.gender === 1 ? '小哥哥' : '小姐姐'}和你打了声招呼`,
                    `Ta距离你只有936米哦`
                ];
            }

            // 防止运营配的是英文逗号，兼容处理
            if (tipString.indexOf(',') > -1) {
                return tipString.split(',');
            }

            // 防止运营没有配逗号，兼容处理
            if (tipString.indexOf(',') === -1 && tipString.indexOf('，') === -1) {
                return [
                    `有位${this.registerInfo.gender === 1 ? '小哥哥' : '小姐姐'}和你打了声招呼`,
                    `Ta距离你只有936米哦`
                ];
            }

            // 如果运营正常配置，直接返回数组
            return tipString.split('，');
        },
        avatarUrl(){
            let maleAvatarArr = [
                    'https://photo.zastatic.com/images/common-cms/it/20211216/1639652347300_917465.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211217/1639737989512_148401_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997744957_777816_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559449478_298857.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997828111_763294_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559314943_244052.jpg'
                ],
                femaleAvatarArr = [
                    'https://photo.zastatic.com/images/common-cms/it/20211216/1639622239042_838296_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559392455_178465.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211223/1640255074577_614188_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639995200716_518292_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211223/1640254785897_439198_t.jpg',
                    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997902772_443948_t.jpg'
                ];

            let index = getRandomInt(0,5);
            return this.registerInfo.gender === 1 ?maleAvatarArr[index]:femaleAvatarArr[index];
        }
    },
    async created() {
        // 获取注册态
        let flagFilled = localStorage.getItem("flagFilled");
        console.log(flagFilled);

        // 没有注册态，跳回大表单页
        if(flagFilled !== '1'){
            this.$router.push({
                path:'index'
            });
        }

        // 注册态只能用一次,刷新也会导致重新进入大表单页
        localStorage.setItem("flagFilled", "0");

        // 清空注册信息
        localStorage.setItem("localFormInfo", "");
        localStorage.setItem("localRegisterInfo", "");
        localStorage.setItem("defaultBirthday", "");
        localStorage.setItem("defaultWorkCity", "");

        // 请求模特信息
        // await this.setModelInfo();
    },
    async mounted() {


        // 打桩
        reportKibana("线下大表单", 6, "下载页访问", {});
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
    },
    methods: {
        ...mapActions([
            'setModelInfo'
        ]),
        goDownloadAnyway(){
            // 打桩
            reportKibana("线下大表单", 7, "下载页-回应按钮点击", {});

            // if(this.cmsConfig.downloadStatus === 0){
            //     // 【不可直接跳转下载】弹窗告知去各大应用市场下载
            //     this.showModal = true;
            //     this.modalType = 'modalDownload';
            //     this.modalParam = {};
            //     return;
            // }

            // 尝试打开app，500毫秒后再去下载
            // visibilityChangeDelay(function () {
            //     downloadApp();
            // }, 500);
            // openApp();
        },
        closeModal(){
            this.showModal = false;
        },
    }
};
</script>

<style lang="scss" scoped>
    @import "../index.scss";
    .info-wrapper {
        position: relative;
        background: #fdd1d6;
        /*padding-bottom: 100px;*/
        overflow: hidden;
    }

    .info-header-wrapper {
        width: 750px;
        height: 334px;
    }

    .info-content-wrapper {
        margin-top: 20px;
        width: 750px;
        height: 334px;
        position: absolute;
        top: 617px;
        left: 0px;
    }

    .info-user-wrapper {
        position: absolute;
        @include set-img("../assets/imgs/new/sms.png");
        margin: 20px auto 0;
        width: 695px;
        height: 443px;
        top: 1000px;
        left: 30px;
        // overflow: hidden;
    }

    .info-user__avatar{
        position: absolute;
        top: 60px;
        left: 20px;
        width: 100px;
        height: 100px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        border: 2px solid #000000;
        border-radius: 16px;

        .info-user__avatar__circle{
            position: absolute;
            bottom: 0;
            right: 0;
            transform: translate(50%,50%);
            width: 22px;
            height: 22px;
            border: 1px solid #000000;
            background-color: #75f871;
            border-radius: 50%;
        }
    }

    .info-user__tips {
        position:absolute;
        width: 480px;
        top: 90px;
        right: 40px;
        font-family: SourceHanSansSC;
        font-size: 30px;
        color: #550632;


        .info-user__tips__message {
            line-height: 50px;

            &:nth-child(2){
                span{

                    padding-bottom: 5px;
                    border-bottom: 2px solid #550632;
                }
                // text-underline-position: above;
                // text-decoration: underline;
            }
        }


    }

    .info-user__btn {
        display: block;
        position: absolute;
        top: 290px;
        left: 50%;
        transform: translateX(-50%);
        width: 616px;
        height: 119px;
        background: linear-gradient(180deg,#fff,#fdc632);
        border-radius: 58px;

        line-height: 119px;
        text-align: center;
        font-family: SourceHanSansSC;
        font-size: 38px;
        font-weight: 700;

        animation: trans 1.5s linear infinite;

        @keyframes trans{
            50%{
                transform: translateX(-50%) scale(0.9)
            }
        }

        .info-user__btn__border{
            position: absolute;
            top: 0;
            left: -10px;
            width: 627px;
            height: 119px;
            @include set-img("../assets/imgs/download_button.png");
        }



    }
</style>
