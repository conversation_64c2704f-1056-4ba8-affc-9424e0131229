<template>
    <question-panel>
        <div class="question-tag">
            <question-tag-item
                v-for="(item, index) in list"
                :key="index"
                :item="item"
                class="question-tag-item"
                :class="[item.check ? 'active' : '']"
                @click="handleCheck(item)"
            />
        </div>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import QuestionTagItem from "./question-tag-item";
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-tag",
    components: {
        QuestionPanel,
        QuestionTagItem
    },
    computed: {
        ...mapState(["cmsConfig", "requirement", "registerForm"]),
        list() {
            const tagList =
                this.registerForm.gender === 0
                    ? [
                          "体贴懂事",
                          "御姐范",
                          "高颜值",
                          "贤惠孝顺",
                          "自信独立",
                          "聪明",
                          "女强人",
                          "甜妹",
                          "温婉居家",
                          "搞笑女"
                      ]
                    : [
                          "责任感爆棚",
                          "1米8",
                          "高颜值",
                          "性格温和",
                          "内敛稳重",
                          "暖男",
                          "靠谱专一",
                          "厨艺好",
                          "温柔顾家",
                          "斜杠青年"
                      ];

            return tagList.map(item => {
                return {
                    text: item,
                    check: this.$z_.includes(this.requirement.tags, item)
                };
            });
        }
    },
    methods: {
        ...mapMutations(["setRequirement"]),
        handleCheck(item) {
            const tags = this.$z_.clone(this.requirement.tags);

            if (item.check) {
                return;
                // const index = this.$z_.findIndex(tags, (text) => {
                //     return text === item.text;
                // });

                // tags.splice(index, 1);
            } else {
                // 最多选择两个
                if (tags.length >= 2) {
                    tags.shift();
                }
                tags.push(item.text);
            }

            this.setRequirement({
                tags
            });
        }
    }
};
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.question-tag {
    padding: 110px 32px 0;
    width: 100%;
    @include flex-center(row, null, null, wrap);

    &-item {
        margin-top: 16px;
    }

    .active {
        background-image: none !important;
        background: #fe4f06;
    }
}
</style>
