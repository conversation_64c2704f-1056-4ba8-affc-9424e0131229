// 输入m,n, 返回整数x, m<=x<=n
export function getRandomInt(m, n) {
    return m + Math.floor(Math.random() * (n - m + 1));
}

export function addClass(obj, cls) {
    var obj_class = obj.className, //获取class内容.
        blank = obj_class != "" ? " " : ""; //判断获取到的 class 是否为空, 如果不为空在前面加个'空格'.
    let added = obj_class + blank + cls; //组合原来的 class 和需要添加的 class.
    obj.className = added; //替换原来的 class.
}

export function removeClass(obj, cls) {
    var obj_class = " " + obj.className + " "; //获取 class 内容, 并在首尾各加一个空格. ex) 'abc    bcd' -> ' abc    bcd '
    (obj_class = obj_class.replace(/(\s+)/gi, " ")), //将多余的空字符替换成一个空格. ex) ' abc    bcd ' -> ' abc bcd '
        (removed = obj_class.replace(" " + cls + " ", " ")); //在原来的 class 替换掉首尾加了空格的 class. ex) ' abc bcd ' -> 'bcd '
    removed = removed.replace(/(^\s+)|(\s+$)/g, ""); //去掉首尾空格. ex) 'bcd ' -> 'bcd'
    obj.className = removed; //替换原来的 class.
}

export function hasClass(obj, cls) {
    var obj_class = obj.className, //获取 class 内容.
        obj_class_lst = obj_class.split(/\s+/); //通过split空字符将cls转换成数组.
    x = 0;
    for (x in obj_class_lst) {
        if (obj_class_lst[x] == cls) {
            //循环数组, 判断是否包含cls
            return true;
        }
    }
    return false;
}

// 获取当前链接的参数
export function getQueryData() {
    var arr = window.location.search.substring(1).match(/[^=&]+=[^&]+/g);
    var res = {};
    if (arr) {
        for (var i = 0; i < arr.length; i++) {
            var str = arr[i];
            var index = str.indexOf('=');
            res[str.substring(0, index)] = decodeURIComponent(str.substring(index + 1) || '');
        }
    }
    return res;
}

// 往url追加参数
export function addParam(url, queryData) {
    if (!url) {
        return url;
    }
    var res = url.indexOf('?') > -1 ? url + '&' : url + '?';
    for (var k in queryData) {
        res += k + '=' + encodeURIComponent(queryData[k]) + '&';
    }
    return res;
}

// 处理输入手机号保留3-4-4格式
export const formatPhone = e => {
    // 存储当前光标位置用于之后重置
    let position = e.target.selectionStart;

    if (e.inputType === "deleteContentBackward") {
        // 删除的情况
        // 删到空格时 需要多删除空格前一位数
        if (position === 3 || position === 8) {
            let temArr = e.target.value.split("");
            temArr.splice(position - 1, 1);
            e.target.value = temArr.join("");
            // 光标也要跟着前移一位
            position -= 1;
        }
        // 格式化
        e.target.value = e.target.value
            .replace(/\D/g, "")
            .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3")
            .trim();
        // 重置光标,setTimeout用于兼容苹果手机
        setTimeout(() => {
            e.target.selectionStart = e.target.selectionEnd = position;
        }, 0);
        // e.target.selectionStart = e.target.selectionEnd = position;
    } else if (e.inputType === "insertText") {
        // 插入的情况
        if (e.target.value.length < 9) {
            e.target.value = e.target.value
                .replace(/\D/g, "")
                .replace(/(\d{3})(\d{0,4})/, "$1 $2");
        } else {
            e.target.value = e.target.value
                .replace(/\D/g, "")
                .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
        }
        if (position < e.target.value.length) {
            // 输入空格后第一位数时 光标要往后移一位
            if (position === 4 || position === 9) {
                position += 1;
            }
            // 重置光标
            setTimeout(() => {
                e.target.selectionStart = e.target.selectionEnd = position;
            }, 0);
        }
    } else {
        // 复制粘贴的情况
        let pasteStr = e.target.value.replace(/\D/g, "");
        // maxlenth为13，所以这里要限制位数大于11时截取
        if (pasteStr > 11) {
            e.target.value = pasteStr
                .substr(0, 11)
                .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
        }
    }

    return e.target.value;
};

const imgPreloader = url => {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = () => {
            resolve('图片加载成功');
        }
        image.onerror = () => {
            reject('图片加载失败')
        }
        image.src = url;
    })
}

export function allImagePreloader(imgs) {
    let promiseArr = [];
    imgs.forEach(src => {
        promiseArr.push(imgPreloader(src));
    })
    return Promise.all(promiseArr)
}
