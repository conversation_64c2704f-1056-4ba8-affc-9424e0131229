<template>
    <div class="collection-wrapper">
        <img class="bg-img"  :src="actType == '1' ? 'https://static.jiebao.zhenai.com/20250107-4086-6de5747e-4001-4498-8fd3-c1b8873308c2.png' : 'https://photo.zastatic.com/images/common-cms/it/20240222/1708571581715_560698_t.png'" alt="">
        <img @click="goMin" class="button" :src="actType == '1' ? 'https://photo.zastatic.com/images/common-cms/it/20250102/1735786812683_192699_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20240222/1708571587035_312100_t.png'" alt="">
    </div>
</template>

<script>

import Api from "@/common/server/base";
export default {
    data() {
        return {
            activityId: '',
            subChannelId:'',
            actType:'', // 默认是进群  1：高质量旅游相亲活动
        };
    },
    async created(){},
    mounted(){
        this.activityId = Z.getParam('a');
        this.subChannelId = Z.getParam('s');
        this.actType = Z.getParam('actType');
        // 打桩
        Z.tj.kibanaV2({
            resourceKey: '地推H5',
            accessPoint: 1,
            accessPointDesc: 'h5落地页曝光',
            ext1: this.activityId,
        });
    },
    methods:{
        async goMin () {
            Z.tj.kibanaV2({
                resourceKey: '地推H5',
                accessPoint: 2,
                accessPointDesc: 'h5落地页按钮点击',
                ext1: this.activityId,
            });
            const params = {
                path: 'pages/common/activity-notice-group/index',
                query: `scene=${encodeURIComponent(`a=${this.activityId}&s=${this.subChannelId}&b=1`)}`,
            };
            let resData = await Api.goYouLianMini(params);
            if (resData.code === 0) {
                location.href = resData.data;
            }
        }
    }
};
</script>

<style lang="scss" scoped>

@keyframes scaleDraw {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
.collection-wrapper{
    width: 750px;
    background-color: #ffb6d6;
    position: relative;
    .bg-img {
        width: 100%;
    }
    .button {
        position: fixed;
        left: 10%;
        bottom: 30px;
        width: 80%;
        animation-name: scaleDraw;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
        animation-duration: 2s;
    }
}

</style>
