import VueRouter from "vue-router";

import Collection from "../views/Collection.vue";
import Info from "../views/Info.vue";

const router = new VueRouter({
    mode: "hash",
    routes: [{
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            component: Collection
        },
        {
            path: "/info",
            component: Info
        }
    ],
    scrollBehavior(to, from, savedPosition) {
        return { x: 0, y: 0 }
    }
});

export default router;
