<template>
    <div 
        class="qr-code"
        v-if="this.wxInfo.code"
    >
        <img
            @touchstart="handleTouchStart"
            @touchend="handleTouchEnd"
            @touchcancel="handleTouchEnd"
            @load="handleLoaded"
            :src="wxInfo.code"
            alt="企微二维码"
        />
        <div class="title">
            添加老师微信
        </div>
        <div class="tips">
            <span>获得免费1对1深度解读的机会</span>
        </div>
        <div
            class="button"
            @click="handleJump"
        >
            去微信添加
        </div>
    </div>
</template>

<script>
import { getMiniPathV2 } from '@/common/business/utils/wecom';
import { mapState } from "vuex";
import { pageTypeChnMap } from '@/common/config/register-dictionary';

export default {
    name:'QrCode',
    props:{
        result:{
            type:String,
            default:''
        },
        proverbs:{
            type:String,
            default:''
        }
    },
    async mounted(){
        this.$reportKibana(this.resourceKey, 121, '报告页-二维码曝光');

        
        
    },
    computed:{
        ...mapState(["resourceKey","wxInfo"]),
    },
    watch:{
        async proverbs(){
            const queryObj = {
                wxQr: this.wxInfo.code,
                workerId:this.wxInfo.workerId,
                pageType: pageTypeChnMap[this.resourceKey],
                ext30: pageTypeChnMap[this.resourceKey],
                result: this.result,
                proverbs: this.proverbs
            };
            this.wxInfo.link = await getMiniPathV2('pages/qrCode/mbti/mbti',queryObj);
        }
    },
    methods:{
        handleJump() {
            this.$reportKibana(this.resourceKey, 122, '报告页-点击跳转小程序', {
                ext18: this.wxInfo.workerId
            });
            window.location.href = this.wxInfo.link; 
        },

        handleTouchStart() {
            this.timer = setTimeout(() => {
                // 模拟长按事件
                this.$reportKibana(this.resourceKey, 123, '报告页-长按二维码', {
                    ext18: this.wxInfo.workerId
                });
                
            }, 500);
        },

        handleTouchEnd() {
            clearTimeout(this.timer);
        },
        handleLoaded() {
            this.$reportKibana(this.resourceKey, 121, '报告页-二维码曝光-加载成功', {
                ext18: this.wxInfo.workerId
            });
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.qr-code{
    position: relative;
    @include flex-center(column, flex-start, center);
    @include set-img('https://photo.zastatic.com/images/common-cms/it/20221017/1665992785430_879364_t.png');
    width: 686px;
    height: 794px;
    &::before{
        position: absolute;
        content: '';
        left: 0;
        top: -56px;
        width: 33px;
        height: 88px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221017/1665995164738_684030_t.png");
    }

    > img {
        width: 378px;
        height: 378px;
        margin-top: 60px;
    }

    > .title{
        margin-top: 40px ;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
        line-height: 28px;
    }

    > .tips{
        position: relative;
        margin-top: 28px;
        font-weight: 500;
        font-size: 44px;
        color: #FFFFFF;
        line-height: 52px;

        > span{
            position: relative;
            z-index: 2;
        }

        &::after{
            position: absolute;
            content: '';
            left: -14px;
            top: 38px;
            width: 520px;
            height: 28px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221017/1665994434116_298691_t.png");
            z-index: 1;
        }
    }

    >.button{
        position: relative;
        margin-top: 60px;
        padding-left: 60px;
        width: 620px;
        height: 120px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221017/1665993127088_645422_t.png");
        font-weight: 400;
        font-size: 36px;
        color: #FFFFFF;
        text-align: center;
        line-height: 120px;

        &::before{
            position: absolute;
            content: '';
            left: 188px;
            top: 50%;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20221017/1665994235225_513595_t.png");
        }
    }
}
</style>
