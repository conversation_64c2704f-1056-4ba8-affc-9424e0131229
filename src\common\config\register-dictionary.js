// 表单名
export const chineseNameMap = {
    gender: '性别',
    workCity: '工作地',
    birthday: '出生年份',
    education: '学历',
    marriage: '婚况',
    salary: '收入',
    phone: '手机号'
};
export const occupation1Obj = {
    100: '销售',
    200: '客户服务',
    300: '计算机/互联网',
    400: '通信/电子',
    500: '生产/制造',
    600: '物流/仓储',
    700: '商贸/采购',
    800: '人事/行政',
    900: '高级管理',
    1000: '广告/市场',
    1100: '传媒/艺术',
    1200: '生物/制药',
    1300: '医疗/护理',
    1400: '金融/银行/保险',
    1500: '建筑/房地产',
    1600: '咨询/顾问',
    1700: '法律',
    1800: '财会/审计',
    1900: '教育/科研',
    2000: '服务业',
    2100: '交通运输',
    2200: '政府机构',
    2300: '军人/警察',
    2400: '农林牧渔',
    2500: '自由职业',
    2600: '在校学生',
    2700: '待业',
    2800: '其他行业',
};

export const occupation2Obj = {
    101: '销售总监',
    102: '销售经理',
    103: '销售主管',
    104: '销售专员',
    105: '渠道/分销管理',
    106: '渠道/分销专员',
    107: '经销商',
    108: '客户经理',
    109: '客户代表',
    110: '其他',
    201: '客服经理',
    202: '客服主管',
    203: '客服专员',
    204: '客服协调',
    205: '客服技术支持',
    206: '其他',
    301: 'IT技术总监',
    302: 'IT技术经理',
    303: 'IT工程师',
    304: '系统管理员',
    305: '测试专员',
    306: '运营管理',
    307: '网页设计',
    308: '网站编辑',
    309: '网站产品经理',
    310: '其他',
    401: '通信技术',
    402: '电子技术',
    403: '其他',
    501: '工厂经理',
    502: '工程师',
    503: '项目主管',
    504: '营运经理',
    505: '营运主管',
    506: '车间主任',
    507: '物料管理',
    508: '生产领班',
    509: '操作工人',
    510: '安全管理',
    511: '其他',
    601: '物流经理',
    602: '物流主管',
    603: '物流专员',
    604: '仓库经理',
    605: '仓库管理员',
    606: '货运代理',
    607: '集装箱业务',
    608: '海关事物管理',
    609: '报单员',
    610: '快递员',
    611: '其他',
    701: '商务经理',
    702: '商务专员',
    703: '采购经理',
    704: '采购专员',
    705: '外贸经理',
    706: '外贸专员',
    707: '业务跟单',
    708: '报关员',
    709: '其他',
    801: '人事总监',
    802: '人事经理',
    803: '人事主管',
    804: '人事专员',
    805: '招聘经理',
    806: '招聘专员',
    807: '培训经理',
    808: '培训专员',
    809: '秘书',
    810: '文员',
    811: '后勤',
    812: '其他',
    901: '总经理',
    902: '副总经理',
    903: '合伙人',
    904: '总监',
    905: '经理',
    906: '总裁助理',
    907: '其他',
    1001: '广告客户经理',
    1002: '广告客户专员',
    1003: '广告设计经理',
    1004: '广告设计专员',
    1005: '广告策划',
    1006: '市场营销经理',
    1007: '市场营销专员',
    1008: '市场策划',
    1009: '市场调研与分析',
    1010: '市场拓展',
    1011: '公关经理',
    1012: '公关专员',
    1013: '媒介经理',
    1014: '媒介专员',
    1015: '品牌经理',
    1016: '品牌专员',
    1017: '其他',
    1101: '主编',
    1102: '编辑',
    1103: '作家',
    1104: '撰稿人',
    1105: '文案策划',
    1106: '出版发行',
    1107: '导演',
    1108: '记者',
    1109: '主持人',
    1110: '演员',
    1111: '模特',
    1112: '经纪人',
    1113: '摄影师',
    1114: '影视后期制作',
    1115: '设计师',
    1116: '画家',
    1117: '音乐家',
    1118: '舞蹈',
    1119: '其他',
    1201: '生物工程',
    1202: '药品生产',
    1203: '临床研究',
    1204: '医疗器械',
    1205: '医药代表',
    1206: '化工工程师',
    1207: '其他',
    1301: '医疗管理',
    1302: '医生',
    1303: '心理医生',
    1304: '药剂师',
    1305: '护士',
    1306: '兽医',
    1307: '其他',
    1401: '投资',
    1402: '保险',
    1403: '金融',
    1404: '银行',
    1405: '证券',
    1406: '其他',
    1501: '建筑师',
    1502: '工程师',
    1503: '规划师',
    1504: '景观设计',
    1505: '房地产策划',
    1506: '房地产交易',
    1507: '物业管理',
    1508: '其他',
    1601: '专业顾问',
    1602: '咨询经理',
    1603: '咨询师',
    1604: '培训师',
    1605: '其他',
    1701: '律师',
    1702: '律师助理',
    1703: '法务经理',
    1704: '法务专员',
    1705: '知识产权专员',
    1706: '其他',
    1801: '财务总监',
    1802: '财务经理',
    1803: '财务主管',
    1804: '会计',
    1805: '注册会计师',
    1806: '审计师',
    1807: '税务经理',
    1808: '税务专员',
    1809: '成本经理',
    1810: '其他',
    1901: '教授',
    1902: '讲师/助教',
    1903: '中学教师',
    1904: '小学教师',
    1905: '幼师',
    1906: '教务管理人员',
    1907: '职业技术教师',
    1908: '培训师',
    1909: '科研管理人员',
    1910: '科研人员',
    1911: '其他',
    2001: '餐饮管理',
    2002: '厨师',
    2003: '餐厅服务员',
    2004: '酒店管理',
    2005: '大堂经理',
    2006: '酒店服务员',
    2007: '导游',
    2008: '美容师',
    2009: '健身教练',
    2010: '商场经理',
    2011: '零售店店长',
    2012: '店员',
    2013: '保安经理',
    2014: '保安人员',
    2015: '家政服务',
    2016: '其他',
    2101: '飞行员',
    2102: '空乘人员',
    2103: '地勤人员',
    2104: '列车司机',
    2105: '乘务员',
    2106: '船长',
    2107: '船员',
    2108: '司机',
    2109: '其他',
    2201: '公务员',
    2202: '其他',
};

// 性别
export const gender = [
    {
        "text": "男士",
        "key": 0
    },
    {
        "text": "女士",
        "key": 1
    }
];

// 性别
export const genderV2 = [
    {
        "text": "男生",
        "key": 0
    },
    {
        "text": "女生",
        "key": 1
    }
];

// 性别
export const genderV3 = [
    {
        "text": "男",
        "key": 0
    },
    {
        "text": "女",
        "key": 1
    }
];
export const isSingle = [
    {
        "text": "是",
        "key": 1
    },
    {
        "text": "否",
        "key": 0
    }
];

// 出生年份
const birthday = [];
let currentYear = new Date().getFullYear();
for (let i = currentYear - 70; i <= currentYear - 18; i++) {
    birthday.push({
        text: i,
        key: i
    });
}
export { birthday };

// 出生年份
const birthdayV2 = {};
for (let i = currentYear - 70; i <= currentYear - 18; i++) {
    const flag = Math.floor(i / 10) % 10 + '0';
    if (!birthdayV2[flag]) {
        birthdayV2[flag] = [];
    }
    birthdayV2[flag].push(i);
}

export { birthdayV2 };

// 出生年份
const birthdayV3 = {};
for (let i = currentYear - 98; i <= currentYear - 18; i++) {
    const flag = Math.floor(i / 10) % 10 + '0';
    if (!birthdayV3[flag]) {
        birthdayV3[flag] = [];
    }
    birthdayV3[flag].push(i);
}

export { birthdayV3 };

// 婚况
export const marriage = [
    {
        "text": "未婚",
        "key": 1
    },
    {
        "text": "离异",
        "key": 3
    },
    {
        "text": "丧偶",
        "key": 4
    }
];

// 学历
export const education = [
    // 此处的显示顺序和后台返回的数据不一样
    {
        "text": "高中及以下",
        "key": 3
    },
    {
        "text": "中专",
        "key": 2
    },
    {
        "text": "大专",
        "key": 4
    },
    {
        "text": "大学本科",
        "key": 5
    },
    {
        "text": "硕士",
        "key": 6
    },
    {
        "text": "博士",
        "key": 7
    }
];

const height = [];
for (let i = 140; i <= 200; i++) {
    height.push({
        text: `${i}cm`,
        key: i
    });
}
export { height };

const heightV2 = {};
for (let i = 129; i <= 211; i++) {
    const group = Math.floor(i / 10) * 10;
    if (!heightV2[group]) {
        heightV2[group] = [];
    }
    heightV2[group].push(i);
}
export { heightV2 };

// 薪水
export const salary = [
    {
        "text": "3000元以下",
        "key": 3
    },
    {
        "text": "3001-5000元",
        "key": 4
    },
    {
        "text": "5001-8000元",
        "key": 5
    },
    {
        "text": "8001-12000元",
        "key": 6
    },
    {
        "text": "12001-20000元",
        "key": 7
    },
    {
        "text": "20001-50000元",
        "key": 8
    },
    {
        "text": "50000元以上",
        "key": 9
    }
];


export const registerResult = {
    NEW_ACCOUNT: { // 未注册过的新用户
        label: '新注册',
        value: 1,
    },
    LOGIN_ACCOUNT: { // 已注册用户，不满足覆盖条件
        label: '登录',
        value: -1,
    },
    MANUAL_OVERWRITE_ACCOUNT: { // 已注册用户，满足覆盖条件，需要用户确认
        label: '覆盖注册',
        value: -2
    },
    AUTO_OVERWRITE_ACCOUNT: { // 已注册用户，满足覆盖条件，不需要用户确认
        label: '覆盖注册',
        value: -3,
    },
};

// 达人和index用的同一个值
export const pageTypeMap = {
    DEFAULT: 1, // resourceKey: '新注册流程2.0'
    INDEX: 3, // resourceKey: '导量H5大表单翻牌',
    DAREN: 3, // resourceKey: '达人'
    PLAN: 4, // resourceKey: '脱单计划H5'
    PET: 5, // resourceKey: '宠物钩子'
    LOVE_GENE: 6, // resourceKey: '恋爱基因'
    QUESTIONS: 7, // resourceKey: 'h5-test-recommend' 即测试题+翻牌
    HALL: 8, // resourceKey: '脱单营业厅'
    MBTI_TEST_A: 9, // resourceKey: 'mbti-test-A' MBTI测试A方案
    MBTI_TEST_B: 10, // resourceKey: 'mbti-test-B' MBTI测试B方案
    MBTI_TEST_C: 16,
    XIHUAN: 132,
    OFFLINETABLE: 8026,
};

export const pageTypeChnMap = {
    '新注册流程2.0': 1,
    '导量H5大表单翻牌': 3,
    '脱单计划H5': 4,
    '宠物钩子': 5,
    '恋爱基因': 6,
    'h5-test-recommend': 7,
    '脱单营业厅': 8,
    'MBTI钩子A方案': 9,
    'MBTI钩子B方案': 10,
    'MBTI钩子C方案': 16,
    '同城交友A方案(活动)': 17,
    '同城交友B方案(群聊)': 18,
    '同城交友C方案(单人)': 19,
    '旧落地页(翻牌)': 20,
    '大表单翻牌(新流程)': 21,
    '大表单翻牌(头条跳转下载)': 23,
    'MBTI钩子D方案': 24,
    '脱单游戏': 29,
    '锵锵锵H5': 30,
    'MBTI钩子D方案(新下载)': 31, // 已下线
    '同城脱单群聊(A)': 34,
    '同城脱单活动(A)': 36,
    '咚咚咚H5': 39,
    '导量H5大表单企微': 40,
    '定制恋人（导APP）': 42,
    '定制恋人（导小程序）': 43,
    '旧落地页(背书)': 44,
    '旧落地页(推荐)': 45,
    'MBTI钩子D方案(企微)':47,
    '大表单橙子': 51,
    '定制恋人新UI(APP)': 55,
    '定制恋人新UI(小程序)': 56,
    '单身自救指南(APP活跃)': 64,
    '单身自救指南(小程序活跃)': 65,
    '订制理想恋人（新注册导APP）': 78,
    '单身自救指南(新APP活跃)': 79,
    '头条广告落地页（测试组1）': 80,
    '头条广告落地页（测试组2）': 81,
    '头条广告落地页（测试组3）': 82,
    '头条广告落地页（测试组4）': 83,
    '全屏下载页（新UI测试）': 86,
    '恋爱人格测试': 131,
    '优恋大表单': 132,
    '禧欢大表单': 133,
    '简爱大表单': 134,
    '禧爱大表单': 135,
    '自定义大表单': 136,
    '新注册路径手机前置': 8022,
    '线下大表单': 8026,
    '靠近品牌钩子': 10000,
    '恋爱段位考试': 8029,

};

// 出生年份

export const getBirthdayDict = ({top = 98, bottom = 18}) => {
    let currentYear = new Date().getFullYear();
    const birthday = {};
    for (let i = currentYear - top, j=0; i <= currentYear - bottom; i++, j++) {
        const flag = Math.floor(i / 10) % 10 + '0';
        if (!birthday[flag]) {
            birthday[flag] = [];
        }
        birthday[flag].push(i);
    }
    const names = Object.keys(birthday);
    return {names, birthday};
};
