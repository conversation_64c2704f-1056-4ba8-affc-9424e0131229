<template>
    <div id="app">
        <router-view
            :key="$route.fullPath"
            v-if="canRender"
        />
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import Api from "@/common/server/base";
import z_ from "@/common/zdash";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';

export default createRoot({
    name: "App",
    data() {
        return {
            cmsConfig: {
                // 默认配置
                planName: "同城脱单活动(A)",
                homeHeadImg: "",
                entity: [],
                agreementStatus: 0,
                downloadStatus: 0,
                homeButtonText: "",
                homeBackgroundColor: ""
            },
            canRender: false,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    mounted() {
        this.handleInitCmsConfig();
        judgeIfInToutiaoIos();
    },
    methods: {
        async handleInitCmsConfig() {
            const id = Z.getParam("materialId");

            // 没有id的情况下，用默认配置
            if (!id) {
                window._zconfig.resourceKey = this.cmsConfig.planName;
                this.canRender = true;
                return;
            }

            const result = await Api.getHookMaterialInfo({ id });

            if (result.isError) {
                window._zconfig.resourceKey = this.cmsConfig.planName;
                this.canRender = true;
                return;
            }

            // 处理渐变
            function handleColor(color) {
                if (color.indexOf("&") > -1) {
                    // 按钮为渐变色
                    const [leftColor, rightColor] = color.split("&");
                    return `linear-gradient(-89deg, ${leftColor} 0%, ${rightColor} 100%)`;
                }
                return color;
            }

            const data = z_.get(result, "data");

            if (data.planName === "同城脱单群聊") {
                data.entity = z_.map(data.entity, o => {
                    return {
                        ...o,
                        groupChatButtonColor: handleColor(
                            o.groupChatButtonColor
                        )
                    };
                });
            }

            data.planName = `${data.planName}(A)`;

            window._zconfig.resourceKey = data.planName;
            this.cmsConfig = Object.assign(
                this.cmsConfig,
                z_.pick(data, Object.keys(this.cmsConfig))
            );
            this.canRender = true;
        },
    }
});
</script>

<style lang="scss" scoped></style>
