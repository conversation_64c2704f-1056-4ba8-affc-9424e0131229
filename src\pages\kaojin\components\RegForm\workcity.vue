<template>
    <div class="workcity">
        <div class="workcity-info">
            <van-picker
                ref="refPicker"
                item-height="1.25rem"
                :visible-item-count="5"
                :show-toolbar="false"
                :columns="list.options"
                class="workcity-info-picker"
            />
        </div>
        <img
            :src="bannerMap[gender]"
            alt=""
            class="marriage_banner"
        >

        <div
            class="btn"
            @click="goNext"
        >
            下一步
        </div>
    </div>
</template>
<script>
import { Picker } from "vant";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import "vant/lib/picker/style";
import { setLocalRegisterForm } from "../../utils/index";
import { PAGE_TYPE } from "../../config";
export default {
    name: "WorkCity",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        vanPicker: Picker
    },
    data() {
        return {
            bannerMap: {
                0: 'https://photo.zastatic.com/images/common-cms/it/20240522/1716373476556_316432.png',
                1: 'https://photo.zastatic.com/images/common-cms/it/20240522/1716373452323_625874.png'
            },
            gender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender,
            dict: Z.workCity
        };
    },
    activated () {
        this.gender = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender;
    },
    mounted() {
        // 初始化定位
        this.handleInitArea();
    },
    methods: {
        async goNext() {
            const picker = this.$refs.refPicker;
            const values = picker.getValues();

            const workCity = values[2].key ? values[2].key : values[1].key ? values[1].key : "";
            // const workCity = values[2].key ? values[1].key : values[1].key ? values[1].key : "";
            const params = {
                key: "cityCode",
                value: workCity,
                isMark: false
            };

            if (["北京", "上海", "重庆", "天津"].includes(values[0].text)) {
                sessionStorage.setItem('workCityReport', values[0].key);
            } else {
                sessionStorage.setItem('workCityReport', values[1].key ? values[1].key : '');
            }
            setLocalRegisterForm(params, PAGE_TYPE);

            this.$report(14, '工作地页-按钮点击', {
                ext28: workCity
            });
            this.$emit('go-next');
        },
        handleInitArea() {
            const workCity =
                (Storage.getItem(
                    `cachedRegisterForm-${PAGE_TYPE}`
                ) &&
                    Storage.getItem(
                        `cachedRegisterForm-${PAGE_TYPE}`
                    ).cityCode) ||
                "";
            if (workCity) {
                const cityArr = findWorkCity(workCity);
                this.$refs.refPicker.setValues(cityArr);
                const loveAutoJumpx = sessionStorage.getItem('loveAutoJumpx');
                if (loveAutoJumpx != 1) {
                    this.goNext();
                }
            } else {
                // 有缓存优先读缓存，否则走定位逻辑
                this.handleLocate();
            }
        },
        handleLocate() {
            const loveAutoJumpx = sessionStorage.getItem('loveAutoJumpx');
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                    // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {
                    //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([
                                result.addressComponent.province,
                                result.addressComponent.city,
                                result.addressComponent.district
                            ]);
                            this.$refs.refPicker.setValues(areaArr);
                            if (loveAutoJumpx != 1) {
                                this.goNext();
                            }
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if (status === "complete") {
                        const areaArr = this.handleLocationPair([
                            result.province,
                            result.city,
                            ""
                        ]);

                        this.$refs.refPicker.setValues(areaArr);
                        if (loveAutoJumpx != 1) {
                            this.goNext();
                        }
                    } else {
                        this.$refs.refPicker.setValues([
                            "广东",
                            "肇庆",
                            "端州区"
                        ]); // 缺省地区
                    }
                });
            });
        },
        handleLocationPair(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            const targetProvince = this.$z_.find(this.dict, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });

            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(
                targetCity.children,
                district => {
                    return district.text.indexOf(sliceDistrict) >= 0;
                }
            );

            return [targetProvince.text, targetCity.text, targetDistrict.text];
        },
    }
};
</script>

<style lang="scss">
.workcity {
    display: flex;
    flex-direction: column;
    align-items: center;
    &-info {
        width: 690px;
        height: 497px;
        background: #FFFFFF;
        border-radius: 56px;
        &-picker {
            background: transparent;
            .van-picker__mask {
                background: linear-gradient(180deg, transparent),
                    linear-gradient(0deg, transparent);
            }
            .van-picker-column__item {
                opacity: 0.7;
                color: #222222;
                font-size: 30px;
                // font-family: 'love';
                > div{
                    line-height: 1.4;
                }

                &--selected {
                    opacity: 1;
                    color: #a98ce6;
                    font-size: 36px;
                }
            }
        }
    }
    .btn {
        position: fixed;
        bottom: 50px;
        left: 50%;
        transform: translateX(-50%);
        width: 550px;
        height: 115px;
        border-radius: 64px;
        background: #222833;
        line-height: 115px;
        text-align: center;
        color: #3cede1;
        font-size: 34px;
        font-weight: 600;

    }
    .van-picker-column {
        z-index: 1;
    }
    .van-picker-column__item--selected {
        font-weight: 600;
        font-size: 34px;
        color: #222833;
        text-align: center;
    }
    .van-picker__frame {
        background-image: linear-gradient(90deg, #B2F8F4 0%, #CBFFEB 100%) !important;
        border-radius: 44px !important;
        z-index: 0;
    }
    .marriage_banner {
        width: 100vw;
        margin-top: 60px;
    }
}
</style>
