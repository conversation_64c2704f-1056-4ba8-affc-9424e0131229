<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curSalary === item.key?'active':''"
        >
            {{ item.text }}
        </div>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Salary",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    mounted() {
        this.$report(38, '收入页访问');
    },
    data() {
        return {
            lock:false,
            curSalary: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).salary || ''
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curSalary = val;
            const params = {
                key: "salary",
                value: val
            };
            this.$report(39, '收入页-按钮点击');
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                if (pageTypeChnMap[PAGE_TYPE] === 79) {
                    this.$router.push({
                        path:`/finish-v2`
                    });
                } else {
                    this.$router.push({
                        path:`/finish`
                    });
                }
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper{
    position: absolute;
    top: 300px;
    left: 50%;
    transform: translateX(-50%);
    @include flex-center(column, flex-start, center);
    max-height: 784px;
    > .item{
        @include flex-center(column, center, center);
        margin-top: 32px;
        padding: 0 48px;
        width: 564px;
        height: 100px;
        background: #EAF1FA;
        border-radius: 55px;
        font-size: 32px;
        color: #17263D;
        line-height: 1.2;
    }

    > .active{
        background: #17263D;
        color: #fff;
    }
}
</style>
