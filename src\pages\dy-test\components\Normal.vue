<template>
    <div class="normal">
        <common-btn
            v-for="(option, index) in options"
            :btnText="option.text"
            :btnStyle="[
                selectIndex === option.text
                    ? btnStyleObj.active
                    : btnStyleObj.normal
            ]"
            :key="index"
            @goNext="goNext(option)"
        />
    </div>
</template>
<script>
import CommonBtn from "@/common/businessV2/components/CommonBtn.vue";
import { session as Session } from "@/common/utils/storage";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { reportKibana } from "@/common/utils/report";
export default {
    name: "Normal",
    props: {
        type: {
            type: String
        },
        options: {
            type: Array,
            default: () => []
        },
        btnStyleObj: {
            type: Object,
            default: () => {}
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    components: {
        CommonBtn
    },
    data() {
        return {
            selectIndex: Session.getItem(this.type) || "",
            btnStyle: ""
        };
    },
    methods: {
        goNext(option) {
            const reportDesc = {
                salary: {
                    point: 8,
                    desc: "收入页-具体收入点击"
                },
                education: {
                    point: 6,
                    desc: "学历页-具体学历点击"
                },
                marriage: {
                    point: 7,
                    desc: "婚况页-具体婚况点击"
                }
            };
            reportKibana(
                this.pageType,
                reportDesc[this.type].point,
                reportDesc[this.type].desc
            );
            if (["salary", "education", "marriage"].includes(this.type)) {
                const params = {
                    key: this.type,
                    value: option.key
                };
                setLocalRegisterForm(params, this.pageType);
            }
            Session.setItem(this.type, option.text);

            this.selectIndex = option.text;
            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
