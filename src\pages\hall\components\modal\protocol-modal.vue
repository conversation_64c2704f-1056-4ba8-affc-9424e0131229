<template>
    <van-popup
        class="protocol-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange">

        <p class="protocol-modal__desc">
            已阅读并同意<span @click="goServiceProtocol">《珍爱网服务协议》</span>和<span @click="goPersonDescription">《个人信息保护政策》</span>
        </p>

        <div class="protocol-modal__btn"
             @click="confirm">
            确认
        </div>
        <div class="protocol-modal__cancel"
             @click="closeModal">
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

export default {
    name: 'protocol-modal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
      value: {
          handler(value) {
              if (value) {
                  this.$report(600, '同意协议弹窗访问');
              }
          },
          immediate: true,
      }
    },
    methods: {
        goServiceProtocol() {
            window.location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
        },
        goPersonDescription() {
            window.location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
        },
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirm() {
            this.$report(601, '同意协议弹窗-同意按钮点击');
            this.$emit('confirm');
        },
        closeModal() {
            this.$report(602, '同意协议弹窗-取消按钮点击');
            this.$emit('input', false);
        }
    }
}
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.protocol-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
        padding: 0 26px;
        > span {
            color: #FFB900;
        }
    }

    &__btn {
        @include flex-center();
        width: 462px;
        height: 88px;
        font-size: 32px;
        color: #6C6D75;
        background: #FFB900;
        border-radius: 44px;
    }

    &__cancel {
        margin-top: 32px;
        font-size: 32px;
        line-height: 50px;
        color: #6C6D75;
        text-align: center;
    }
}
</style>
