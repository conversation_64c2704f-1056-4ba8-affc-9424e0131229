<template>
    <z-image
        :src="avatarSrc"
        :width="width"
        :height="height"
        border-radius="50%"
    />
</template>

<script>
const maleAvatarArr = [
    'https://photo.zastatic.com/images/common-cms/it/20211216/1639652347300_917465.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211217/1639737989512_148401_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997744957_777816_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559449478_298857.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997828111_763294_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559314943_244052.jpg'
],
femaleAvatarArr = [
    'https://photo.zastatic.com/images/common-cms/it/20211216/1639622239042_838296_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211215/1639559392455_178465.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211223/1640255074577_614188_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211220/1639995200716_518292_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211223/1640254785897_439198_t.jpg',
    'https://photo.zastatic.com/images/common-cms/it/20211220/1639997902772_443948_t.jpg'
];

export default {
    name: "RandomAvatar",
    props: {
        gender: {
            type: Number,
            required: true
        },
        width: {
            type: Number,
            required: true
        },
        height: {
            type: Number,
            required: true
        }
    },
    computed: {
        avatarSrc(){
            return this.gender === 0 
                   ? this.$z_.shuffle(femaleAvatarArr)[0]
                   : this.$z_.shuffle(maleAvatarArr)[0];
        }
        
    },
}
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';


</style>
