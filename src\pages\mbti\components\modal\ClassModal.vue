<template>
    <van-popup
        class="class-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="class-modal__title">
            前往[珍爱APP]领取专属课程
        </h1>
        <div class="class-modal__info">
            免费领取倒计时: <span>{{ timeText }}</span>
        </div>

        <p class="class-modal__desc">
            请前往应用市场搜索下载珍爱APP
        </p>

        <div
            class="class-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from "vant";

export default {
    name: "ClassModal",
    components: {
        VanPopup: Popup
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        timeText: {
            type: String,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit("input", value);
        },
        closeModal() {
            this.$emit("input", false);
        }
    }
};
</script>

<style lang="scss">
@import "~@/common/styles/common.scss";

.class-modal {
    width: 558px;
    background: #ffffff;
    border-radius: 32px;
    padding: 48px 32px;
    color: #26273c;
    @include flex-center(column, null, center);

    &__title {
        margin-bottom: 24px;
        font-size: 36px;
        font-weight: 600;
    }
    &__info {
        width: 400px;
        height: 62px;
        padding-left: 28px;
        color: #fff;
        font-size: 26px;
        line-height: 62px;
        background-image: linear-gradient(90deg, #F99CFF 0%, rgba(250,137,255,0.00) 100%, rgba(250,137,255,0.00) 100%);
        border-radius: 32px 0 0 32px;
        > span {
            color: #ff55a3;
            font-size: 32px;
            font-weight: 500;
        }
    }
    &__desc {
        margin-top: 48px;
        font-size: 28px;
        font-weight: 500;
        margin-bottom: 48px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
