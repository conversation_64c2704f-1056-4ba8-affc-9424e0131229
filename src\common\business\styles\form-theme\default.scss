@import "~@/common/styles/common.scss";
.form-wrapper {
    .form-item {
        &--normal {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100px;
            font-size: 28px;
            font-weight: 400;
            color: #26273c;
            line-height: 100px;
            &__label {
                width: 240px;
            }
            &__value {
                position: relative;
                padding-right: 36px;
                font-size: 28px;
                font-weight: 400;
                line-height: 40px;
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 0;
                    transform: translateY(-50%);
                    width: 28px;
                    height: 28px;
                    @include set-img("https://photo.zastatic.com/images/common-cms/it/20220517/1652781985652_831209_t.png");
                }
            }
        }

        &--gender {
            &__value {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 304px;
                height: 100px;
                &__item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 138px;
                    height: 60px;
                    border: 2px solid #26273c;
                    border-radius: 30px;
                    font-size: 28px;
                    font-weight: 400;
                    color: #26273c;
                    line-height: 60px;
                    text-align: center;
                    &--selected {
                        border: none;
                        color: #ffffff;
                    }
                }
            }
        }

        &--phone {
            position: relative;
            font-size: 28px;
            font-weight: 400;
            color: #26273c;
            &__label {
                height: 100px;
                line-height: 100px;
            }
            &__value {
                margin: 0 auto;
                padding-left: 50px;
                padding-top: 24px;
                height: 88px;
                background-color: rgba($color: #26273c, $alpha: 0.05);
                border-radius: 44px;
                &__input {
                    height: 40px;
                    background-color: transparent;
                    font-size: 28px;
                    color: #26273c;
                    line-height: 40px;
                    text-align: left;
                    width: 500px;

                    // &::placeholder{
                    //     color:red;
                    // }
                }
            }
            &__clear {
                position: absolute;
                top: 28px;
                right: 24px;
                width: 32px;
                height: 32px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20220517/1652782303916_599182_t.png");
            }
            &__value__wrapper {
                position: relative;
            }
        }
    }
}