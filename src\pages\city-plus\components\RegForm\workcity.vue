<template>
    <div class="workcity">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}{{ cmsConfig.planName === '同城脱单群聊(A)' ? '群聊' : '活动' }}
        </div>
        <div class="workcity-info">
            <van-picker
                class="workcity-info-picker"
                ref="refPicker"
                item-height="1.25rem"
                :visible-item-count="5"
                :show-toolbar="false"
                :columns="list.options"
                @change="handleReport"
            />
        </div>
        <div
            class="btn"
            @click="goNext"
        >
            下一步
        </div>
    </div>
</template>
<script>
import { Picker } from "vant";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import "vant/lib/picker/style";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
export default {
    name: "WorkCity",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            dict: Z.workCity
        };
    },
    inject: ["cmsConfig"],
    components: {
        vanPicker: Picker
    },
    mounted() {
        this.$report(5, '工作地访问');
        // 初始化定位
        this.handleInitArea();
    },
    methods: {
        goNext() {
            const picker = this.$refs.refPicker;
            const values = picker.getValues();

            const workCity = values[2].key
                ? values[2].key
                : values[1].key
                    ? values[1].key
                    : "";
            const params = {
                key: "workCity",
                value: workCity
            };
            setLocalRegisterForm(params, this.cmsConfig.planName);
            this.$report(5, '工作地页-下一步按钮点击');
            this.$emit("val-updated", workCity);
        },

        handleInitArea() {
            const workCity =
                (Storage.getItem(
                    `cachedRegisterForm-${this.cmsConfig.planName}`
                ) &&
                    Storage.getItem(
                        `cachedRegisterForm-${this.cmsConfig.planName}`
                    ).workCity) ||
                "";
            if (workCity) {
                const cityArr = findWorkCity(workCity);
                this.$refs.refPicker.setValues(cityArr);
            } else {
                // 有缓存优先读缓存，否则走定位逻辑
                this.handleLocate();
            }
        },

        handleLocate() {
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                    // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {
                    //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([
                                result.addressComponent.province,
                                result.addressComponent.city,
                                result.addressComponent.district
                            ]);
                            this.$refs.refPicker.setValues(areaArr);
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if (status === "complete") {
                        const areaArr = this.handleLocationPair([
                            result.province,
                            result.city,
                            ""
                        ]);
                        this.$refs.refPicker.setValues(areaArr);
                    } else {
                        this.$refs.refPicker.setValues([
                            "广东",
                            "肇庆",
                            "端州区"
                        ]); // 缺省地区
                    }
                });
            });
        },

        handleLocationPair(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            const targetProvince = this.$z_.find(this.dict, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });

            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(
                targetCity.children,
                district => {
                    return district.text.indexOf(sliceDistrict) >= 0;
                }
            );

            return [targetProvince.text, targetCity.text, targetDistrict.text];
        },

        handleReport() {
            this.$report(5, '工作地页-滑动省');
        }
    }
};
</script>

<style lang="scss">
.workcity {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    &-info {
        margin-top: 58px;
        &-picker {
            background: transparent;
            .van-picker__mask {
                background: linear-gradient(180deg, transparent),
                    linear-gradient(0deg, transparent);
            }
            .van-picker-column__item {
                opacity: 0.7;
                color: #222222;
                font-size: 30px;
                &--selected {
                    opacity: 1;
                    color: #0f1122;
                    font-size: 36px;
                }
            }
        }
    }
    .btn {
        margin: 52px auto 0;
        width: 654px;
        height: 110px;
        color: #ffffff;
        font-size: 32px;
        text-align: center;
        line-height: 110px;
        background: #5368f0;
        border-radius: 80px;
    }
}
</style>
