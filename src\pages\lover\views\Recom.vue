<template>
    <div class="recom-wrapper">
        <z-image
            :width="750"
            :height="566"
            src="https://photo.zastatic.com/images/common-cms/it/20220902/1662119689555_882189_t.png"
        />
        <div class="model">
            <div
                class="model-card"
                v-for="item, index in vagueModel"
                :key="index"
            >
                <div :class="(currentIndex === index) && isHidden ? 'modal-card-hide' : ''">
                    <img
                        :class="{'model-card-hide' :currentIndex === index && isHidden, 'model-card-blur': currentIndex !== index }"
                        :src="item.avatar"
                    />
                </div>

                <!-- 动效 -->
                <div
                    :id="'blur'+index"
                    class="model-card-svga"
                ></div>

                <common-button
                    :config="{width: 160, height: 64, fontSize: 32, des: '选Ta'}"
                    :class="{'button-disabled': currentIndex === index}"
                    @click="handleSelect(item, index)"
                />
            </div>
        </div>
        <z-image
            :width="750"
            :height="576"
            :src="lockPhoto"
            class="locked"
            v-if="currentIndex < 0"
        />
        <div
            class="model-detail"
            v-else
        >
            <div class="info">
                <div class="header">
                    <img :src="modelDetail.avatar">
                    <div>
                        <img :src="modelDetail.mainImg">
                        <img
                            :src="modelDetail.momentImg1"
                        >
                    </div>
                </div>
                <div class="des">
                    Ta的详细资料
                </div>
                <div class="detail">
                    <div
                        v-for="(item, key) in detailMap"
                        :key="key"
                    >
                        <span>{{ item }}</span>
                        <span>{{ modelDetail[key] }}</span>
                        <span v-if="key === 'name'"></span>
                    </div>
                </div>
                <div class="online">
                    当前APP在线
                </div>
                <div class="icon" />
            </div>
            <div
                class="activity"
                ref="activity"
            >
                <div class="header">
                    <span>Ta的动态</span>
                    <span @click="handleDownload(1)">查看所有 36条</span>
                </div>
                <div class="bottom">
                    <img :src="modelDetail.momentImg2" />
                    <img :src="modelDetail.momentImg3" />
                    <img :src="modelDetail.momentImg4 || modelDetail.momentImg4 || modelDetail.momentImg3" />
                </div>
            </div>
        </div>

        <div class="button-area">
            <common-button
                class="button"
                :config="{width: 654, height: 116, fontSize: 36, des: currentIndex < 0 ? expertJumpULoveApplet ? '点击按钮跳转优恋空间' : '下载APP 解锁全部' : '喜欢Ta'}"
                @click="handleDownload(currentIndex < 0 ? 0 : 3)"
            >
                />
            </common-button>
        </div>

        <CommonDownloadGuideModal
            :styleConfig="{
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: 'linear-gradient(154deg, #7566EB 0%, #4A3BC0 100%)'
            }"
            v-model="downloadVisible"
            :page-type="cmsConfig.planName"
        >
            <template slot="default">
                <div v-html="downloadModalDesMap[type].title" />
            </template>
            <template slot="desc">
                <div v-html="downloadModalDesMap[type].desc" />
            </template>
        </CommonDownloadGuideModal>

        <div
            class="tip"
            :style="{'transform': `translateX(${this.distance})`}"
            @click="handleDownload(4)"
        >
            <img :src="randomAvatar">有位理想恋人向你发来消息<span></span>
        </div>
    </div>
</template>

<script>
import CommonButton from '../components/CommonButton';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { session, storage } from "@/common/utils/storage";
import { Toast } from "vant";
import CommonDownloadGuideModal from "@/common/business/components/CommonDownloadGuideModal.vue";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { _queryIdealLoverModelListNoReg, _getModelInfo } from '../api';
import { getRandomInt } from "@/common/utils/tools.js";
import Api from '@/common/server/base';
import { CryptoDes } from '@/common/utils/crypto';
const cryptoDes = new CryptoDes('rlWU9Uty8atXZVE8')
export default {
    name: 'Recom',
    components: {
        CommonButton,
        CommonDownloadGuideModal
    },
    inject: ['cmsConfig'],
    data() {
        return {
            currentIndex: -1,
            detailMap: {
                name: '姓名：',
                ageString: '年龄：',
                workCityString: '工作地：',
                educationString: '学历：',
                salaryString: '月收入：'
            },
            vagueModel: [],
            modelDetail: {},
            downloadVisible: false,
            downloadModalDesMap: {
                0: {
                    title: '请前往各大应用市场<br>搜索【珍爱网】下载珍爱APP',
                    desc: '快来与Ta相遇，收获你的爱情吧！'
                },
                1: {
                    title: '查看Ta的更多动态',
                    desc: '1.请到应用市场搜索下载<span style="color: #4A3BC0">【珍爱APP】</span><br>2.在<span style="color: #4A3BC0">【珍爱APP】</span>内点击搜索Ta的昵称，即可查看Ta的全部动态'
                },
                2: {
                    title: '如何查看全部定制恋人',
                    desc: '1.请到应用市场搜索下载<span style="color: #4A3BC0">【珍爱APP】</span><br>2.在<span style="color: #4A3BC0">【珍爱APP】</span>内点击【推荐】，即可查看按照您需求定制的全部恋人'
                },
                3: {
                    title: '如何和Ta聊天',
                    desc: '1.请到应用市场搜索下载<span style="color: #4A3BC0">【珍爱APP】</span><br>2.在<span style="color: #4A3BC0">【珍爱APP】</span>内点击搜索Ta的昵称，即可开始聊天'
                },
                4:  {
                    title: '如何和Ta聊天',
                    desc: '1.请到应用市场搜索下载<span style="color: #4A3BC0">【珍爱APP】</span><br>2.在<span style="color: #4A3BC0">【珍爱APP】</span>内点击搜索Ta的昵称，即可开始聊天'
                },
            },
            // 弹窗类型
            type: 0,
            // 是否隐藏
            isHidden: false,
            distance: '-100%',
            isReport: false,
            randomAvatar: '',
            expertJumpULoveApplet: false, // 是否跳优恋空间
            youlianLink: '', // 跳优恋空间链接
        };
    },
    mounted() {
        this.$report(14, '引导下载页-访问');
        this.handleFetchList();
        this.handleLocateTip();
        this.isJumpYoulian();
    },
    computed: {
        lockPhoto() {
            return session.getItem("gender") === 0
                ? "https://photo.zastatic.com/images/common-cms/it/20220902/1662108067174_498470_t.png"
                : "https://photo.zastatic.com/images/common-cms/it/20220909/1662705361877_612396_t.png";
        }
    },
    watch: {
        currentIndex(val) {
            if (val > -1) {
                window.addEventListener("scroll", this.handleExposure);
            }
        }
    },
    methods: {
        async isJumpYoulian() {
            const res = await Api.getRegChainConfig({
                channelId: Z.getParam('channelId'),
                subChannelId: Z.getParam('subChannelId'),
            })
            if (res.code === 0) {
                this.expertJumpULoveApplet = res.data.results.expertJumpULoveApplet
            }
        },
        async getYoulianLink() {

            if (this.expertJumpULoveApplet) {
                const memberId = sessionStorage.getItem('reg_memberid')
                this.$report(123, " 测试结果页-短链生成进入");
                let res1 = {}
                if (memberId) {
                    const demem = cryptoDes.encode(memberId)
                    res1 = await Api.getValidToken({memberId: demem})
                    this.$report(123, " 测试结果页-换取token", {
                        ext1: JSON.stringify(res1)
                    });
                }
                const linkUrl = Api.domain().includes('api-test.zajiebao.com') ? encodeURIComponent('https://mp.weixin.qq.com/s/MODFT629m9B3w9v8H15Gig') : encodeURIComponent('https://mp.weixin.qq.com/s/QeszlKRc_D3oKYCoCDE-4w')
                const params = {
                    path: 'pages/main/meet/index',
                    query: `channelId=${Z.getParam('channelId')}&subChannelId=${Z.getParam('subChannelId')}&zaAPPToken=${res1.data ? res1.data : ''}&linkUrl=${linkUrl}`,
                };
                const res2 = await Api.goYouLianMini(params)
                if (res2.code === 0) {
                    this.$report(123, " 测试结果页-换取token", {
                        ext1: JSON.stringify(res2)
                    });
                    this.youlianLink = res2.data
                    location.href = this.youlianLink
                }
            }
        },
        async handleFetchList() {
            const registerForm = storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            );
            const {
                gender,
                birthday,
            } = registerForm;
            const sendData = {
                gender,
                birthday,
                stature: session.getItem("Figure"),
                dressed: session.getItem("Ootd"),
                count: 3,
            };
            const result = await _queryIdealLoverModelListNoReg(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            const randomNum = getRandomInt(0, 2);

            this.vagueModel = result.data.list;
            this.randomAvatar = this.vagueModel[randomNum] && this.vagueModel[randomNum].avatar;
        },

        async handleFetchDetail(modelId, age) {
            const registerForm = storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            };
            // const age = new Date().getFullYear() - new Date(registerForm.birthday).getFullYear();
            const sendData = {
                sex: registerForm.gender,
                age,
                education: registerForm.education,
                salary: registerForm.salary,
                workCity: registerForm.workCity,
                modelId: modelId
            };

            const result = await _getModelInfo(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            this.modelDetail = {
                ...result.data.modelInfoVo,
                ageString: `${age}岁`
            };
        },

        handleDownload(type) {
            this.type = type;
            const desMap = {
                0: '引导下载页-下载APP 解锁全部按钮点击',
                1: '引导下载页-查看所有动态按钮点击',
                3: '引导下载页-喜欢Ta按钮点击',
                4: '引导下载页-消息提醒点击'
            };
            this.$report(14, desMap[type]);
            if (this.expertJumpULoveApplet) {
                this.getYoulianLink()
                return false
            }
            if (this.cmsConfig.downloadStatus === 0) {
                this.downloadVisible = true;
            } else {

                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }
        },

        handleSelect(item, index) {
            if (this.currentIndex > -1) {
                this.$report(15, '引导下载页-选Ta按钮点击（已选中后点击）');
                this.handleDownload(2);
                return;
            }

            if (this.currentIndex < 0) {
                this.$report(14, '引导下载页-选Ta按钮点击（首次点击）');
                this.currentIndex = index;
                this.isHidden = true;
                setTimeout(() => {
                    this.isHidden = false;
                }, 1000);
                this.handlesetSVGA(item.avatar);
                this.handleFetchDetail(item.modelId, item.age);
            }
        },

        handlesetSVGA(avatar){
            let player = new window.SVGA.Player(`#blur${this.currentIndex}`),
                parser = new window.SVGA.Parser(`#blur${this.currentIndex}`);

            parser.load(require('../assets/svgaBroken.svga'), (videoItem)=>{
                player.setImage(avatar, 'key');
                player.loops = 1;
                player.setVideoItem(videoItem);
                player.startAnimation();
            });
        },

        handleLocateTip() {
            setTimeout(() => {
                this.$report(14, '引导下载页-消息提醒曝光');
                this.distance = '2.5%';
            }, 5000);
        },

        handleExposure() {
            const windowHeight = document.documentElement.clientHeight;
            const target = this.$refs['activity'].getBoundingClientRect();
            if (!this.isReport && (target.top < windowHeight - 200)) {
                this.$report(14, '引导下载页-动态模块曝光');
                this.isReport = true;
            }
        }
    }
};

</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.recom-wrapper {
    position: relative;
    .model {
        margin-top: -370px;
        padding: 55px 56px 72px;
        width: 750px;
        height: 420px;
        @include flex-center(row, space-between, null);
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220908/1662617922451_910082_t.jpg");
        &-card {
            width: 202px;
            position: relative;
            @include flex-center(column, null, center);
            >div:nth-child(1) {
                height: 202px;
                border: 1px solid #5848C0;
                border-radius: 16px;
                margin-bottom: 24px;
                img {
                    margin-bottom: 24px;
                    width: 202px;
                    height: 202px;
                    border: 1px solid #E1E0EF;
                    border-radius: 16px;
                }
            }

            &-blur {
                filter: blur(4px);
            }

            &-hide {
                opacity: 0;
            }

            &-svga {
                position: absolute;
                left: 0;
                top: 0;
                width: 202px;
                height: 202px;
                z-index: 2;
            }
        }
    }
    .locked {
        margin-bottom: 240px;
    }
    .model-detail {
        padding-bottom: 240px;
        .info {
            position: relative;
            width: 702px;
            margin: 0 auto;
            background-image: linear-gradient(-38deg, #FDF7E4 0%, #E9EBFF 53%);
            border: 1px solid #5848C0;
            border-radius: 32px;
            .header {
                @include flex-center(row, space-between, null);
                >img:nth-child(1) {
                    width: 464px;
                    height: 464px;
                    border-radius: 32px 0 0 32px;
                }
                >div:nth-child(2) {
                    @include flex-center(column, space-between, null);
                    img {
                        width: 226px;
                        height: 226px;
                    }
                    >img:nth-child(1) {
                        border-radius: 0 32px 0 0;
                    }
                    >img:nth-child(2) {
                        border-radius: 0 0 32px 0;
                    }
                }
            }
            .des {
               font-weight: 600;
               font-size: 44px;
               color: #26273C;
               padding: 32px 0px 24px 32px;
            }
            .detail {
                padding-left: 32px;
                padding-bottom: 24px;
                div {
                    margin-bottom: 24px;
                    >span:nth-child(1) {
                        font-weight: 400;
                        font-size: 32px;
                        color: #6C6D75;
                    }
                    >span:nth-child(2) {
                        font-weight: 400;
                        font-size: 32px;
                        color: #26273C;
                    }
                    >span:nth-child(3) {
                        display: inline-block;
                        position: relative;
                        top: 5px;
                        width: 82px;
                        height: 36px;
                        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220907/1662533274881_869418_t.png");
                    }
                }
            }
            .online {
                position: absolute;
                left: 20px;
                top: 396px;
                @include flex-center(row, center, center);
                width: 194px;
                height: 48px;
                background: #26273C;
                border-radius: 32px;
                opacity: 0.5;
                font-size: 24px;
                color: #FFFFFF;
            }
            .icon {
               position: absolute;
               left: 451px;
               top: 589px;
               width: 232px;
               height: 231px;
               @include set-img("https://photo.zastatic.com/images/common-cms/it/20220905/1662345827646_513477_t.png")
            }
        }
        .activity {
            padding: 95px 56px 66px;
            width: 750px;
            height: 443px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220902/1662110099476_24522_t.png");
            .header {
                >span:nth-child(1) {
                    font-weight: 600;
                    font-size: 36px;
                    color: #11111A;
                }
                >span:nth-child(2) {
                    position: relative;
                    top: 10px;
                    right: 24px;
                    float: right;
                    font-weight: 400;
                    font-size: 28px;
                    color: #6C6D75;
                    &::after {
                        position: absolute;
                        content: "";
                        width: 32px;
                        height: 32px;
                        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220902/1662113700300_159965_t.png");
                    }
                }
            }
            .bottom {
                margin-top: 32px;
                @include flex-center(row, space-between, null);
                img {
                   width: 206px;
                    height: 206px;
                    border: 1px solid #E1E0EF;
                    border-radius: 16px;
                }
            }
        }
    }

    .button-area {
        position: fixed;
        z-index: 1;
        left: 0;
        right: 0;
        bottom: 0;
        width: 750px;
        height: 208px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220902/1662108811907_617281_t.png");
        .button {
            margin: 22px auto 0px;
        }
    }

    .button-disabled {
        opacity: 0.4;
    }

    .tip {
        @include flex-center(row, null, center);
        z-index: 5;
        padding-left: 36px;
        position: fixed;
        top: 36px;
        left: 0;
        border: 2px solid rgba(255,255,255,0.66);
        background-image: linear-gradient(180deg, #F6E8F9 0%, rgba(255,255,255,0.90) 100%);
        border-radius: 24px;
        width: 712px;
        height: 138px;
        font-weight: 400;
        font-size: 36px;
        color: #5847BF;
        transition: all 1s ease-in-out;
        img {
            border-radius: 50%;
            border: 2px solid #FFFFFF;
            display: inline-block;
            width: 85px;
            height: 85px;
            margin-right: 24px;
        }
        span {
            display: inline-block;
            margin-left: 68px;
            width: 38px;
            height: 38px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220907/1662540034827_951230_t.png");
        }
    }
}
</style>
