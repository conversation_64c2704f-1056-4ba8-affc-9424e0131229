// 兼容处理Z.getParam无法取到hash后URL参数的情况
// let tmpFunc = Z.getParam;
// Z.getParam = function(name){
//     if(location.href.indexOf("#")>-1){
//         // 获取hash前参数
//         let paramBeforeHash = tmpFunc(name);

//         // 获取hash后参数
//         const u = location.href.split(`#`)[1]; // 取hash后面
//         const r = new RegExp(`(\\?|&)` + name + `=(.*?)(#|&|$)`, `i`);
//         const m = u.match(r);
//         let paramAfterHash = decodeURIComponent(m ? m[2] : ``);

//         return paramBeforeHash || paramAfterHash;
//     }
// }
export const PROTOCOL = window.location.protocol;


// 渠道号和子渠道号
export let channelId = Z.getParam('channelId');
export let subChannelId = Z.getParam('subChannelId');

// 测试类型，来源于落地页的性别选择，用于区分男女
export let TEST_TYPE = Z.getParam('testType');

// 页面来源
export let FROM = Z.getParam('from');

// 页面来源，是否在sapp中
export let IS_SAPP = FROM === 'sapp';

// 是否春晚注册渠道
export var IS_SPRING_CHANNEL = ['915313'].indexOf(channelId) !== -1;

// 是否走覆盖流程，若走覆盖流程，记为1；若不走覆盖流程，记为2
export let oExt9 = (function () {
    let res = '';
    return {
        set: function (val) {
            res = val;
        },
        get: function () {
            return res;
        }
    }
})();
