<template>
    <div
        class="love-page"
        :style="{'background-image':`url(${ home ? cmsConfig.homeBackgroundImg : require('../assets/images/main-bg.jpeg') })`}">
        <slot/>

        <div
            v-if="backBtnVisible"
            class="love-page__back"
            @click="$emit('click-back', $event)">
            <z-image
                :src="require('../assets/images/icon-back.png')"
                :width="32"
                :height="32"/>
            <span>返回</span>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex";

export default {
    name: 'love-page',
    props: {
        home: {
            type: Boolean,
            default: false,
        },
        backBtnVisible: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        ...mapState([
            'cmsConfig'
        ]),
    },
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.love-page {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    overflow: auto;
    background: #0D0A28 no-repeat;
    background-size: cover;

    &__back {
        position: fixed;
        top: 32px;
        left: 40px;
        z-index: 10;

        @include flex-center();

        > span {
            position: relative;
            top: 2px;
            font-size: 28px;
            color: #FFFFFF;
            margin-left: 4px;
        }
    }
    &::-webkit-scrollbar {
        display: none;
    }
}
</style>
