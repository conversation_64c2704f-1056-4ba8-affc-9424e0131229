<template>
    <div
        class="collection-wrapper"
        :style="cmsConfig.pageColor"
        v-show="cmsConfig.pageColor"
    >
        <!-- CMS配置的头图 -->
        <img
            class="collection-banner"
            :src="cmsConfig.formImg"
            :style="{
                visibility:cmsConfig.formImg?'visible':'hidden',
                height:cmsConfig.formImg?'auto':'0px'
            }"
        />

        <!-- 表单部分 1代表弹出样式表单 0 平铺式表单-->
        <template v-if="cmsConfig.formType === 1">
            <collection-form />
        </template>
        <template v-if="cmsConfig.formType === 0">
            <CollectionFormPull />
        </template>
    </div>
</template>

<script>

import {mapState,mapMutations,mapActions} from 'vuex';
import {CollectionForm,CollectionFormPull} from '../components/collection/index.js';
import {getRandomInt} from "@/common/utils/tools.js";
import {_getMaterial,_getRandomAvatar} from "../js/api.js";
import {reportKibana} from '@/common/utils/report.js';

export default {
    components:{
        CollectionForm,
        CollectionFormPull,
    },
    data() {
        return {
            onlineNumber:null,
            player:null,
            testPhone:''
        };
    },
    computed:{
        ...mapState([
            'formInfo',
            'registerInfo',
            'cmsConfig'
        ]),

    },
    async created(){
        // 随机生成在线人数
        this.setOnlineNumber();
        // 获取CMS配置
        await this.setCmsConfig();
        // this.$route.push('/info')

    },
    mounted(){
        // 打桩
        reportKibana("线下大表单", 1, "首页访问", {});
        reportKibana("线下大表单", 3000, "首页访问（监控）", {});

        // 定位到页面顶部
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
        window.scrollTo(0, 0);

        // banner动效
        // this.setSVGA();
    },
    methods:{

        ...mapMutations([
            "setFormInfo"
        ]),
        ...mapActions([
            "setCmsConfig",

        ]),
        setOnlineNumber(){
            this.onlineNumber = getRandomInt(360000,499997);
            // let timer = setInterval(()=>{
            //     let count = getRandomInt(1,3)
            //     this.onlineNumber += count;
            //     if(this.onlineNumber > 499997 ){
            //             clearInterval(timer);
            //         }
            // },2000);
        },
        // async setSVGA(){
        //     let player = new SVGA.Player('#svgaAvatars'),
        //         parser = new SVGA.Parser('#svgaAvatars');

        //     let resData = await _getRandomAvatar({});
        //     if(resData.isError){
        //         return this.$toast(resData.errorMessage);
        //     }

        //     // 后台给的是200x200，压缩至100x100
        //     let avatarList = resData.data.list;
        //     avatarList.forEach((item)=>{
        //         item.avatar += '?imageMogr2/blur/18x35/thumbnail/100x100';
        //     });

        //     parser.load(require('../assets/imgs/svgaAvatar1.svga'), (videoItem)=>{
        //         // 设置头像
        //         for(let i=0;i<avatarList.length;i++){
        //             player.setImage(avatarList[i].avatar,`key${i+1}`);
        //         }
        //         player.setVideoItem(videoItem);
        //         player.loops = 1;
        //         player.startAnimation();
        //         player.onFinished(()=>{
        //             // 动画执行一次，然后只循环48帧之后的上下浮动部分
        //             player.startAnimationWithRange({location:48,length:48});
        //         });
        //     });
        // },
        closeModal(){
            this.showModal = false;
        },
        closeSelect(){
            this.showSelect = false;
        },
        checkRegisterInfo(){
            return true;
        }
    }

    // beforeRouteUpdate(to, from, next) {
    //     console.log(111,to);
    //     console.log(222,from);
    //     next(true)
    // }
};
</script>

<style lang="scss" scoped>


.collection-wrapper{
    font-family: Source Han Sans SC;
    width: 100%;
    background-color: #ffffff;
    min-height: 100vh;
    box-sizing: border-box;
}
.collection-banner{
    width: 100%;
    visibility: hidden;
    display:block;
    // margin-bottom: -230px;
}



</style>
