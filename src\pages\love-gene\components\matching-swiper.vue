<template>
    <z-image
        class="matching-swiper"
        :src="styleConfig.matchingBg"
        :width="672"
        :height="styleConfig.matchingBgHeight">
        <z-image
            class="matching-swiper__text"
            :src="styleConfig.matchingText"
            :width="styleConfig.matchingTextWidth"
            :height="styleConfig.matchingTextHeight"
            block/>

        <div
            v-if="type !== 'form'"
            class="matching-swiper__data">
            高达<span>94.98%</span>
        </div>

        <div class="matching-swiper__swiper-box">
            <z-swiper
                v-if="list.length"
                auto-play
                :list="list"
                :height="110"
                :inner-width="540"
                :side-gap="18"
                :span-gap="32">

                <z-image
                    slot="left"
                    :width="16"
                    :height="32"
                    :src="require('../assets/images/icon-triangle-left.png')"/>

                <z-image
                    slot-scope="{ item }"
                    :src="item"
                    :width="110"
                    :height="110"
                    img-size="cover"
                    style="border-width: 2px"
                    :border-radius="16"
                    class="matching-swiper__item"/>

                <z-image
                    slot="right"
                    :width="16"
                    :height="32"
                    :src="require('../assets/images/icon-triangle-right.png')"/>
            </z-swiper>
        </div>

        <div
            v-if="type === 'form'"
            class="matching-swiper__desc">
            对方也想了解你<br>
            只需20秒完善个人信息，与Ta取得联系
        </div>
    </z-image>
</template>

<script>
import { mapState } from "vuex";
import Api from '@/common/server/base';
import ZAvatar from "@/common/components/z-avatar";
import ZSwiper from '@/common/components/z-swiper';
import MATCHING_BG from '../assets/images/matching-bg.png';
import MATCHING_BG_FORM from '../assets/images/matching-bg-form.png';
import MATCHING_TEXT from '../assets/images/matching-text.png';
import MATCHING_TEXT_FORM from '../assets/images/matching-text-form.png';

export default {
    name: 'matching-swiper',
    components: {
        ZAvatar,
        ZSwiper,
    },
    props: {
        type: {
            type: String,
        },
    },
    data() {
        let styleConfig = this.type === 'form' ? {
            matchingBg: MATCHING_BG_FORM,
            matchingBgHeight: 464,
            matchingText: MATCHING_TEXT_FORM,
            matchingTextWidth: 574,
            matchingTextHeight: 126,
        } : {
            matchingBgHeight: 450,
            matchingBg: MATCHING_BG,
            matchingText: MATCHING_TEXT,
            matchingTextWidth: 530,
            matchingTextHeight: 138,
        }

        return {
            list: [],
            styleConfig,
        }
    },
    computed: {
        ...mapState([
            'requirement',
            'registerForm',
        ]),
    },
    async created() {
        const ageRange = this.$z_.get(this.requirement, 'ageRange') || '';
        let rawAge = Number(ageRange.slice(0, 2));
        let age;

        switch (rawAge) {
            case 0 :
                age = this.$z_.sample([ 21, 24, 27, 30 ]);
                break;
            case 23:
                age = 20;
                break;
            case 24:
                age = 27;
                break;
            default:
                age = 30;
                break;
        }

        const res = await Api.getSpecifyGenderRandomAvatar({
            sex: this.registerForm.gender === 0 ? 1 : 0,
            limit: 8,
            age: 20,
        });

        const list = res.data.list.map((item) => {
            return this.$utils.formatImgUrl(item.avatar, 150, 150);
        })
        this.$emit('receive-data', list);

        this.list = list;
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.matching-swiper {
    @include relative-center();

    &__text {
        margin-top: 48px;
        margin-left: 32px;
    }

    &__data {
        @include flex-center();
        height: 76px;
        margin-top: 8px;
        font-weight: 500;
        font-size: 44px;

        & > span {
            color: #5AE1FF;
            font-size: 52px;
        }
    }

    &__swiper-box {
        margin-top: 20px;
        min-height: 110px;
    }

    &__item {
        border: 2px solid #FFFFFF;
    }

    &__desc {
        margin-top: 28px;
        font-size: 26px;
        color: #FFFFFF;
        text-align: center;
        line-height: 40px;
    }
}
</style>
