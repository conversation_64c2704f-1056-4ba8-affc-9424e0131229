<template>
    <div class="birthday">
        <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(option, name) in heightV2"
                :key="name"
            >
                <div
                    class="birthday-info-card"
                >
                    <h4 class="birthday-info-title">
                        {{ name }}cm
                    </h4>
                    <span
                        class="bic-num"
                        :class="{ active: item === curHeight }"
                        v-for="item in option"
                        :key="item"
                        @click="goNext(item)"
                    >{{ item }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    setLocalRegisterForm,
} from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { heightV2 } from "@/common/config/register-dictionary";
import { PAGE_TYPE } from "../../config";
export default {
    name: "Height",
    data() {
        const form = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`);
        return {
            heightV2,
            curHeight: form ? form.height : '',
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;
            this.curHeight = val;
            const params = {
                key: "height",
                value: val,
            };
            setLocalRegisterForm(params, PAGE_TYPE);
            this.$report(29, "身高页-按钮点击");
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);

        }
    },
    mounted() {

        this.$refs.birthScroll.scrollTo(0, 900);
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    .birthday-scroll {
        overflow-y: scroll;
        height: calc(83vh - 364px);
    }
    &-info {
        &-title {
            font-weight: 500;
            font-size: 36px;
            line-height: 54px;
            color: #767DFF;
            position: absolute;
            top: 32px;
            left: 30px;
        }
        &-card {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding: 8px 6px 8px 144px;
            margin: 24px 48px 48px;
            // background: #fff;
            // background-color: #EBEFF1;
            border-radius: 60px;
            position: relative;
            &::after {
                content: "";
                position: absolute;
                display: block;
                background-color: #767DFF;
                width: 2px;
                top: 92px;
                right: 0;
                bottom: 8px;
                left: 80px;
            }
            > span {
                flex: 0 0 auto;
                display: block;
                color: #26273C;
                font-size: 36px;
                line-height: 96px;
                width: 124px;
                height: 96px;
                border-radius: 50px;
                padding: 0 38px 0 30px;;
            }
            .active {
                color: #fff;
    background-color: #767dff;
            }
        }
    }
}
</style>
