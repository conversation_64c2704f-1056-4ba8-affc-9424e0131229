<template>
    <div class="ques">
        <button
            class="ques_btn"
            :class="{ active: current === index }"
            v-for="(option, index) in columns"
            :key="index"
            @click="goNext(option, index)"
        >
            {{ option.text }}
        </button>
    </div>
</template>

<script>
import { education } from "@/common/config/register-dictionary";
export default {
    name: "Education",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            columns: education,
            current: -1
        };
    },
    activated() {
        document.querySelector("#quesItem").scrollTop = 0;
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            6, // 记录点
            "注册-学历曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    methods: {
        goNext(option, index) {
            this.current = index;
            this.$select.mark({
                education: option.key
            });
            this.$storage.saveToStorage("__regInfo__", "education", option.key);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
