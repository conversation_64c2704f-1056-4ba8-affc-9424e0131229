<template>
    <question-panel>
        <div class="question-drift" ref="refDrift">
            <!-- 滑动的item -->
            <z-carousel
                v-for="(row, rowIndex) in list"
                :key="rowIndex"
                :width="670"
                background="transparent"
            >
                <div class="question-drift__row">
                    <question-tag-item
                        v-for="(item, index) in row"
                        :key="index"
                        :item="item"
                        @click="fixItem($event, item)"
                        :class="[item.check ? 'active' : '']"
                    />
                </div>
            </z-carousel>

            <!-- 选中后固定的item -->
            <question-tag-item
                v-if="requirement.driftTag.check"
                :item="requirement.driftTag"
                :style="computedStyle"
            />
        </div>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import QuestionTagItem from "./question-tag-item";
import { mapState, mapMutations } from "vuex";
import ZCarousel from "@/common/components/z-carousel.vue";

export default {
    name: "question-drift",
    components: {
        QuestionPanel,
        QuestionTagItem,
        ZCarousel
    },
    computed: {
        ...mapState(["requirement"]),
        computedStyle() {
            return {
                position: "absolute",
                top: this.$utils.pxToRem(this.requirement.driftTag.computedTop),
                left: this.$utils.pxToRem(
                    this.requirement.driftTag.computedLeft
                ),
                background: "#FE4F06"
            };
        },
        list() {
            const tagList = [
                ["一起打球", "一起健身", "一起开黑"],
                ["一起爬山", "一起看电影", "一起线上剧本杀"],
                ["一起线上狼人杀", "一起打卡咖啡店"],
                ["一起聊天半小时", "一起玩线上游戏"]
            ];

            return tagList.map(arr => {
                return arr.map(item => ({
                    text: item,
                    check:
                        item === this.requirement.driftTag.text ? true : false
                }));
            });
        }
    },
    data() {
        return {
            activeDom: []
        };
    },
    methods: {
        ...mapMutations(["setRequirement"]),
        fixItem(e, item) {
            this.$forceUpdate();

            // 将选中元素和其无缝滚动的副本隐藏
            this.hideActiveDom();

            // 获取drift框相对视口的位置，获取item相对视口的位置
            const driftContainer = this.$refs.refDrift, // drift框
                dom = e.currentTarget, // 选中的item
                containerLeft = driftContainer.getBoundingClientRect().left,
                containerTop = driftContainer.getBoundingClientRect().top,
                { left, top } = dom.getBoundingClientRect();

            // 计算相对定位用于固定
            item.computedTop = (top - containerTop) * this.$utils.getScale();
            item.computedLeft = (left - containerLeft) * this.$utils.getScale();
            item.check = true;

            // 设置当前选中项 记录位置信息和文案
            this.setRequirement({
                driftTag: item
            });
        },
        hideActiveDom() {
            const driftContainer = this.$refs.refDrift; // drift框

            // 恢复上次选中的item和其无缝滚动的副本
            if (this.activeDom.length > 0) {
                this.activeDom.forEach(activeDom => {
                    activeDom.style.visibility = "visible";
                });
            }

            // 将选中元素和其无缝滚动的副本隐藏
            this.$nextTick(() => {
                this.activeDom = driftContainer.querySelectorAll(".active");
                this.activeDom.forEach(activeDom => {
                    activeDom.style.visibility = "hidden";
                });
            });
        }
    },
    mounted() {
        this.hideActiveDom();
    }
};
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.question-drift {
    align-self: flex-start;
    position: relative;
    padding-top: 118px;
    white-space: nowrap;
    overflow: hidden;

    &__row {
        @include flex-center(row, flex-start, center);
        margin-top: 16px;
    }

    .active {
        background-image: none !important;
        background: #fe4f06;
    }
}
</style>
