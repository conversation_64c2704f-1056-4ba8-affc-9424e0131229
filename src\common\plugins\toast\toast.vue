<template>
    <transition name="fade">
        <section id="c-toast" :class="styles" :style="styles" class="c-toast f-flex-box justify-center align-center" v-show="isVisible">
            {{message}}
        </section>
    </transition>
</template>

<script>
    import { getZIndex } from '../../utils/helper'
    export default {
        name: 'toast',
        data() {
            return {
                isVisible: false,
                timer: -1,
                message: '',
                zIndex: 100,
                style: {},
                classes: {}
            }
        },
        computed: {
            styles() {
                return Object.assign({}, this.style, {
                    zIndex: 3000
                })
            }
        }
    }
</script>

<style lang="scss">
    .c-toast {
        position: fixed;
        top: 40%;
        left: 50%;
        background-color: rgba(0, 0, 0, .8);
        min-height: 50px;
        color: white;
        max-width: 80%;
        font-size: 36px;
        padding: 20px;
        z-index: 100;
        border-radius: 10px;
        word-break: break-all;
        white-space: nowrap;
        transform: translate(-50%, -50%);
    }

    .fade-enter,
    .fade-leave-to {
        opacity: 0;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity .5s;
    }
</style>
