<template>
    <div class="rcommd-wrapper">
        <div class="rcommd">
            <div class="rcommd-lock">
                <div class="rcommd-lock-info">
                    <div class="rli-img">
                        <div
                            class="rli-img-left"
                            :style="{
                                backgroundImage: `url(${currentModel &&
                                    (currentModel.mainImg ||
                                        currentModel.avatar)})`
                            }"
                        >
                            <span>当前APP在线</span>
                        </div>
                        <div class="rli-img-right">
                            <div
                                class="img"
                                :style="{
                                    backgroundImage: `url(${currentModel &&
                                        currentModel.momentImg1})`
                                }"
                            ></div>
                            <div
                                class="img"
                                :style="{
                                    backgroundImage: `url(${currentModel &&
                                        currentModel.momentImg2})`
                                }"
                            ></div>
                        </div>
                    </div>
                    <div class="rli-bar">
                        <h4>{{ currentModel && currentModel.name }}</h4>
                        <span>已实名</span>
                        <span>已购房</span>
                        <span>已购车</span>
                    </div>
                    <div class="rli-info">
                        <p class="rli-info-text">
                            {{ currentModel && currentModel.age }}岁 ·
                            {{ currentModel && currentModel.educationString }} ·
                            工作地:
                            {{ currentModel && currentModel.workCityString }}
                        </p>
                        <p class="rli-info-text">
                            手机号：{{ currentModel && currentModel.phone }}
                            <span @click="goDownload(3)">查看完整手机号</span>
                        </p>
                        <p class="rli-info-text">
                            微信号：{{ currentModel && currentModel.weChat }}
                            <span @click="goDownload(2)">查看完整微信号</span>
                        </p>
                    </div>
                    <div class="rli-oper">
                        <div
                            class="rli-oper-left"
                            :class="{ disabled: this.currentIndex <= 0 }"
                            @click="handleOperMinus"
                        ></div>
                        <div class="rli-oper-btn" @click="goDownload(1)"></div>
                        <div
                            class="rli-oper-right"
                            :class="{ disabled: this.currentIndex >= 7 }"
                            @click="handleOperAdd"
                        ></div>
                    </div>
                </div>
            </div>
            <div class="rcommd-common">
                <div class="rcommd-common-title">
                    下载「珍爱APP」立即享受
                </div>
                <div class="rcommd-person">
                    <div class="com-title">
                        免费领取专属恋爱异性 <span>88位</span>
                    </div>
                    <!-- 轮播 -->
                    <div class="rcommd-swiper">
                        <za-swiper
                            v-if="avatarList.length"
                            item-width-mode="custom"
                            :list="avatarList"
                            :inner-width="this.$utils.pxToRem(600)"
                            :inner-height="this.$utils.pxToRem(184)"
                            :span-gap="this.$utils.pxToRem(24)"
                            auto-play
                        >
                            <template slot-scope="{ item }" slot="default">
                                <div
                                    class="rcommd-swiper-img"
                                    :style="{
                                        backgroundImage: `url(${item.avatar})`
                                    }"
                                >
                                    <span
                                        >{{ item.name }} ·
                                        {{ item.age }}岁</span
                                    >
                                </div>
                            </template>
                        </za-swiper>
                    </div>
                    <div class="com-btn" @click="goGetMatch">
                        立即领取
                    </div>
                </div>
                <div class="rcommd-gift">
                    <div class="com-title">
                        珍爱红娘脱单锦囊 <span>1个</span>
                    </div>
                    <div class="rcommd-gift-tips">
                        每个人都有恋爱盲点，如何避开盲点，找到自己的恋爱优势，珍爱官方红娘会给你最好的建议！
                    </div>
                    <div class="rcommd-gift-img">
                        <img
                            src="https://photo.zastatic.com/images/common-cms/it/20220806/1659754769771_564032_t.png"
                            alt=""
                        />
                    </div>
                    <div class="com-btn" @click="goGetGift">
                        打开看看
                    </div>
                </div>
                <div class="rcommd-class">
                    <div class="com-title">珍爱脱单课程 <span>1份</span></div>
                    <div class="rcommd-class-tips">
                        {{
                            registerForm.gender === 1
                                ? "《怎样判断他是不是真心喜欢我？》"
                                : "《8个两性交往技巧，让她情不自禁爱上你》"
                        }}
                    </div>
                    <div class="rcommd-class-list">
                        <p class="rcl-text">售价：<span>限时免费赠送</span></p>
                        <p class="rcl-text">
                            如何领取：下载珍爱APP即可自动获赠
                        </p>
                        <p class="rcl-text">
                            如何学习：在珍爱APP内学习
                        </p>
                    </div>
                    <div class="rcommd-class-clock">
                        免费领取倒计时：<span>{{ timeText }}</span>
                    </div>
                    <div class="com-btn" @click="handleProced(1)">
                        领取课程
                    </div>
                </div>
            </div>
        </div>
        <div class="rcommd-bottom">
            <div class="rcommd-bottom-btn" @click="handleProced(2)"></div>
            <div class="rcommd-bottom-link">
                <a class="rcommd-bottom-link" @click="handleGoBack">访问网页版可查看更多优质异性&gt;&gt;</a>
            </div>
        </div>
        <DownloadModal v-model="downloadVisible" :downloadNum="downloadNum" />
        <ProcedModal
            v-model="procedVisible"
            :procedNum="procedNum"
            :timeText="timeText"
        />
        <MatchModal
            v-model="matchVisible"
            :modelList="avatarList.slice(0, 5)"
            :gender="registerForm.gender"
        />
        <ProposalModal v-model="proposalVisible" />
    </div>
</template>

<script>
import DownloadModal from "../components/DownloadModal.vue";
import MatchModal from "../components/MatchModal.vue";
import ProcedModal from "../components/ProcedModal.vue";
import ProposalModal from "../components/ProposalModal.vue";
import { _getModelInfos, _getSpecifyGenderRandomAvatar } from "../api";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { Toast } from "vant";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import ZaSwiper from "@za/vue-za-swiper";
import "@za/vue-za-swiper/dist/style.css";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "Rcommd",
    data() {
        return {
            timeDelay: 15 * 60,
            timeText: "15分00秒",
            downloadVisible: false,
            downloadNum: 0,
            procedVisible: false,
            matchVisible: false,
            proposalVisible: false,
            procedNum: 0,
            modelList: [],
            registerForm: Storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            ) || {
                gender: 0,
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            currentIndex: 0,
            avatarList: [],
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false
            },
            screenHeight: 0
        };
    },
    inject: ["cmsConfig"],
    components: {
        DownloadModal,
        ProcedModal,
        MatchModal,
        ProposalModal,
        ZaSwiper
    },
    computed: {
        currentModel() {
            return this.modelList && this.modelList[this.currentIndex];
        }
    },
    created() {
        this.getModelInfos();
        this.getAvatars();
    },
    mounted() {
        // 监听返回事件，强制跳转至 wap 站
        window.history.pushState(null, "", "");
        window.addEventListener("popstate", () => {
            this.$report(12, "引导下载页-返回按钮点击");
            this.handleGoBack();
        });

        this.$report(12, "引导下载页访问");
        setInterval(() => {
            if (this.timeDelay > 0) {
                this.timeDelay--;
                let m = Math.floor(this.timeDelay / 60);
                let s = Math.floor(this.timeDelay % 60);
                this.timeText = `${m}分${s < 10 ? "0" + s : s}秒`;
            }
        }, 1000);

        window.addEventListener("scroll", this.handleExposure);
        this.screenHeight = document.documentElement.clientHeight;
    },
    destroyed() {
        window.removeEventListener("scroll", this.handleExposure);
        window.removeEventListener("popstate", this.handleGoBack);
    },
    methods: {
        handleProced(val) {
            const REPORTENUM = {
                1: "引导下载页-领取课程按钮点击",
                2: "引导下载页-主下载按钮点击"
            };
            this.$report(12, REPORTENUM[val]);
            if (this.cmsConfig.downloadStatus === 0) {
                this.procedNum = val;
                this.procedVisible = true;
            } else {
                this.handleDownload();
            }
        },
        async getModelInfos() {
            const age =
                new Date().getFullYear() -
                new Date(this.registerForm.birthday).getFullYear();
            const sendData = {
                limit: 8,
                salary: this.registerForm.salary,
                workCity: this.registerForm.workCity,
                education: this.registerForm.education,
                sex: this.registerForm.gender,
                age
            };
            const result = await _getModelInfos(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            this.modelList = result.data.modelInfoVos;
        },
        async getAvatars() {
            // 计算用户的年龄
            let age =
                new Date().getFullYear() -
                new Date(this.registerForm.birthday).getFullYear();

            let sendData = {
                sex: +this.registerForm.gender === 0 ? 1 : 0, // 当前性别为男(0),则传女(1)
                limit: 20, // 随机头像个数为3
                age
            };
            let resData = await _getSpecifyGenderRandomAvatar(sendData);
            if (resData.isError) {
                this.$toast(resData.errorMessage);
                return;
            }

            this.avatarList = resData.data.list.map(item => ({
                avatar: item.avatar + "?imageMogr2/thumbnail/88x88",
                name: item.name,
                age: item.age
            }));
        },
        handleOperMinus() {
            this.$report(12, "引导下载页-点击上一个按钮点击");
            if (this.currentIndex <= 0) {
                return;
            } else {
                this.currentIndex--;
            }
        },
        handleOperAdd() {
            this.$report(12, "引导下载页-点击下一个按钮点击");
            if (this.currentIndex >= 7) {
                return;
            } else {
                this.currentIndex++;
            }
        },
        goDownload(id) {
            const REPORTENUM = {
                1: "引导下载页-喜欢Ta按钮点击",
                2: "引导下载页-查看微信号按钮点击",
                3: "引导下载页-查看手机号按钮点击"
            };
            this.$report(12, REPORTENUM[id]);

            this.downloadNum = id;
            if (this.cmsConfig.downloadStatus === 0) {
                this.downloadVisible = true;
            } else {
                this.handleDownload();
            }
        },
        goGetMatch() {
            this.$report(12, "引导下载页-领取对象按钮点击");
            if (this.cmsConfig.downloadStatus === 0) {
                this.matchVisible = true;
            } else {
                this.handleDownload();
            }
        },
        goGetGift() {
            this.$report(12, "引导下载页-领取锦囊按钮点击");
            if (this.cmsConfig.downloadStatus === 0) {
                this.proposalVisible = true;
            } else {
                this.handleDownload();
            }
        },
        handleDownload() {
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({ value: true });
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        handleExposure() {
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$report(12, "引导下载页第2屏曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$report(12, "引导下载页第3屏曝光");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$report(12, "引导下载页第4屏曝光");
                this.reportLock.fourthScreen = true;
            }
            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleExposure);
            }
        },
        handleGoBack() {
            this.$report(12, "引导下载页-访问网页入口点击");
            const channelId = Z.getParam("channelId");
            const subChannelId = Z.getParam("subChannelId");
            location.href = `${
                location.protocol
            }//i.zhenai.com/m/wap/index/index.html?channelId=${channelId}&subChannelId=${subChannelId}`;
        }
    }
};
</script>
<style lang="scss" scoped>
.rcommd {
    position: relative;
    z-index: 1;
    padding: 40px 30px 258px;
    background: #b3c9ff;
    &-wrapper {
        overflow: auto;
    }
    &::before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 212px;
        right: 0;
        width: 136px;
        height: 212px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694253583_130_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    &::after {
        content: "";
        position: absolute;
        z-index: -1;
        bottom: 0;
        right: 0;
        width: 136px;
        height: 106px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220806/1659750504410_533060_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    &-swiper {
        margin-top: 32px;
        &-img {
            position: relative;
            overflow: hidden;
            width: 184px;
            height: 184px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            border-radius: 16px;
            > span {
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                height: 46px;
                line-height: 46px;
                color: #fff;
                font-size: 22px;
                text-align: center;
                background: rgba(0, 0, 0, 0.27);
            }
        }
    }
    &-lock {
        padding-top: 212px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694564577_292107_t.png)
            no-repeat;
        background-size: 100% 212px;
        background-position: left top;
        &-info {
            margin-top: -40px;
            padding: 40px 48px;
            background: #fff;
            border-radius: 40px;
        }
        .rli-img {
            display: flex;
            &-left {
                position: relative;
                width: 388px;
                height: 388px;
                margin-right: 22px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                border-radius: 16px;
                > span {
                    position: absolute;
                    left: 10px;
                    bottom: 16px;
                    padding: 8px 36px 4px 24px;
                    line-height: 40px;
                    color: #ffffff;
                    font-size: 24px;
                    background: #5368f0;
                    border-radius: 26px;
                }
            }
            &-right {
                .img {
                    width: 184px;
                    height: 184px;
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    border-radius: 16px;
                }
                .img:nth-of-type(1) {
                    margin-bottom: 20px;
                }
            }
        }
        .rli-bar {
            display: flex;
            align-items: center;
            margin-top: 16px;
            height: 50px;
            > h4 {
                flex-shrink: 0;
                margin-right: 24px;
                color: #191c32;
                font-size: 34px;
                font-weight: 600;
            }
            > span {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                height: 50px;
                margin-right: 16px;
                padding: 2px 16px 0 50px;
                color: #191c32;
                font-size: 22px;
                background-color: rgba(194, 206, 231, 0.2);
                border-radius: 26px;
                background-repeat: no-repeat;
                background-position: left 16px center;
            }
            > span:nth-of-type(1) {
                background-size: 26px 28px;
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694259332_301940_t.png);
            }
            > span:nth-of-type(2) {
                background-size: 24px 24px;
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694262005_504445_t.png);
            }
            > span:nth-of-type(3) {
                background-size: 24px 22px;
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694287972_393355_t.png);
            }
        }
        .rli-info {
            margin-top: 26px;
            color: #191c32;
            font-size: 28px;
            > p {
                margin-bottom: 24px;
                > span {
                    color: #5368f0;
                    font-weight: 500;
                    text-decoration: underline;
                }
            }
        }
        .rli-oper {
            display: flex;
            align-items: center;
            &-btn {
                width: 298px;
                height: 88px;
                margin: 0 26px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694268875_269604_t.png)
                    no-repeat;
                background-size: 100% 100%;
                &:active {
                    opacity: 0.7;
                }
            }
            &-left,
            &-right {
                width: 122px;
                height: 110px;
                background: #eceeff;
                border-radius: 78px;
                background-size: 46px 46px;
                background-repeat: no-repeat;
                background-position: center center;
            }
            &-left {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694266780_373119_t.png);
                &:active {
                    opacity: 0.7;
                }
                &.disabled {
                    transform: rotateY(180deg);
                    background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694264650_228922_t.png);
                }
            }
            &-right {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694266780_373119_t.png);
                transform: rotateY(180deg);
                &:active {
                    opacity: 0.7;
                }
                &.disabled {
                    transform: rotateY(0deg);
                    background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659694264650_228922_t.png);
                }
            }
        }
    }
    &-common {
        position: relative;
        margin-top: 40px;
        padding: 100px 24px 28px;
        border: 2px solid #ffffff;
        background: #8aa6ff
            url(https://photo.zastatic.com/images/common-cms/it/20220806/1659753671065_382430_t.png)
            no-repeat;
        background-size: 404px 84px;
        background-position: center top;
        border-radius: 40px;
        box-shadow: inset 0 2px 16px 0 rgba(250, 251, 255, 0.3);
        &::before {
            content: "";
            position: absolute;
            top: 40px;
            right: 0;
            width: 148px;
            height: 106px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220806/1659753665151_653108_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        &::after {
            content: "";
            position: absolute;
            z-index: -1;
            top: 478px;
            left: -32px;
            width: 138px;
            height: 212px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220806/1659750602167_116213_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        &-title {
            color: #fff;
            font-size: 32px;
            font-weight: 500;
            text-align: center;
        }
    }
    &-person,
    &-gift,
    &-class {
        margin-top: 40px;
        background: #ffffff;
        border: 2px solid #ffffff;
        border-radius: 24px;
    }
    .com-title {
        position: relative;
        padding-left: 50px;
        color: #0f1122;
        font-size: 32px;
        font-weight: 600;
        height: 72px;
        line-height: 72px;
        background: linear-gradient(
            90deg,
            #f99cff 0%,
            rgba(250, 137, 255, 0) 90%,
            rgba(250, 137, 255, 0) 90%
        );
        border-radius: 24px 0 24px 0;
        &::before {
            content: "";
            position: absolute;
            left: 24px;
            top: 14px;
            width: 22px;
            height: 38px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220806/1659754763667_456912_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        > span {
            color: #f16598;
            font-size: 36px;
        }
    }
    &-person {
        padding-bottom: 32px;
    }
    .com-btn {
        margin: 32px auto 0;
        width: 298px;
        height: 88px;
        color: #fff;
        font-size: 32px;
        font-weight: 500;
        line-height: 88px;
        text-align: center;
        background: #5368f0;
        border-radius: 78px;
        &:active {
            opacity: 0.7;
        }
    }
    &-gift {
        padding-bottom: 32px;
        &-tips {
            padding: 24px 24px 16px;
            color: #0f1122;
            font-size: 28px;
            line-height: 44px;
        }
        &-img {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 298px;
            margin: 0 24px;
            background: linear-gradient(269deg, #f5f0fe 0%, #eff7fe 100%);
            border-radius: 24px;
            > img {
                width: 296px;
                height: 218px;
            }
        }
    }
    &-class {
        padding-bottom: 32px;
        &-tips {
            padding-top: 24px;
            padding-bottom: 32px;
            color: #0f1122;
            font-size: 28px;
            font-weight: 500;
        }
        &-list {
            margin: 0 24px;
            padding: 40px 0 8px 24px;
            background-image: linear-gradient(269deg, #f5f0fe 0%, #eff7fe 100%);
            border-radius: 24px;
            .rcl-text {
                position: relative;
                padding-left: 26px;
                padding-bottom: 32px;
                color: #0f1122;
                font-size: 28px;
                &::before {
                    content: "";
                    position: absolute;
                    top: 6px;
                    left: 0;
                    width: 18px;
                    height: 18px;
                    background: linear-gradient(
                        129deg,
                        #6487fe 9%,
                        #d694ff 92%
                    );
                    border-radius: 50%;
                }
                > span {
                    color: #f16598;
                    font-size: 32px;
                    font-weight: 500;
                }
            }
        }
        &-clock {
            padding-top: 32px;
            color: #f16598;
            font-size: 28px;
            font-weight: 500;
            text-align: center;
            > span {
                font-size: 36px;
                font-weight: 600;
            }
        }
    }
    &-bottom {
        position: fixed;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1;
        text-align: center;
        background-color: rgba(255, 255, 255, .65);
        border-radius: 32px 32px 0 0;
        &-link {
            text-align: center;
            margin: 16px 0 32px;
            height: 28px;
            a {
                margin: 0;
                color: #888b8a;
                font-size: 28px;
                font-weight: 500;
                vertical-align: top;
            }
        }
        &-btn {
            margin: 32px auto 0;
            width: 620px;
            height: 134px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220809/1660042261990_813500_t.png)
                no-repeat;
            background-size: 100% 100%;
            &:active {
                opacity: 0.7;
            }
        }
    }
}
</style>
