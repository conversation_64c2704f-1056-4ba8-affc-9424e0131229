<template>
    <div class="collection-form-wrapper">
        <!-- 填写进度 -->
        <div class="content__progress">
            <div class="progress__info">
                {{ `完善资料，为你匹配离你最近的异性朋友` }}
            </div>
            <div
                class="progress__bar"
                ref="refProgressBar"
            >
                <div
                    class="progress__bar__svga"
                    id="svgaProgress"
                ></div>
                <div
                    class="progress__bar__content"
                    ref="refProgress"
                >
                    {{
                        computedProgress === 100
                            ? `资料已完善 ${computedProgress}%`
                            : `${cmsConfig.formProgressText} ${computedProgress}%`
                    }}
                </div>
                <!-- <div class="progress__bar__blur"></div> -->
            </div>
        </div>

        <!-- 填写内容 -->
        <div class="content__collection">
            <!-- 注册项 -->
            <div
                v-for="(item, index) in formInfo"
                :key="index"
                class="collection__item"
                @click="openSelect(item)"
            >
                <div class="collection__item__label">
                    {{ item.label }}
                </div>
                <!-- 性别项 -->
                <div
                    v-if="item.index === 'gender'"
                    class="collection__item__select--gender"
                >
                    <button
                        v-for="(item, index) in formInfo[0].selectArr"
                        :key="index"
                        class="gender-item"
                        @click="setGender(item)"
                        :class="
                            item.key === registerInfo.gender ? 'gender-item--selected' : ''
                        "
                    >
                        {{ item.text }}
                    </button>
                </div>

                <!-- 工作地、出生年份、学历、婚况、月收入 -->
                <div
                    v-else
                    class="collection__item__select"
                >
                    <span v-if="item.value">{{ item.value }}</span>
                    <span
                        v-else
                        class="color--4633EF"
                    >{{ "待完善" }}</span>
                </div>
            </div>

            <!-- 手机号 -->
            <div class="collection__phone">
                <div class="collection__phone__label">
                    您的手机号
                </div>
                <div class="collection__phone__input">
                    <input
                        ref="refPhoneInput"
                        type="tel"
                        :value="phone"
                        @input="limit"
                        placeholder="请输入11位手机号"
                        maxlength="13"
                    />
                </div>
                <div
                    class="collection__phone__clear"
                    @click="phone = ''"
                ></div>
            </div>

            <!-- 图形验证码 -->
            <div
                v-if="showImgCode"
                class="collection__code"
            >
                <div class="collection__code__input">
                    <input
                        ref="refImgCodeInput"
                        type="text"
                        v-model="imgCode"
                        placeholder="请输入验证码"
                        maxlength="4"
                    />
                </div>
                <div
                    class="collection__code__img"
                    ref="refImgCode"
                ></div>
                <div
                    class="collection__code__refresh"
                    @click="setImgCode"
                ></div>
            </div>
        </div>

        <div
            class="bell__submit"
            @click="submitRegisterInfo('buttonIndex')"
            :class="finished ? 'bell__submit__finished' : ''"
        >
            <div class="bell__submit__icon"></div>
        </div>

        <!-- 协议 -->
        <div class="content__protocal">
            <div
                :class="
                    hasCheckProtocal
                        ? 'content__protocal__checked'
                        : 'content__protocal__uncheck'
                "
                @click="checkProtocal"
            ></div>
            已阅读并同意<span @click="goUrl(1)">《珍爱网服务协议》</span>和<span
                @click="goUrl(2)"
            ><br />《个人信息保护政策》&nbsp;</span>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
            :validate-code="validateCode"
        />

        <!-- 底部选择框 -->
        <select-panel
            v-if="showSelect"
            @close-select="closeSelect"
            :select-type="selectType"
            :select-param="selectParam"
        />
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
import { SelectPanel, Modal } from "../common/index.js";
import { reportError, reportMagic } from "@/common/utils/report.js";
import { channelId, subChannelId, oExt9 } from "@/common/js/const.js";
import { reportKibana } from "@/common/utils/report.js";
import oUserSelect from "@/common/ocpx/huichuan.js";
import Api from "@/common/server/base";
import Prototype from "@/common/framework/prototype";
import {
    registerResult,
    pageTypeMap
} from "@/common/config/register-dictionary.js";

export default {
    components: {
        SelectPanel,
        Modal
    },
    data() {
        return {
            progress: 0,
            isTransitionOver: false,
            handlerTransition: false,
            player: null,
            showModal: false,
            modalType: "modalValidate",
            modalParam: {},
            showSelect: false,
            selectType: "null",
            selectParam: {},
            showImgCode: false,
            lockBaseInfo: false,
            lockPhone: false,
            lockOverwrite: false,
            messageCode: ""
        };
    },
    computed: {
        ...mapState([
            "formInfo",
            "registerInfo",
            "code",
            "cmsConfig",
            "isCover",
            "hasCheckProtocal",
            "regMemberId"
        ]),
        ...mapGetters(["getProgress", "getNormalPhone"]),
        computedProgress() {
            // 5s中之内始终保持匀速自增至50%
            if (!this.isTransitionOver) {
                return this.progress;
            } else {
                // 6个表单项占6，手机号码占4
                let rest = this.getProgress === 7 ? 100 : 60 + 6 * this.getProgress;
                this.setProgress(rest);
                // 优化，正常填完6项只差手机号时，将手机号显示出来
                if (this.getProgress === 6) {
                    this.$refs.refPhoneInput.scrollIntoView();
                }
                return rest;
            }
        },
        phone: {
            get() {
                return this.registerInfo.phone;
            },
            set(newVal) {
                // console.log("手机号校验",newVal);
                // newVal = newVal.replace(/[^\d]/g,'');
                this.setRegisterInfo({
                    phone: newVal
                });
                // this.phone2 = e.target.value.replace(/[^\d]/g,"");
            }
        },
        imgCode: {
            get() {
                return this.code;
            },
            set(newVal) {
                this.setCode(newVal);
            }
        },
        finished() {
            // 完成7项基本资料的填写
            return this.getProgress === 7;
        }
    },
    created() {},
    mounted() {
        // 设置进度条动效
        this.setSVGA();

        // 设置进度条
        setTimeout(() => {
            this.setProgress(60);
        }, 0);

        this.scrollInput();
    },
    methods: {
        scrollInput() {
            // 【兼容】安卓端控制输入组件不被软键盘遮挡
            if (/Android/i.test(navigator.userAgent)) {
                window.addEventListener("resize", () => {
                    if (
                        document.activeElement.tagName.toUpperCase() === "INPUT" ||
                        document.activeElement.tagName.toUpperCase() === "TEXTAREA"
                    ) {
                        window.setTimeout(() => {
                            document.activeElement.scrollIntoView({
                                block: "center"
                            });
                        }, 0);
                    }
                });
            }
        },
        limit(e) {
            e.target.value = e.target.value.replace(/[^(\d|\s)]/g, "");
            this.phone = this.formatPhone(e);
        },
        formatPhone(e) {
            // 存储当前光标位置用于之后重置
            let position = e.target.selectionStart;

            if (e.inputType === "deleteContentBackward") {
                // 删除的情况
                // 删到空格时 需要多删除空格前一位数
                if (position === 3 || position === 8) {
                    let temArr = e.target.value.split("");
                    temArr.splice(position - 1, 1);
                    e.target.value = temArr.join("");
                    // 光标也要跟着前移一位
                    position -= 1;
                }
                // 格式化
                e.target.value = e.target.value
                    .replace(/\D/g, "")
                    .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3")
                    .trim();
                // 重置光标,setTimeout用于兼容苹果手机
                setTimeout(() => {
                    e.target.selectionStart = e.target.selectionEnd = position;
                }, 0);
                // e.target.selectionStart = e.target.selectionEnd = position;
            } else if (e.inputType === "insertText") {
                // 插入的情况
                if (e.target.value.length < 9) {
                    e.target.value = e.target.value
                        .replace(/\D/g, "")
                        .replace(/(\d{3})(\d{0,4})/, "$1 $2");
                } else {
                    e.target.value = e.target.value
                        .replace(/\D/g, "")
                        .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
                }
                if (position < e.target.value.length) {
                    // 输入空格后第一位数时 光标要往后移一位
                    if (position === 4 || position === 9) {
                        position += 1;
                    }
                    // 重置光标
                    setTimeout(() => {
                        e.target.selectionStart = e.target.selectionEnd = position;
                    }, 0);
                }
            } else {
                // 复制粘贴的情况
                let pasteStr = e.target.value.replace(/\D/g, "");
                // maxlenth为13，所以这里要限制位数大于11时截取
                if (pasteStr > 11) {
                    e.target.value = pasteStr
                        .substr(0, 11)
                        .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
                }
            }

            return e.target.value;
        },
        formatMobile(mobile, e) {
            if (!mobile) return "";
            let result = mobile.replace(/[^(\d|\s)]/g, "");
            if (e.inputType === "insertText") {
                if (result.length === 3 || result.length === 8) {
                    return result.replace(result, result + " ");
                }
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/(\d$)/g, " $1");
                }
            } else if (e.inputType === "deleteContentBackward") {
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/\s$/, "");
                }
            }
            // 复制粘贴的情况
            result = result.replace(/\D/g, "");

            if (result.length > 11) {
                result = result.substr(0, 11);
            }

            if (result.length > 3 && result.length < 8) {
                result = result.replace(/^(\d{3})/g, "$1 ");
            } else if (result.length >= 8) {
                result = result.replace(/^(\d{3})(\d{4})/g, "$1 $2 ");
            }

            return result;
        },
        ...mapMutations([
            "setRegisterInfo",
            "setFormInfo",
            "setCmsConfig",
            "setCode",
            "setOverwriteRegistrationSwitch",
            "setRegMemberId",
            "setIsCover",
            "setHasCheckProtocal"
        ]),
        ...mapActions([
            // "setModelInfo"
        ]),
        async setSVGA() {
            let player = new SVGA.Player("#svgaProgress"),
                parser = new SVGA.Parser("#svgaProgress");

            parser.load(require("../../assets/imgs/svgaProgress.svga"), videoItem => {
                player.setVideoItem(videoItem);
                player.loops = 0;
                player.startAnimation();
            });
        },
        setProgress(rate) {
            let domProgress = this.$refs.refProgress,
                refProgressBar = this.$refs.refProgressBar;

            // 取消
            if (!this.handlerTransition) {
                this.handlerTransition = domProgress.addEventListener(
                    "transitionend",
                    () => {
                        domProgress.style.transition = "none";
                        this.isTransitionOver = true;
                    },
                    false
                );
            }

            // 初始自增长至60%
            this.$refs.refProgress.style.width = `${rate}%`;
            let requestID;
            const fn = () => {
                this.progress = parseInt(
                    (domProgress.offsetWidth / refProgressBar.offsetWidth) * 100
                );
                if (this.progress > 60) {
                    // return;
                    return cancelAnimationFrame(requestID);
                }
                requestID = requestAnimationFrame(fn);
            };

            fn();
        },
        controlProgress() {
            this.registerInfo.forEach(item => {
                console.log(item);
            });
        },
        closeModal() {
            this.showModal = false;
            this.modalParam = {};
        },
        closeSelect() {
            this.showSelect = false;
        },
        // 页面跳转
        jump(path) {
            this.$router.push({
                path,
                query: {
                    // plan:123
                }
            });
        },
        async submitRegisterInfo(from) {
            // 自测，改

            // localStorage.setItem("flagFilled", "1");
            // this.jump("blindinfo")
            // return;

            let ext17 = [];
            this.formInfo.forEach(item => {
                if (item.value) {
                    ext17.push(item.value);
                }
            });
            if (this.phone) {
                ext17.push(this.phone.replace(/\s/g, ""));
            }
            ext17 = ext17.join(",");
            // 如果是大表单页按钮点击时触发，需要打桩
            if (from === "buttonIndex") {
                // 信息未填写完整却点击了提交
                if (!this.finished) {
                    reportKibana("简爱大表单", 2, "首页-提交按钮点击", {
                        ext17
                    });
                } else {
                    reportKibana("简爱大表单", 3, "首页-提交按钮点击（可点状态）", {
                        ext17
                    });
                }
            }

            // 锁，已经发送请求
            if (this.lockBaseInfo || this.lockPhone) {
                return;
            }

            // 如果是确认协议弹窗时触发,需额外处理
            if (from === "buttonProtocol") {
                // 更新UI
                this.setHasCheckProtocal(true);
                // 关闭弹窗，因为此时可能还需要填写图形验证码
                this.closeModal();
            }

            // 信息未填完整
            if (!this.finished) {
                this.$toast("请完善资料再提交");
                return;
            }

            // 未勾选协议
            if (!this.hasCheckProtocal) {
                this.openModal("modalProtocol", {});
                return;
            }

            // 需要填写图形验证码
            if (this.showImgCode && !this.imgCode) {
                this.$toast("请输入图形验证码");
                return;
            }

            // 老注册页qms上报逻辑
            Z.tj.qms({
                dataType: "stat",
                data: {
                    type: "tag",
                    tname: `click.${channelId || ""}.${subChannelId || ""}`
                }
            });

            // 提交注册信息
            let sendData = { ...this.registerInfo },
                resData = null;
            sendData.height = -1; // 没有身高项，为兼容老注册页直接传-1
            delete sendData.phone; // 基础信息提交时，不提交手机号

            this.lockBaseInfo = true;
            // first参数用于确定是否直接跳老注册的密码页,新注册页没有密码页，该逻辑暂不迁移
            // if (Z.getParam('first')) {
            //     let isCover = Z.getParam('cover');
            //     sendData.phone = Z.getParam('phone');
            //     sendData.isCover = isCover ? 1 : 0;
            //     resData = await _submitWapRegBaseInfoToRegister(sendData);
            // } else {
            // resData = await _submitWapRegBaseInfo(sendData);
            resData = await Api.submitWapRegBaseInfo(sendData);

            // }
            this.lockBaseInfo = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                // 老注册页qms上报逻辑
                reportError(
                    "新注册页(大表单H5),注册信息提交失败 |" +
                        resData.errorMessage +
                        " | " +
                        JSON.stringify(this.registerInfo)
                );
                // 打桩
                return;
            }

            // 提交手机号 / (手机号+图形验证码)等其他信息
            await this.submitPhoneInfo();
        },
        async submitPhoneInfo() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();

            let sendData = {
                phone: this.getNormalPhone,
                type: 0,
                imgCode: this.imgCode,
                // 极验参数，暂不迁移
                // challenge: self.gtObj.challenge,
                // validate: self.gtObj.validate,
                // seccode: self.gtObj.seccode,
                // 落地页url, 用于记录百度投放跳转到落地页时添加的参数
                landingUrl: document.referrer || undefined
            };

            // 【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype")
            };

            for (const v in toutiaoParamlist) {
                if (toutiaoParamlist[v]) {
                    sendData[v] = toutiaoParamlist[v];
                }
            }

            this.lockPhone = true;
            // 自改
            // let resData = await _submitWapRegNoPasswordInfo(sendData);
            let resData = await Api.sendWapMessageCodeV2(sendData);
            // let resData = {
            //     data:{
            //         type: -1,
            //         memberID: 20000000,
            //         overwriteRegistrationSwitch: false
            //     },
            //     isError:false,
            //     errorMessage:"ddddddd"
            // }

            this.lockPhone = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                // 重置图形验证码
                // this.setImgCode();
                return;
            }

            // 走验证码逻辑
            this.goValidate();
        },
        setGender(item) {
            this.setFormInfo({
                gender: item.text
            });
            this.setRegisterInfo({
                gender: item.key
            });

            // 处理回传
            oUserSelect.mark({
                gender: item.key
            });
        },
        openSelect(currentItem) {
            if (["gender"].includes(currentItem.index)) {
                return;
            } else if (["workCity", "birthday"].includes(currentItem.index)) {
                this.selectType = "selectSlide";
            } else if (
                ["marriage", "education", "salary"].includes(currentItem.index)
            ) {
                this.selectType = "selectBoard";
            }
            this.selectParam = currentItem;
            this.showSelect = true;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.showModal = true;
        },
        checkProtocal() {
            // 默认都是勾选
            // CMS配置不需要勾选
            if (this.cmsConfig.agreementStatus === 0) {
                return;
            }

            // 需要勾选
            this.setHasCheckProtocal(!this.hasCheckProtocal);
            // this.hasCheckProtocal = !this.hasCheckProtocal;
        },
        modifyCustomer(data) {
            Z.ajax({
                type: "POST",
                url:
                    "https://call-api.zhenai.com/workapi/externalController/modifyCustomer",
                dataType: "json",
                opts: {
                    headers: {
                        "Content-Type": "application/json;charset=UTF-8"
                    }
                },
                data,
                success: function(results) {
                    console.log(results);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.log(xhr, textStatus, errorThrown);
                }
            });
        },
        setImgCode() {
            // 重置验证码
            this.imgCode = "";

            this.$nextTick(() => {
                let refImgCode = this.$refs.refImgCode,
                    refImgCodeInput = this.$refs.refImgCodeInput;

                let src =
                    window.location.protocol +
                    "//api.zhenai.com/register/getWapRegisterImgCode.do?t=" +
                    new Date().getTime();

                if (refImgCode) {
                    refImgCode.style.backgroundImage = `url(${src})`;
                    refImgCodeInput.focus();
                    // 输入框位置调整
                    refImgCodeInput.scrollIntoView({
                        block: "center"
                    });
                }
                return;
            });
        },
        // 覆盖注册
        async coverRegister() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = Prototype.$gather.getOverwriteAccountParams(
                this.messageCode,
                pageTypeMap.XIHUAN
            );
            const resData = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                return;
            }

            if (resData.data.memberID) {
                // 存vuex
                this.setRegMemberId(resData.data.memberID);
                // 记录ocpc回传状态
                oUserSelect.mark({
                    msgValid: true
                });
            }

            reportKibana("简爱大表单", 4, "注册成功并生成ID", {
                ext17: "覆盖注册"
            });

            this.closeModal();
            this.onRegisterFinished();
        },
        // 先处理OCPX相关，然后打开验证码弹窗
        goValidate() {
            Prototype.$gather.setBeforeValidateCodeOCPC();

            // 打开验证码窗口
            this.openModal("modalValidate", {});
        },
        goUrl(type) {
            if (type === 1) {
                location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
            } else if (type === 2) {
                location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
            }
        },
        async validateCode(messageCode) {
            this.$gather.setAbtZaTTTCookie();
            const sendData = this.$gather.getValidateCodeParams(
                this.getNormalPhone,
                messageCode,
                pageTypeMap.XIHUAN
            );
            const resData = await Api.submitWapRegNoPasswordInfoV2(sendData);

            if (!resData.isError) {
                // 取号成功
                let {
                    type = undefined,
                    memberID = undefined,
                    oldMemberID = undefined, // 覆盖注册的旧会员id
                    overwriteRegistrationSwitch = undefined // 开关, 用于控制已注册弹窗中是否允许尝试打开/下载APP
                } = resData.data;

                // 存vuex
                this.setOverwriteRegistrationSwitch(overwriteRegistrationSwitch);

                // 微信朋友圈投放
                const [userId, externalUserId] = [
                    Z.getParam("userId"),
                    Z.getParam("externalUserId")
                ];
                let customId = memberID || oldMemberID;
                if (userId && externalUserId && customId) {
                    this.modifyCustomer({
                        userId,
                        externalUserId,
                        memberId: customId,
                        channelId,
                        subChannelId
                    });
                }

                if (memberID) {
                    // 存vuex
                    this.setRegMemberId(memberID);
                    // 记录ocpc回传状态
                    oUserSelect.mark({
                        msgValid: true
                    });
                }

                this.messageCode = messageCode;
                switch (type) {
                case registerResult.LOGIN_ACCOUNT.value:
                    // 展示已注册弹窗，不提供覆盖注册按钮
                    reportKibana("简爱大表单", 4, "注册成功并生成ID", {
                        ext17: "登录"
                    });
                    reportKibana("简爱大表单", 5, "手机号验证成功", {
                        ext17: "登录"
                    });
                    this.setIsCover(false);

                    oExt9.set(2);
                    // this.openModal("modalRegistered", {
                    //     showCoverButton: false
                    // });
                    this.setRegMemberId(customId);
                    this.onRegisterFinished();
                    break;
                case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                    reportKibana("简爱大表单", 5, "手机号验证成功", {
                        ext17: "覆盖注册"
                    });
                    this.setIsCover(true);
                    oExt9.set(1);
                    // 展示已注册弹窗，提供覆盖注册按钮
                    // this.openModal("modalRegistered", {
                    //     showCoverButton: true
                    // });
                    this.coverRegister()
                    break;
                case registerResult.NEW_ACCOUNT.value:
                    // 正常注册
                    reportKibana("简爱大表单", 4, "注册成功并生成ID", {
                        ext17: "新注册"
                    });
                    reportKibana("简爱大表单", 5, "手机号验证成功", {
                        ext17: "新注册"
                    });


                    oExt9.set(2);
                    // 走验证码逻辑
                    // this.goValidate();
                    this.onRegisterFinished();
                    break;
                case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                    // 一年内未活跃非珍心会员 && 不弹窗 && 覆盖资料
                    reportKibana("简爱大表单", 4, "注册成功并生成ID", {
                        ext17: "覆盖注册"
                    });
                    reportKibana("简爱大表单", 5, "手机号验证成功", {
                        ext17: "覆盖注册"
                    });

                    this.setIsCover(true);
                    oExt9.set(1);
                    // 走覆盖注册的逻辑
                    // this.coverRegister();
                    this.onRegisterFinished();
                    break;
                default:
                    this.$toast("验证错误，请重试");
                    break;
                }
            }

            return resData;
        },
        async handlegudgeonMark() {
            const sendData = {memberId: this.regMemberId || null, memberTaskType: 8};
            const result = await Api.gudgeonMark(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        },
        onRegisterFinished() {
            // 核对成功
            this.$toast("注册成功");

            // h5注册标记
            this.handlegudgeonMark();

            this.closeModal();

            this.clearCache();

            // 设置注册态
            localStorage.setItem("flagFilled", "1");

            this.jump("info");
        },
        clearCache() {
            // 清空注册信息
            localStorage.setItem("localFormInfo", "");
            localStorage.setItem("localRegisterInfo", "");
            localStorage.setItem("defaultBirthday", "");
            localStorage.setItem("defaultWorkCity", "");
            localStorage.setItem("defaultEducation", "");
            localStorage.setItem("defaultSalary", "");
            localStorage.setItem("defaultMarriage", "");
            // 清空协议勾选状态
            localStorage.removeItem("protocolStatus");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.collection-form-wrapper {
  padding-bottom: 70px;
}

.content__progress {
  margin: 0 auto 0;
  width: 694px;
  height: 261px;
  background: #fd72cf;
  // border-image: linear-gradient(-15deg, #54A6FF, #980AEC)10 stretch stretch;
  border-radius: 32px 32px 0px 0px;
  border: 2px solid #000;
}

.progress__info {
  padding-top: 39px;
  text-align: center;
  // padding: 39px 0 0 35px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
}

.progress__bar {
  position: relative;
  margin: 35px auto 0;
  width: 620px;
  height: 50px;
}

.progress__bar__svga {
  position: absolute;
  top: 0px;
  left: 0px;
  height: 74px;
  width: 645px;
  overflow: hidden;
}

.progress__bar__content {
  padding-left: 20px;
  padding-right: 20px;
  width: 0;
  height: 50px;
  border-radius: 25px;
  background: #fee75f;
  // background: linear-gradient(180deg, #FF6CDA, #F9BDF9);
  // text-shadow: 0px 0px 19px #ff14c4;
  font-size: 26px;
  font-weight: 700;
  color: #000;
  line-height: 52px;
  text-align: right;
  white-space: nowrap;
  transition: width 5s linear;
}

.content__collection {
  margin: -64px auto 0;
  padding-bottom: 78px;
  width: 694px;
  // height: 911px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
  border-radius: 32px;
  overflow: hidden;
  border: 2px solid #000;
}

.collection__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 36px;
  width: 686px;
  height: 100px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
  line-height: 100px;
}

.collection__item:nth-child(1) {
  margin-top: 46px;
}

.collection__item__label {
  width: 240px;
}

.collection__item__select {
  position: relative;
  padding-right: 36px;
  font-size: 32px;
  font-weight: 400;
  line-height: 40px;
}

.color--4633EF {
  color: #fc77a8;
}

.collection__item__select::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 12px;
  height: 20px;
  @include set-img("../../assets/imgs/right-arrow.png");
}

.collection__item__select--gender {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 304px;
  height: 100px;
}

.gender-item {
  @include set-flex(center, center);
  width: 138px;
  height: 60px;
  border: 2px solid #26273c;
  border-radius: 30px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
  line-height: 60px;
  text-align: center;
}

.gender-item--selected {
  border: none;
  background: #020202;
  color: #ffffff;
}

.collection__phone {
  position: relative;
  // margin: 0 auto;
  padding: 0 36px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
}

.collection__phone__label {
  height: 100px;
  line-height: 100px;
}

.collection__phone__input {
  // 兼容处理safari光标漂移，input外多套一层+padding
  margin: 0 auto;
  padding-left: 50px;
  padding-top: 24px;
  width: 621px;
  height: 88px;
  background-color: rgba($color: #26273c, $alpha: 0.05);
  border-radius: 44px;
  input {
    height: 40px;
    background-color: transparent;
    font-size: 32px;
    color: #26273c;
    line-height: 40px;
    text-align: left;
    width: 500px;
  }
}

.collection__phone__clear {
  position: absolute;
  top: 130px;
  right: 85px;
  width: 32px;
  height: 32px;
  @include set-img("../../assets/imgs/icon-clear.png");
}

.collection__code {
  position: relative;
  margin: 32px auto 0;
  padding: 0 36px;
  height: 88px;
}

.collection__code__input {
  margin: 0 auto;
  padding-left: 50px;
  padding-top: 24px;
  width: 621px;
  height: 88px;
  background-color: rgba($color: #26273c, $alpha: 0.05);
  border-radius: 44px;
  text-align: left;
  input {
    display: block;
    height: 40px;
    background-color: transparent;
    font-size: 32px;
    color: #26273c;
    line-height: 40px;
    text-align: left;
  }
}

.collection__code__img {
  display: block;
  position: absolute;
  top: 16px;
  right: 150px;
  width: 147px;
  height: 56px;
  border-radius: 31px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collection__code__refresh {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 86px;
  width: 32px;
  height: 32px;
  @include set-img("../../assets/imgs/icon-refresh.png");
}

.content__submit {
  display: block;
  margin: 41px auto 0;
  width: 686px;
  height: 110px;
  // background: linear-gradient(0deg, #FFC334, #FF8F02);
  border-radius: 55px;
  font-size: 30px;
  font-weight: 700;
  color: #ffffff;
  line-height: 110px;
  text-align: center;
  box-shadow: inset 0 0 16px 2px #ffffff;
  background: linear-gradient(0deg, #979797, #888888);
  border: 2px solid #000;
}

.content__submit--unfinished {
}

.bell__submit {
  margin: 41px auto 0;
  width: 493px;
  height: 113px;
  overflow: hidden;
  @include set-img("../../assets/imgs/new/button.png");
  /*.bell__submit__icon {
    margin: 40px auto 0;
    width: 119px;
    height: 28px;
    // @include set-img("../../assets/imgs/form_bell_disabled.png");
  }*/
}

.bell__submit__finished {
  @include set-img("../../assets/imgs/new/button.png");
    /*.bell__submit__icon {
      margin: 40px auto 0;
      width: 119px;
      height: 28px;
      @include set-img("../../assets/imgs/form_bell.png");

    animation: shake 0.8s linear infinite;
    transform-origin: top right;

    @keyframes shake {
      25% {
        transform: scale(1.2);
      }

      75% {
        transform: scale(1);
      }
    }
  }*/
}

.content__protocal {
  margin: 49px auto 0;
  // width: 500px;
  font-size: 24px;
  color: #000;
  line-height: 38px;
  text-align: center;
  span {
    color: #fa329f;
  }
}

.content__protocal__checked {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  @include set-img("../../assets/imgs/checked.png");
}

.content__protocal__uncheck {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  @include set-img("../../assets/imgs/uncheck.png");
}
</style>
