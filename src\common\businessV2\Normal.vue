<template>
    <div class="normal">
        <common-btn
            v-for="(option, index) in options"
            :btnText="option.text"
            :btnStyle="[
                selectIndex === option.text
                    ? btnStyleObj.active
                    : btnStyleObj.normal
            ]"
            :key="index"
            @goNext="goNext(option)"
        />
    </div>
</template>
<script>
import CommonBtn from "./components/CommonBtn.vue";
import { session as Session } from "@/common/utils/storage";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { reportKibana } from "@/common/utils/report";
export default {
    name: "Normal",
    props: {
        type: {
            type: String
        },
        options: {
            type: Array,
            default: () => []
        },
        btnStyleObj: {
            type: Object,
            default: () => {}
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    components: {
        CommonBtn
    },
    data() {
        return {
            selectIndex: Session.getItem(this.type) || "",
            btnStyle: ""
        };
    },
    methods: {
        goNext(option) {
            const reportDesc = {
                Car: {
                    point: 6,
                    desc: "是否在意车房页-选项点击"
                },
                salary: {
                    point: 7,
                    desc: "收入页-具体收入点击"
                },
                Difflove: {
                    point: 8,
                    desc: "能否接受异地页-选项点击"
                },
                Inplace: {
                    point: 10,
                    desc: "两人在一起最重要的页-选项点击"
                },
                education: {
                    point: 11,
                    desc: "学历页-具体学历点击"
                },
                Age: {
                    point: 13,
                    desc: "择偶年龄页-点击具体选项"
                },
                Wedding: {
                    point: 13,
                    desc: "选择对象页-点击具体选项"
                },
                marriage: {
                    point: 13,
                    desc: "婚况页-点击具体选项"
                }
            };
            reportKibana(
                this.pageType,
                reportDesc[this.type].point,
                reportDesc[this.type].desc
            );
            if (["salary", "education", "marriage"].includes(this.type)) {
                const params = {
                    key: this.type,
                    value: option.key
                };
                setLocalRegisterForm(params, this.pageType);
            }
            Session.setItem(this.type, option.text);

            this.selectIndex = option.text;
            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
