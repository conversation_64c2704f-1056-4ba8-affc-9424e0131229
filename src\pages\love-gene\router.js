import VueRouter from "vue-router";
import { storage as Storage } from "@/common/utils/storage";
import z_ from "@/common/zdash";

const Home = () => import('./views/home');
const Questions = () => import('./views/questions');
const Form = () => import('./views/form');
const DownloadGuide = () => import('./views/download-guide');

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: '/',
            component: Home
        },
        {
            path: '/home',
            component: Home
        },
        {
            name: 'questions',
            path: '/questions/:id',
            component: Questions
        },
        {
            name: 'form',
            path: '/form',
            component: Form
        }, {
            name: 'downloadGuide',
            path: '/download-guide',
            component: DownloadGuide
        }
    ],
});

router.beforeEach((to, from, next) => {
    if (to.name === 'form' || (to.name === 'questions' && to.params.id === '3')) {
        const cachedRegisterForm = Storage.getItem('cachedRegisterForm');
        const gender = z_.get(cachedRegisterForm, 'gender');

        if (gender === -1 || gender === '' || z_.isNil(gender)) {
            Storage.setItem('callbackTips', '请先完善性别信息');

            next({
                path: '/questions/0',
            });

            return;
        }
    }

    next();
});

// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0

    // firefox
    document.documentElement.scrollTop = 0

    // safari
    window.pageYOffset = 0
});

export default router;
