<template>
    <div class="form-wrapper">
        <div class="header">
            <div
                class="back"
                @click="goBack"
            ></div>
        </div>
        <div class="title">
            真棒！<br />
            你的恋爱成功率
            <span class="highlight">很高</span>
            <span
                class="icon-question"
                @click="handleModalShow"
            ></span>
        </div>
        <common-form
            :page-type="planName"
            class="form"
            :filter-config="['phone']"
            :style-config="{
                valueColor: '#26273C'
            }"
            :is-need-sub-title="false"
            :show-label="false"
        />
        <common-button
            class="submit-btn"
            :config="{ width: 654, height: 110, fontSize: 32, des: '确定' }"
            :class="{'submit-disabled': !finished}"
            @click="handleSubmit"
        />
        <!-- <common-submit
            :page-type="planName"
            class="submit"
            :is-need-protocol="false"
            :style-config="{
                modalConfig: {
                    confirmButtonColor: '#FFFFFF',
                    confirmButtonBgColor: 'linear-gradient(154deg, #7566EB 0%, #4A3BC0 100%)',
                    cancleButtonColor: '#6C6D75',
                }
            }"
            :handle-after-regisiter="handleJump"
            :handle-login="handleJump"
        >
            <common-button
                :config="{ width: 654, height: 110, fontSize: 32, des: '确定' }"
                :class="{'submit-disabled': !finished}"
            />
        </common-submit> -->

        <div class="protocol">
            已阅读并同意<a
                href="//i.zhenai.com/m/portal/register/prDeal.html"
            >珍爱网服务协议</a>
            和<a
                href="//i.zhenai.com/m/portal/register/serverDeal.html"
            >个人信息保护政策</a>
        </div>

        <rate-modal
            v-model="modalVisible"
        />
    </div>
</template>

<script>
import { Toast } from "vant";
import Api from '@/common/server/base';
import z_ from '@/common/zdash';
// import CommonSubmit from "@/common/business/CommonSubmit";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import { reportError } from "@/common/utils/report";
import CommonForm from "../components/CommonForm";
import CommonButton from "../components/CommonButton";
import RateModal from "../components/RateModal";

export default {
    name: "Form",
    components: {
        CommonForm,
        // CommonSubmit,
        CommonButton,
        RateModal
    },
    data() {
        const planName = Session.getItem('planName');
        return {
            registerForm: Storage.getItem(
                `cachedRegisterForm-${planName}`
            ) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            planName,
            modalVisible: false,
            lockSubmit: false,
        };
    },
    computed: {
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            return this.allFillIn && this.validatedPhone;
        },
    },

    mounted() {
        this.handleWatchForm();
        this.$report(10, "手机验证页访问");
    },
    methods: {
        handleWatchForm() {
            window.addEventListener("setItemEvent", e => {
                if (e.key === `cachedRegisterForm-${this.planName}`) {
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        },
        goBack() {
            this.$report(10, "手机验证页-返回按钮点击");
            this.$router.back();
        },
        handleModalShow() {
            this.modalVisible = true;
        },// 处理提交基本信息
        async handleSubmit(isReport = true) {
            if (isReport) {
                this.$report(10, '手机验证页-立即验证按钮点击');
            }


            if (this.lockSubmit) {
                return;
            }

            if (!this.handleAllFillIn()) {
                Toast('请完善资料再提交');
                return;
            }

            if (!this.handleValidatedPhone()) {
                Toast('请输入正确的手机号');
                return;
            }

            if (this.isNeedProtocol && !this.isCheckProtocol) {
                this.modalVisible.protocolModal = true;
                return;
            }


            if (isReport) {
                this.$report(10, '手机验证页-提交按钮点击（有效点击）');
            }

            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.planName}`
            ) || this.registerForm;

            const sendData = {
                ...registerForm
            };
            delete sendData.phone;

            this.lockSubmit = true;

            try {
                const baseInfoPostResult = await Api.submitWapRegBaseInfo(sendData);

                if (baseInfoPostResult.isError) {
                    this.$toast(baseInfoPostResult.errorMessage);
                    // 老注册页qms上报逻辑
                    reportError('新注册页(大表单H5),注册信息提交失败 |' + baseInfoPostResult.errorMessage + ' | ' + JSON.stringify(registerForm));
                    return;
                }

                this.$router.push({
                    path: "/code"
                });
            } finally {
                this.lockSubmit = false;
            }
        },
        handleAllFillIn() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.planName}`
            ) || this.registerForm;
            return z_.every(registerForm , value => {
                return !z_.isNil(value) && value !== "";
            });
        },
        handleValidatedPhone() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.planName}`
            ) || this.registerForm;
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.form-wrapper {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    background: #F3F5F6;
        
    &::before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0;
        right: 0;
        width: 360px;
        height: 394px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684725437883_497577_t.png');
    }

    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        bottom: 0;
        width: 750px;
        height: 1400px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684724562598_300963_t.png');
    }

    .header {
        padding: 20px 0 0;
        .back {
            width: 72px;
            height: 72px;
            margin-left: 48px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684492059170_707059_t.png');
            background-position: center center;
        }
    }

    .title {
        margin: 72px 48px;
        font-weight: 400;
        line-height: 80px;
        color: #26273cff;
        font-size: 60px;
        .highlight {
            color: #FF7598;
        }
        .icon-question {
            display: inline-block;
            width: 32px;
            height: 32px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20190515/1557911050565_70279_t.png');
        }
    }

    .form {
        margin-top: 144px;
        padding: 0 48px;
        /deep/ .form-item--phone__value__wrapper {
            .form-item--phone__value {
                background-color: #fff;
                width: 654px;
                height: 112px;
                border-radius: 60px;
                padding: 0 0 0 48px;
                .form-item--phone__value__input {
                    font-size: 42px;
                    line-height: 112px;
                    height: 112px;
                }
            }
            .form-item--phone__clear {
                top: 40px;
                right: 48px;
            }
        }
    }

    .submit-btn {
        &.button-wrap {
            margin: 32px auto 0;
            border: 0;
            border: 0;
            background: #191C32;
        }
        &.submit-disabled {
            background: #AEB1B6;
        }
    }

    .protocol {
        margin-top: 48px;
        font-size: 24px;
        color: #9395A4;
        line-height: 38px;
        text-align: center;
        a {
            color: #767DFF;
        }
    }
}
</style>
