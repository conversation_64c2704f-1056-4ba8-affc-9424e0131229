<template>
    <div class="phone">
        <span>+86</span>
        <input
            type="tel"
            placeholder="输入常用手机号，查看与你匹配的异性"
            maxlength="13"
            :value="value"
            @input="handleInputPhone"
        />
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { formatPhone } from "@/common/utils/tools.js";
import { storage as Storage} from "@/common/utils/storage";

export default {
    name: 'Phone',
    inject: ["cmsConfig"],
    data() {
        return {
            value: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) ? Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).phone :'',
            isReport: false
        };
    },
    mounted() {
        this.$report(11, '手机验证页访问');
    },
    methods: {
        handleInputPhone(e) {
            if (e.target.value.length === 13 && !this.isReport) {
                this.$report(11, '手机验证页-输入11位手机号');
                this.isReport = true;
            }

            e.target.value = e.target.value.replace(/[^(\d|\s)]/g, "");
            const params = {
                key: "phone",
                value: formatPhone(e),
                isMark: false
            };
            setLocalRegisterForm(params, this.cmsConfig.planName);
        }
    }
};

</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.phone {
    padding-left: 24px;
    width: 612px;
    height: 110px;
    background-color: #F3F5F6;
    border-radius: 24px;
    @include flex-center(row, null, center);
    >span {
        font-weight: 500;
        font-size: 32px;
        color: #26273C;
        padding-right: 24px;
    }
    >input {
        width: 100%;
        background-color: transparent;
        font-weight: 400;
        font-size: 28px;
        color: #26273C;
        caret-color: #4138FF;
    }
    ::-webkit-input-placeholder {
        color:#AEB1B6;
    }

    :-moz-placeholder { 
        color:#AEB1B6;
    }

    ::-moz-placeholder {
        color: #AEB1B6; 
    }

    :-ms-input-placeholder {
        color: #AEB1B6;
    }

}
</style>