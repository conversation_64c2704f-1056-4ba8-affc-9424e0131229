<template>
    <div class="about">
        <div class="about-back">
            <span @click="goBack"></span>
        </div>
        <div class="about-title">
            {{ currentPage.label }}
        </div>
        <section class="about-item">
            <keep-alive>
                <component
                    :is="currentPage.uiType"
                    :btnStyleObj="btnStyleObj"
                    :options="currentPage.options"
                    :type="currentPage.type"
                    :key="currentPage.label"
                    :pageType="cmsConfig.planName"
                    :isNewUI="cmsConfig.isNewUI"
                />
            </keep-alive>
        </section>
    </div>
</template>
<script>
import { education, salary, birthday, marriage } from "@/common/config/register-dictionary";
import Normal from "@/common/businessV2/Normal.vue";
import Figure from "@/pages/lover/components/Figure.vue";
import Voice from "@/pages/lover/components/Voice.vue";
import WorkCity from "@/common/businessV2/WorkCity.vue";
import Birthday from "@/common/businessV2/Birthday.vue";
import { session as Session, storage } from "@/common/utils/storage";

const girlFigure = [
    {
        text: "冰肌蝶背",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480818_562624_t.jpg"
    },
    {
        text: "性感尤物",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480808_584178_t.jpg"
    },
    {
        text: "娇小可人",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480643_920626_t.jpg"
    },
    {
        text: "杨柳细腰",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480707_241018_t.jpg"
    }
];

const boyFigure = [
    {
        text: "行走荷尔蒙",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662704972123_595719_t.jpg"
    },
    {
        text: "肉肉的暖男",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705027737_808052_t.jpg"
    },
    {
        text: "男友力爆棚",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705090421_743161_t.jpg"
    },
    {
        text: "清冷少年感",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705128205_137194_t.jpg"
    }
];

const girlOotd = [
    {
        text: "开朗率真",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480678_362534_t.jpg"
    },
    {
        text: "纯情少女",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480473_876422_t.jpg"
    },
    {
        text: "惹火辣妹",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480720_972563_t.jpg"
    },
    {
        text: "窈窕淑女",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480659_730654_t.jpg"
    }
];
const boyOotd = [
    {
        text: "潮酷型男",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705425253_808303_t.jpg"
    },
    {
        text: "休闲自在",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480458_965287_t.jpg"
    },
    {
        text: "西装霸总",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480791_677068_t.jpg"
    },
    {
        text: "活力爽朗",
        url:
            "https://photo.zastatic.com/images/common-cms/it/20220909/1662705480525_852130_t.jpg"
    }
];
const desMap = {
    0: {
        accessPoint: 3,
        accessDes: "定制声音页-"
    },
    1: {
        accessPoint: 4,
        accessDes: "定制身材页-"
    },
    2: {
        accessPoint: 5,
        accessDes: "定制穿搭页-"
    },
    3: {
        accessPoint: 6,
        accessDes: "是否在意车房页-"
    },
    4: {
        accessPoint: 7,
        accessDes: "收入页-"
    },
    5: {
        accessPoint: 8,
        accessDes: "能否接受异地页-"
    },
    6: {
        accessPoint: 9,
        accessDes: "工作地页-"
    },
    7: {
        accessPoint: 10,
        accessDes: "两人在一起最重要的页-"
    },
    8: {
        accessPoint: 11,
        accessDes: "学历页-"
    },
    9: {
        accessPoint: 13,
        accessDes: "择偶年龄页-"
    },
    10: {
        accessPoint: 13,
        accessDes: "出生年份页-"
    },
    11: {
        accessPoint: 13,
        accessDes: "选择对象页-"
    },
    12: {
        accessPoint: 13,
        accessDes: "婚况页-"
    }
};

const mediaGroupType = Session.getItem("mediaGroupType");
if (mediaGroupType == "10") {
    boyFigure[0].url =
        "https://photo.zastatic.com/images/common-cms/it/20221013/1665627401382_829739_t.jpg";
    girlFigure[2].url =
        "https://photo.zastatic.com/images/common-cms/it/20221013/1665627403126_103624_t.jpg";
    girlOotd[2].url =
        "https://photo.zastatic.com/images/common-cms/it/20221013/1665627399650_865969_t.jpg";
}
// if (mediaGroupType == "11") {
//     boyFigure[0].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751121395_9304_t.jpg";
//     girlOotd[0].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751121710_370541_t.jpg";
//     girlOotd[1].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751121553_66723_t.jpg";
//     girlOotd[2].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751121871_835606_t.jpg";
//     girlFigure[2].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751121913_255734_t.jpg";
//     girlFigure[1].url =
//         "https://photo.zastatic.com/images/common-cms/it/20221014/1665751741100_868165_t.jpg";
// }

export default {
    name: "About",
    inject: ["cmsConfig"],
    data() {
        return {
            currentPage: {
                type: "",
                uiType: "",
                label: ""
            },
            btnStyleObj: {
                normal: {
                    color: "#4a3bc0",
                    border: "2px solid #4a3bc0"
                },
                active: {
                    color: "#fff",
                    background:
                        "linear-gradient(154deg, #7566eb 0%, #4a3bc0 100%)",
                    border: "none"
                }
            },
            ModelImages: []
        };
    },
    components: {
        Normal,
        Figure,
        WorkCity,
        Voice,
        Birthday,
    },
    created() {
        const gender = Session.getItem("gender");
        if (gender === 1) {
            this.ModelImages[0] = [...boyFigure];
            this.ModelImages[1] = [...boyOotd];
        } else {
            this.ModelImages[0] = [...girlFigure];
            this.ModelImages[1] = [...girlOotd];
        }

        if (this.$route.params.id < this.conlists.length) {
            this.init();
        } else {
            if (Number(storage.getItem("ext30")) === 78) {
                this.$router.replace({
                    path: "/lockForm"
                });
            } else {
                this.$router.replace({
                    path: "/lock"
                });
            }
        }
    },
    computed: {
        conlists() {
            const list = [
                {
                    type: "Voice",
                    uiType: "Voice",
                    label: "哪种声音更让你心动?"
                },
                {
                    type: "Figure",
                    uiType: "Figure",
                    label: "哪种身材更让你心动?",
                    options: [
                        {
                            key: 0,
                            ...this.ModelImages[0][0]
                        },
                        {
                            key: 1,
                            ...this.ModelImages[0][1]
                        },
                        {
                            key: 2,
                            ...this.ModelImages[0][2]
                        },
                        {
                            key: 3,
                            ...this.ModelImages[0][3]
                        }
                    ]
                },
                {
                    type: "Ootd",
                    uiType: "Figure",
                    label: "哪种穿搭更让你心动?",
                    options: [
                        {
                            key: 0,
                            ...this.ModelImages[1][0]
                        },
                        {
                            key: 1,
                            ...this.ModelImages[1][1]
                        },
                        {
                            key: 2,
                            ...this.ModelImages[1][2]
                        },
                        {
                            key: 3,
                            ...this.ModelImages[1][3]
                        }
                    ]
                },
                {
                    type: "Car",
                    uiType: "Normal",
                    label: "是否在意对方有无车房?",
                    options: [
                        {
                            text: "不在意",
                            key: 0
                        },
                        {
                            text: "在意",
                            key: 1
                        }
                    ]
                },
                {
                    type: "salary",
                    uiType: "Normal",
                    label: "你的月收入是?",
                    options: salary
                },
                {
                    type: "Difflove",
                    uiType: "Normal",
                    label: "能否接受异地恋?",
                    options: [
                        {
                            text: "能",
                            key: 0
                        },
                        {
                            text: "不能",
                            key: 1
                        }
                    ]
                },
                {
                    type: "workCity",
                    uiType: "WorkCity",
                    label: "你在哪个城市?"
                },
                {
                    type: "Inplace",
                    uiType: "Normal",
                    label: "你认为两个人在一起最重要的是?",
                    options: [
                        {
                            text: "相似的背景",
                            key: 0
                        },
                        {
                            text: "共同的话题",
                            key: 1
                        },
                        {
                            text: "相互的支持",
                            key: 2
                        }
                    ]
                },
                {
                    type: "education",
                    uiType: "Normal",
                    label: "你的学历是?",
                    options: education
                },
                {
                    type: "Age",
                    uiType: "Normal",
                    label: "你对双方年龄有要求吗?",
                    options: [
                        {
                            text: "希望双方年龄相近",
                            key: 0
                        },
                        {
                            text: "希望对方比我大",
                            key: 1
                        },
                        {
                            text: "希望对方比我小",
                            key: 2
                        },
                        {
                            text: "没要求，都能接受",
                            key: 3
                        }
                    ]
                },
                {
                    type: "birthday",
                    uiType: "Birthday",
                    label: "你的出生年份是?",
                    options: birthday
                },
                {
                    type: "Wedding",
                    uiType: "Normal",
                    label: "你希望找恋人还是结婚对象?",
                    options: [
                        {
                            text: "恋人",
                            key: 0
                        },
                        {
                            text: "结婚对象",
                            key: 1
                        },
                    ]
                },
                {
                    type: "marriage",
                    uiType: "Normal",
                    label: "你的婚况是?",
                    options: marriage
                }
            ];
            if (Number(storage.getItem("ext30")) !== 78) {
                return list.slice(0, -4);
            }
            return list;
        }
    },
    methods: {
        init() {
            this.$report(
                desMap[this.$route.params.id].accessPoint,
                desMap[this.$route.params.id].accessDes + "访问"
            );
            this.currentPage = this.conlists[this.$route.params.id];
        },
        goBack() {
            this.$report(
                desMap[this.$route.params.id].accessPoint,
                desMap[this.$route.params.id].accessDes + "返回按钮点击"
            );
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.about {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(180deg, #f6f6fb 40%, #d4d1ff);
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        bottom: 0;
        width: 750px;
        height: 550px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220905/1662349717190_46242_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    &::before {
        content: "";
        position: absolute;
        top: 100px;
        right: 24px;
        width: 56px;
        height: 150px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220905/1662349955701_385218_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .about-title {
        padding: 18px 66px 64px;
        color: #4a3bc0;
        font-size: 56px;
        font-weight: 500;
        text-align: center;
        line-height: 84px;
    }
    .about-back {
        position: relative;
        z-index: 9;
        padding-top: 22px;
        display: flex;
        width: 100%;
        > span {
            width: 64px;
            height: 64px;
            margin-left: 14px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220908/1662618931393_661279_t.png)
                no-repeat;
            background-size: 36px 36px;
            background-position: center center;
        }
    }
    &-item {
        position: relative;
        z-index: 1;
    }
}
</style>
