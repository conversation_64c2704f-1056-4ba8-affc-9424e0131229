<template>
    <div
        class="qiang-root"
        :style="{background: cmsConfig.reportViewType === 1 ? styleConfig.globalBg : `url(${styleConfig.globalBg})`}"
    >
        <router-view v-if="canRender" />
    </div>
</template>

<script>
import { _getRecommendMembers } from "./api";
import { createRoot } from '@/common/framework';
import Api from '@/common/server/base';
import z_ from "@/common/zdash";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';

export default createRoot({
    name: "App",
    data() {
        return {
            cmsConfig: {
                reportViewType: 1,
                globalGroundColor: '#000000',
                globalButtonColor: '#FFF100',
                globalButtonTextColor: '#000000',
                homeButtonText: '获取联系方式',
                formButtonText: '立即添加好友',
                downloadButtonText: '立即聊天',
                userInfoStr: '2000109863',
                downloadStatus: 0,
                agreementStatus: 0,
            },
            styleConfig: {
                homeTopImg: '',
                globalBg: '',
                interviewTitleImg: '',
                indexInformationCard: {
                    bgColor: '',
                    borderColor: '',
                    tagIcon: '',
                    tagColor: '',
                    tagCopyColor: '',
                    titleColor: '',
                    fontColor: ''
                },
                leftIcon: '',
                rightIcon: '',
                mainColor: '',
                buttonFontColor: '',
                recommendTitleImg: '',
                formImg: '',
                formDetailImg: '',
                resultTopImg: '',
                resultMiddleImg: '',
                resultInformationCard: {
                    bgColor: '',
                    borderColor: '',
                    titleColor: '',
                    fontColor: '',
                    onlineIcon: ''
                }
            },
            memberList: {
                promotionsMemberInfoVo: {},
                recomList: []
            },
            canRender: false,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig,
            memberList: this.memberList,
            styleConfig: this.styleConfig
        };
    },
    async mounted() {
        await this.handleInitCmsConfig();
        this.getRecommendMembers();
        judgeIfInToutiaoIos();
    },
    methods: {
        async handleInitCmsConfig() {
            const id = Z.getParam('materialId');

            // 没有id的情况下，用默认配置
            if (!id) {
                window._zconfig.resourceKey = '锵锵锵H5';
                this.canRender = true;
                return;
            }

            let result = {};
            try {
                result = await Api.getMaterial({id});
            }catch(e) {
                console.log(e);
            }

            if (result.isError) {
                this.$toast(result.errorMessage);
                this.canRender = true;
                return;
            }

            const data = z_.get(result, 'data.materialVo');
            this.cmsConfig = Object.assign(this.cmsConfig, z_.pick(data, Object.keys(this.cmsConfig)));
            const isQiang = this.cmsConfig.reportViewType === 1;
            window._zconfig.resourceKey = isQiang ? '锵锵锵H5' : '咚咚咚H5';

            this.styleConfig = Object.assign(this.styleConfig, {
                homeTopImg: isQiang ? 'https://photo.zastatic.com/images/cms/banner/20220718/2132534187527206.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658284759061_455127_t.png',
                globalBg: isQiang ? '#000' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658285320033_836777_t.png',
                interviewTitleImg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658128333381_879626_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658283008341_567664_t.png',
                indexInformationCard: {
                    bgColor: isQiang ? '#000' : '#fff',
                    borderColor: isQiang ? '#FFF100' : '#000000',
                    tagBgColor: isQiang ? 'linear-gradient(90deg, #FFFFFF 0%, #FFF900 100%)' : '#000000',
                    tagColor: isQiang ? '#1A202C' : '#FFFFFF',
                    tagCopyColor: isQiang ? '#FFF100' : '#000000',
                    titleColor: isQiang ? '#FFFFFF' : '#000000',
                    fontColor: isQiang ? '#FFFFFF' : '#000000',
                },
                leftIcon: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658109692341_147480_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658286184808_606033_t.png',
                rightIcon: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658127572963_985902_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658288803160_836784_t.png',
                mainColor: isQiang ? '#FFF100' : '#FB3CE3',
                buttonFontColor: isQiang ? '#000000' : '#FFFFFF',
                recommendTitleImg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658130702946_444250_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658284879254_336336_t.png',
                formImg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220716/1657939600031_211159_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658289396269_432012_t.png',
                formDetailBg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220720/1658308422478_484143_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658289807779_674125_t.png',
                resultTopImg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220817/1660720234692_112999_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220817/1660720307203_828645_t.png',
                resultMiddleImg: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220719/1658203440583_751578_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658301333551_708722_t.png',
                resultInformationCard: {
                    bgColor: isQiang ? '#000' : '#fff',
                    borderColor: isQiang ? '#FFF100' : '#000000',
                    titleColor: isQiang ? '#FFFFFF' : '#000000',
                    fontColor: isQiang ? '#FFFFFF' : '#000000',
                    onlineIcon: isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220720/1658303687410_211302_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658303403954_441420_t.png'
                }
            });

            this.canRender = true;
        },

        // reportViewType为1，代表是给男生看，所有sex传1，返回女生
        // reportViewType为2，代表是给女生看，所有sex传0，返回男生
        async getRecommendMembers() {
            let res = {};
            try { 
                res = await _getRecommendMembers({
                    page: 1,
                    pageSize: 20,
                    sex: this.cmsConfig.reportViewType === 1 ? 1 : 0,
                    userInfoStr: this.cmsConfig.userInfoStr
                });
            } catch(e){
                console.log(e);
            }
            
            if (res.isError) {
                this.$toast(res.errorMessage);
                return;
            }

            this.memberList.promotionsMemberInfoVo = res.data.promotionsMemberInfoVo;
            const modelList = res.data.list;
            this.memberList.recomList = modelList.slice(0, 10);
            setTimeout(() => {
                this.memberList.recomList.push(...modelList.slice(10));
            }, 500);
        },
    }
});
</script>

<style lang="scss">
.qiang-root {
    min-height: 100vh;
    font-weight: 400;
}

</style>
