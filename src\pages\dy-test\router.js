import VueRouter from "vue-router";
const Form = () => import("./views/Form");
import Index from "./views/Index.vue";
const About = () => import("./views/About.vue");
const Animate = () => import("./views/Animate.vue");
const Code = () => import("./views/Code.vue");

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            name: "index",
            component: Index
        },
        {
            path: "/form",
            name: "Form",
            component: Form
        },
        {
            path: "/about/:id",
            name: 'about',
            component: About
        },
        {
            path: '/animate',
            name: 'Animate',
            component: Animate
        },
        {
            path: '/code',
            name: 'Code',
            component: Code
        }
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
