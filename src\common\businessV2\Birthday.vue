<template>
    <div class="workcity">
        <div class="workcity-info">
            <van-picker
                class="workcity-info-picker"
                ref="refPicker"
                item-height="1.25rem"
                :visible-item-count="5"
                :show-toolbar="false"
                :columns="dict"
            />
        </div>
        <common-btn
            btnText="下一步"
            :btnStyle="btnStyleObj.active"
            @goNext="goNext"
        />
    </div>
</template>
<script>
import CommonBtn from "./components/CommonBtn.vue";
import { Picker } from "vant";
import { storage as Storage } from "@/common/utils/storage";
import "vant/lib/picker/style";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { reportKibana } from "@/common/utils/report";
import { birthday } from "@/common/config/register-dictionary";
export default {
    name: "WorkCity",
    props: {
        type: {
            type: String
        },
        btnStyleObj: {
            type: Object,
            default: () => {}
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            dict: birthday
        };
    },
    components: {
        vanPicker: Picker,
        CommonBtn
    },
    mounted() {
        // 初始化定位
        this.handleInitArea();
    },
    methods: {
        goNext() {
            const picker = this.$refs.refPicker;

            const year = picker.getColumnValue(0).key;
            //实际传给后台的数据为 年/1/1 对应的毫秒值
            const birthday = new Date(year + "/" + 1 + "/" + 1).getTime();
            const params = {
                key: "birthday",
                value: birthday
            };
            setLocalRegisterForm(params, this.pageType);
            reportKibana(this.pageType, 13, "出生年份页-点击确认按钮");
            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        },

        handleInitArea() {
            let birthday =
                (Storage.getItem(`cachedRegisterForm-${this.pageType}`) &&
                    Storage.getItem(`cachedRegisterForm-${this.pageType}`)
                        .birthday);
            // 这里取到的出生年份是毫秒数
            if (birthday) {
                birthday = new Date(birthday).getFullYear();
            } else {
                birthday = 1990;
            }
            if (birthday) {
                this.$refs.refPicker.setValues([birthday]);
            }
        },
    }
};
</script>

<style lang="scss">
.workcity {
    &-info {
        margin-top: 58px;
        margin-bottom: 40px;
        &-picker {
            background: transparent;
            .van-picker__mask {
                background: linear-gradient(180deg, transparent),
                    linear-gradient(0deg, transparent);
            }
            .van-picker-column__item {
                opacity: 0.7;
                color: #222222;
                font-size: 30px;
                &--selected {
                    opacity: 1;
                    color: #0f1122;
                    font-size: 36px;
                }
            }
        }
    }
    .btn {
        margin: 52px auto 0;
        width: 654px;
        height: 110px;
        color: #ffffff;
        font-size: 32px;
        text-align: center;
        line-height: 110px;
        background: #5368f0;
        border-radius: 80px;
    }
}
</style>
