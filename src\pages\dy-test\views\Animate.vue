<template>
    <div class="animate">
        <div class="animate-header">
            <div
                class="back"
                @click="goBack"
            ></div>
        </div>

        <div
            class="svga"
            id="svga"
        ></div>

        <div
            class="percent"
            v-if="percentAnimate"
        >
            {{ percent }}%
        </div>
    </div>
</template>

<script>
import { storage as Storage, session as Session } from "@/common/utils/storage";

export default {
    name: "Animate",
    data() {
        const planName = Session.getItem('planName');
        const formInfo = Storage.getItem(`cachedRegisterForm-${planName}`);
        return {
            isShowResult: false,
            gender: formInfo ? formInfo.gender : 0,
            percent: 0,
            percentAnimate: false,
        };
    },
    mounted() {
        this.$report(9, "匹配动效页访问");
        this.setSVGA();
    },
    methods: {
        goBack() {
            this.$report(9, "匹配动效页-返回按钮点击");
            this.$router.back();
        },
        setSVGA() {
            let player = new window.SVGA.Player(`#svga`),
                parser = new window.SVGA.Parser(`#svga`);
            const maleSvga = require('../assets/animate-male.svga');
            const femaleSvga = require('../assets/animate-female.svga');
            const targetSvga = this.gender === 0 ? femaleSvga : maleSvga;

            parser.load(targetSvga, (videoItem)=>{
                player.loops = 1;
                player.setVideoItem(videoItem);
                player.startAnimation();

                let timer = null;
                const self = this;
                setTimeout(() => {
                    self.percentAnimate = true;
                    timer = setInterval(() => {
                        if (self.percent >= 100) {
                            self.percentAnimate = false;
                            clearInterval(timer);
                        } else {
                            self.percent+=2;
                        }
                    }, 40);
                }, 2000);

                setTimeout(() => {
                    this.$router.push({
                        path: "/form"
                    });
                }, 4000);
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.animate {
    position: relative;
    height: 100vh;
    min-height: 1334px;
    background: #F3F5F6;
    z-index: 1;
    &::before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0;
        right: 0;
        width: 750px;
        height: 686px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684724562516_581755_t.png');
    }
    &-header {
        padding: 20px 0 0;
        .back {
            width: 72px;
            height: 72px;
            margin-left: 48px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684492059170_707059_t.png');
            background-position: center center;
        }
    }
    .svga {
        width: 750px;
        height: 1334px;
        position: absolute;
        top: 0;
        right: 0;
        z-index: -1;
        overflow: hidden;
    }
    .percent {
        font-weight: 500;
        font-size: 36px;
        color: #767DFF;
        line-height: 38px;
        position: absolute;
        top: 872px;
        right: 96px;
    }
}
</style>
