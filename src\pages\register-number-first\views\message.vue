<template>
    <div class="finish" id="loginMain">
        <div class="panel">
            <Phone
                ref="refPhone"
                class="panel-phone"
            />
            <register-submit
                ref="refRegisterSubmit"
                :page-type="PAGE_TYPE"
                :style-config="{
                    modalConfig: {
                        confirmButtonColor: '#FFFFFF',
                        confirmButtonBgColor: '#5243FE',
                        cancleButtonColor: '#000000',
                    },
                    protocolConfig: {
                        textColor: '#222833',
                        protocolColor: '#A98CE6',
                        protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20230516/1684220210896_758809_t.png'
                    }
                }"
                :handle-after-regisiter="handleJump"
                :handle-login="handleJump"
                :handle-cancle="handleCancel"
            >
                <div
                    class="panel-submit"
                    @click="handleReport"
                >
                    确定
                </div>
            </register-submit>
        </div>
    </div>
</template>
<script>
import RegisterSubmit from '../components/RegForm/registerSubmit';
import Phone from "../components/RegForm/phone.vue";
import { PAGE_TYPE } from "../config";
import { bindEvaluate } from '../api';
import { storage,session as Session } from "@/common/utils/storage";

export default {
    name: "Message",
    components: {
        Phone,
        RegisterSubmit,
    },
    data() {
        return {
            isShow: true,
            PAGE_TYPE,
        };
    },
    created(){
        this.$report(3000, '首页访问 (监控)');
    },
    activated(){
        this.$report(36, '手机验证页访问');
        // 获取当前可视区域的高度  键盘弹起页面图片被压缩 vh被键盘占据了一部分
        // let height = document.documentElement.clientHeight;
        // window.onresize = () => { // 在页面大小发生变化时调用
        //     // 把获取到的高度赋值给根div
        //     document.getElementById('loginMain').style.height = height + 'px';
        // };
    },
    methods: {
        handleJump() {
            let form = {
                uuid: storage.getItem('uuid'),
                evaluateType: 1,
                memberId: Session.getItem('reg_memberid')
            };
            if(Session.getItem('reg_memberid')) {
                bindEvaluate(form).then(()=>{});
            }

            this.$router.push({ path: '/result'});
        },
        handleReport(e) {
            this.$report(3001, '首页点击确定(监控)');
            if(this.$refs.refPhone.inputValue.length !== 11){
                this.$toast("请输入手机号");
                e.stopPropagation();
            }
        },
        //取消
        handleCancel() {

        }
        // handleSubmit() {
        //     // 触发提交手机号逻辑
        //     this.$refs.refRegisterSubmit.handleSubmit(false);
        // }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.finish{
    // width: 100vw;
    // height: 100vh;
    // background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825084549_711274_t.png');
    // background-repeat: no-repeat;
    // margin:0px;
    // background-size:100% 100%;
    // background-attachment:fixed;
    // display: flex;
    // align-items: center;
    width: 100vw;
    /*height: 100vh;*/
    display: flex;
    align-items: center;
    justify-content: center;
    .panel{
        margin-top: 80px;
        padding: 0 30px;
        width: 99vw;
        /*height: 83vh;*/
        display: flex;
        flex-direction: column;
        align-items: center;

        &-result{
            margin-top: 96px;
            position: relative;
            display: inline-block;

            .title {
                font-weight: 400;
                font-size: 40px;
                color: #03355E;
                height: 56px;
                line-height: 56px;
                margin: 0 auto;
                position: relative;
                z-index: 2;

                &::before{
                    content: '';
                    position: absolute;
                    bottom: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 168px;
                    height: 10px;
                    background-image: linear-gradient(90deg, #99f4ffde 0%, #16abfbab 100%);
                    z-index: -1;
                }
            }

            .rate {
                font-size: 140px;
                font-weight: 700;
                letter-spacing: 0;
                text-align: center;
                background-image: linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                -webkit-background-clip: text;
                color: transparent;
                position: relative;

                .subfix {
                    font-size: 60px;
                    position: absolute;
                    right: -52px;
                    bottom: 14px;
                    background-image: linear-gradient(-200deg, #31A3E0, #4C94FF);
                    background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF);
                    -webkit-background-clip: text;
                }
            }
        }

        &-title {

            margin-top: 175px;
            font-weight: 400;
            line-height: 1.2;
            font-family: 'love';
            font-size:52px;
            text-align: left;
            color: #251A34;
            letter-spacing: 8px;
            margin-bottom: 130px;

        }

        &-phone{
            // margin-top: 256px;
            background: #EAF1FA;
            border: 2px solid #91B3D6;
            border-radius: 24px;
        }

        &-submit{
            margin: 32px auto 0;
            width: 550px;
            height: 100px;
            box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
            border-radius: 55px;
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;
            line-height: 100px;
            background: linear-gradient(180deg,#5243FE, #9A55F0);

        }
    }
    /deep/ .common-protocol {
        align-items: flex-start;

        .common-protocol__checke-box {
            flex: 0 0 auto;
            width: 22px;
            height: 22px;
        }

        .common-protocol__text {
            font-size: 24px;
            line-height: 30px;
            a {
                font-weight: 500;
            }
        }
    }
}
</style>
