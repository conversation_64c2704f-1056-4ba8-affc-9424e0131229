<template>
    <div class="info-wrapper" :style="cmsConfig.pageColor">
        <info-header></info-header>
        <info-avatar @open-modal="openModal(arguments)"></info-avatar>
        <info-content @open-modal="openModal(arguments)"></info-content>
        <info-footer @open-modal="openModal(arguments)"></info-footer>
        <info-default></info-default>
        <!-- 弹窗 -->
        <modal 
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
        ></modal>
    </div>
</template>

<script>
import {mapState,mapMutations,mapActions} from 'vuex';
import {InfoHeader,InfoAvatar,InfoContent,InfoFooter,InfoDefault} from '../components/info/index.js';
import {Modal} from '../components/common/index.js';

export default {
    components:{
        InfoHeader,
        InfoAvatar,
        InfoContent,
        InfoFooter,
        InfoDefault,
        Modal
    },
    data() {
        return {
            showModal:false,
            modalType:"modalValidate",
            modalParam:{},
        };
    },
    computed:{
        ...mapState([
            'cmsConfig'
        ]),
    },
    async created(){
        // 获取注册态
        let flagFilled = localStorage.getItem("flagFilled");
        console.log(flagFilled);

        // 没有注册态，跳回大表单页 
        if(flagFilled !== '1'){
            this.$router.push({
                path:'index'
            })
        }

        // 注册态只能用一次,刷新也会导致重新进入大表单页
        localStorage.setItem("flagFilled", "0");

        // 清空注册信息
        localStorage.setItem("localFormInfo", "");
        localStorage.setItem("localRegisterInfo", "");
        localStorage.setItem("defaultBirthday","");
        localStorage.setItem("defaultWorkCity","");

        // 请求模特信息
        await this.setModelInfo();
    },
    mounted(){
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
    },
    methods:{
        ...mapActions([
            'setModelInfo'
        ]),
        openModal(args){
            this.modalType = args[0];
            this.modalParam = args[1];
            this.showModal = true;
        },
        closeModal(){
            this.showModal = false;
        },
    }
};
</script>

<style lang="scss" scoped>
.info-wrapper{
    background: lightblue;
    padding-bottom: 100px;
}
</style>
