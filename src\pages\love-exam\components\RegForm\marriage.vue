<template>
    <div class="wrapper">
        <div class="item_container">
            <div
                v-for="(item,index) in list.options"
                :key="index"
                @click="goNext(item.key)"
                class="item"
                :class="curMarriage == item.key?'active':''"
            >
                {{ item.text }}
            </div>
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curMarriage: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).marriage || ''
        };
    },
    mounted() {

    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curMarriage = val;
            const params = {
                key: "marriage",
                value: val
            };
            this.$report(103, '婚况页-按钮点击');
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    .item_container {
        margin-top: 140px;
    }
    .item{
        width: 610px;
        height: 88px;
        background: #ffff;
        border-radius: 45px;
        display: flex;
        align-items: center;
        justify-content: center;

        line-height: 88px;
        font-size: 30px;
        text-align: center;
        letter-spacing: 4px;
        margin-bottom: 30px;
        border: 2px solid #B8BCCC;
    }
    .active{
        background: #fff0f0;
        border: none;
        color: #D7204A;
    }
}
</style>
