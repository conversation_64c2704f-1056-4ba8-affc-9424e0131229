<template>
    <div id="app">
        <router-view></router-view>
        <toast/>
    </div>
</template>

<script>
import "@/common/styles/reset.scss";
import Toast from "@/common/components/Toast.vue";
import fixedClickDelay from '@/common/utils/fixed_click_delay.js';
import {mapState,mapMutations,mapGetters} from 'vuex';
import { RadioGroup, Radio } from 'vant';
// import "./index.scss";

fixedClickDelay()

Vue.use(Toast);
Vue.use(RadioGroup);
Vue.use(Radio);



export default {
    components: {

    },
    data() {
        return {

        };
    },
    computed:{
        // ...mapState([
        //     'formInfo',
        //     'registerInfo'
        // ]),
    },
    created(){
        // 用户填写存本地，刷新后资料依然保留
        let localFormInfo = JSON.parse(localStorage.getItem("localFormInfo")?localStorage.getItem("localFormInfo"):0);
        let localRegisterInfo = JSON.parse(localStorage.getItem("localRegisterInfo")?localStorage.getItem("localRegisterInfo"):0);

        if(localFormInfo){
            localFormInfo.forEach((item)=>{
                this.setFormInfo({[item.index]:item.value});
            })
        }

        if(localRegisterInfo){
            for(let key in localRegisterInfo){
                this.setRegisterInfo({[key]:localRegisterInfo[key]})
            }
        }

        // 【老注册页】用于后台自然流量注册统计
        (function setAbtParams(){
            var abtParams = Z.cookie.get('abt_params');

            try{
                var arr = abtParams.split('|');
                if(arr.length!==5){
                    abtParams = ''
                }
            }catch(e){
            }

            if(!abtParams){
                var pageKey = Z.getParam('pageKey'),  // 根据不同业务key写入
                    planId = 0,
                    schemeId = 0;
                var channelId = Z.getParam('channelId') || 0;
                var subChannelId = Z.getParam('subChannelId') || 0;
                var tmp = [pageKey, channelId, subChannelId, planId, schemeId]
                Z.cookie.set('abt_params', tmp.join('|'), '.zhenai.com', '/', 24*1);
            }
        })();
    },
    mounted(){

    },
    methods:{
        ...mapMutations([
            'setFormInfo',
            'setRegisterInfo'
        ])
    }
};
</script>

<style lang="scss">
    // 解决IPHONE6 P 光标漂移的问题
    html{
        position: relative;
        overflow-y: auto;
        // -webkit-overflow-scrolling: touch;
    }
    body{
        max-height: 100vh;
        overflow-y: auto;

        margin: 0;
        padding: 0;
        font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    }
    // #id {
    //     color:red;
    //     font-size: 30px;
    // }

    // .abc{
    //     font-size: 80px;
    // }
</style>
