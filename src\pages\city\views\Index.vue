<template>
    <div
        class="home-wrapper"
        :style="{backgroundColor: cmsConfig.pageColor || '#bbe8ff'}"
    >
        <z-image
            :width="750"
            :height="1002"
            :src="cmsConfig.formImg"
        />
        <card @select-join="handleMatch" />
        <div
            class="home-wrapper__btn bounceIn"
            :class="{
                'home-wrapper__btn-mt-a' : cmsConfig.planName === '同城交友A方案(活动)',
                'home-wrapper__btn-mt-b': cmsConfig.planName === '同城交友B方案(群聊)',
                'home-wrapper__btn-mt-c': cmsConfig.planName === '同城交友C方案(单人)'
            }"
            @click="handleMatch"
            :style="{ background: cmsConfig.homeButtonColor }"
        >
            <div class="home-wrapper__btn-title">
                {{ cmsConfig.homeButtonText }}
            </div>
        </div>

        <common-protocol
            class="home-wrapper__protocol"
            :is-checked.sync="isCheckProtocol"
            :agreement-status="cmsConfig.agreementStatus"
            :style-config="{
                textColor: '#6C7175',
                protocolColor: '#333',
                protocolCheckedUrl:
                    'https://photo.zastatic.com/images/common-cms/it/20220602/1654159772231_16933_t.png'
            }"
        />

        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :page-type="cmsConfig.planName"
            :style-config="{
                confirmButtonColor: '#ffffff',
                confirmButtonBgColor: cmsConfig.homeButtonColor,
                cancleButtonColor: cmsConfig.homeButtonColor
            }"
        />
    </div>
</template>

<script>
import Card from "../components/Card.vue";
import CommonProtocol from "@/common/business/CommonProtocol.vue";
import CommonProtocolModal from "@/common/business/components/CommonProtocolModal.vue";

export default {
    name: "Index",
    data() {
        return {
            isCheckProtocol: false,
            isShowModal: false
        };
    },

    inject: ["cmsConfig"],

    mounted() {
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                this.isCheckProtocol = value === 0;
            },
            {
                immediate: true
            }
        );
        this.$report(1, '首页访问');
        this.$report(3000, '首页访问（监控）');
    },

    components: {
        Card,
        CommonProtocol,
        CommonProtocolModal
    },

    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.isShowModal = false;
            this.$router.push({
                path: "/collection/0"
            });
        },

        handleMatch() {
            this.$report(2, '首页-主按钮点击');
            if (!this.isCheckProtocol) {
                this.isShowModal = true;
            } else {
                this.$router.push({
                    path: "/collection/0"
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.home-wrapper {
    min-height: 100vh;
    &__btn {
        width: 658px;
        height: 132px;
        margin: 0 auto;
        opacity: 0.88;
        background: #ff4881;
        border: 4px solid #ffffff;
        border-radius: 66px;
        color: #ffffff;
        @include flex-center();
        &-title {
            font-size: 40px;
            font-weight: 500;
        }
        &-mt-a {
            margin-top: 60px;
        }
        &-mt-b {
            margin-top: 50px;
        }
        &-mt-c {
            margin-top: 107px;
        }
    }
    &__protocol {
        margin-top: 36px;
        padding-bottom: 68px;
    }
    .bounceIn {
        animation-duration: 1.5s;
        animation-name: bounceIn;
    }
}

@keyframes bounceIn {
    from,
    20%,
    40%,
    60%,
    80%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }

    20% {
        transform: scale3d(1.1, 1.1, 1.1);
    }

    40% {
        transform: scale3d(0.9, 0.9, 0.9);
    }

    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }

    80% {
        transform: scale3d(0.97, 0.97, 0.97);
    }

    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

</style>
