<template>
    <love-page
        :back-btn-visible="backBtnVisible"
        @click-back="clickBack">
        <div :class="['questions', currentQuestionIndex === 3 ? 'questions--big-padding' : '']">
            <z-image
                :src="cmsConfig.questionHeaderImg"
                :width="562.5"
                :height="327"/>

            <z-image
                class="questions__middle-wrapper"
                :src="require('../assets/images/question-middle-img.png')"
                :width="532"
                :height="114">
                <div class="questions__middle">
                    <template v-if="currentQuestionIndex === 0">
                        <div>你的理想型是什么样的</div>
                        <div>可以提出4个要求</div>
                    </template>
                    <template v-else>
                        <div>提出你的要求</div>
                        <div>帮你分配契合的理想型对象</div>
                    </template>
                </div>
            </z-image>

            <question-progress :step-index="currentQuestionIndex"/>

            <component :is="currentQuestionComponent"/>

            <!-- TODO Bottom 做到 page 里面，就不需要 questions--big-padding 处理 -->
            <simple-button
                v-if="currentQuestionIndex === 3"
                :width="670"
                fix-bottom
                background="linear-gradient(90deg, #FC9CFF 0%, #7146FF 50%, #57F3FF 100%)"
                @click="goForm">
                选好了，立即匹配
            </simple-button>
        </div>
    </love-page>
</template>

<script>
import { mapState } from 'vuex';
import { createPage } from '@/common/framework';
import QuestionProgress from "../components/question-progress";
import QuestionGender from "../components/question-gender";
import QuestionAge from "../components/question-age";
import QuestionSalary from "../components/question-salary";
import QuestionTag from "../components/question-tag";

const questionMap = {
    0: {
        accessPoint: 2,
        accessPointDesc: '问题页1访问',
        component: 'question-gender',
    },
    1: {
        accessPoint: 5,
        accessPointDesc: '问题页2访问',
        component: 'question-age',
    },
    2: {
        accessPoint: 12,
        accessPointDesc: '问题页3访问',
        component: 'question-salary',
    },
    3: {
        accessPoint: 18,
        accessPointDesc: '问题页4访问',
        component: 'question-tag',
    },
}

const QUESTION_LENGTH = 4

export default createPage({
    name: "Questions",
    components: {
        QuestionProgress,
        QuestionGender,
        QuestionAge,
        QuestionSalary,
        QuestionTag
    },
    data() {
        return {
            currentQuestionComponent: '',
            currentQuestionIndex: -1,
        }
    },
    computed: {
        ...mapState([
            'requirement',
            'cmsConfig'
        ]),
        backBtnVisible() {
            return this.currentQuestionIndex > 0;
        }
    },
    watch: {
        $route: {
            handler() {
                this.currentQuestionIndex = Number(this.$route.params.id);

                if (this.$route.params.id !== '' && this.currentQuestionIndex < QUESTION_LENGTH) {
                    this.currentQuestion = questionMap[this.currentQuestionIndex];
                    this.init();
                    return;
                }

                this.$toast('链接错误');
            },
            immediate: true,
        }
    },
    methods: {
        init() {
            this.$report(this.currentQuestion.accessPoint, this.currentQuestion.accessPointDesc);
            this.currentQuestionComponent = this.currentQuestion.component;
        },
        clickBack() {
            this.$router.push({
                path: `/questions/${ this.currentQuestionIndex - 1 }`,
            });
        },
        goForm() {
            this.$report(19, '问题页4-点击“立即匹配按钮”');

            if (this.$z_.isEmpty(this.requirement.tags)) {
                this.$toast('请选择标签再匹配');
                return;
            }

            this.$router.push({
                path: `/form`,
            });
        }
    }
})
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.questions {
    padding: 0 38px 70px;
    @include flex-center(column, null, center);

    &--big-padding {
        padding-bottom: 200px;
    }

    &__header-img {
        width: 100%;
    }

    &__middle-wrapper {
        padding-top: 10px;
    }

    &__middle {
        @include flex-center(column, null, center);

        > div:first-child {
            font-size: 36px;
            font-weight: 500;
        }

        > div:last-child {
            font-size: 32px;
            line-height: 48px;
            margin-top: 20px;
        }
    }
}
</style>
