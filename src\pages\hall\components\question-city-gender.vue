<template>
    <div class="wrapper">
        <div class="city" @click="openPicker()">
            <div class="city-title">您的工作地是</div>
            <z-image
                class="city-bg"
                :src="require('../assets/images/city-bg.png')"
                :width="514"
                :height="114"
                block
            >
                <div class="city-info">
                    <span class="city-info__text">{{
                        cutStirng(cityItem.value.split("/")[0])
                    }}</span>
                    <span class="city-info__line"></span>
                    <span class="city-info__text">{{
                        cutStirng(cityItem.value.split("/")[1])
                    }}</span>
                    <span class="city-info__line"></span>
                    <span class="city-info__text">{{
                        cutStirng(cityItem.value.split("/")[2])
                    }}</span>
                </div>
            </z-image>
        </div>

        <div class="gender">
            <div class="gender-title">您的性别是</div>
            <div class="gender-panel">
                <div
                    v-for="(item, index) in list"
                    :key="index"
                    class="gender-panel__item"
                    @click="clickGender(item.value, index)"
                >
                    <z-image
                        class="gender-panel__item__bg"
                        :src="item.src"
                        :width="160"
                        :height="160"
                    />
                    <z-image
                        v-show="registerForm.gender === item.value"
                        class="gender-panel__item__active"
                        :src="require('../assets/images/gender-active.png')"
                        :width="160"
                        :height="160"
                    />
                    <div class="gender-panel__item__text">
                        {{ item.label }}
                    </div>
                </div>
            </div>
        </div>

        <picker :visible.sync="showPicker" :param="cityItem" />
    </div>
</template>

<script>
import QuestionPanel from "./question-panel";
import { mapState, mapMutations } from "vuex";
import Picker from "./picker.vue";

export default {
    name: "question-city-gender",
    components: {
        QuestionPanel,
        Picker
    },
    computed: {
        ...mapState(["registerForm", "formItems"]),
        cityItem(){
            return this.formItems.find((item) => {
                return item.key === 'workCity'
            })
        }
    },
    data() {
        return {
            list: [
                {
                    active: false,
                    label: "男生",
                    value: 0,
                    src: require("../assets/images/gender-male.png")
                },
                {
                    active: false,
                    label: "女生",
                    value: 1,
                    src: require("../assets/images/gender-female.png")
                }
            ],
            showPicker: false
        };
    },
    methods: {
        ...mapMutations(["setRequirement", "setRegisterForm"]),
        clickGender(value) {
            this.setRegisterForm({
                key: "gender",
                value
            });

            this.setRequirement({
                tags:[] // 清空关联项
            });
        },
        openPicker() {
            this.showPicker = true;
        },
        cutStirng(str) {
            if (!str){
                return '';
            } else if (str.length > 4){
                return str.substr(0,3)+"..."
            } else {
                return str;
            }
        }
    }
};
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.wrapper {
    width: 100%;
    margin-top: 130px;
    @include flex-center(column, null, center);
    .city {
        &-title {
            font-weight: 700;
            font-size: 28px;
            color: #fe4f06;
            text-align: center;
        }

        &-bg {
            margin-top: 40px;
        }

        &-info {
            @include flex-center(row, center, center);
            // line-height: 114px;
            height: 114px;
            &__text {
                padding: 0;
                width: 112px;
                font-weight: 700;
                font-size: 28px;
                color: #ffffff;
                text-align: center
            }

            &__line {
                margin: 0 10px;
                height: 28px;
                width: 2px;
                background: #fff;
            }
        }
    }

    .gender {
        margin-top: 98px;
        &-title {
            font-weight: 700;
            font-size: 28px;
            color: #fe4f06;
            text-align: center;
        }

        &-panel {
            margin-top: 60px;
            width: 470px;
            @include flex-center(row, space-between, center);
            &__item {
                position: relative;

                &__active {
                    position: absolute;
                    left: 0px;
                    top: 0px;
                }

                &__text {
                    margin-top: 52px;
                    font-size: 28px;
                    color: #fff;
                    text-align: center;
                }
            }
        }
    }
}
</style>
