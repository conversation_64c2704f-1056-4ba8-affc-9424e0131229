<template>
    <div class="radio-list">
        <van-radio-group
            direction="horizontal"
            v-model="radio"
            @change="changeRadio"
        >
            <van-radio
                :name="item.text"
                v-for="(item, index) in list"
                :key="item.key"
                >{{ item.text }}</van-radio>
        </van-radio-group>
    </div>
</template>

<script>
export default {
    name: "radio-list",
    props: {
        number: {
            type: String
        },
        list: {
            type: Array
        }
    },
    data() {
        return {
            radio: null

        };
    },
    watch: {
        number: {
            handler(text) {

                this.radio = text;
            },
            immediate: true,
        }
    },
    methods: {
        changeRadio (text){
            let index = this.list.findIndex((tem)=>tem.text === text);
            this.$emit('change', this.list[index]);
        }
    }
};
</script>

<style scoped lang="scss">

.radio-list {
    width: 100%;
}
</style>
<style lang="scss">
@import "../../index.scss";
.radio-list .van-radio{
    width:44%;
    margin-bottom: 16px;
}

.radio-list .van-radio__icon--checked .van-icon{
    background-color: $themeColor;
    border-color: $themeColor;
}
</style>
