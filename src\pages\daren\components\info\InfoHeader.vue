<template>
    <!-- CMS配置的头图 -->
    <img 

            class="info-header-wrapper" 
            :src="cmsConfig.downloadImg" 
            :style="{
                visibility:cmsConfig.downloadImg?'visible':'hidden',
                height:cmsConfig.downloadImg?'auto':'0px'
            }"
    />
</template>

<script>
import {mapState} from 'vuex';
import {reportKibana} from '@/common/utils/report.js';


export default {
    components:{
    },
    data() {
        return {
            // aaa:require("../../assets/imgs/car-logo.png")
        };
    },
    computed:{
        ...mapState([
            'cmsConfig'
        ]),
    },
    created(){
        
    },
    mounted(){
        // 打桩
        reportKibana(
            "导量H5大表单翻牌",
            5,
            "翻牌下载页访问",
            {
                ext16: 2, // 1 投放版 2 达人版
                ext18:this.$route.path === '/info'? 1 : 2 // 1 无盲盒 2 有盲盒
            }
        );
    },
    methods:{
             
    },
   
};
</script>

<style lang="scss" scoped>
.info-header-wrapper{
    width: 750px;
    height: 334px;
    // height: auto;
    // height: 330px;
    // background-image: url();
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
}
</style>
