import { post, get } from '@/common/utils/ajax.js';

// 获取测试结果
export const _getDelistingGuideResult = (data) => post(`/H5hook/getDelistingGuideResult`, data);

// 获取指定数目的真人模特object数组
export const _queryIdealLoverRecList = (data) => post(`/H5hook/queryIdealLoverRecList`, data);

// 获取指定object真人模特详细信息
export const _queryIdealLoverRecDetail = (data) => post(`/H5hook/queryIdealLoverRecDetail`, data);

// 标记喜欢
export const _signDelistingGuideStatus = (data) => post(`/H5hook/signDelistingGuideStatus`, data, );

// 获取推荐模特列表 - 带预览图
export const _queryIdealLoverModelList = (data) => post('/register/hook/queryIdealLoverModelList', data);

// 获取模特信息
export const _getModelInfo = (data) => post("/register/getModelInfo.do", data);
