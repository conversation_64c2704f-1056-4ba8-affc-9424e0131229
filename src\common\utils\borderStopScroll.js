/**
 * borderStopScroll思想是：当到达临界点比如底部的时候，再往上滑动，这时应该阻止用户的滚动，达到阻止页面背景滚动的效果
 *
 * ### Useage
 *borderStopScroll({
 *wrapEle: document.querySelector('.layBox')
 *});
 */

// polyfill passive
function applyPassive() {
    let passiveIfSupported = false;
    const options = Object.defineProperty({}, 'passive', {
        get() {
            passiveIfSupported = { passive: true };
            return passiveIfSupported;
        }
    });
    try {
        window.addEventListener('testPassive', null, options);
    } catch (err) {
        //
    }
    return passiveIfSupported;
}
const eventOptions = applyPassive();

function cancelDefault(e) {
    if (e.cancelable) e.preventDefault();
}

function stopBub(ele) {
    ['touchstart', 'touchmove', 'touchend', 'touchcancel'].forEach((value) => {
        ele.addEventListener(value, (e) => {
            e.stopPropagation();
        }, false);
    });
}

function borderStopScroll({
        wrapEle = null, // {HTMLElement} 滚动外层容器
        live = false, // {Boolean} wrapEle的scrollHeight在当前状态是否是变化的。
        callback // 一个回调事件
    }) {
    let wrapOffsetHeight = 0;
    let wrapScrollHeight = 0;
    let hasScroll = 'initial';
    const point = {
        startY: 0,
        endY: 0
    };
    function wrapScroll() {
        const tempScrollHeight = wrapScrollHeight;
        const scrollTop = wrapEle.scrollTop;
        let moveStatus = 'top'; // 判断边界状态
        if (scrollTop <= 0) {
            moveStatus = 'top'; // 到顶部
        } else if (wrapOffsetHeight + scrollTop + 1 >= tempScrollHeight) {
            moveStatus = 'bottom'; // 到底部
        } else {
            moveStatus = 'center';
        }
        return moveStatus;
    }
    function onceOperate() {
        wrapScrollHeight = wrapEle.scrollHeight;
        wrapOffsetHeight = wrapEle.offsetHeight;
        if (wrapScrollHeight > wrapOffsetHeight) hasScroll = true;
        else hasScroll = false;
    }

    if (wrapEle) {
        stopBub(wrapEle);

        wrapEle.addEventListener('touchstart', (e) => {
            if (hasScroll === 'initial' || live) onceOperate();
            if (hasScroll) point.startY = e.targetTouches[0].pageY;
            if (callback) {
                callback();
            }
        }, eventOptions);

        wrapEle.addEventListener('touchmove', (e) => {
            if (hasScroll) {
                const moveStatus = wrapScroll();
                point.endY = e.targetTouches[0].pageY;
                if ((moveStatus === 'top' && point.endY - point.startY >= 0) || (moveStatus === 'bottom' && point.endY - point.startY <= 0)) {
                    cancelDefault(e);
                }
            } else {
                cancelDefault(e);
            }
        }, false);
    }
}

export default borderStopScroll;
