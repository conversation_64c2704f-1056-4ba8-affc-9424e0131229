/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
*{
  box-sizing: border-box;
}
 html, body, div, span, applet, object, iframe,
 h1, h2, h3, h4, h5, h6, p, blockquote, pre,
 a, abbr, acronym, address, big, cite, code,
 del, dfn, em, img, ins, kbd, q, s, samp,
 small, strike, strong, sub, sup, tt, var,
 b, u, i, center,
 dl, dt, dd, ol, ul, li,
 fieldset, form, label, legend,
 table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed,
 figure, figcaption, footer, header,
 menu, nav, output, ruby, section, summary,
 time, mark, audio, video, input ,button{
   margin: 0;
   padding: 0;
   border: 0;
   font-size: 100%;
   vertical-align: baseline;
 }

 /* HTML5 display-role reset for older browsers */
 article, aside, details, figcaption, figure,
 footer, header, menu, nav, section{
   display: block;
 }

 body {
   line-height: 1;
 }

 blockquote, q {
   quotes: none;
 }

 blockquote:before, blockquote:after, q:before, q:after {
   content: none;
 }

 table {
   border-collapse: collapse;
   border-spacing: 0;
 }

 li {
   list-style: none;
 }

 body {
   -webkit-text-size-adjust: none;
   -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
 }

 // 消除边框
input,img{
    border: none;
}

// 消除高亮
input{
    outline: none;
    border-radius: 0;
}

body{
  // 解决 Ios 300毫秒延迟
  touch-action: manipulation;
}

button{
  border: none;
  background: #fff;
}

// iphone12 自动输入验证码黄色背景的兼容
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-transition-delay:99999s;
}
