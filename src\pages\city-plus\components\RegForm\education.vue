<template>
    <div class="education">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}
        </div>
        <div class="education-info">
            <div
                class="education-info-btn"
                :class="{ active: curEducation === option.key }"
                v-for="option in list.options"
                @click="goNext(option.key)"
            >
                {{ option.text }}
            </div>
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
export default {
    name: "Education",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    inject: ["cmsConfig"],
    mounted() {
        this.$report(7, '学历页访问');
    },
    data() {
        return {
            curEducation: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) && Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).education || ''
        };
    },
    methods: {
        goNext(val) {
            this.curEducation = val;
            const params = {
                key: "education",
                value: val
            };
            setLocalRegisterForm(params, this.cmsConfig.planName);
            this.$report(7, '学历页-具体学历点击');
            setTimeout(() => {
                this.$emit("val-updated", val);
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
.education {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    &-info {
        margin: 50px 48px 0;
        &-btn {
            width: 100%;
            height: 110px;
            margin-bottom: 20px;
            color: #26273c;
            font-size: 32px;
            line-height: 110px;
            text-align: center;
            border-radius: 80px;
            background: #fff;
            &.active {
                color: #fff;
                background: #5368f0;
            }
        }
    }
}
</style>
