<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curMarriage === item.key?'active':''"
        >
            {{ item.text }}
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE, changeProfile } from "../../config";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curMarriage: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).marriage || ''
        };
    },
    mounted() {

    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curMarriage = val;
            const params = {
                key: "marriage",
                value: val,
                isMark: false,
            };
            this.$report(33, '婚况页-按钮点击');
            setLocalRegisterForm(params, PAGE_TYPE);
            changeProfile({...params, token: oCookie.get('token')})
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    .item{
        width: 396px;
        height: 89px;
        background-color: #A98CE6;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 45px;
        // font-family: 'love';

        line-height: 48px;
        font-size: 30px;
        text-align: center;
        color: #FFFFFF ;
        letter-spacing: 4px;
        box-shadow: 0 3px 3px 0 #bd92eb66;
    }
    > .active{
        background: linear-gradient(180deg,#5243FE, #9A55F0)
    }
}
</style>
