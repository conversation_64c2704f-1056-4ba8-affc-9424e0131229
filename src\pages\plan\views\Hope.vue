<template>
    <div class="plan-question">
        <div :class="judgeBigScreen ? 'header' : 'header header-adapt'">
            <i class="goback" @click="handleGoBack" />
            <span>上一题</span>
        </div>

        <div class="inner">
            <div class="question">
                你希望多久脱单？
            </div>

            <ul class="answer-list">
                <li :class="planInfo.hope === 0 ? 'answer answer-active' : 'answer'" @click="handleAnswerClick(0)">
                    3个月内
                </li>
                <li :class="planInfo.hope === 1 ? 'answer answer-active' : 'answer'" @click="handleAnswerClick(1)">
                    半年内
                </li>
                <li :class="planInfo.hope === 2 ? 'answer answer-active' : 'answer'" @click="handleAnswerClick(2)">
                    一年内
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import {reportKibana} from '@/common/utils/report.js';
export default {
    components: {},
    data() {
        return {};
    },
    computed: {
        ...mapState(['materialId', 'planInfo']),
        judgeBigScreen() {
            //这里根据返回值 true 或false ,返回true的话 则为全面屏
            var result = false;
            var rate = window.screen.height / window.screen.width;
            var limit = window.screen.height == window.screen.availHeight ? 1.8 : 1.65; // 临界判断值
            // window.screen.height为屏幕高度
            //  window.screen.availHeight 为浏览器 可用高度
            if (rate > limit) {
                result = true;
            }
            return result;
        }
    },
    created() {
        if (localStorage.getItem("za_localPlanInfo")) {
            this.setPlanInfo(JSON.parse(localStorage.getItem("za_localPlanInfo")));
        }
    },
    mounted() {
        reportKibana("脱单计划H5", 6, "脱单时间页访问", { ext16: this.materialId });
    },
    methods: {
        ...mapMutations(["setPlanInfo"]),
        handleAnswerClick(key) {
            if (key === 0) {
                reportKibana("脱单计划H5", 7, "脱单时间页点击3个月按钮", { ext16: this.materialId });
            } else if (key === 1) {
                reportKibana("脱单计划H5", 8, "脱单时间页点击半年按钮", { ext16: this.materialId });
            } else if (key === 2) {
                reportKibana("脱单计划H5", 9, "脱单时间页点击一年按钮", { ext16: this.materialId });
            }
            this.setPlanInfo({
                hope: key
            });
            this.$router.push("/way");
        },
        handleGoBack() {
            this.$router.push("/purpose");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.plan-question {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    .header {
        font-size: 30px;
        font-family: Helvetica;
        color: #191c32;
        line-height: 1;
        position: absolute;
        top: 8%;
        left: 48px;
        .goback {
            display: inline-block;
            width: 72px;
            height: 72px;
            @include set-img("../assets/images/icon-goback.png");
            background-size: cover;
            vertical-align: middle;
        }
        span {
            vertical-align: middle;
        }
    }
    .header-adapt {
        top: 2%;
    }
    .inner {
        position: absolute;
        top: 50%;
        right: 48px;
        left: 48px;
        height: 750px;
        transform: translateY(-60%);
        text-align: center;
        @include set-img("../assets/images/bg-question-card.png");
        background-size: cover;
        font-family: SourceHanSansCN-Medium, SourceHanSansCN;
        .question {
            font-size: 44px;
            font-weight: 500;
            color: #191c32;
            line-height: 38px;
            margin-top: 138px;
            &::after {
                content: "";
                display: block;
                width: 274px;
                height: 24px;
                background-color: #b2e0fe;
                margin: -6px auto 0;
            }
        }
        .answer {
            height: 100px;
            margin: 100px 30px 0;
            font-size: 32px;
            font-weight: 500;
            color: #103954;
            line-height: 100px;
            text-align: center;
            border-radius: 80px;
            background-color: #f6f5f8;
            border: 1px solid #e6e6e6;
            & + .answer {
                margin-top: 20px;
            }
            &:active {
                color: #fff;
                background-color: #279bae;
                border: 1px solid #279bae;
            }
        }
        .answer-active {
            color: #fff;
            background-color: #279bae;
            border: 1px solid #279bae;
        }
    }
}
.plan-question::before {
  content: '';
  display: block;
  height: 588px;
  @include set-img("../assets/images/bg-question2-top.png");
  background-size: cover;
  background-position: 0 0;
}
.plan-question::after {
  content: '';
  display: block;
  height: 860px;
  @include set-img("../assets/images/bg-question2-bottom.png");
  background-size: cover;
  background-position: 0 0;
}
</style>
