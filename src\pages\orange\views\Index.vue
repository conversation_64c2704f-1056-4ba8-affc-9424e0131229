<template>
    <div class="index-wrap">
        <z-image
            class="header"
            :width="751"
            :height="729"
            :src="cmsConfig.homeHeadImg"
        />
        
        <div class="form">
            <div class="form-detail">
                <common-form
                    page-type="大表单橙子"
                    :filter-config="['gender', 'workCity','birthday','education','marriage','salary','phone']"
                    :style-config="{
                        theme: 'orange',
                        color: '#F971AD',
                        fontColor: '#F5F5F5',
                        labelColor: '#6C6D75',
                        valueColor: '#000000',
                        phoneBgColor: '#F2F4F5',
                        selectorColor: '#F971AD',
                        selectorFontColor: '#FFFFFF',
                    }"
                />
            </div>
            <div class="form-submit">
                <common-submit
                    page-type="大表单橙子"
                    :agreement-status="this.cmsConfig.agreementStatus"
                    :is-need-protocol="true"
                    :style-config="{
                        modalConfig: {
                            confirmButtonColor: '#fff',
                            confirmButtonBgColor: '#F971AD',
                            cancleButtonColor: '#F971AD',
                        },
                        protocolConfig: {
                            textColor: '#000000',
                            protocolColor: '#F971AD',
                            protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20221010/1665389761971_444263_t.png'
                        }
                    }"
                    :handle-after-regisiter="openSubmitModal"
                    :handle-login="download"
                >
                    <div
                        class="form-submit-button"
                        @click="submit"
                        :style="{
                            backgroundColor:cmsConfig.homeButtonColor,
                        }"
                    >
                        {{ cmsConfig.homeButtonText }}
                    </div>
                </common-submit>
            </div>
        </div>

        <SubmitModal 
            v-model="showModal.submit"
            @download="download"
            @openRecommendModal="showModal.recommend = true"
        />
        <RecommendModal
            v-model="showModal.recommend"
            @download="download"
            @goResult="goResult"
        />
    </div>
</template>

<script>
import CommonForm from '@/common/business/CommonForm';
import CommonSubmit from '@/common/business/CommonSubmit';
import SubmitModal from '../components/SubmitModal';
import RecommendModal from '../components/RecommendModal';

export default {
    name: 'Index',
    inject: ["cmsConfig","pageType","download"],
    components: {
        CommonForm,
        CommonSubmit,
        SubmitModal,
        RecommendModal,
    },
    data() {
        return {
            showModal:{
                submit: false,
                recommend: false,
            }
        };
    },
    mounted(){
        this.$report(1, '大表单页访问');
    },
    methods: {
        openSubmitModal(){
            this.showModal.submit = true;
        },
        goResult() {
            this.$router.push('/result');
        },
        submit(){
            this.$report(1, '大表单页主按钮点击');
        }
    }
};

</script>

<style lang="scss" scope>
@import '~@/common/styles/common.scss';

.index-wrap {
    @include flex-center(column, null, center);
    position: relative;

    .header {
        position: relative;
    }

    .form {
        &-detail {
            margin: 0 auto;
            padding: 16px 20px 0px;
        }
        &-submit {
            position: relative;
            margin: 20px 0 40px;
            z-index: 1;

            &-button {
                margin: 20px auto 0;
                width: 654px;
                height: 98px;
                border-radius: 49px;
                font-weight: 500;
                font-size: 36px;
                color: #FFFFFF;
                text-align: center;
                line-height: 98px;
            }
        }
    }
}
</style>