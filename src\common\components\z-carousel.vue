<template>
    <!-- 支持设置不同背景，不同长度内容保持速度一致 -->
    <div 
        ref="refWrapper" 
        class="carousel-wrapper" 
        :style="customWrapperStyle"
    >
        <div
            ref="refContent"
            class="carousel-content"
            :style="customContentStyle"
        >
            <slot/>
        </div>

        <!-- 无缝滚动 -->
        <div
            v-if="showContentRepeat"
            ref="refContentRepeat"
            class="carousel-content"
            :style="customContentStyle"
        >
            <slot/>
        </div>
    </div>
</template>

<script>

export default {
    name: "z-carousel",
    props: {
        height: {
            type: Number,
        },
        width: {
            type: Number,
            required: true
        },
        background: {
            type: String,
            default: "#fff"
        },
        gap: {
            type: Number,
            default: 20
        },
        animationDuration: {
            type: Number,
            default: 5
        },
        animationTimingFunction: {
            type: String,
            default: "linear"
        },
        animationDelay: {
            type: Number,
            default: 0
        },
        animationIterationCount: {
            type: [Number, String],
            default: "infinite"
        }
    },
    data() {
        return {
            showContentRepeat: false,
            paddingLeft: 0
        };
    },
    computed: {
        customWrapperStyle() {
            return {
                width: this.$utils.pxToRem(this.width),
                height: this.height ? this.$utils.pxToRem(this.height) : 'auto',
                background: this.background
            };
        },
        customContentStyle() {
            return {
                paddingLeft: this.$utils.pxToRem(this.paddingLeft)
            };
        }
    },
    mounted() {
        // 初始化无缝循环
        this.compareWidth();
        // 设置动画
        this.setAnimation();
    },
    methods: {
        compareWidth() {
            let contentOffsetWidth = this.$refs.refContent.offsetWidth * this.$utils.getScale();
            if (contentOffsetWidth < this.width) {
                // 内容小于轮播框时
                this.showContentRepeat = false;
                this.paddingLeft = this.width;
            } else {
                if (this.animationIterationCount === "infinite") {
                    // 无缝循环
                    this.showContentRepeat = true;
                    this.paddingLeft = this.gap;
                } else {
                    this.showContentRepeat = false;
                    this.paddingLeft = this.width + this.gap;
                }
            }
        },
        setAnimation() {
            // 设置循环动画
            this.$nextTick(() => {
                // 匀速控制，防止内容不一致时滚动速度不一致
                let ratio = this.$refs.refContent.offsetWidth / this.$refs.refWrapper.offsetWidth;

                let animationSetting = `
                    carousel 
                    ${this.animationDuration * ratio}s 
                    ${this.animationTimingFunction} 
                    ${this.animationDelay}s
                    ${this.animationIterationCount} 
                    `;
                    
                this.$refs.refContent.style.animation = animationSetting;
                this.showContentRepeat === true
                    ? (this.$refs.refContentRepeat.style.animation = animationSetting)
                    : "";
            });
        }
    }
};
</script>

<style>
/* scoped会改变keyframes的名字，导致动态设置的animation指定keyframes时失效，所以不放在scoped中 */
@keyframes carousel {
    from {
        transform: translateX(0%);
    }
    to {
        transform: translateX(-100%);
    }
}
</style>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";
.carousel-wrapper {
    @include flex-center(row, flex-start);
    height: 50px;
    width: 250px;
    overflow: hidden;
}

.carousel-content {
    white-space: nowrap;
    display: inline-block;
    font-size: 30px;
    line-height: 50px;
}
</style>
