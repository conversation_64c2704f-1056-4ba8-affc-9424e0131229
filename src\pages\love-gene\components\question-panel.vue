<template>
    <z-image
        class="question-panel"
        :src="src"
        :width="672"
        :height="height">
        <div class="question-panel__title">{{ title }}</div>
        <slot/>
    </z-image>
</template>

<script>
export default {
    name: "question-panel",
    props: {
        src: {
            type: String,
            required: true,
        },
        title: {
            type: String,
            required: true,
        },
        height: {
            type: Number,
            required: true,
        },
    }
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.question-panel {
    padding-top: 48px;
    @include flex-center(column, null, center);

    &__title {
        text-align: center;
        font-weight: 500;
        font-size: 32px;
    }
}
</style>
