<template>
    <div class="form-wrap">
        <img
            class="header"
            :src="styleConfig.formImg"
            alt="表单头图"
        >
        <div class="content">
            <!-- 表单 -->
            <common-form
                class="form"
                :style="{'background-image': `url(${styleConfig.formDetailBg})`, 'backgroundSize': '100% 100%', 'backgroundRepeat': 'no-repeat'}"
                :page-type="pageType"
                :filter-config="['gender', 'workCity','birthday','education','marriage','salary','phone']"
                :style-config="{
                    color: isQiang? '#FFF100' : '#FB3CE3',  
                    fontColor: isQiang? '#000' : '#FFF',
                    selectorColor: '#000',
                    selectorFontColor: '#fff',
                    labelColor: isQiang ? '#a3a3a1' : '#000000',
                    valueColor: isQiang ? '#fff' : '#000000',
                    phoneBgColor: isQiang ? '#6C6D75' : '#AEAFB3',
                }"
            />
            <!-- 提交注册按钮 -->
            <common-submit
                :page-type="pageType"
                :is-need-protocol="false"
                :style-config="{
                    modalConfig: {
                        confirmButtonColor: '#FFF',
                        confirmButtonBgColor: '#000',
                        cancleButtonColor: '#000',
                    }
                }"
                :handle-after-regisiter="handleJump"
                :handle-login="handleJump"
            >
                <div
                    class="submit"
                    :class="{'submit-disabled': !finished}"
                    :style="{'backgroundColor': styleConfig.mainColor, 'color': styleConfig.buttonFontColor}"
                >
                    {{ cmsConfig.formButtonText }}
                </div>
            </common-submit>
        </div>
    </div>
</template>

<script>
import CommonForm from '@/common/business/CommonForm';
import CommonSubmit from '@/common/business/CommonSubmit';
import { storage as Storage } from "@/common/utils/storage";

export default {
    name: "Form",
    inject: ["cmsConfig", 'styleConfig'],
    data() {
        return {
            registerForm: Storage.getItem(`cachedRegisterForm-${this.pageType}`) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },
    computed: {
        isQiang() {
            return this.cmsConfig.reportViewType === 1;
        },
        pageType() {
            return this.cmsConfig.reportViewType === 1 ? '锵锵锵H5' : '咚咚咚H5';
        },
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            return this.allFillIn && this.validatedPhone;
        }
    },
    components: {
        CommonForm,
        CommonSubmit
    },
    mounted() {
        this.handleWatchForm();
        this.$report(4, '大表单注册页访问');
    },
    methods: {
        handleWatchForm() {
            window.addEventListener("setItemEvent", (e) => {
                if(e.key === `cachedRegisterForm-${this.pageType}`){
                    // 如果数据有变化就更新data
                    this.registerForm = Object.assign(
                        this.registerForm,
                        JSON.parse(e.newValue)
                    );
                }
            });
        },

        handleJump() {
            this.$router.push('/result');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.form-wrap {
    .header {
        width: 100%;
        height: 630px;
    }
    .content {
        width: 646px;
        border-radius: 32px;
        margin: -200px auto 0px;
        img {
            height: 140px;
        }
        .form {
            margin-top: -60px;
            padding: 108px 16px 40px;
            width: 646px;
            height: 920px;
            border-radius: 32px;
            width: 100%;
            z-index: 1;
            position: relative;
        }

        .submit {
            @include flex-center(row, center, center);
            width: 654px;
            height: 96px;
            border-radius: 76px;
            font-size: 36px;
            font-family: 'fz';
            margin: 48px 0px 40px;
            &-disabled {
                opacity: 0.5;
            }
        }
    }
}
</style>
