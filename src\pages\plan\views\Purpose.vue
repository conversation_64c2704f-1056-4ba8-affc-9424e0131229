<template>
    <div class="plan-question">
        <div class="inner">
            <div class="question">
                你的脱单目的?
            </div>
            <ul class="answer-list">
                <li
                    :class="planInfo.purpose === 0 ? 'answer answer-active' : 'answer'"
                    @click="handleAnswerClick(0)"
                >
                    结婚
                </li>
                <li
                    :class="planInfo.purpose === 1 ? 'answer answer-active' : 'answer'"
                    @click="handleAnswerClick(1)"
                >
                    恋爱
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import { reportKibana } from "@/common/utils/report.js";
export default {
    components: {},
    data() {
        return {};
    },
    computed: {
        ...mapState(["materialId", "planInfo"])
    },
    created() {
        if (localStorage.getItem("za_localPlanInfo")) {
            this.setPlanInfo(JSON.parse(localStorage.getItem("za_localPlanInfo")));
        }
    },
    mounted() {
        reportKibana("脱单计划H5", 3, "脱单目的页访问", { ext16: this.materialId });
    },
    methods: {
        ...mapMutations(["setPlanInfo"]),
        handleAnswerClick(key) {
            if (key === 0) {
                reportKibana("脱单计划H5", 4, "脱单目的页点击结婚按钮", {
                    ext16: this.materialId
                });
            } else if (key === 1) {
                reportKibana("脱单计划H5", 5, "脱单目的页点击恋爱按钮", {
                    ext16: this.materialId
                });
            }
            this.setPlanInfo({
                purpose: key
            });
            this.$router.push("/hope");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.plan-question {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  .inner {
    position: absolute;
    top: 50%;
    right: 48px;
    left: 48px;
    height: 750px;
    transform: translateY(-60%);
    text-align: center;
    @include set-img("../assets/images/bg-question-card.png");
    background-size: cover;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    .question {
      font-size: 44px;
      font-weight: 500;
      color: #191c32;
      line-height: 38px;
      margin-top: 138px;
      &::after {
        content: "";
        display: block;
        width: 274px;
        height: 24px;
        background-color: #fed9e2;
        margin: -6px auto 0;
      }
    }
    .answer {
      height: 100px;
      margin: 100px 30px 0;
      font-size: 32px;
      font-weight: 500;
      color: #103954;
      line-height: 100px;
      text-align: center;
      border-radius: 80px;
      background-color: #f6f5f8;
      border: 1px solid #e6e6e6;
      & + .answer {
        margin-top: 20px;
      }
      &:active {
        color: #fff;
        background-color: #279bae;
        border: 1px solid #279bae;
      }
    }
    .answer-active {
      color: #fff;
      background-color: #279bae;
      border: 1px solid #279bae;
    }
  }
}
.plan-question::before {
  content: '';
  display: block;
  height: 588px;
  @include set-img("../assets/images/bg-question1-top.png");
  background-size: cover;
  background-position: 0 0;
}
.plan-question::after {
  content: '';
  display: block;
  height: 860px;
  @include set-img("../assets/images/bg-question1-bottom.png");
  background-size: cover;
  background-position: 0 0;
}
</style>
