<template>
    <div
        class="common-protocol"
        :style="{ color: styleConfig.textColor }"
    >
        <div
            class="common-protocol__checke-box"
            @click="handleToggleCheck"
        >
            <div
                v-if="isChecked"
                :style="{ background: `url(${styleConfig.protocolCheckedUrl}) center center no-repeat`, backgroundSize: 'contain'}"
                class="common-protocol__checke-box-checked"
            />
            <div
                v-else
                :style="{ borderColor: styleConfig.protocolColor}"
                class="common-protocol__checke-box-normal"
            />
        </div>
        <div class="common-protocol__text">已阅读并同意<a
            :style="{ color: styleConfig.protocolColor }"
            @click="jump()"
            href="javascript:void(0)"
        >《用户协议》</a>
            和 <a
                :style="{ color: styleConfig.protocolColor }"
                href="https://i.zhenai.com/m/near/agreement/index.html#/privacy"
            >《隐私政策》</a>
        </div>
    </div>
</template>

<script>

export default {
    name: 'CommonProtocol',
    props: {
        agreementStatus: {
            type: Number,
            default: 0,
        },
        isChecked: {
            type: Boolean,
            default: true
        },
        styleConfig: {
            type: Object,
            default: {
                textColor: "#FFFFFF",
                protocolColor: "#767DFF",
                protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20240521/1716286644947_257214.png'
            }
        }
    },
    methods: {
        jump () {
            window.location.href = 'https://i.zhenai.com/m/near/agreement/index.html#/user'
        },
        handleToggleCheck() {
            if (this.agreementStatus === 0) {
                return;
            }

            this.$emit('update:isChecked', !this.isChecked);
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.common-protocol {
    height: 36px;
    @include flex-center(row, center, center);
    font-size: 22px;
    position: relative;
    &__checke-box {
        margin-right: 8px;
        margin-top: 2px;
        width: 26px;
        height: 26px;
        &-normal {
            width: 100%;
            height: 100%;
            border-width: 2px;
            border-style: solid;
            border-radius: 50%;
        }
        &-checked {
            width: 100%;
            height: 100%;
            border: none;
        }
    }
    &__text {
        text-align: left;
    }
    a {
        font-weight: 600;
        text-decoration: none;
    }
}
</style>
