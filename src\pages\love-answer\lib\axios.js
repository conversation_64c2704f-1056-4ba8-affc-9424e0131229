
import axios from 'axios'
axios.defaults.timeout = 12000 // 12s 超时
// http request 拦截器
axios.interceptors.request.use(
  config => {
    config.metadata = { startTime: new Date().getTime(), }
    if (config.method === 'post') {
      config.data = {
        ...config.data
    }
    }
    return config
  },
  error => {
    // 请求错误时
    return Promise.reject(error)
  }
)
// http response 拦截器
axios.interceptors.response.use(
  res => {
    return res.data
  },
  error => {
    return Promise.reject(error)
  }
)
export default axios
