import { post, get } from '@/common/utils/ajax.js';

// 定制理想恋人保存身材和穿着接口
export const _saveIdealLoverDressed = (data) => post(`/register/hook/saveIdealLoverDressed`, data);

// 引导页模特列表
export const _queryIdealLoverModelList = (data) => post('/register/hook/queryIdealLoverModelList', data);

// 获取模特信息
export const _getModelInfo = (data) => post("/register/getModelInfo.do", data);

// 获取渠道配置
export const _getChannelConfig = (data) => post("/register/getChannelConfig", data);

// 获取大表单页头部的7个随机头像
export const _getRandomAvatar = (data) => get("/register/getRandomAvatar.do", data);

// 新测试组 ext30=78 的未登录获取推荐
export const _queryIdealLoverModelListNoReg = (data) => post('/register/hook/queryIdealLoverModelListNoReg', data);
