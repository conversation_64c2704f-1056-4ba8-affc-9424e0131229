import VueRouter from "vue-router";
import IsSingle from './views/isSingle.vue';
import Quiz from './views/Quiz.vue';
import Result from './views/Result.vue';
import Message from './views/message.vue';
import TestResult from './views/testResult.vue';
import Register from './views/register.vue';
import SuccessAnimation from './views/successAnimation.vue';
import DownApp from './views/downApp.vue';



const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/message"
        },
        {
            path: "/is-single",
            name: 'IsSingle',
            component: IsSingle
        },
        {
            path: '/quiz',
            name: 'quiz',
            component: Quiz
        },
        {
            path: '/result',
            name: 'Result',
            component: Result
        },
        {
            path: '/message',
            name: 'Message',
            component: Message
        },
        {
            path: '/testresult',
            name: 'TestResult',
            component: TestResult
        },
        {
            path: '/register/:id',
            name: 'Register',
            component: Register
        },
        {
            path: '/successAnimation',
            name: 'SuccessAnimation',
            component: SuccessAnimation
        },
        {
            path: '/downApp',
            name: 'DownApp',
            component: DownApp
        },
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0, 0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
