<template>
    <div
        class="rcommd"
        ref="rcommd"
    >
        <div
            class="rcommd-group"
            :style="{backgroundColor: cmsConfig.pageColor || '#bbe8ff'}"
            v-if="planType !== 'C'"
        >
            <div class="rcommd-group-title">
                恭喜！你将{{ swipeName[0] }}以下 <span :style="{ color: cmsConfig.homeButtonColor }"> 9 </span> 个
                {{ swipeName[1] }}
            </div>
            <div
                class="rcommd-group-swipe"
                @touchstart="goTouchStart"
                @touchmove="goTouchMove"
            >
                <za-swiper
                    v-if="planA.length"
                    item-width-mode="custom"
                    :list="planA"
                    :inner-width="this.$utils.pxToRem(750)"
                    :inner-height="this.$utils.pxToRem(162)"
                    :span-gap="this.$utils.pxToRem(24)"
                    auto-play
                >
                    <template
                        slot-scope="{ item }"
                        slot="default"
                    >
                        <div
                            class="rgs-item"
                            @click="handleClickA"
                        >
                            <img
                                :src="
                                    `${item.img}?imageMogr2/thumbnail/180x150`
                                "
                                alt=""
                            />
                            <div class="rgs-item-info">
                                <h4>{{ item.name }}</h4>
                                <p>
                                    单身{{ sex === 1 ? "男生" : "女生" }}：<span
                                        class="spe"
                                        :style="{ color: cmsConfig.homeButtonColor }"
                                    >{{ item.num }}位</span>
                                </p>
                            </div>
                        </div>
                    </template>
                </za-swiper>
                <za-swiper
                    v-if="planB.length"
                    item-width-mode="custom"
                    :list="planB"
                    :inner-width="this.$utils.pxToRem(750)"
                    :inner-height="this.$utils.pxToRem(162)"
                    :span-gap="this.$utils.pxToRem(24)"
                    auto-play
                >
                    <template
                        slot-scope="{ item }"
                        slot="default"
                    >
                        <div
                            class="rgs-info"
                            @click="handleClickB"
                            :style="{'background-image' : cmsConfig.questionBodyImg1}"
                        >
                            <h4>{{ item.name }}</h4>
                            <p>
                                单身{{ sex === 1 ? "男生" : "女生" }}：<span
                                    class="spe"
                                >{{ item.num }}位</span>
                            </p>
                        </div>
                    </template>
                </za-swiper>
            </div>
        </div>
        <section class="rcommd-mod">
            <div
                class="rcommd-mod-title"
                v-html="mainTitle"
            ></div>
            <div
                class="rcommd-card"
                v-for="(model, index) in modelList"
                :key="index"
            >
                <div
                    class="rcommd-card-img"
                    :style="{
                        backgroundImage: `url(${
                            model.mainImg
                        }?imageMogr2/thumbnail/640x640)`
                    }"
                >
                    <span
                        class="rci-nail"
                        @click="handleDownload(2)"
                        :style="{ backgroundColor: cmsConfig.homeButtonColor }"
                    >{{
                        mainText
                    }}</span>
                </div>
                <div class="rcommd-card-info">
                    <div class="rci-title">
                        <h3>{{ model.name }}</h3>
                        <span></span>
                        <span></span>
                        <strong><em></em> APP在线</strong>
                    </div>
                    <div class="rci-tips">
                        {{
                            `${model.workCity} · ${model.age} · ${
                                model.education
                            } · ${model.occupation}`
                        }}
                    </div>
                    <div class="rci-imgs">
                        <template v-for="(avatar, index) in model.avatarList">
                            <img
                                :src="`${avatar}?imageMogr2/thumbnail/154x154`"
                                alt=""
                                @click="handlePreview(model.avatarList, index)"
                            />
                        </template>
                    </div>
                    <div class="rci-slogan">
                        <h4>内心恋爱独白</h4>
                        <div class="rci-detail">
                            <div
                                class="rci-detail-txt"
                                v-if="!model.isCollapse"
                            >
                                <span
                                    @click="handleCollapse(index)"
                                >展开更多
                                </span>
                                {{ model.introduction }}
                            </div>
                            <div
                                class="rci-detail-active"
                                v-else
                            >
                                {{ model.introduction }}
                            </div>
                        </div>
                    </div>
                    <div class="rci-economy">
                        <h4>Ta的经济情况</h4>
                        <div
                            class="rci-economy-info"
                            v-if="model.house"
                        >
                            <img
                                src="https://photo.zastatic.com/images/common-cms/it/20220601/1654082550773_360355_t.png"
                                alt=""
                            />
                            <p>购房信息</p>
                        </div>
                        <div
                            class="rci-economy-info"
                            v-if="model.car"
                        >
                            <img
                                src="https://photo.zastatic.com/images/common-cms/it/20220601/1654082548157_751969_t.png"
                                alt=""
                            />
                            <p>购车信息</p>
                        </div>
                        <div class="rci-economy-info">
                            <img
                                src="https://photo.zastatic.com/images/common-cms/it/20220601/1654082566873_656655_t.png"
                                alt=""
                                @click="handleDownload(5)"
                            />
                            <p>月收入信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div
            class="rcommd-btn"
            :class="{ bounceInDown: showDownStatus }"
            :style="{ background: cmsConfig.homeButtonColor }"
            ref="rcommdBtn"
            v-show="showDownStatus"
            @click="handleDownload(1)"
        >
            {{ downText }}
        </div>
        <CommonDownloadGuideModal
            :styleConfig="{
                confirmButtonColor: '#fff',
                confirmButtonBgColor: cmsConfig.homeButtonColor
            }"
            v-model="downloadVisible"
            :page-type="planName"
        >
            <template slot="default">
                请到应用商店搜索<br />“珍爱网”下载珍爱APP
            </template>
            <template slot="desc">
                Ta在珍爱APP给你留了几条信息<br />赶快去查看吧~
            </template>
        </CommonDownloadGuideModal>
    </div>
</template>

<script>
import { _getRecommendModels } from "../api";
import CommonDownloadGuideModal from "@/common/business/components/CommonDownloadGuideModal.vue";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

import ZaSwiper from "@za/vue-za-swiper";
import "@za/vue-za-swiper/dist/style.css";
import { storage as Storage, session as Session} from "@/common/utils/storage";
import { Toast } from "vant";
import { ImagePreview } from "vant";

export default {
    name: "Rcommd",
    inject: ["cmsConfig"],
    data() {
        return {
            modelList: [],
            planName: "",
            planType: "",
            planA: [],
            planB: [],
            planNum: Storage.getItem("cityRandomNum"),
            downloadVisible: false,
            showDownStatus: false,
            registerForm: Storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            )
                ? Storage.getItem(
                    `cachedRegisterForm-${this.cmsConfig.planName}`
                )
                : {
                    birthday: "",
                    education: "",
                    workCity: ""
                },
            sex: Storage.getItem(
                `cachedRegisterForm-${this.cmsConfig.planName}`
            )
                ? Storage.getItem(
                    `cachedRegisterForm-${this.cmsConfig.planName}`
                ).gender
                : "",
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            screenHeight: 0, // 一屏高度
            timer: null
        };
    },
    components: {
        CommonDownloadGuideModal,
        ZaSwiper,
        [ImagePreview.Component.name]: ImagePreview.Component
    },
    computed: {
        swipeName() {
            if (this.planType === "A") {
                return ["参与", "活动"];
            }
            if (this.planType === "B") {
                return ["加入", "群聊"];
            }
            return [];
        },
        mainTitle() {
            if (this.planType === "A") {
                return `从 <span style="color: ${this.cmsConfig.homeButtonColor}">${
                    this.planNum
                }</span> 位${this.sex === 1 ? "男生" : "女生"}中邀请喜欢的同行`;
            }
            if (this.planType === "B") {
                return `从 <span style="color: ${this.cmsConfig.homeButtonColor}">${
                    this.planNum
                }</span> 位${this.sex === 1 ? "男生" : "女生"}中邀请喜欢的聊天`;
            }
            if (this.planType === "C") {
                return `已解锁 <span style="color: ${this.cmsConfig.homeButtonColor}">${
                    this.planNum
                }</span> 位${this.sex === 1 ? "男生" : "女生"}资料，可添加${
                    this.sex === 1 ? "他们" : "她们"
                }为好友`;
            }
        },
        mainText() {
            if (this.planType === "A") {
                return `邀请${this.sex === 1 ? "他" : "她"}同行`;
            }
            if (this.planType === "B") {
                return `邀请${this.sex === 1 ? "他" : "她"}聊天`;
            }
            if (this.planType === "C") {
                return `加为好友`;
            }
        },
        downText() {
            if (this.planType === "A") {
                return `下载APP，邀约${
                    this.sex === 1 ? "男生" : "女生"
                }与你同行`;
            }
            if (this.planType === "B") {
                return `下载APP，邀约${
                    this.sex === 1 ? "男生" : "女生"
                }群聊约会`;
            }
            if (this.planType === "C") {
                return `下载APP，与她约会吧`;
            }
        }
    },
    created() {
        this.getRecommendModels();
        this.planName = this.cmsConfig.planName;
        const planName = this.cmsConfig.planName;
        planName.replace(/A|B|C/, m => {
            this.planType = m;
        });
    },
    mounted() {
        window.addEventListener("scroll", this.handleScroll);
        this.screenHeight = document.documentElement.clientHeight;
        this.$report(8, "推荐页访问");
    },
    methods: {
        async getRecommendModels() {
            let age =
                new Date().getFullYear() -
                new Date(this.registerForm.birthday).getFullYear();
            const res = await _getRecommendModels({
                limit: 20,
                id: Z.getParam("materialId"),
                sex: this.sex,
                age,
                education: this.registerForm.education,
                workCity: this.registerForm.workCity
            });
            const modelList = res.data.modelList;

            this.modelList = modelList.slice(0, 10);
            if (this.planType === "A") {
                this.planA = res.data.planA;
            }
            if (this.planType === "B") {
                this.planB = res.data.planB;
            }
            setTimeout(() => {
                this.modelList.push(...modelList.slice(10));
            }, 500);
        },
        handleDownload(id) {
            if (this.cmsConfig.downloadStatus === 0) {
                this.downloadVisible = true;
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }

            switch (id) {
            case 1:
                this.$report(13, "推荐页-点击主按钮");
                break;
            case 2:
                this.$report(15, "推荐页-点击主图内小按钮");
                break;
            case 3:
                this.$report(16, "推荐页-点击照片区域");
                break;
            case 4:
                this.$report(17, "推荐页-点击“展开更多心里独白”");
                break;
            case 5:
                this.$report(18, "推荐页-点击“月收入”");
                break;
            }
        },
        handleScroll(e) {
            const { top } = this.$refs.rcommd.getBoundingClientRect();
            if (-top >= screen.height * 1.5) {
                this.showDownStatus = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$report(9, "推荐页-第2屏幕曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$report(10, "推荐页-第3屏幕曝光");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$report(11, "推荐页-第4屏幕曝光");
                this.reportLock.fourthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 4 &&
                !this.reportLock.fifthScreen
            ) {
                this.$report(12, "推荐页-第5屏幕曝光");
                this.reportLock.fifthScreen = true;
            }

            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleScroll);
            }
        },
        goTouchStart(e) {
            this.startX = e.touches[0].clientX;
        },
        goTouchMove(e) {
            this.moveX = e.touches[0].clientX;
            if (this.timer) {
                return;
            }
            this.timer = setTimeout(() => {
                if (this.startX - this.moveX > 0) {
                    this.startX = this.moveX = 0;
                    this.$report(14, "推荐页-手动滑动活动卡片");
                    this.timer = null;
                }
            }, 500);
        },
        handleClickA() {
            this.$toast(
                `邀请以下心仪${
                    this.sex === 1 ? "男生" : "女生"
                }和你一起参加活动`
            );
        },
        handleClickB() {
            this.$toast(
                `邀请以下心仪${
                    this.sex === 1 ? "男生" : "女生"
                }和你一起群聊约会`
            );
        },
        handleCollapse(index) {
            const current = this.modelList[index];
            this.modelList.splice(index, 1, {
                ...current,
                isCollapse: true
            });
        },
        handlePreview(avatars, index) {
            ImagePreview({
                images: avatars,
                startPosition: index
            });
        }
    }
};
</script>
<style lang="scss" scoped>
@mixin set-background($url, $size) {
    background: #eeeef4 url($url);
    background-size: 100% $size;
    background-repeat: no-repeat;
}
@mixin set-background1() {
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
@mixin set-font($color: #26273c, $size: 32px, $weight: 500, $lheight: 52px) {
    color: $color;
    font-size: $size;
    font-weight: $weight;
    line-height: $lheight;
}
.rcommd {
    color: #26273c;
    &-group {
        padding-bottom: 48px;
        &-title {
            padding: 48px 0;
            @include set-font(#AEAFB3, 40px, 600);
            text-align: center;
        }
        &-swipe {
            margin-left: 24px;
        }
        .rgs-item {
            display: flex;
            align-items: center;
            width: 568px;
            height: 100%;
            background: #fff;
            border-radius: 16px;
            > img {
                margin: 6px 24px 6px 6px;
                width: 180px;
                height: 150px;
                border-radius: 12px;
                object-fit: cover;
            }
        }
        .rgs-info {
            height: 162px;
            padding: 30px 32px;
            border-radius: 16px;
            color: #fff;
            text-align: left;
            box-sizing: border-box;
            overflow: hidden;

            > h4 {
                font-size: 28px;
                font-weight: 600;
            }
            > p {
                padding-top: 24px;
                font-size: 24px;
            }
            .spe {
                font-size: 30px;
                font-weight: 600;
            }
        }
        // .za-swiper__item:nth-child(odd) {
        //     .rgs-info {
        //         background: linear-gradient(270deg, #f3c8ff 0%, #fa9dfa 100%);
        //     }
        // }
        .rgs-item-info {
            > h4 {
                padding-bottom: 24px;
                color: #1a202c;
                font-size: 28px;
                font-weight: 600;
            }
            > p {
                color: #9395a4;
                font-size: 24px;
            }
            .spe {
                font-size: 30px;
                font-weight: 600;
            }
        }
    }
    &-mod {
        padding: 40px 24px 200px;
        @include set-background(
            "https://photo.zastatic.com/images/common-cms/it/20220601/1654074999993_785145_t.png",
            456px
        );
        &-title {
            font-size: 40px;
            font-weight: 600;
            text-align: center;
        }
    }
    &-card {
        margin-top: 60px;
        padding: 30px 30px 48px;
        background: #fff;
        box-shadow: 0 20px 40px 0 rgba(201, 203, 222, 0.1);
        border-radius: 40px;
        &-img {
            position: relative;
            height: 640px;
            border-radius: 30px;
            @include set-background1;
        }
        .rci-nail {
            position: absolute;
            left: 32px;
            bottom: 32px;
            padding: 12px 52px 12px 16px;
            color: #fff;
            font-size: 32px;
            background:
                url(https://photo.zastatic.com/images/common-cms/it/20220601/1654082153471_445009_t.png);
            background-repeat: no-repeat;
            background-size: 32px 32px;
            background-position: right 16px center;
            border-radius: 36px;
        }
        &-info {
            margin-top: 40px;
        }
        .rci-title {
            display: flex;
            align-items: center;
            height: 56px;
            > h3 {
                color: #26273c;
                font-size: 44px;
                font-weight: 600;
            }
            > span:nth-of-type(1) {
                width: 108px;
                height: 40px;
                margin-left: 20px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220601/1654082420405_81888_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
            > span:nth-of-type(2) {
                width: 148px;
                height: 40px;
                margin-right: auto;
                margin-left: 8px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220601/1654082505892_553024_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
            > strong {
                color: #aeafb3;
                font-size: 28px;
                line-height: 56px;
                > em {
                    position: relative;
                    top: -4px;
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    background: #35e2bd;
                    border-radius: 50%;
                }
            }
        }
        .rci-tips {
            margin-top: 16px;
            color: #6c6d75;
            font-size: 28px;
        }
        .rci-imgs {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            font-size: 0;
            > img {
                width: 154px;
                height: 154px;
                border-radius: 16px;
            }
        }
        .rci-slogan {
            margin-top: 60px;
            > h4 {
                color: #1a202c;
                font-size: 36px;
                font-weight: 600;
            }
        }
        .rci-detail {
            position: relative;
            display: flex;
            margin-top: 42px;
            padding: 48px 24px;
            background: linear-gradient(to bottom, #ffc9f0, #fff7fb);
            border-radius: 32px;
            &::before {
                content: "";
                position: absolute;
                left: 32px;
                top: -12px;
                width: 64px;
                height: 48px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220601/1654082555954_906410_t.png)
                    no-repeat;
                @include set-background1;
            }
            &-txt {
                // display: -webkit-box;
                // -webkit-line-clamp: 3;
                // -webkit-box-orient: vertical;
                // overflow: hidden;
                position: relative;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: justify;
                line-height: 1.5;
                max-height: 4.5em;
                @include set-font;
                > span {
                    position: relative;
                    padding-right: 60px;
                    padding-left: 40px;
                    color: #aeafb3;
                    float: right;
                    clear: both;
                    background: url(https://photo.zastatic.com/images/common-cms/it/20220602/1654160674428_541883_t.png)
                        no-repeat;
                    background-size: 32px 32px;
                    background-position: center right 20px;
                    &:before {
                        content: "...";
                        position: absolute;
                        left: 30px;
                        color: #333;
                        transform: translateX(-100%);
                    }
                }
                &::before {
                    content: "";
                    float: right;
                    height: calc(100% - 42px);
                    background: red;
                }
            }
            &-active {
                line-height: 1.5;
                @include set-font;
            }
        }
        .rci-economy {
            margin-top: 60px;
            overflow: hidden;
            > h4 {
                @include set-font(#1a202c, 36px, 600, 1);
            }
            &-info {
                margin-top: 40px;
                float: left;
                text-align: center;
                margin-right: 10px;
                @include set-font(#26273c, 32px, 400, 1);
                > img {
                    width: 204px;
                    height: 112px;
                    margin-bottom: 20px;
                }
            }
            &-info:last-child {
                margin-right: 0;
            }
        }
    }
    &-btn {
        position: fixed;
        left: 46px;
        bottom: 42px;
        width: 658px;
        height: 110px;
        border-radius: 55px;
        @include set-font(#fff, 40px, 500, 110px);
        text-align: center;
    }
}
@keyframes bounceInDown {
    from,
    60%,
    75%,
    90%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        transform: translate3d(0, -3000px, 0) scaleY(3);
    }

    60% {
        opacity: 1;
        transform: translate3d(0, 25px, 0) scaleY(0.9);
    }

    75% {
        transform: translate3d(0, -10px, 0) scaleY(0.95);
    }

    90% {
        transform: translate3d(0, 5px, 0) scaleY(0.985);
    }

    to {
        transform: translate3d(0, 0, 0);
    }
}

.bounceInDown {
    animation-name: bounceInDown;
    animation-duration: 1.5s;
}
</style>
