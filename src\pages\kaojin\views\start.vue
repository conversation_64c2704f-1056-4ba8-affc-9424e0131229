<template>
    <div class="wrapper" @click="jump()">
        <img v-if="type != '1'" class="logo" src="https://photo.zastatic.com/images/common-cms/it/20240521/1716281105098_649979.png" alt="">
        <div class="content">
            <img class="member_banner" src="https://file.zastatic.com/images/common-cms/it/20240522/1716374228194_693838.gif" alt="">
            <img src="https://photo.zastatic.com/images/common-cms/it/20240522/1716357708917_230710.png" alt="" class="mid_banner">
            <div class="btn">立即参加</div>
        </div>
        <div v-if="type == '1'" class="footer-text">深圳市珍惜信息技术有限公司</div>
    </div>
</template>

<script>
export default {
    name: "Start",

    data() {
        return {
            type: '', // 默认是靠近  1：珍惜
        };
    },
    created() {
        this.type = Z.getParam("type")

    },
    mounted () {
        this.$report(1, "首页-访问");
        this.$report(3000, "首页访问（监控）");
    },
    methods: {
        jump() {
            this.$report(2, "首页-按钮点击");

            this.$router.push({ path: '/register/0'});
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    position: relative;
    text-align: center;
    height: 100vh;
    background-size: contain;
    background-size: cover;
    .logo {
        width: 71px;
        height: 71px;
        position: absolute;
        top: 32px;
        left: 37px;
    }
    .content {
        padding-top: 150px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }
    .member_banner {
        width: 660px;
        height: 280px;
    }
    .mid_banner {
        width: 100vw;
        margin-top: 22px;
    }
    .bg {
        width: 750px;
    }
    .btn {
        width: 550px;
        height: 115px;
        border-radius: 64px;
        background: #222833;
        line-height: 115px;
        text-align: center;
        color: #3cede1;
        margin-top: 79px;
    }
    .footer-text {
        font-size: 22px;
        text-align: center;
        color: #000;
        position: fixed;
        bottom: 60px;
        left: 240px;
    }
}
</style>
