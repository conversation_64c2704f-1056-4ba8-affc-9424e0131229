<template>
    <div class="info-avatar-wrapper">
        <div
            class="info-avatar"
            :class="modelInfo.mainImg ? '' : 'blur'"
            :style="{
                backgroundImage: modelInfo.mainImg
                    ? `url(${modelInfo.mainImg})`
                    : `url(${require('../../assets/imgs/default-main.png')})`
            }"
        >
            <div class="avatar__icon--online">
                APP在线
            </div>
            <div class="avatar__icon--location">
                在您附近
            </div>
            <div
                class="avatar__icon--hello"
                @click="openModal('modalDownload', {})"
            >
                打个招呼
            </div>
        </div>

        <div v-if="!modelInfo.mainImg" class="info-avatar--blur">
            <div class="avatar--blur__lock"></div>
            <div class="avatar--blur__text">
                您有一次约会机会
                <span>选择约会的对象，可解锁Ta的全部资料</span>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

export default {
    props: {},
    components: {},
    data() {
        return {};
    },
    computed: {
        ...mapState(["modelInfo"])
    },
    created() {},
    mounted() {},
    methods: {
        openModal(modalType, modalParam) {
            // 打桩
            this.$report(8, "翻牌下载页-招呼按钮点击", {
                ext16: 1, // 1 投放版 2 达人版
                ext18: this.$route.path === "/info" ? 1 : 2 // 1 无盲盒 2 有盲盒
            });

            this.$emit("open-modal", modalType, modalParam);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.info-avatar-wrapper {
    position: relative;
    margin: 40px auto 0;
    width: 702px;
    height: 702px;
    overflow: hidden;
    border-radius: 32px;
    z-index: 3;
}

.info-avatar {
    width: 100%;
    height: 100%;
    border-radius: 32px;
    font-size: 26px;
    color: #fdfdfd;
    @include set-img("../../assets/imgs/default-main.png");
}

.avatar__icon--online {
    position: absolute;
    top: 550px;
    left: 32px;
    padding-left: 54px;
    width: 194px;
    height: 48px;
    background: rgba($color: #26273c, $alpha: 0.5);
    border-radius: 24px;
    line-height: 48px;
}

.avatar__icon--online::before {
    content: "";
    position: absolute;
    left: 22px;
    top: 50%;
    transform: translateY(-50%);
    width: 22px;
    height: 22px;
    background-image: url(../../assets/imgs/icon-online.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.avatar__icon--location {
    position: absolute;
    top: 622px;
    left: 32px;
    padding-left: 54px;
    width: 194px;
    height: 48px;
    background: rgba($color: #26273c, $alpha: 0.5);
    border-radius: 24px;
    line-height: 48px;
}

.avatar__icon--location::before {
    content: "";
    position: absolute;
    left: 22px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 24px;
    background-image: url(../../assets/imgs/icon-location.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.avatar__icon--hello {
    position: absolute;
    top: 582px;
    left: 468px;
    width: 201px;
    height: 88px;
    background: #787cff;
    border-radius: 44px;
    box-shadow: 3px 5px 36px 7px rgba(120, 124, 255, 0.4);

    font-size: 32px;
    font-weight: 700;
    color: #fdfdfd;
    line-height: 88px;
    text-align: center;
}

.info-avatar--blur {
    position: absolute;
    left: 0;
    top: 0;
    background: rgba($color: #464849, $alpha: 0.5);

    width: 100%;
    height: 100%;
    border-radius: 32px;
}

.avatar--blur__lock {
    position: absolute;
    left: 50%;
    top: 153px;
    transform: translateX(-50%);
    @include set-img("../../assets/imgs/lock.png");
    width: 32px;
    height: 38px;
}

.avatar--blur__text {
    margin: 220px auto 0;
    width: 600px;

    font-size: 36px;
    font-weight: 700;
    color: #ffffff;
    line-height: 53px;
    text-align: center;
    span {
        display: block;
        font-size: 32px;
    }
}
</style>
