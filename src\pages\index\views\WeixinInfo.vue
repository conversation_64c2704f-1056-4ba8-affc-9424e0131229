<template>
    <div class="weixin-wrapper">
        <info-header />
        <div
            class="qrcode"
            :style="{ backgroundImage: `url(${cmsConfig.qRcodeBasemap})` }"
        >
            <div class="qrcode-text">
                {{ cmsConfig.formWechatText }}
            </div>
            <img
                v-if="!!imgUrl"
                class="qrcode-img"
                :src="imgUrl"
                alt="企微二维码图片"
                ref="img"
                @load="handleQrCodeLoaded"
            />
            <div
                class="qrcode-button"
                :style="{ backgroundColor: cmsConfig.miniButtonColor}"
                v-if="cmsConfig.isJumpMini === '1'"
                @click="handleJump"
            >
                {{ cmsConfig.miniButtonText }}
            </div>
            <div
                class="qrcode-tips"
                v-else
            >
                长按可保存二维码图片
            </div>
        </div>
        <div class="steps">
            <div class="steps-title">
                若无法保存图片，请按以下步骤重新尝试
            </div>
            <div class="steps-item-wrapper">
                <div
                    v-for="(item, index) in stepList"
                    :key="index"
                    class="steps-item"
                >
                    <div
                        class="steps-item__img"
                        :style="{ backgroundImage: `url(${item.img})` }"
                    ></div>
                    <div class="steps-item__text">
                        {{ item.text }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex";
import { _getPrivateChannelCode } from "../js/api.js";
import { InfoHeader } from "../components/info/index.js";
import { getMiniPath } from '@/common/business/utils/wecom';

export default {
    components: {
        InfoHeader
    },
    data() {
        return {
            stepList: [
                {
                    img:
                        "https://photo.zastatic.com/images/common-cms/it/20220601/1654064410875_104816_t.png",
                    text: "1.截图保存"
                },
                {
                    img:
                        "https://photo.zastatic.com/images/common-cms/it/20220601/1654064410923_761295_t.png",
                    text: "2.打开微信"
                },
                {
                    img:
                        "https://photo.zastatic.com/images/common-cms/it/20220601/1654064410928_450693_t.png",
                    text: "3.扫码添加"
                }
            ],
            imgUrl: "",
            wxUrl: ""
        };
    },
    computed: {
        ...mapState(["cmsConfig"])
    },
    async created() {
        let flagFilled = localStorage.getItem("flagFilled");

        // 没有注册态，跳回大表单页
        if (flagFilled !== "1") {
            this.$router.push({
                path: "index"
            });
        }

        if (this.cmsConfig.schemeType === 11) {
            const { originId, crmId } = this.cmsConfig;
            try {
                const res = await _getPrivateChannelCode({ originId, crmId });
                // this.imgUrl = `${
                //     res.data.qrCodeUrl
                // }?imageMogr2/thumbnail/370x370`;
                this.imgUrl = res.data.qrCodeUrl;
                this.$report(7, "H5添加企微页-二维码曝光", {
                    ext16: 1,
                    ext18: this.cmsConfig.crmId
                });
            } catch (e) {
                console.error(e);
                this.$toast("网络异常");
            }
        } else {
            this.imgUrl = this.cmsConfig.formWechatImg;
            this.$report(7, "H5添加企微页-二维码曝光", {
                ext16: 1
            });
            // this.imgUrl =
            //     "https://photo.zastatic.com/images/cms/banner/20220901/4466367356934151.png";
        }
        this.$nextTick(async() => {
            this.initLongPress();
            this.wxUrl = await getMiniPath(this.imgUrl, null, 'pages/bigformWecom/bigformWecom', Z.getParam('materialId'));
        });

        // 注册态只能用一次
        localStorage.setItem("flagFilled", "0");

        // 清空注册信息
        localStorage.setItem("localFormInfo", "");
        localStorage.setItem("localRegisterInfo", "");
        localStorage.setItem("defaultBirthday", "");
        localStorage.setItem("defaultWorkCity", "");
    },
    mounted() {
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
    },
    methods: {
        handleLongPress() {
            this.$report(6, "H5添加企微页-长按保存图片", {
                ext16: 1
            });
        },
        initLongPress() {
            const _this = this;
            let timeout = 0;
            this.$refs.img.addEventListener(
                "touchstart",
                () => {
                    timeout = setTimeout(_this.handleLongPress, 300); // 长按时间超过800ms，则执行传入的方法
                },
                false
            );
            this.$refs.img.addEventListener(
                "touchend",
                () => {
                    clearTimeout(timeout); // 长按时间少于800ms，不会执行传入的方法
                },
                false
            );
        },
        handleQrCodeLoaded() {
            this.$report(8, "H5添加企微页-二维码曝光-加载成功", {
                ext16: 1,
                ext18: this.cmsConfig.crmId || ""
            });
        },
        handleJump() {
            this.$report(9, "H5添加企微页-点击跳转小程序", {
                ext16: 1,
                ext18: this.cmsConfig.crmId || ""
            });

            setTimeout(() => {
                window.location.href = this.wxUrl;
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";
@import "../index.scss";

.weixin-wrapper {
    padding-bottom: 262px;
}

.qrcode {
    position: relative;
    margin: 45px auto 0;
    width: 690px;
    height: 801px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    overflow: hidden;
    text-align: center;

    &-text {
        position: absolute;
        top: 20px;
        left: 20px;
        padding: 20px 30px;
        width: 653px;
        height: 175px;
        @include set-img(
            "https://photo.zastatic.com/images/common-cms/it/20220601/1654065659074_911250_t.png"
        );
        font-weight: 700;
        font-size: 36px;
        color: #000000;
        letter-spacing: 0;
        line-height: 54px;
        text-align: left;
    }

    &-img {
        margin: 211px auto 0;
        width: 370px;
        height: 370px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        -webkit-touch-callout: default;
    }

    &-tips {
        margin: 86px auto 0;
        font-weight: 700;
        font-size: 36px;
        color: #0d0d0d;
        line-height: 28px;
        text-align: center;
    }

    &-button {
        @include flex-center(row, center, center);
        width: 642px;
        height: 102px;
        border: 4px solid #FFFFFF;
        line-height: 102px;
        border-radius: 48px;
        background: #000000;
        padding: 35px 33px;
        margin: 52px auto 0;
        font-weight: 500;
        font-size: 32px;
        color: #FFFFFF;
    }
}

.steps {
    &-title {
        margin: 60px auto 0;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        text-align: center;
        line-height: 44px;
    }
    &-item-wrapper {
        margin: 32px auto 0;
        @include set-flex(space-between, center);
        padding: 0 74px;
    }
    &-item {
        &__img {
            margin: 0 auto;
            width: 120px;
            height: 120px;
            background-size: 100% 100%;
        }
        &__text {
            margin: 32px auto 0;
            font-size: 28px;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            line-height: 44px;
        }
    }
}
</style>
