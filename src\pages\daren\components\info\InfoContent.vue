<template>
    <div class="info-content-wrapper">
        <div class="content__list">
            <div    
                v-if="modelInfo.sex === 0 && (modelInfo.house === 2 || modelInfo.vehicle === 1)"
                class="list__logo--car"
            ></div>
            <!-- <div class="list__logo--phone" @click="openModal('modalDownload',{},'phoneButton')">查看完整手机号</div>
            <div class="list__logo--wechat" @click="openModal('modalDownload',{},'weChatButton')">查看完整微信号</div> -->
            <div class="list__title">Ta的详细资料</div>

            <div class="list__item">
                <div class="list__item__label">昵称</div>
                <div class="list__item__value">{{modelInfo.name}}</div>
            </div>

            <div class="list__item">
                <div class="list__item__label">珍爱会员ID</div>
                <div class="list__item__value">{{modelInfo.memberID}}</div>
            </div>

            <div class="list__item" v-if="modelInfo.educationString !== '未填写'">
                <div class="list__item__label">学历</div>
                <div class="list__item__value">{{modelInfo.educationString}}</div>
            </div>

            <div class="list__title" v-if="hasHobby">Ta的兴趣爱好</div>

            <div class="list__item" v-if="modelInfo.hobbyFood">
                <div class="list__item__label">最喜欢吃</div>
                <div class="list__item__value">{{modelInfo.hobbyFood}}</div>
            </div>

            <div class="list__item" v-if="modelInfo.hobbySong">
                <div class="list__item__label">最喜欢听</div>
                <div class="list__item__value">{{modelInfo.hobbySong}}</div>
            </div>

            <div class="list__item" v-if="modelInfo.hobbyBook">
                <div class="list__item__label">最喜欢的书</div>
                <div class="list__item__value">{{modelInfo.hobbyBook}}</div>
            </div>

            <div class="list__item" v-if="modelInfo.hobbyName">
                <div class="list__item__label">最欣赏的名人</div>
                <div class="list__item__value">{{modelInfo.hobbyName}}</div>
            </div>

            <div class="list__item" v-if="modelInfo.hobbyThing">
                <div class="list__item__label">最喜欢做的事</div>
                <div class="list__item__value">{{modelInfo.hobbyThing}}</div>
            </div>
            
        </div>

        <div class="content__photo" v-if="hasMoment">
            <div class="photo__title">
                <div class="photo__title--main">Ta的动态</div>
                <div class="photo__title--sub" @click="openModal('modalDownload',{},'momentButton')">查看所有{{momentCount}}条</div>
            </div>

            <div class="content-block">
                <img class="photo__block--main" :src="modelInfo['momentImg1']" >
                <div class="photo__block--sub">
                    <template v-for="(item,index) in photoArr">
                        <img 
                            :key="index"
                            class="block--sub__item"
                            v-if="modelInfo['momentImg'+(index+2)]"
                            :src="modelInfo['momentImg'+(index+2)]" 
                        >
                    </template>
                    <!-- 更多图片 -->
                    <div class="block--sub__item--last" @click="openModal('modalDownload',{},'photoButton')">更多照片</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {mapState,mapMutations,mapActions} from 'vuex';
import {getRandomInt} from '@/common/utils/tools';
import {reportKibana} from '@/common/utils/report.js';

export default {
    components:{
    },
    data() {
        
        const photoArr = ['','',''];
        
        return {
            momentCount:getRandomInt(10,50),
            photoArr
        };
    },
    computed:{
        ...mapState([
            'modelInfo'
        ]),
        hasHobby(){
            return  this.modelInfo.hobbyFood || 
                    this.modelInfo.hobbySong ||
                    this.modelInfo.hobbyBook ||
                    this.modelInfo.hobbyName ||
                    this.modelInfo.hobbyThing
        },
        hasMoment(){
            return  this.modelInfo.momentImg1 &&
                    this.modelInfo.momentImg2 &&
                    this.modelInfo.momentImg3 &&
                    this.modelInfo.momentImg4 
        }
    },
    created(){
    },
    mounted(){
        // this.jump()
        
    },
    methods:{
        openModal(modalType,modalParam,from){
            let description,
                accessPoint;
            if(from === 'phoneButton'){
                description = '翻牌下载页-查看手机按钮点击';
                accessPoint = 9;
            } else if (from === 'weChatButton'){
                description = '翻牌下载页-查看微信按钮点击';
                accessPoint = 10;
            } else if (from === 'momentButton'){
                description = '翻牌下载页-查看动态按钮点击';
                accessPoint = 11;
            }
            // 打桩
            reportKibana(
                "导量H5大表单翻牌",
                accessPoint,
                description,
                {
                    ext16: 2,
                    ext18:this.$route.path === '/info'? 1 : 2 // 1 无盲盒 2 有盲盒
                }
            );
            this.$emit("open-modal",modalType,modalParam);
        }   
    },
   
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.info-content-wrapper{
    position: relative;
    margin: -70px auto 0;
    width: 702px;
    
}

.content__list{
    // 重合部分高度为50px
    padding-top: 50px;
    padding-left: 32px;
    padding: 50px 32px 64px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #FFFFFF);
    border-radius: 32px;
    border: 2px solid #000;
}

.list__logo--car{
    position: absolute;
    top: 102px;
    right: -20px;
    width: 198px;
    height: 198px;
    @include set-img('../../assets/imgs/car-logo.png')
}

.list__logo--phone{
    position: absolute;
    top: 606px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787CFF;
    border-radius: 24px;

    font-size: 24px;
    color: #FFFFFF;
    line-height: 48px;
    text-align: center;
}

.list__logo--wechat{
    position: absolute;
    top: 686px;
    right: 49px;
    width: 234px;
    height: 48px;
    background: #787CFF;
    border-radius: 24px;

    font-size: 24px;
    color: #FFFFFF;
    line-height: 48px;
    text-align: center;
}

.list__title{
    margin-top: 82px;
    font-size: 36px;
    font-family: Source Han Sans SC;
    font-weight: 700;
    color: #26273C;
    // line-height: 63px;
}

.list__item{
    @include set-flex(flex-start,flex-start);
    margin-top: 50px;
    font-size: 32px;
    line-height: 30px;
}

.list__item__label{
    position: relative;
    display: inline-block;
    color: #6C6D75;
    flex-shrink: 0;
    line-height: 40px;
}

.list__item__value{
    position: relative;
    padding-left: 32px;
    color: #26273C;
    line-height: 40px;
}

.list__item__value::before{
    content:':';
    position: absolute;
    left: 5px;
    top: 18px;
    // top: 50%;
    color: #6C6D75;
    transform: translateY(-50%);
}

.content__photo{
    margin: 39px auto 0;
    width: 702px;
    height: 518px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #FFFFFF);
    border-radius: 32px;
    border: 2px solid #000;
}

.photo__title{
    @include set-flex(space-between,center);
    padding: 0 33px;
    height: 140px;

}

.photo__title--main{
    font-size: 36px;
    font-weight: 700;
    color: #26273C;
    line-height: 140px;
}

.photo__title--sub{
    position: relative;
    padding-right: 28px;
    font-size: 26px;
    font-weight: 400;
    color: #6C6D75;
    line-height: 140px;
}

.photo__title--sub::after{
    content: '';
    @include set-img('../../assets/imgs/right-arrow.png');
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-52%);
    width: 32px;
    height: 32px;
    font-weight: 400;
    color: #6C6D75;
    line-height: 140px;
}

.content-block{
    @include set-flex(space-between,center);
    margin: 0 auto;
    width: 640px;

}

.photo__block--main{
    width: 315px;
    height: 315px;
    border-radius: 16px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    object-fit: cover;
}

.photo__block--sub{
    @include set-flex(space-between,center);
    flex-wrap: wrap;
    align-content: space-between;
    width: 315px;
    height: 315px;
}

.block--sub__item{
    width: 154px;
    height: 154px;
    border-radius: 16px;
    // background: grey;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    object-fit: cover;
}

.block--sub__item--last{
    width: 154px;
    height: 154px;
    background-color: rgba($color: #26273C, $alpha: 0.1);
    border-radius: 16px;

    font-size: 26px;
    font-weight: 400;
    color: rgba($color:#26273C,$alpha:0.5);
    line-height: 154px;
    text-align: center;
}

</style>
