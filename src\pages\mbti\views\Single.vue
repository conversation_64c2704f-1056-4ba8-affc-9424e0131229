<template>
    <div class="single">
        <template v-if="cmsConfig.reportViewType === 4">
            <div class="caption">请选择你要测试的版本</div>
            <div class="desc">
                每年近<span>万人</span>参与测试 <br />
                请根据你的实际情况选择测试版本，这样才能获得最准确的分析报告
            </div>
            <div class="choice">
                <div
                    class="choice-info"
                    @click="goChoose(2)"
                    :class="{ active: single == '2' }"
                ></div>
                <div
                    class="choice-info"
                    @click="goChoose(1)"
                    :class="{ active: single == '1' }"
                ></div>
            </div>
            <!--<div class="rcommd">
                <div class="rcommd-title">
                    已有<strong>{{ num }}</strong
                    >人生成测试报告
                </div>
                <div class="broadcast-wrap spe" ref="broadcast">
                    <ul
                        class="broadcast"
                        :style="{
                            transform: 'translateY(' + topNum + 'px)'
                        }"
                        :class="{ anim: isAnim }"
                    >
                        <li
                            v-for="list in Lists"
                            :key="list.text"
                            class="broadcast-item"
                        >
                            <img :src="list.avatar" alt="" />
                            <p>{{ list.text }}</p>
                        </li>
                    </ul>
                </div>
            </div>-->
        </template>
        <template v-else>
            <h3 class="title"></h3>
            <h4 class="subtitle">
                每年近<strong></strong>万人参与测试
            </h4>
            <div class="tips">
                请根据你的实际情况选择测试版本，这样才能获得最准确的分析报告
            </div>
            <div class="panel">
                <div
                    class="panel-info"
                    @click="goChoose(2)"
                    :class="{ active: single == '2' }"
                >
                    <div
                        class="img"
                        :style="{ backgroundImage: `url(${pic2})` }"
                    ></div>
                    <div class="mark">
                        非单身版
                    </div>
                    <div class="time">
                        完成需1~3分钟
                    </div>
                </div>
                <div
                    class="panel-info"
                    @click="goChoose(1)"
                    :class="{ active: single == '1' }"
                >
                    <div
                        class="img"
                        :style="{ backgroundImage: `url(${pic1})` }"
                    ></div>
                    <div class="mark">
                        单身版
                    </div>
                    <div class="time">
                        完成需1~3分钟
                    </div>
                </div>
            </div>
            <div class="result">
                <div class="result-title">
                    已有<strong>{{ num }}</strong
                    >人生成测试报告
                </div>
                <div class="result-info">
                    <div class="broadcast-wrap" ref="broadcast">
                        <ul
                            class="broadcast"
                            :style="{
                                transform: 'translateY(' + topNum + 'px)'
                            }"
                            :class="{ anim: isAnim }"
                        >
                            <li
                                v-for="list in Lists"
                                :key="list.text"
                                class="broadcast-item"
                            >
                                <img :src="list.avatar" alt="" />
                                <p>{{ list.text }}</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import { storage } from "../lib/utils.js";
import { mapState } from "vuex";
import { get } from "@/common/utils/ajax.js";

export default {
    name: "Single",
    data() {
        return {
            num: Math.floor(Math.random() * 400000) + 100000,
            single: "",
            topNum: 0,
            timer: null,
            broadcastHeight: 0,
            isAnim: false,
            Lists: [],
            pic1:
                "https://photo.zastatic.com/images/common-cms/it/20220425/1650869885340_88984_t.png",
            pic2:
                "https://photo.zastatic.com/images/common-cms/it/20220425/1650869887661_790636_t.png"
        };
    },

    computed: {
        ...mapState(["cmsConfig", "resourceKey"])
    },

    created() {
        this.single = storage.getItem("single");
        this.getModelInfos();
        this.$reportKibana(this.resourceKey, 2, "是否单身页访问");
    },
    mounted() {
        this.$nextTick(() => {
            this.broadcastHeight = this.$refs.broadcast.clientHeight;
        });

        this.timer = setInterval(() => {
            this.scrollUp();
            this.num += 3;
        }, 2000);
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
    methods: {
        scrollUp() {
            this.topNum = -this.broadcastHeight;
            this.isAnim = !this.isAnim;
            setTimeout(() => {
                this.Lists.push(this.Lists[0]);
                this.Lists.shift();
                this.topNum = 0;
                this.isAnim = !this.isAnim;
            }, 800);
        },
        async getModelInfos() {
            const Lists = (await get("/register/getRandomAvatar.do")).data.list;
            Lists.forEach(list => {
                list.text = `${list.name}在${Math.floor(Math.random() * 9) +
                    1}分钟前生成了【MBTI恋爱人格报告】`;
            });

            this.Lists = Lists.map(list => {
                return {
                    avatar: list.avatar,
                    text: list.text
                };
            });
        },
        goChoose(single) {
            const description =
                single === 1
                    ? "是否单身页-选择单身版"
                    : "是否单身页-选择非单身版";
            const accessPoint = single === 1 ? 3 : 4;
            this.$reportKibana(this.resourceKey, accessPoint, description);

            storage.setItem("single", single);
            this.single = single;
            setTimeout(() => {
                this.$router.push({
                    path: "/quiz/0"
                });
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
.single {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
        width: 622px;
        height: 48px;
        background: url(../assets/images/single-title.png) no-repeat;
        background-size: 100% 100%;
    }
    .caption {
        position: relative;
        left: 32px;
        width: 100%;
        height: 56px;
        padding-left: 76px;
        margin-top: -16px;
        color: #fff;
        font-size: 36px;
        font-weight: 500;
        text-align: left;
        line-height: 56px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577029972_719710_t.png)
            no-repeat;
        background-size: 60px 56px;
        background-position: left center;
    }
    .desc {
        padding: 32px 90px 24px 32px;
        color: #cbcad2;
        font-size: 28px;
        line-height: 42px;
        > span {
            color: #5ad9ff;
            font-size: 36px;
            font-weight: 600;
        }
    }
    .choice {
        display: flex;
        &-info {
            width: 276px;
            height: 626px;
        }
        &-info:nth-of-type(1) {
            margin-right: 84px;
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577018494_38397_t.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            &.active {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579033804_136971_t.png);
            }
        }
        &-info:nth-of-type(2) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577018150_793119_t.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            &.active {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579033816_108846_t.png);
            }
        }
    }
    .rcommd {
        margin-top: 48px;
        height: 196px;
        background: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.79) 0%,
            rgba(83, 110, 255, 0) 84%
        );
        border-radius: 0 0 32px 32px;
        &-title {
            width: 686px;
            height: 64px;
            padding-left: 112px;
            color: #fff;
            font-size: 32px;
            line-height: 64px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579479727_582408_t.png)
                no-repeat;
            background-size: 100% 100%;
            > strong {
                color: #5ad9ff;
                font-weight: 600;
            }
        }
    }
    .panel {
        display: flex;
        margin-top: 80px;

        .panel-info {
            position: relative;
            z-index: 1;
            width: 304px;
            height: 488px;
            text-align: center;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650527934681_899357_t.png)
                no-repeat;
            background-size: 100% 100%;
            .img {
                position: relative;
                z-index: 2;
                width: 142px;
                height: 128px;
                background-repeat: no-repeat;
                background-size: 142px 114px;
                margin: 70px auto 0;
            }
        }
        .panel-info:nth-of-type(1) {
            margin-right: 54px;
        }
        .panel-info:nth-of-type(2) {
            .img {
                position: relative;
                top: -2px;
                left: -1px;
                width: 142px;
                height: 128px;
                background-repeat: no-repeat;
                background-size: 126px 128px;
                background-position: center center;
            }
        }
        .mark {
            margin-top: 118px;
            color: #00ffff;
            font-size: 36px;
            font-weight: 500;
        }
        .time {
            margin-top: 20px;
            color: #c9c8ce;
            font-size: 28px;
        }
        .active {
            background: none;
            &::after {
                content: "";
                position: absolute;
                z-index: -1;
                left: -14px;
                top: -14px;
                width: 332px;
                height: 516px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650527934433_974684_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
        }
    }

    .result {
        width: 686px;
        height: 228px;
        margin-top: 100px;
        margin-bottom: 100px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650527931132_594581_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .result-title {
        padding-top: 18px;
        color: #ffffff;
        font-size: 32px;
        text-align: center;
        > strong {
            color: #00ffff;
            font-size: 32px;
            font-weight: 600;
        }
    }
    .subtitle,
    .tips {
        max-width: 622px;
        text-align: center;
        color: #fff;
        font-size: 28px;
        line-height: 48px;
    }
    .subtitle {
        margin-top: 48px;
        font-weight: 400;
        > strong {
            position: relative;
            top: 2px;
            color: #00ffff;
            font-size: 36px;
            font-weight: 600;
        }
    }
    .broadcast-wrap {
        position: relative;
        height: 80px;
        margin: 60px 32px 0;
        overflow: hidden;
        &.spe {
             margin: 26px 32px 0;
        }
    }
    .broadcast {
        position: absolute;
        top: 0;
        left: 0;
    }
    .broadcast-item {
        display: flex;
        align-items: center;
        height: 80px;
        color: #c9c8ce;
        font-size: 28px;
        > img {
            width: 80px;
            height: 80px;
            margin-right: 16px;
            border-radius: 50%;
        }
        > p {
            max-width: 526px;
            line-height: 40px;
        }
    }
    .anim {
        transition: all ease 0.5s;
    }
}
</style>
