<template>
    <div class="guide-to-app">
        <div class="inner">
            <a
                class="rule-text"
                @click="handleIntroClick"
            >
                你的脱单计划数据根据37%法制制定>>
            </a>
            <div class="statistic-card">
                <div
                    class="statistic-item"
                    v-for="s in statistics"
                    :key="s.key"
                >
                    <div class="value">
                        <span class="num">{{ s.num }}</span>{{ s.unit }}
                    </div>
                    <div class="label">
                        {{ s.text }}
                    </div>
                </div>
            </div>
            <div class="acticity-card">
                <div class="title"></div>
                <div class="activity-wrapper">
                    <div
                        class="acticity-item"
                        v-for="a in acticities"
                        :key="a.key"
                    >
                        <div
                            :class="`acticity ${a.key}`"
                            @click="handleClick"
                        ></div>
                        <h5 class="name">
                            {{ a.name }}
                        </h5>
                    </div>
                </div>
            </div>
        </div>
        <div class="guide-bottom">
            <button
                class="bottom-btn"
                @click="handleClick"
            >
                立即报名查看更多活动
            </button>
            <div class="bubble">
                以上都不感兴趣？没关系，点击下方参加更多活动
            </div>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="modalVisible"
            @close-modal="closeModal"
            @ok="handleModalOk"
            :modal-type="modalType"
            :modal-param="modalParam"
        />
    </div>
</template>

<script>
import { mapState } from "vuex";
import { Modal } from "../components/common/index.js";
import {reportKibana} from '@/common/utils/report.js';
export default {
    components: {
        Modal
    },
    data() {
        return {
            acticities: [
                { key: "act1", name: "观影约会" },
                { key: "act2", name: "美食聚餐" },
                { key: "act3", name: "户外交友" },
                { key: "act4", name: "桌游活动" },
                { key: "act5", name: "圈子交友" },
                { key: "act6", name: "相亲联谊" }
            ],
            modalVisible: false,
            modalType: "",
            modalParams: {}
        };
    },
    computed: {
        ...mapState(["planInfo", 'materialId']),
        statistics() {
            const statisticMap = [[1, 8, 4, 3], [1, 6, 3, 2], [1, 4, 2, 1]];
            const { hope } = this.planInfo;
            const statistic = statisticMap[hope || 0];
            return [
                { key: "act", num: statistic[0], unit: "场", text: "活动·月" },
                {
                    key: "meet",
                    num: statistic[1],
                    unit: "人",
                    text: "结识异性·活动"
                },
                {
                    key: "date",
                    num: statistic[2],
                    unit: "人",
                    text: "保持联系"
                },
                {
                    key: "contact",
                    num: statistic[3],
                    unit: "次",
                    text: "约会·次"
                }
            ];
        }
    },
    created() {},
    mounted() {
        reportKibana("脱单计划H5", 16, "活动脱单页访问", { ext16: this.materialId });
    },
    methods: {
        handleClick() {
            reportKibana("脱单计划H5", 17, "活动脱单页点击报名按钮", { ext16: this.materialId });
            this.$router.push("/collection");
        },
        closeModal() {
            this.modalVisible = false;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.modalVisible = true;
        },
        handleModalOk() {},
        handleIntroClick() {
            this.openModal("modalMethod", {});
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.guide-to-app {
  width: 100vw;
  min-height: 100vh;
  @include set-img("../assets/images/bg-privilege.png");
  background-color: #ecf6ff;
  background-size: contain;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  padding: 340px 0 234px;
  .inner {
    .rule-text {
      display: block;
      font-size: 26px;
      font-weight: bold;
      color: #279bae;
      line-height: 38px;
      margin-left: 58px;
    }
    .statistic-card {
      display: flex;
      margin: 20px 48px;
      height: 216px;
      @include set-img("../assets/images/bg-stastic.png");
      background-size: cover;
      background-position: 0% 50%;
      .statistic-item {
        flex: 1;
        text-align: center;
        margin-top: 86px;
        .value {
          font-size: 26px;
          font-weight: bolder;
          color: #ff668a;
          line-height: 40px;
          .num {
            font-size: 40px;
            font-family: SourceHanSansCN-Heavy, SourceHanSansCN;
          }
        }
        .label {
          font-size: 24px;
          font-weight: 400;
          color: #9395a4;
          line-height: 1;
          margin-top: 20px;
        }
      }
    }
    .acticity-card {
      background-color: #f9fbfc;
      margin: 20px 48px;
      border-radius: 80px;
      .title {
        width: 542px;
        height: 82px;
        @include set-img("../assets/images/bg-friends-title.png");
        background-size: cover;
        background-position: 0% 50%;
        margin: 0 auto;
      }
      .activity-wrapper {
        margin: 0 37px;
        display: flex;
        flex-flow: row wrap;
        padding-bottom: 40px;
        .acticity-item {
          flex: 0 0 50%;
          text-align: center;
          margin-top: 46px;
          .acticity {
            width: 240px;
            height: 180px;
            margin: 0 auto;
            &.act1 {
              @include set-img("../assets/images/activity-date.png");
              background-size: contain;
            }
            &.act2 {
              @include set-img("../assets/images/activity-dinner.png");
              background-size: contain;
            }
            &.act3 {
              @include set-img("../assets/images/activity-outdoor.png");
              background-size: contain;
            }
            &.act4 {
              @include set-img("../assets/images/activity-boardgame.png");
              background-size: contain;
            }
            &.act5 {
              @include set-img("../assets/images/activity-circle.png");
              background-size: contain;
            }
            &.act6 {
              @include set-img("../assets/images/activity-blinddate.png");
              background-size: contain;
            }
          }
          .name {
            font-family: SourceHanSansCN-Medium, SourceHanSansCN;
            font-size: 26px;
            font-weight: 500;
            color: #191c32;
            line-height: 40px;
          }
        }
      }
    }
  }
  @include set-bottom-btn("../assets/images/bg-button-bubble.png");
}
</style>
