
// 组件共享的样式放在这里
// z-index
// 1-99 页面层次
// 100 mask层
// 999 modal层

// 设置图片
@mixin set-img($url) {
    background-image: url($url);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50% 0%;
}

@mixin set-flex($justify,$align) {
    display: flex;
    justify-content: $justify;
    align-items: $align;
}

.blur{
    filter: blur(4px);
}

@mixin set-bottom-btn($url) {
    .guide-bottom {
        position: fixed;
        right: 0;
        bottom: 48px;
        left: 0;
        z-index: 11;
        .bottom-btn {
            z-index: 1;
            display: block;
            height: 110px;
            width: calc(100% - 120px);
            margin: 0 60px;
            padding: 0;
            border-radius: 80px;
            font-size: 32px;
            font-weight: 500;
            color: #fff;
            background: linear-gradient(180deg, #ff7599 0%, #ff5883 100%);
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.17),
                inset 0px 2px 12px 0px rgba(255, 239, 239, 0.76);
        }
        .bubble {
            display: block;
            position: absolute;
            font-size: 20px;
            font-weight: 400;
            color: #ff7599;
            line-height: 20px;
            top: -36px;
            right: 58px;
            padding: 14px 24px 22px 26px;
            // css 类点九图实现
            border-image-source: url($url);
            border-image-slice: 20 50 42 25 fill; // 每个区域截取宽度为 10px
            border-image-width: 20px 50px 42px 25px; // 设置各个区域的图片宽度
            border-image-repeat: repeat; // 图片重复或拉伸模式
        }
    }
}
