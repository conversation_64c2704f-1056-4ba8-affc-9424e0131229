<template>
    <div class="radio-list">
        <div v-for="(item, index) in list" :key="index">
            <div 
                :class="getItemClass(item)" 
                @click="$emit('change', item)"
            >
                {{ item.label }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "radio-list",
    props: {
        number: {
            type: String
        },
        list: {
            type: Array
        }
    },
    data() {
        return {
            inited: false
        };
    },
    methods: {
        getItemClass(item) {
            const result = ["radio-list__item"];

            if (this.number === item.number) {
                result.push("radio-list__item--active");
                return result;
            }

            return result;
        }
    }
};
</script>

<style scoped lang="scss">
@mixin normal() {
    background-image: url("../assets/images/selection-bg.png");
}

@mixin active() {
    background-image: none;
    background: #fe4f06;
}

.radio-list {
    width: 526px;
    margin-top: 120px;

    &__item {
        font-weight: 700;
        font-size: 28px;
        line-height: 80px;
        text-align: center;
        margin-bottom: 16px;
        border-radius: 42px;
        background-size: cover;
        width: 528px;
        height: 82px;
        @include normal();
    }

    &__item--active {
        @include active();
    }
}
</style>
