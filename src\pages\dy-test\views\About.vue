<template>
    <div
        class="about"
        :class="{'custom': currentPage.uiType !== 'Normal'}"
    >
        <div class="about-header">
            <span
                class="back-btn"
                @click="goBack"
            ></span>
            <span
                class="login-btn"
                @click="handleLogin"
                v-if="currentPage.type === 'gender'"
            >登录</span>
        </div>
        <div class="about-title">
            {{ currentPage.label }}
        </div>
        <div class="about-subtitle">
            {{ currentPage.description }}
        </div>
        <section class="about-item">
            <keep-alive>
                <component
                    :is="currentPage.uiType"
                    :btnStyleObj="btnStyleObj"
                    :options="currentPage.options"
                    :type="currentPage.type"
                    :key="currentPage.label"
                    :page-type="planName"
                />
            </keep-alive>
        </section>
    </div>
</template>
<script>
import { education, salary, marriage } from "@/common/config/register-dictionary";
import Normal from "../components/Normal.vue";
import WorkCity from "../components/WorkCity.vue";
import Birthday from "../components/Birthday.vue";
import Gender from "../components/Gender.vue";
import Height from "../components/Height.vue";
import { session as Session } from "@/common/utils/storage";
// import { session as Session } from "@/common/utils/storage";

const desMap = [
    {
        accessPoint: 2,
        accessDes: "性别页"
    },
    {
        accessPoint: 3,
        accessDes: "工作地页"
    },
    {
        accessPoint: 4,
        accessDes: "出生年份页"
    },
    {
        accessPoint: 5,
        accessDes: "身高页"
    },
    {
        accessPoint: 6,
        accessDes: "学历页"
    },
    {
        accessPoint: 7,
        accessDes: "婚况页"
    },
    {
        accessPoint: 8,
        accessDes: "收入页"
    }
];

// const mediaGroupType = Session.getItem("mediaGroupType");
// if (mediaGroupType == "10") {
// }

export default {
    name: "About",
    data() {
        return {
            currentPage: {
                type: "",
                uiType: "",
                label: ""
            },
            btnStyleObj: {
                normal: {
                    color: "#767DFF",
                    border: "0",
                    background: "#fff",
                },
                active: {
                    color: "#fff",
                    border: "0",
                    background: "#191C32",
                }
            },
            planName: Session.getItem("planName"),
        };
    },
    components: {
        Normal,
        WorkCity,
        Birthday,
        Gender,
        Height,
    },
    created() {
        if (this.$route.params.id < this.conlists.length) {
            this.init();
        } else {
            this.$router.replace({
                path: "/animate"
            });
        }
    },
    computed: {
        conlists() {
            const list = [
                {
                    type: "gender",
                    uiType: "Gender",
                    label: "请问你的性别是",
                    description: "完善资料，为你匹配最合适的人",
                },
                {
                    type: "workCity",
                    uiType: "WorkCity",
                    label: "你的工作地区在哪里",
                    description: "资料越完整，为你推荐的异性越精准",
                },
                {
                    type: "birthday",
                    uiType: "Birthday",
                    label: "你是哪一年出生的",
                    description: "请真实填写，将为你推荐合适的异性",
                },
                {
                    type: "height",
                    uiType: "Height",
                    label: "你的身高",
                    description: "资料越完整，为你推荐的异性越精准",
                },
                {
                    type: "education",
                    uiType: "Normal",
                    label: "你的学历",
                    options: education,
                    description: "资料越完整，为你推荐的异性越精准",
                },
                {
                    type: "marriage",
                    uiType: "Normal",
                    label: "你的婚姻状况",
                    options: marriage,
                    description: "请真实填写，将为你推荐合适的异性",
                },
                {
                    type: "salary",
                    uiType: "Normal",
                    label: "你的月收入",
                    options: salary,
                    description: "资料越完整，为你推荐的异性越精准",
                }
            ];
            return list;
        }
    },
    methods: {
        init() {
            this.$report(
                desMap[this.$route.params.id].accessPoint,
                desMap[this.$route.params.id].accessDes + "访问"
            );
            this.currentPage = this.conlists[this.$route.params.id];
        },
        goBack() {
            this.$report(
                desMap[this.$route.params.id].accessPoint,
                desMap[this.$route.params.id].accessDes + "-返回按钮点击"
            );
            this.$router.back();
        },
        handleLogin() {
            this.$report(4, '登录按钮点击');
            setTimeout(function () {
                if (Z.getParam('from') === 'sapp') {
                    Z.client.invoke('ui', 'logout', { canBack: true });
                    return false;
                }
                window.location.href = 'https://i.zhenai.com/m/portal/login.html' + location.search;
            }, 200);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.about {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #F3F5F6;
    &.custom {
        &::before {
            content: "";
            position: absolute;
            z-index: -1;
            top: 0;
            right: 0;
            width: 750px;
            height: 686px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684724562516_581755_t.png');
        }
        &::after {
            display: none;
        }
    }
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        top: 0;
        width: 750px;
        height: 1400px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230522/1684724562598_300963_t.png');
    }
    &-title {
        margin: 72px 48px 0;
        font-weight: 400;
        font-size: 64px;
        color: #191C32;
        line-height: 80px;
    }
    &-subtitle {
        margin: 32px 48px 52px;
        font-weight: 400;
        font-size: 36px;
        color: #26273C;
        line-height: 44px;
    }
    &-header {
        position: relative;
        z-index: 9;
        padding-top: 20px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        .back-btn {
            width: 72px;
            height: 72px;
            margin-left: 48px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684492059170_707059_t.png');
            background-position: center center;
        }
        .login-btn {
            font-size: 30px;
            color: #26273C;
            line-height: 46px;
            margin-right: 48px;
        }
    }
    &-item {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}
/deep/ {
    .common-btn {
        height: 110px;
        font-size: 32px;
    }
}
</style>
