// 自测点
// 各投放媒体sdk
// 注意： report方法上报后必须返回 true， 用于判断是否已经上报过， 以避免sdk加载过慢导致没上报的问题
var SDK_MAP = {
    // 头条, 已验证
    // 落地页无需处理 https://ad.oceanengine.com/openapi/doc/index.html?id=1664414109218824
    toutiao: (function (){
        return {
            // sdk
            initSdk: function (id, adUrl){

            },
            report: function (id, adUrl){
                try{
                    var reportUrl = 'https://ad.toutiao.com/track/activate/?callback=__CLICKID__&link=__LINK__&event_type=__EVENTTYPE__&conv_time=__CONVTIME__';
                    reportUrl = reportUrl.replace('__LINK__', adUrl)
                        .replace('__CLICKID__', Z.getParam('clickid'))
                        .replace('__EVENTTYPE__', id)
                        .replace('__CONVTIME__',  Math.ceil(+new Date()/1000));
                    var img = new Image();
                    img.onload = img.onerror = function () {
                        img = null;
                    }
                    img.src = reportUrl;
                    return true;
                }catch(e){
                    console.log(e);
                }
            },
        }
    })(),
    // 微博，  已验证
    // 落地页需要添加微博对应的处理， 需要把非业务参数都带到注册页
    weibo: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;

                var script = document.createElement('script');
                script.type = 'text/javascript';
                script.crossOrigin = 'anonymous';
                script.src = 'https://cheka.biz.weibo.com/index';
                script.onerror = function() {
                    var request = new XMLHttpRequest();
                    var web_url = window.encodeURIComponent(window.location.href);
                    var url = 'https://cheka.biz.weibo.com/v1/error';
                    var data = {
                        "error": {
                            "url": web_url,
                            "message": "404",
                            "name": "__SDK_CDN__",
                            "detail": {}
                        }
                    }
                    request.open('POST', url, true);
                    request.send('param=' + encodeURIComponent(data));
                }
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(script, s);

                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window.wbadmt.send({
                        'eventid': id,
                        'eventtype': 'form',
                        'eventvalue': ''
                    })
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // 小米， 已验证
    // pms需要添加小米对应的处理， 需要把非业务参数都带到注册页
    // 落地页且需要植入sdk,  sdk和注册页的sdk一样
    xiaomi: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                // 注意，小米的sdk要根据转化id有相应变化
                !function(e,c){var r=e.mi_tracker=e.mi_tracker||[];r.loaded||(r.log=function(){r.push(arguments)},r.load=function(){var e=c.getElementsByTagName("script")[0],t=c.createElement("script");t.async=!0,t.src="//cdn.cnbj1.fds.api.mi-img.com/prd-static/mi_tracker/1.0.0/sdk.js",e.parentNode.insertBefore(t,e),r.loaded=!0},r.load(),r.log("view",{conversionId: id}))}(window,document);
                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window.mi_tracker.log("form", {conversionId : id});
                    return true;
                }catch(e){
                    console.log(e);
                }
            },
        }
    })(),
    // 爱奇艺 已验证
    // 无需接入sdk
    iqiyi: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){

            },
            report: function (id, adUrl){
                try{
                    var reportUrl = 'https://tc.cupid.iqiyi.com/dsp_lpapi?link=__LINK__&conv_time=__CONVTIME__&event_type=__EVENTTYPE__';
                    var referref = encodeURIComponent(window.document.referrer);
                    var time = Math.ceil(+new Date()/1000);
                    // 目前爱奇艺接口的link参数必须是 投放链接+爱奇艺在投放链接上加的参数
                    reportUrl = reportUrl.replace('__LINK__', referref)
                        .replace('__EVENTTYPE__', id)
                        .replace('__CONVTIME__',  time);
                    Z.ajax({
                        type: 'GET',
                        url: reportUrl,
                        dataType: 'json',
                        success: function (results) {
                            Z.tj.kibana({
                                resourceKey: '注册页爱奇艺ocpx回传成功',
                                ext9: Z.platform.isIos ? 'ios' : 'android',
                                ext10: id,
                                ext11: time,
                                ext12: referref,
                                ext13: results ? results.status : undefined,
                                ext14: results ? results.message : undefined,
                            });
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            Z.tj.kibana({
                                resourceKey: '注册页爱奇艺ocpx回传失败',
                                ext9: Z.platform.isIos ? 'ios' : 'android',
                                ext10: id,
                                ext11: time,
                                ext12: referref,
                            });
                        }
                    });
                    Z.tj.kibana({
                        resourceKey: '注册页爱奇艺ocpx回传日志',
                        ext9: Z.platform.isIos ? 'ios' : 'android',
                        ext10: id,
                        ext11: time,
                        ext12: referref,
                    });
                    return true;
                }catch(e){
                    console.log(e);
                }
            },
        }
    })(),
    // 百度
    // 落地页无需接入sdk
    baidu: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                // 因ejs已全量接入百度，所以无需再次接入，后面代码是为了避免ejs删除代码做的兼容
                if(window._agl && window._agl.push){
                    return;
                }

                window._agl = window._agl || [];
                (function () {
                    _agl.push(
                        ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
                    );
                    (function () {
                        var agl = document.createElement('script');
                        agl.type = 'text/javascript';
                        agl.async = true;
                        agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
                        var s = document.getElementsByTagName('script')[0];
                        s.parentNode.insertBefore(agl, s);
                    })();
                })();

                hasInitSdk = true;
            },
            report: function (){
                try{
                    window._agl && window._agl.push(['track', ['success', {t: 3}]])
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // uc
    // 落地页需要接入sdk， 和注册页的sdk一样
    uc: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;

                (function(w,d,t,s,q,m,n){if(w.utq)return;q=w.utq=function(){q.process?q.process(arguments):q.queue.push(arguments);};q.queue=[];m=d.getElementsByTagName(t)[0];n=d.createElement(t);n.src=s;n.async=true;m.parentNode.insertBefore(n,m);})(window,document,'script','https://image.uc.cn/s/uae/g/0s/ad/utracking.js');utq('set', 'convertMode', true);utq('set', 'trackurl', 'huichuan.sm.cn/lp');

                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    typeof window.utq==='function' && window.utq('track', 'CompleteRegistration', id);
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // 网易
    // 落地页不需要接入sdk
    netease: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                var ns = document.createElement('script');ns.type = 'text/javascript';ns.async = true;
                ns.src = 'https://163h5.nos-jd.163yun.com/h5/libs/analyze.js';
                var f = document.getElementsByTagName('script')[0];f.parentNode.insertBefore(ns,f);
                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window._nfe && typeof window._nfe.report==='function' && window._nfe.report({convert_id : id,convert_method : '1' ,convert_type : '4'})
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // 快手
    kuaishou: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;

                (function (root) {
                    var ksscript = document.createElement('script'); ksscript.type = 'text/javascript'; ksscript.async = true;
                    ksscript.src = 'https://static.yximgs.com/udata/pkg/ks-ad-trace-sdk/ks-trace.0.0.8.beta.js';
                    ksscript.onerror = function () {
                        var request = new XMLHttpRequest();
                        var cb = (function(t){var e=new RegExp("(^|&)"+t+"=([^&|#]*)(&|#|$)"),n=window.location.href.indexOf('?')>-1?window.location.href.split('?')[1].match(e):null;return null!=n?decodeURIComponent(n[2]):null}('callback'));
                        var url = 'https://e.kuaishou.com/rest/log/activate?jsEventType=3&callback=' + cb + '&eventType=4&eventTime=' + Date.now();
                        request.open('GET', url, true);
                        request.send(null);
                    }
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ksscript, s);
                })(window);

                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window._ks_trace && typeof window._ks_trace.push==='function' && window._ks_trace.push({ event: 'click3'});
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // vivo
    vivo: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                (function (root) {
                    var script = document.createElement('script'); script.type = 'text/javascript'; script.async = true;
                    script.src = 'https://sspstatic.vivo.com.cn/ssp/js/vadevent.1.0.0.js';
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(script, s);
                })(window);
                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    var actName = 'submit';
                    var actProp ={act:'submit', name:'转化组件'};
                    window.VAD_EVENT && typeof window.VAD_EVENT.sendAction==='function' && window.VAD_EVENT.sendAction(actName, actProp);
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // B站
    bilibili: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                (function (root) {
                    var script = document.createElement('script'); script.type = 'text/javascript'; script.async = true;
                    script.src = 'https://s1.hdslb.com/bfs/cm/cm-sdk/static/js/track-collect.js';
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(script, s);
                })(window);
                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window.bilicm.mbase.send_data();
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
    // 喜马拉雅
    xmly: (function (){
        var hasInitSdk = false;
        return {
            // sdk
            initSdk: function (id, adUrl){
                if(hasInitSdk)return;
                (function (win, doc, src, version) {
                    var sc = doc.createElement('script');
                    sc.type = "text/javascript";
                    sc.src = src + '?v=' + version;
                    win.XMLY_ADOCPC_APPKEY = 'a767013c267c8fe443ea9ba4109eedff';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(sc, s);
                    var xmlyAdLog = win.xmlyAdLog = win.xmlyAdLog || [];
                    xmlyAdLog.track = xmlyAdLog.track || function (type, msg) {
                        xmlyAdLog.push([type, msg]);
                    }
                })(
                    window,
                    document,
                    '//s1.xmcdn.com/yx/ad-jssdk-static/last/dist/app.min.js',
                    new Date().getTime()
                );
                hasInitSdk = true;
            },
            report: function (id, adUrl){
                try{
                    window.xmlyAdLog.track(2,{key:"bb64ad4bc94e4f339c4481f0508215ec"});
                    return true;
                }catch(e){
                    console.log(e)
                }
            },
        }
    })(),
}

// 回传上报函数
export function ocpxJS(ocpxConfig){
    var SDK = SDK_MAP[ocpxConfig.mediaType];
    if(!SDK){
        return;
    }
    // 投放url
    var AD_URL = encodeURIComponent(ocpxConfig.adUrl);
    var convertId = ocpxConfig.convertId;
    // 回传上报函数
    SDK.initSdk(convertId, AD_URL);

    return function (appointConvertId) {
        return SDK.report(appointConvertId || convertId, AD_URL);
    }
}

