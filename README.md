## 项目简介

```
珍爱新注册页（包含各种H5钩子，主要用于投放）
```

## 上手指南

```
1.安装要求
    使用私有库vue框架，npm源修改为http://registry.npm.zhenaioa.com
2.安装步骤
    npm install
    npm run dev --page=目录名称
    npm run prod --page=目录名称
    npm run prod --static=1（静态页面copy）
```

## 构建部署

```
cis + rrs
```

## 测试

```
测试环境：   *********
预发布环境： *********

本地地址： http://localhost/目录名称/index.html#/index
访问地址： https://i.zhenai.com/matchmaker-newregistera/目录名称/index.html

本地测试： mac运行命令 sudo npm run dev --page=目录名称（mac环境需使用80端口）
```

## 框架和技术选型

```
使用vue框架

├── README.md                （项目说明）
|—— static                   （静态资源，不经webpack编译）
├── package.json             （node配置 ）
|—— src                      （源码存放）
   |—— comomn                （通用代码，组件，样式）
   |—— static_page           （静态copy页面，会copy到dist目录）
   |—— pages                 （页面存放文件夹）
```

## 特别说明

涉及表单、以及注册相关的已经封装在目录 src/common/business 下，具体相关的组件可以查看对应的 README，主要设计重复逻辑以及样式

表单项存储在 localStorage 之中，key 为`cachedRegisterForm-${pageType}`
如果要同步 localStorage 中的数据，可以监听`setItemEvent`事件，该事件在 src/common/business/utils/localRegisterForm.js 中定义，在更新 localStorage 时被抛出，具体可参照 commonForm 中`window.addEventListener("setItemEvent", (e) => {});`

## commit 校验

接入方式和注意事项参照 wiki 文档：
https://www.tapd.cn/60025888/markdown_wikis/show/#1160025888001003197

提交流程：
（1）git add .
（2）npm run cz （按照要求选择 type，scope 等）
（3）git push
首次运行需重新装包，配置文件为根目录下的 cz-config.js，其中 type 直接参照公共模板，scopes 分为公共模块（如 commonForm）和各个业务钩子，以方便快速定位改动范围，新增钩子后记得同步到配置文件中

已接入自动化化构建了
build.config.json
build 下的 publicPath 在 test 环境 访问需要改成/matchmaker-newregistera/
线上 需要改成 //i.zhenai.com/matchmaker-newregistera/
