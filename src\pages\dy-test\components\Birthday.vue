<template>
    <div class="birthday">
        <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(option, name) in birthdayV2"
                :key="name"
            >
                <h4 class="birthday-info-title">
                    {{ name }}后
                </h4>
                <div
                    class="birthday-info-card"
                >
                    <span
                        class="bic-num"
                        :class="{ active: item === curBirthday }"
                        v-for="item in option"
                        :key="item"
                        @click="goNext(item)"
                    >{{ item }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    setLocalRegisterForm,
    keyToValue
} from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { birthdayV2 } from "@/common/config/register-dictionary";

export default {
    name: "BirthdayV2",
    props: {
        pageType: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            birthdayV2,
            curBirthday: keyToValue(
                "birthday",
                (Storage.getItem(
                    `cachedRegisterForm-${this.pageType}`
                ) &&
                    Storage.getItem(
                        `cachedRegisterForm-${this.pageType}`
                    ).birthday) ||
                    ""
            )
        };
    },
    methods: {
        goNext(val) {
            this.curBirthday = val;
            const params = {
                key: "birthday",
                value: new Date(val + "/" + 1 + "/" + 1).getTime(),
                isMark: false
            };
            setLocalRegisterForm(params, this.pageType);
            setLocalRegisterForm(
                { key: "year", value: val },
                this.pageType
            );
            this.$report(4, "出生年份页-具体年份点击");

            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    },
    mounted() {
        // this.$report(4, "出生年份页访问");
        this.$refs.birthScroll.scrollTo(0, 340);
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    .birthday-scroll {
        overflow-y: scroll;
        height: calc(100vh - 364px);
    }
    &-info {
        &-title {
            margin-left: 74px;
            font-weight: 500;
            font-size: 36px;
            color: #767DFF;
        }
        &-card {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding: 8px 6px;
            margin: 24px 48px 48px;
            background: #fff;
            background-color: #EBEFF1;
            border-radius: 60px;
            > span {
                flex: 0 0 auto;
                display: block;
                color: #26273C;
                font-size: 36px;
                line-height: 96px;
                width: 128px;
                height: 96px;
                border-radius: 50px;
                text-align: center;
            }
            .active {
                color: #767DFF;
                background-color: #fff;
            }
        }
    }
}
</style>
