{"sourcemapName": "zhenaicom.m.newregister", "alias": {"@": "src"}, "entryPage": "index.js", "outputPath": "dist", "eslintSwitch": false, "eslintignore": ["dist"], "copyStatic": [{"from": "static", "to": "dist/static"}], "externals": {"vue": "<PERSON><PERSON>", "vuex": "Vuex", "vue-router": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dev": {"host": "localhost", "port": "80", "proxyTable": {}, "devtool": "source-map", "publicPath": "/"}, "build": {"devtool": "nosources-source-map", "publicPath": "//i.zhenai.com/matchmaker-newregistera/"}}