@mixin btn-style {
    width: 654px;
    height: 110px;
    padding: 0 20px;
    line-height: 110px;
    border: none;
    color: #26273C;
    font-size: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: #ffffff;
    border-radius: 80px;
}

// 设置图片
@mixin set-img($url) {
    background-image: url($url);
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

@mixin set-flex($justify,$align) {
    display: flex;
    justify-content: $justify;
    align-items: $align;
}

.ques {
    padding-top: 52px;
    padding-bottom: 40px;
    overflow-y: scroll;
    &_btn {
        @include btn-style;
        margin-bottom: 40px;
    }
    .active {
        color: #767DFF;
        box-shadow: 0px 4px 22px 0px #e8e5fe;
    }
}
