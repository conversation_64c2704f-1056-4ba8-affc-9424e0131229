
// UA信息
// client/App版本/系统平台(Android、ios)/系统版本/手机型号(格式：品牌_型号)/wifi mac/渠道号/子渠道号/包名/屏幕高度/屏幕宽度/唯一请求id/网络情况网络(0:2G;1:3G;2:4G;3:WIFI;-1:未知)/唯一识别/校验码/广告标示(imei)
const isApp = /client|zhenai/ig.test(navigator.userAgent)
export const appUa = (function (){
    try{
        if(isApp){
            let [
                name,
                appVersion,
                platform,
                version,
                phoneName,
                wifiMac,
                channelId,
                subChannelId,
                packageName,
                screenHeight,
                screenWidth,
                uniqReqId,
                network,
                uniqId,
                code,
                imei
            ] = window.navigator.userAgent.match(/client\/((([^\/]+)\/?)+)(?=\s?)/g)[0].split('/')
            return {
                name,
                appVersion,
                platform,
                version,
                phoneName,
                wifiMac,
                channelId,
                subChannelId,
                packageName,
                screenHeight,
                screenWidth,
                uniqReqId,
                network,
                uniqId,
                code,
                imei
            }
        }
        return {}
    }catch(e){
        return {}
    }
})()
export const env = {
    isWeiBo: /WeiBo/ig.test(navigator.userAgent),
    isWeiXin: /MicroMessenger/ig.test(navigator.userAgent),
    isIos: /\(i[^;]+;( U;)? CPU.+Mac OS X/.test(navigator.userAgent),
    isAndroid: /Android/ig.test(navigator.userAgent) || /adr/ig.test(navigator.userAgent),
    isApp: isApp,
    appVersion: appUa.appVersion,
};

// 点击事件名（用于兼容手机与PC）
export const TAP = /(MacIntel|Win32)/.test(navigator.platform) === true ? 'click' : /(android|iphone|ipad)/ig.test(navigator.userAgent) ? 'tap' : 'click';

// 滑动事件名（用于兼容手机与PC）
export const TouchMove = 'touchmove';

export const PLATFORM_NUM = (function (){
    if (env.isApp) {
        if(env.isIos){
            return 28
        }else{
            return 27
        }
    }else{
        return 2
    }
})()

export const CHANNEL_ID = (function (){
    let res = Z.getParam('channelId') || ''
    if(!res){
        if (env.isApp === true) {
            return appUa.channelId || "";
        }
    }
    return res
})()

export const SUBCHANNEL_ID = (function (){
    let res = Z.getParam('subChannelId') || ''
    if(!res){
        if (env.isApp === true) {
            return appUa.subChannelId || "";
        }
    }
    return res
})()

;(function () {
    if (env.isApp === true) {
        Z.setChannelId(CHANNEL_ID, SUBCHANNEL_ID);
    }
})();

/**
 * 打开App，兼容所有版本
 */
export function openApp() {
    var appHref = 'zhenaiapp://platformapi/startApp';
    if (Z.platform.isIos) {
        appHref = 'zhenaiwang://';
    }
    window.location.href = appHref;
}

/**
 * 下载 APP
 * @param {String} 渠道包
 */
export const downloadNativeApp = (ch) => {
    if (/(iphone|ipod|ipad)/ig.test(navigator.userAgent)) {
        window.location.href = 'itms-apps://itunes.apple.com/cn/app/zhen-ai-wang/id575846819?mt=1';
    } else if (/micromessenger/ig.test(navigator.userAgent)) {
        window.location.href = 'http://a.app.qq.com/o/simple.jsp?pkgname=com.zhenai.android';
    } else {
        window.location.href = 'zhenaiapp://platformapi/startApp?appId=20000001&fromPage=0&memberId=105388926';

        setTimeout(() => {
            window.location.href = `https://images.zastatic.com/apk/zhenai/zhenai_${ch}.apk?1=1`;
        }, 1000);
    }
};
