<template>
    <div class="collection-wrapper">
        <!-- 表单部分 -->
        <collection-form />
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { CollectionForm } from "../components/collection/index.js";
import { reportKibana } from "@/common/utils/report.js";

export default {
    components: {
        CollectionForm
    },
    data() {
        return {};
    },
    computed: {
        ...mapState(["formInfo", "registerInfo", "cmsConfig", "materialId"])
    },
    created() {},
    mounted() {
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
        // 打桩
        reportKibana("脱单计划H5", 20, "大表单页访问", {
            ext16: this.materialId
        });
    },
    methods: {
        ...mapMutations(["setFormInfo"]),
        ...mapActions(["setCmsConfig"]),
        closeModal() {
            this.showModal = false;
        },
        closeSelect() {
            this.showSelect = false;
        },
        checkRegisterInfo() {
            return true;
        }
    }
};
</script>

<style lang="scss" scoped>
.collection-wrapper {
  font-family: Source Han Sans SC;
  width: 100%;
  background-color: #ffffff;
}
.collection-banner {
  width: 750px;
  visibility: hidden;
}

.collection-header {
  position: relative;
  width: 750px;
  height: 294px;
}

.header__avatars {
  position: absolute;
  top: -20px;
  left: 0px;
  height: 240px;
  width: 750px;
  overflow: hidden;
}

.header__online {
  position: absolute;
  top: 190px;
  left: 123px;
  padding-top: 14px;
  width: 482px;
  height: 54px;
  background: #6200ae;
  border-radius: 27px;
  font-size: 28px;
  color: #ffffff;
  text-align: center;

  span {
    margin: 0 6px;
    font-weight: 700;
    color: #ff6dbe;
  }
}

.result-page {
  width: 750px;
  height: 1624px;
  background-image: url("../assets/images/bg-success.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 0;
}
</style>
