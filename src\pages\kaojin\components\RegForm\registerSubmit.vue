<template>
    <div
        class="common-submit"
    >
        <div
            @click="handleSubmit"
            class="common-submit__button"
        >
            <slot></slot>
        </div>

        <common-protocol
            class="common-submit__protocol"
            v-if="isNeedProtocol"
            :is-checked.sync="isCheckProtocol"
            :is-need.sync="isNeedCheck"
            :agreement-status="agreementStatus"
            :style-config="styleConfig.protocolConfig"
        />

        <!-- 协议弹窗 -->
        <common-protocol-modal
            v-model="modalVisible.protocolModal"
            @confirm="handleConfirmProtocol"
            :page-type="pageType"
            :style-config="styleConfig.modalConfig"
        />

        <!-- 短信验证码弹窗 -->
        <RegisterMgsCodeModal
            v-model="modalVisible.msgCodeModal"
            :page-type="pageType"
            :validate-code="handleValidateCode"
            :style-config="styleConfig.modalConfig"
        />

        <!-- 注册过后弹窗 -->
        <register-end-modal
            v-model="modalVisible.registerEndModal"
            @confirm="handleConfirmProtocol"
            :imgStatus="imgStatus"
        />
    </div>
</template>

<script>
import CommonProtocol from './CommonProtocol.vue';
import CommonProtocolModal from './CommonProtocolModal.vue';
import RegisterEndModal from './RegisterEndModal.vue';
import RegisterMgsCodeModal from './RegisterMgsCodeModal.vue';
import { Toast } from "vant";

import z_ from '@/common/zdash';
import Api from '@/common/server/base';
import Prototype from "@/common/framework/prototype";
import oUserSelect from '@/common/ocpx/huichuan';
import { storage as Storage, session as Session } from "@/common/utils/storage";

import { reportError, reportKibana, reportLoveKibana } from "@/common/utils/report";
import { pageTypeChnMap, registerResult } from "@/common/config/register-dictionary.js";
import { getCode, login, register } from '../../nearApi';
import { registeredH5CheckReport } from '../../api';

export default {
    name: 'RegisterSubmit',
    components: {
        CommonProtocol,
        CommonProtocolModal,
        RegisterMgsCodeModal,
        RegisterEndModal,
    },
    props: {
        // 勾子类型
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        agreementStatus: {
            type: Number,
            default: 1
        },
        // 是否需要协议
        isNeedProtocol: {
            type: Boolean,
            default: true
        },
        styleConfig: {
            type: Object,
            default: {
                modalConfig: {
                    confirmButtonColor: '#ffffff',
                    confirmButtonBgColor: '#000000',
                    cancleButtonColor: '#000000',
                },
                protocolConfig: {
                    textColor: "#FFFFFF",
                    protocolColor: "#767DFF",
                    protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20220512/1652336753832_292583_t.png'
                }
            }
        },
        // 注册成功之后的回调
        handleAfterRegisiter: {
            type: Function,
            default: () => {}
        },

        handleLogin: {
            type: Function,
            default: () => {}
        },

        handleCancle: {
            type: Function,
            default: () => {}
        },

    },
    mounted() {

        this.$watch('agreementStatus', (value) => {
            this.isCheckProtocol = value === 0;
        }, {
            immediate: true
        });
        if (this.$route.query.imgStatus !== undefined) {
            this.imgStatus = Number(this.$route.query.imgStatus);
            this.modalVisible.registerEndModal = true;
        }
    },
    data() {
        return {
            imgStatus: 1,
            isNeedCheck: false,
            // 控制弹窗是否展示
            modalVisible: {
                protocolModal: false,
                registerOverwriteModal: false,
                msgCodeModal: false,
                registerEndModal: false,
            },
            isCheckProtocol: false,
            // 校验账号结果
            validateAccountResult: {},
            // 防止多次触发
            lockSubmit: false,
            lockOverwrite: false,
            registerForm:{
                gender: "",
                cityCode: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },
    computed: {

    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.modalVisible.protocolModal = false;
            this.handleSubmit(false);
        },

        // reg_id存session，原因是ocpx从session里取的值是reg_memberid
        handleSetRegMemberId(id) {
            Session.setItem('reg_memberid', id);
            oUserSelect.mark({
                msgValid: false,
                isMark: false,
            });
        },

        handleAllFillIn() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            return z_.every(registerForm , value => {
                return !z_.isNil(value) && value !== "";
            });
        },

        handleValidatedPhone() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },


        // 处理提交基本信息
        async handleSubmit(isReport = true) {
            if (isReport) {
                this.$report(49, "大表单注册页-提交按钮点击", {});
            }


            if (this.lockSubmit) {
                return;
            }

            if (!this.handleAllFillIn()) {
                Toast('请完善资料再提交');
                return;
            }

            if (!this.handleValidatedPhone()) {
                Toast('请输入正确的手机号');
                return;
            }

            if (this.isNeedProtocol && !this.isCheckProtocol) {
                this.modalVisible.protocolModal = true;
                return;
            }
            this.modalVisible.msgCodeModal = true;
            this.modalVisible.registerEndModal = false;
            this.$report(3002, "获取验证码(监控)", {});
            if (isReport) {
                this.$report(49, "提交按钮点击（有效点击）", {});
            }

        },
        toEnd() {
            setTimeout(() => {
                this.$toast.clear();
                this.$router.push({
                    url: '/register/6',
                    query: {
                        imgStatus: this.imgStatus
                    }
                });

            }, 500);

        },

        // 处理校验验证码相关
        async handleValidateCode(messageCode) {
            this.$toast.loading({forbidClick: true});

            const {errorCode, data, isError} = await login({
                type: 0,
                token: messageCode,
            });
            if (!isError) {
                // 已注册
                this.imgStatus = 0;
                // this.modalVisible.registerEndModal = true;
                this.modalVisible.msgCodeModal = false;
                this.toEnd();
                return {errorCode, data, isError};
            } else if (errorCode !== '-11507001') {
                // 除了未注册-11507001，其余错误情况终止流程
                this.$toast.clear();
                return {errorCode, data, isError};
            }


            // 注册流程
            const registerForm = Storage.getItem(`cachedRegisterForm-${this.pageType}`) || this.registerForm;

            const sendData = {
                ...registerForm,
                salaryMonth: registerForm.salary,
                educationZa: registerForm.education,
            };
            delete sendData.salary
            delete sendData.education

            const resData = await register(sendData);

            if (!resData.isError) {
                Session.setItem('reg_memberId', resData.data.memberId);
                this.$report(4, "注册成功并生成ID", {
                    ext17: "注册成功",
                    ext18:  JSON.stringify(sendData),
                    ext29: resData.data.memberId,
                });
                // this.$toast("注册成功！");
                this.modalVisible.msgCodeModal = false;
                this.imgStatus = 1;
                // this.modalVisible.registerEndModal = true;
                await registeredH5CheckReport({
                    ...registerForm,
                    ua: Z.getUA(),
                    phone: registerForm.phone,
                    clickId: localStorage.getItem('AD_URL'),
                    submitPhone: true,
                    msgValid: true,
                });
                this.toEnd();

            } else {
                this.$report(4, "注册失败", {
                    ext17: "注册失败",
                    ext18:  JSON.stringify(sendData),
                });
                if (resData.msg && resData.msg.includes('注册失败')) {
                    this.imgStatus = 0;
                    // this.modalVisible.registerEndModal = true;
                    this.modalVisible.msgCodeModal = false;
                    this.toEnd();
                }
                this.showModal = false;
            }

            return resData;
        },

        // 根据类型展示不同弹窗
        handleCheckOverrideAccount(validateAccountResult) {
            const { type, oldMemberID } = validateAccountResult;

            console.log(validateAccountResult);

            this.validateAccountResult = validateAccountResult;

            switch (type) {
            case registerResult.LOGIN_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.modalVisible.registerOverwriteModal = true;
                break;
            case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                // 兼容如果用户选择登陆，需要memberId的情况
                if (oldMemberID) {
                    // 修复：需要memberId存本地，但不应该触发回传
                    Session.setItem('reg_memberid', oldMemberID);
                    // this.handleSetRegMemberId(oldMemberID);
                }
                this.modalVisible.registerOverwriteModal = true;
                break;
            case registerResult.NEW_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.NEW_ACCOUNT.label);
                break;
            case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                this.handleFinishedRegister(registerResult.AUTO_OVERWRITE_ACCOUNT.label);
                break;
            }

            const resultType = z_.find(registerResult, {
                value: type
            });

            this.$report(16, "手机号验证成功", {
                ext17: resultType.label,
            });
        }
    },

    // 处理覆盖注册
    async handleOverwriteAccount() {
        if (this.lockOverwrite) {
            return;
        }

        this.lockOverwrite = true;

        const sendData = Prototype.$gather.getOverwriteAccountParams(this.messageCode, pageTypeChnMap[this.pageType]);
        const result = await Api.overwriteAccount(sendData);

        this.lockOverwrite = false;

        if (result.isError) {
            this.$toast(result.errorMessage);
            return;
        }

        if(result.data.memberID) {
            this.handleSetRegMemberId(result.data.memberID);
        }

        this.modalVisible.registerOverwriteModal = false;
        this.handleFinishedRegister(registerResult.MANUAL_OVERWRITE_ACCOUNT.label);

    },

    handleFinishedRegister(ext) {
        this.$report(51, "注册成功并生成ID", {
            ext17: ext
        });
        this.$toast('注册成功');
        Storage.setItem('registerFinished', true);
        this.handleAfterRegisiter();
    },

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';


.common-submit {
    &__button {
        @include flex-center(row, center, center);
    }
    &__protocol {
        margin-top: 49px;
    }
    &__app-info {
        color: #FFFFFF;
        opacity: 0.6;
        font-size: 22px;
        line-height: 40px;
        padding: 40px 39px 0;
        text-align: center;
    }
}
</style>
