<template>
    <div class="ques">
        <button
            class="ques_btn"
            :class="{ active: current === option.key }"
            v-for="(option, index) in columns"
            :key="index"
            @click="goNext(option)"
        >
            {{ option.text }}
        </button>
    </div>
</template>

<script>
import { salary } from "@/common/config/register-dictionary";
export default {
    name: "Salary",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            columns: salary,
            current: 2
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            8, // 记录点
            "注册-月收入曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    methods: {
        goNext(option) {
            this.current = option.key;
            this.$select.mark({
                salary: option.key
            });
            this.$storage.saveToStorage("__regInfo__", "salary", option.key);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
