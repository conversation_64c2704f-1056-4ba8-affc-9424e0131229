<template>
    <div
        class="exam_result"
    >
        <img
            class="result_img"
            :src="img"
            alt=""
            @click="clickRecommend"
        >
        <img
            class="kit_icon"
            @click="clickKitIcon"
            src="https://photo.zastatic.com/images/common-cms/it/20240805/1722844884115_526674.png"
            alt=""
        >
        <van-popup
            v-if="kitVisible"
            v-model="kitVisible"
            class="common-protocol-modal"
        >
            <div class="kit_popup_container">
                <div class="kit_imgs">
                    <img
                        class="kit_imgs_1"
                        src="https://photo.zastatic.com/images/common-cms/it/20240801/1722501407046_599542.png"
                        alt=""
                    >
                    <img
                        class="kit_imgs_2"
                        src="https://photo.zastatic.com/images/common-cms/it/20240801/1722501414263_717915.png"
                        alt=""
                    >
                </div>
                <div class="tips">
                    收到1个珍爱官方的 <span class="special">脱单锦囊</span>
                </div>
                <div
                    class="btn"
                    @click="clickKit"
                >
                    打开看看
                </div>
            </div>
            <img
                class="close_icon"
                @click="close"
                src="https://photo.zastatic.com/images/common-cms/it/20240805/1722844568612_270073.png"
                alt=""
            >
        </van-popup>
    </div>
</template>

<script>
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import Api from '@/common/server/base';
import { Toast, Popup as VanPopup } from "vant";
import { PAGE_TYPE } from '../config';
import { setLocalRegisterForm } from '@/common/business/utils/localRegisterForm';
import { CryptoDes } from '@/common/utils/crypto';
const cryptoDes = new CryptoDes('rlWU9Uty8atXZVE8')
export default {
    name:'Result',
    components:{
        VanPopup
    },
    data(){
        return {
            reportLock:{
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            img: '',
            expertJumpULoveApplet: false, // 是否跳优恋空间
            youlianLink: '', // 跳优恋空间链接
            kitVisible: false,
        };
    },
    async created(){
        this.getResult();
        this.isJumpYoulian();
    },
    activated(){

        this.$report(114, '测试报告页访问');
    },
    mounted(){
        setTimeout(() => {
            this.kitVisible = true;
            this.$report(131, '测试结果页-脱单锦囊弹窗曝光');
        }, 3000);
    },
    methods:{
        async isJumpYoulian() {
            const res = await Api.getRegChainConfig({
                channelId: Z.getParam('channelId'),
                subChannelId: Z.getParam('subChannelId'),
            })
            if (res.code === 0) {
                this.expertJumpULoveApplet = res.data.results.expertJumpULoveApplet
            }
        },
        async getYoulianLink() {

            if (this.expertJumpULoveApplet) {
                const memberId = sessionStorage.getItem('reg_memberid')
                this.$report(123, " 测试结果页-短链生成进入");
                let res1 = {}
                if (memberId) {
                    const demem = cryptoDes.encode(memberId)
                    res1 = await Api.getValidToken({memberId: demem})
                    this.$report(123, " 测试结果页-换取token", {
                        ext1: JSON.stringify(res1)
                    });
                }
                const linkUrl = Api.domain().includes('api-test.zajiebao.com') ? encodeURIComponent('https://mp.weixin.qq.com/s/MODFT629m9B3w9v8H15Gig') : encodeURIComponent('https://mp.weixin.qq.com/s/QeszlKRc_D3oKYCoCDE-4w')
                const params = {
                    path: 'pages/main/meet/index',
                    query: `channelId=${Z.getParam('channelId')}&subChannelId=${Z.getParam('subChannelId')}&zaAPPToken=${res1.data ? res1.data : ''}&linkUrl=${linkUrl}`,
                };
                const res2 = await Api.goYouLianMini(params)
                if (res2.code === 0) {
                    this.$report(123, " 测试结果页-短链生成", {
                        ext1: JSON.stringify(res2)
                    });
                    this.youlianLink = res2.data
                    location.href = this.youlianLink
                }
            }
        },
        getResult() {
            const img = this.$route.params.resultImg;
            if (img) {
                this.img = img;
                const params = {
                    key: "exam_res_img",
                    value: img
                };
                setLocalRegisterForm(params, PAGE_TYPE);
            } else {
                const exam_res_img = Storage.getItem(
                    `cachedRegisterForm-${PAGE_TYPE}`
                ).exam_res_img;
                this.img = exam_res_img;

            }



        },
        handleDownload() {
            this.$report(44, "报告页-点击下载APP按钮");
            if (this.expertJumpULoveApplet) {
                this.getYoulianLink()
                return false
            }
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({ value: true });
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        clickKit() {
            this.handleDownload();
        },
        clickRecommend() {
            this.$report(138, '测试结果页-窗口2-点击-人物推荐');
            this.handleDownload();
        },
        spliceData(options){
            let data = options.map(ele => {
                let title = ele.content.match(/\d[^\d|\r\n]*?\r\n/g);
                if(title && title.length > 0){
                    ele.navtitle = title;
                    ele.content = ele.content + "\r\n\r\n";
                    ele.content = ele.content.match(/\r\n[^\r\n|\r\n\r\n]*?\r\n\r\n/g);
                }
                return ele;
            });

            return data;
        },
        close() {
            this.kitVisible = false;
        },
        clickKitIcon() {
            this.handleDownload();
            this.$report(137, '测试结果页-窗口1-点击脱单锦囊悬浮球');

        },

    },
    destroyed(){
        window.removeEventListener('scroll',this.listenScreen);
    }

};
</script>

<style lang="scss">
.exam_result{

    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffe6e0;
    height: 100vh;
    .result_img {
        width: 100vw;
    }
    .common-protocol-modal  {
        overflow: visible;
    }
    .kit_popup_container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .kit_imgs {
        margin-top: -326px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &_1 {
            width: 431.99px;
            height: 123.02px;
            margin-bottom: 15px;
        }
        &_2 {
            width: 340px;
            height: 250px;
        }
    }
    .tips {
        font-size: 28px;
        font-weight: 400;
        letter-spacing: 0;
        text-align: center;
        margin-top: 30px;
        .special {
            color: #D7204A;
        }
    }
    .btn {
        margin-top: 60px;
        width: 358px;
        height: 100px;
        background-image: linear-gradient(180deg, #FF9E8B 4%, #FF5B87 100%);
        border-radius: 54px;
        font-weight: 500;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        line-height: 100px;
    }
    .close_icon {
        width: 48px;
        height: 48px;
        position: absolute;
        bottom: -90px;
        left: 50%;
        transform: translateX(-50%);
    }
    .kit_icon {
        width: 160px;
        height: 150px;
        position: fixed;
        right: 0;
        top: 440px;
    }
}
</style>
