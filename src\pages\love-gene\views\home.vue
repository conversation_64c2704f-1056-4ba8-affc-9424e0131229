<template>
    <love-page home>
        <div class="home__container">
            <z-image
                class="home__logo"
                :src="require('../assets/images/main-logo.png')"
                block
                :height="194"/>

            <div
                :style="{ visibility: isDefaultCmsConfig ? 'visible' : 'hidden' }"
                class="home__video" id="svgaObject"/>

            <div class="home__text">
                <div>提出你的要求</div>
                <div>帮你分配契合的理想型对象</div>
            </div>

            <simple-button
                class="home__button"
                fix-bottom
                :width="670"
                :background="cmsConfig.homeButtonColor"
                @click="start">
                {{ cmsConfig.homeButtonText }}
            </simple-button>
        </div>
    </love-page>
</template>

<script>
import { mapState } from 'vuex';
import { createPage } from '@/common/framework';

export default createPage({
    name: 'Home',
    visitReport: {
        accessPoint: 1,
        accessPointDesc: '首页访问',
    },
    computed: {
        ...mapState([
            'isDefaultCmsConfig',
            'cmsConfig'
        ]),
    },
    methods: {
        start() {
            this.$router.push({
                path: "/questions/0",
            });
        },
        setSVGA() {
            const player = new SVGA.Player('#svgaObject');
            const parser = new SVGA.Parser('#svgaObject');

            parser.load(require('../assets/register-object.svga'), (videoItem) => {
                player.setVideoItem(videoItem);
                player.startAnimation();
            });
        },
    },
    mounted() {
        const unwatch = this.$watch('isDefaultCmsConfig', (value) => {
            if (value) {
                this.setSVGA();
                unwatch && unwatch();
            }
        }, {
            immediate: true,
        })
    },
})
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.home {
    &__container {
        padding-bottom: 192px;
    }

    &__logo {
        margin-top: 64px;
    }

    &__video {
        @include relative-center();
        width: 654px;
        height: 696px;
        overflow: hidden; // 加载 SVGA 后 DOM 会溢出，加 overflow: hidden 防止屏幕出现滚动条
    }

    &__text {
        @include flex-center(column, null, center);
        width: 100vw;

        > div:first-child {
            font-size: 36px;
            line-height: 54px;
            font-weight: 500;
        }

        > div:last-child {
            font-size: 32px;
            margin-top: 40px;
        }
    }
}
</style>
