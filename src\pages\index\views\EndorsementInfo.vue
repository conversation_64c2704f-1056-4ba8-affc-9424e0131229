<!-- 老注册页AB测试方案测试组对应的下载页（背书方案） -->
<template>
    <div class="endorsement-info-wrapper">
        <z-image
            :width="750"
            :height="606"
            src="https://photo.zastatic.com/images/common-cms/it/20220916/1663297671252_747472.jpeg"
        />
        <div class="first-card">
            <div>圈子小？没有心动对象</div>
            <div>工作忙？社交时间少</div>
            <div>家里催？相亲对象少</div>
        </div>
        <div
            class="second-card"
            v-if="recomList.length > 0"
        >
            <div class="header"></div>
            <div
                class="content"
            >
                <div
                    class="box"
                    v-for="(item, index) in recomList"
                    :key="index"
                    @click="handleClickItem"
                >
                    <img :src="`${item.avatar}?imageMogr2/thumbnail/112x112`">
                    <div>{{ item.age }}｜{{ item.profession }}</div>
                    <div>匹配度: <span>{{ (Math.random() * 4 + 95).toFixed(1) }}%</span></div>
                </div>
            </div>
        </div>
        <div class="third-card">
            <div class="header"></div>
            <div class="content">
                <div class="title">
                    <img src="https://photo.zastatic.com/images/common-cms/it/20220916/1663301856059_977399.png">观点资料
                </div>
                <div class="des">
                    快速了解Ta的婚恋观、人生观、自我观、人际观
                </div>
                <div class="pic-list">
                    <z-image
                        v-for="(item, key) in PICMAP"
                        :key="key"
                        class="pic"
                        :width="148"
                        :height="148"
                        :src="item"
                    >
                        {{ key }}
                    </z-image>
                </div>
                <div class="title">
                    <img src="https://photo.zastatic.com/images/common-cms/it/20220916/1663301856059_977399.png">心灵视界
                </div>
                <div class="des">
                    由珍爱研究院结合多年婚恋经验精心打造的个性与婚恋观测试，深入了解Ta的内心，帮你找到最合适的异性
                </div>
                <z-image
                    :width="640"
                    :height="420"
                    src="https://photo.zastatic.com/images/common-cms/it/20220916/1663309930193_528425.png"
                />
            </div>
        </div>
        <z-image
            :width="750"
            :height="604"
            src="https://photo.zastatic.com/images/common-cms/it/20220916/1663310008925_893849.png"
            class="fourth-card"
        />
        <div class="bottom-button">
            <div
                class="download-button"
                @click="handleDownload(14, '旧落地页(背书)')"
            >
            </div>
            <div
                class="bottom-link"
                @click="handleViewWap(14, '旧落地页(背书)')"
            >
                继续访问网页版
            </div>
        </div>
    </div>
</template>

<script>
import ZImage from "@/common/components/z-image";
import { handleDownload, handleViewWap} from '../js/util';
import { _getCommonRecommendList } from '../js/api';
import { reportKibana } from "@/common/utils/report.js";

export default {
    name: 'EndorsementInfo',
    data() {
        return {
            PICMAP: {
                '婚恋观': 'https://photo.zastatic.com/images/common-cms/it/20220916/1663309231987_380556.png',
                '人生观': 'https://photo.zastatic.com/images/common-cms/it/20220916/1663309248628_347288.png',
                '自我观': 'https://photo.zastatic.com/images/common-cms/it/20220916/1663309266459_715887.png',
                '人际观': 'https://photo.zastatic.com/images/common-cms/it/20220916/1663309285949_602715.png'
            },
            recomList: []
        };
    },
    mounted() {
        reportKibana("旧落地页(背书)", 14, "背书下载页访问");
        this.handleGetRecomList();

        window.addEventListener("popstate", () => {
            this.handleViewWap();
        });
    },
    destroyed() {
        window.removeEventListener("popstate", this.handleViewWap);
    },
    components: {
        ZImage
    },
    methods: {
        handleDownload,
        handleViewWap,
        async handleGetRecomList() {
            const result = await _getCommonRecommendList({count: 8});

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
            
            this.recomList = result.data.list;
        },

        handleClickItem() {
            reportKibana("旧落地页(背书)", 14, "背书下载页-人物头像点击");
            this.$toast('请下载珍爱APP，立即与Ta约会');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.endorsement-info-wrapper {
    min-height: 100vh;
    background-color: #8B8EFF;
    .first-card {
        position: relative;
        margin-top: -150px;
        width: 750px;
        height: 460px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663298228040_137298.png");
        div {
            position: absolute;
            left: 262px;
            font-weight: 400;
            font-size: 30px;
            color: #000000;
        }
        >div:nth-child(1) {
            top: 158px;
        }
        >div:nth-child(2) {
            top: 234px;
        }
        >div:nth-child(3) {
            top: 310px;
        }
    }
    
    .second-card {
        width: 686px;
        margin: 0 auto;
        .header {
            width: 686px;
            height: 140px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663299682414_823220.png");
        }
        .content {
            @include flex-center(row, space-between, null);
            flex-wrap: wrap;
            padding: 38px 14px 24px 16px;
            margin-top: -38px;
            background-image: linear-gradient(180deg, #FFFFFF 0%, #FFE6F3 100%);
            border-radius: 40px;
            .box {
                @include flex-center(column, null, center);
                width: 152px;
                height: 200px;
                margin-bottom: 32px;
                img {
                    width: 112px;
                    height: 112px;
                    border: 2px solid #FFFFFF;
                    border-radius: 50%;
                    object-fit: cover;
                }
                >div:nth-child(2) {
                    width: 144px;
                    text-align: center;
                    margin-top: 14px;
                    margin-bottom: 8px;
                    font-weight: 400;
                    font-size: 20px;
                    color: #7A7A7A;
                    @include truncate;
                }
                >div:nth-child(3) {
                    @include flex-center(row, center, center);
                    width: 148px;
                    height: 44px;
                    background: #05C2FF;
                    border-radius: 40px;
                    border-radius: 22px;
                    color: #FFFFFF;
                    font-size: 18px;
                    font-weight: 400;
                    span {
                        position: relative;
                        bottom: 2px;
                        font-size: 22px;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    .third-card {
        margin-top: -58px;
        margin-bottom: 56px;
        .header {
            width: 718px;
            height: 254px;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663301193666_634028.png");
        }
        .content {
            margin: -40px auto 0;
            padding: 40px 24px 32px 24px;
            width: 686px;
            height: 1024px;
            background-image: linear-gradient(180deg, #FFFFFF 0%, #FFE6F3 100%);
            border-radius: 40px;
            .title {
                img {
                    width: 36px;
                    height: 28px;
                    margin-right: 14px;
                }
                font-weight: 500;
                font-size: 36px;
                color: #18181E;
            }
            .des {
                margin-top: 24px;
                margin-bottom: 24px;
                font-weight: 400;
                font-size: 28px;
                color: #7A7A7A;
                line-height: 42px;
            }
            .pic-list {
                margin-bottom: 48px;
                width: 638px;
                @include flex-center(row, space-between, null);
                .pic {
                    @include flex-center(row, center, center);
                    font-weight: 500;
                    font-size: 30px;
                    color: #FFFFFF;
                }
            }
        }
    }

    .fourth-card {
        margin-bottom: 302px;
    }

    .bottom-button {
        position: fixed;
        right: 0;
        bottom: 40px;
        left: 0;
        text-align: center;
        z-index: 1;
        .download-button {
            width: 622px;
            height: 116px;
            margin: 0 auto;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663311123222_130830.png");
        }
        .bottom-link {
            margin: 32px auto 0px;
            width: 258px;
            height: 58px;
            background: rgba(139,142,255,0.70);
            border-radius: 29px;
            @include flex-center(row, center, center);
            font-weight: 400;
            font-size: 30px;
            color: #FFFFFF;
        }
    }
}
</style>