<template>
    <div
        class="result-info"
        :style="{'background': styleConfig.resultInformationCard.bgColor, 'border': `2px solid ${styleConfig.resultInformationCard.borderColor}`}"
    >
        <div class="info-top">
            <img 
                :src="memberInfo.photo1 && `${memberInfo.photo1}?imageMogr2/rquality/35`"
                class="avatar"
            >
            <div
                class="text"
            >
                <div
                    class="nickname"
                    :style="{'color': styleConfig.resultInformationCard.titleColor}"
                >
                    {{ memberInfo.nickName }}
                </div>
                <div
                    class="memberId"
                    :style="{'color': styleConfig.resultInformationCard.borderColor}"
                >
                    珍爱会员ID：<span>{{ memberInfo.memberId }}</span>
                </div>
                <div
                    class="online"
                    :style="{'color': styleConfig.mainColor}"
                >
                    <img :src="styleConfig.resultInformationCard.onlineIcon">当前APP在线
                </div>
            </div>
        </div>
        <div class="info-bottom">
            <div :style="{'color': styleConfig.resultInformationCard.fontColor}">
                {{ cmsConfig.reportViewType === 1? '她': '他' }}正在与<span :style="{'color': styleConfig.mainColor}">{{ userNumber }}位</span>{{ cmsConfig.reportViewType === 1?'男生':'女生' }}聊天中
            </div>
            <button
                :style="{'backgroundColor': styleConfig.mainColor, 'color': styleConfig.buttonFontColor}"
                @click="handleDownload"
            >
                立即聊天
            </button>
        </div>

        <CommonDownloadGuideModal
            :styleConfig="{
                confirmButtonColor: '#FFF',
                confirmButtonBgColor: '#000'
            }"
            v-model="downloadVisible"
            :page-type="pageType"
        >
            <template slot="default">
                如何与{{ cmsConfig.reportViewType === 1? '她': '他' }}聊天
            </template>
            <template slot="desc">
                <div class="desc">
                    1.请到应用市场搜索下载【珍爱APP】<br />
                    2.在【珍爱APP】内搜索{{ cmsConfig.reportViewType === 1? '她': '他' }}的ID/昵称，即可开始聊天
                </div>
            </template>
        </CommonDownloadGuideModal>
    </div>
</template>

<script>
import { getRandomInt } from "@/common/utils/tools.js";
import { Toast } from "vant";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { session as Session} from "@/common/utils/storage";

import CommonDownloadGuideModal from "@/common/business/components/CommonDownloadGuideModal.vue";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "ResultInfo",
    inject: ["cmsConfig", "styleConfig"],
    props: {
        memberInfo: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        CommonDownloadGuideModal
    },
    data() {
        return {
            downloadVisible: false
        };
    },
    computed: {
        userNumber(){
            return getRandomInt(5,20);
        },
        pageType() {
            return this.cmsConfig.reportViewType === 1 ? '锵锵锵H5' : '咚咚咚H5';
        },
    },

    methods: {
        handleDownload() {
            this.$report(7, '引导下载页-下载按钮点击');
            if (this.cmsConfig.downloadStatus === 0) {
                this.downloadVisible = true;
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }
        }

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.result-info{
    position: relative;
    margin: 46px auto 0;
    width: 670px;
    height: 360px;
    border-radius: 32px;

    &::before{
        content:"";
        position: absolute;
        top: -50px;
        right: -32px;
        width: 102px;
        height: 136px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20220719/1658203440633_565513_t.png')
    }
    
    .info-top{
        display:flex;
        padding: 40px;
        .avatar{
            width: 182px;
            height: 184px;
            object-fit: cover;
            border-radius: 12px;
        }

        .text{
            margin-left: 33px;
            font-weight: 400;
            font-size: 28px;
            line-height: 40px;
        }

        .nickname{
            font-weight: 700;
            font-size: 36px;
            line-height: 36px;
        }

        .memberId{
            margin-top: 26px;
            >span {
                font-weight: 600;
            }
        }

        .online{
            position:relative;
            margin-top: 26px;
            >img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
        }
    }

    .info-bottom{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        > div{
            font-weight: 400;
            font-size: 28px;

            > span{
                font-weight: 700;
            }
        }

        > button{
            width: 208px;
            height: 72px;
            border-radius: 36px;
            font-weight: 700;
            font-size: 28px;
        }
    }

    .desc {
        padding: 0 12px;
        text-align: left;
        font-weight: 400;
        font-size: 28px;
    }
}
</style>