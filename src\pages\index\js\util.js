import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { session } from "@/common/utils/storage";
import { Toast } from "vant";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { reportKibana } from "@/common/utils/report.js";

export const handleDownload = (accessPoint, ext30) => {
    const description = accessPoint === 14 ? '背书下载页-主下载按钮点击' : '推人下载页-主下载按钮点击';
    reportKibana(ext30, accessPoint, description);
    visibilityChangeDelay(function() {
        if (session.getItem("isToutiaoIos")) {
            CommonDownloadGuideModalV2({value: true});
        } else {
            Toast({
                message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                duration: 5000
            });
            downloadApp();
        }
    }, 500);
    openApp();
};

export const handleViewWap = (accessPoint, ext30) => {
    const description = accessPoint === 14 ? '背书下载页-访问网页版按钮点击' : '推人下载页-访问网页版按钮点击';
    reportKibana(ext30, accessPoint, description);

    const channelId = Z.getParam("channelId");
    const subChannelId = Z.getParam("subChannelId");
    location.href = `${
        location.protocol
    }//i.zhenai.com/m/wap/index/index.html?channelId=${channelId}&subChannelId=${subChannelId}`;
};