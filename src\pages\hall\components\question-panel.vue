<template>
    <z-image
        class="question-panel"
        :src="require('../assets/images/question-panel.png')"
        :width="696"
        :height="582"
    >
        <slot/>
    </z-image>
</template>

<script>
export default {
    name: "question-panel",
}
</script>

<style scoped lang="scss">
@import '~@/common/styles/common.scss';

.question-panel {
    margin: -24px 0 0 26px;
    @include flex-center(column, null, center);

    &__title {
        text-align: center;
        font-weight: 500;
        font-size: 32px;
    }
}
</style>
