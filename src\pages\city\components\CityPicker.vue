<template>
    <div class="city-picker-wrapper">
        <van-picker
            ref="refPicker"
            show-toolbar
            toolbar-position="bottom"
            :columns="dict"
            item-height="1.25rem"
            @confirm="handleConfirm"
            visible-item-count="5"
        >
            <div
                slot="confirm"
                class="city-picker-wrapper__confirm"
                :style="{ background: cmsConfig.homeButtonColor }"
            >
                下一步
            </div>
        </van-picker>
    </div>
</template>

<script>
import { storage as Storage } from "@/common/utils/storage";
import { setLocalRegisterForm, findWorkCity } from "@/common/business/utils/localRegisterForm.js";
import { Picker } from "vant";

export default {
    name: 'CityPicker',
    components: {
        [Picker.name]: Picker,
    },
    inject: ['cmsConfig'],
    data() {
        return {
            dict: Z.workCity
        };
    },

    mounted() {
        this.handleInitArea();
        this.$report(3, '工作地页面访问');
    },

    methods: {
        handleConfirm(area) {
            let code = '';
            const picker = this.$refs.refPicker;
            const currentCity = area[0];
            if (["北京", "上海", "重庆", "天津"].includes(currentCity)) {
                code = picker.getColumnValue(1).key;
            } else {
                code = picker.getColumnValue(2).key;
            }

            const params = {
                key: 'workCity',
                value: code
            };

            setLocalRegisterForm(params, this.cmsConfig.planName);
            this.$report(4, '工作地页面-按钮点击');
            this.$router.push({
                path: "/collection/1"
            });
        },

        handleInitArea() {
            const workCity = Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) && Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).workCity || '';
            if (workCity) {
                const cityArr = findWorkCity(workCity);
                this.$refs.refPicker.setValues(cityArr);
            } else {
                // 有缓存优先读缓存，否则走定位逻辑
                this.handleLocate();
            }
        },

        handleLocate() {
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                    // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {  //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([result.addressComponent.province, result.addressComponent.city, result.addressComponent.district]);
                            this.$refs.refPicker.setValues(areaArr);
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if(status === 'complete'){
                        const areaArr = this.handleLocationPair([result.province, result.city, '']);
                        this.$refs.refPicker.setValues(areaArr);
                    } else {
                        this.$refs.refPicker.setValues(['广东', '肇庆', '端州区']); // 缺省地区
                    }
                });


            });
        },

        handleLocationPair(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            const targetProvince = this.$z_.find(this.dict, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });
            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(targetCity.children, district => {
                return district.text.indexOf(sliceDistrict) >= 0;
            });

            return[targetProvince.text, targetCity.text, targetDistrict.text];
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.city-picker-wrapper {
    width: 690px;
    height: 887px;
    margin: 80px auto 0px;
    background: url('https://photo.zastatic.com/images/common-cms/it/20220530/1653892927104_524494_t.png') no-repeat;
    background-size: 100% 100%;
    &__confirm {
        width: 100%;
        height: 100%;
        @include flex-center(row, center, center);
        border-radius: 55px;
    }
    .van-picker {
        width: 478px;
        height: 426px;
        margin: 0 auto;
        position: relative;
        top: 160px;
    }
    .van-picker-column__item--selected {
        font-weight: 600;
    }
    .van-picker__cancel {
        display: none;
    }
    .van-picker__confirm {
        flex-shrink: 0;
        order: 0;
        margin: 0 auto;
        padding: 0;
        position: relative;
        top: 260px;
        left: -90px;
        width: 658px;
        height: 110px;
        background: #FF5E90;
        border: 4px solid #FFFFFF;
        border-radius: 55px;
        font-size: 40px;
        font-weight: 400;
        color: #ffffff;
        line-height: 50px;
    }
}
</style>

