<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        :close-on-click-overlay="false"
        class="common-protocol-modal-img"
    >
        <div class="common-protocol-modal__img">
            <img
                class="banner"
                src="https://photo.zastatic.com/images/common-cms/it/20240522/1716367279332_594977.png"
                alt=""
            >
            <div
                class="tips"
                v-if="imgStatus === 1"
            >
                注册成功，进入靠近品牌合作小程序珍爱优恋空间，马上推荐异性
            </div>
            <div
                class="tips"
                v-else
            >
                检测手机号已注册，进入靠近品牌合作小程序珍爱优恋空间，马上推荐异性
            </div>
            <div class="download_btn" @click="downloadApp">
                进入小程序
            </div>
            <div class="cancel_btn" @click="cancel">
                取消
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { reportKibana,reportLoveKibana } from "@/common/utils/report";
import Api from '@/common/server/base';

export default {
    name: 'CommonProtocolModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: true,
        },
        imgStatus: {
            type: Number,
            default: 1,
        },
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(18, '弹出引导下载靠近弹窗');
                }
            },
            immediate: true,
        }
    },
    methods: {
        async downloadApp() {
            this.$report(19, '点击下载靠近');
            // location.href = Z.platform.isIos ? "https://apps.apple.com/cn/app/id1624615408" : "https://images.zastatic.com/apk/kaojin/kaojin_1006_1.apk";
            const params = {
                path: 'pages/matchmaker/matchmaker-spy/index',
                query: `channelId=${Z.getParam('channelId')}&subChannelId=${Z.getParam('subChannelId')}`,
            };
            const res = await Api.goYouLianMini(params)
            if (res.code === 0) {
                this.$report(123, "跳转优恋", {
                    ext1: JSON.stringify(res)
                });
                this.youlianLink = res.data
                location.href = this.youlianLink
            }
        },
        cancel() {
            this.handleVisibleChange(false)
        },
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirm() {
            this.$report(601, "同意协议弹窗-同意按钮点击", {});
            this.$report(42, "手机验证页-用户协议弹窗-同意", {});
            this.$emit('confirm');
        },
        handleCloseModal() {
            this.$report(602, "同意协议弹窗-取消按钮点击", {});
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.common-protocol-modal-img {
    width: 610px;
    height: 616px;
    background-color: #FFFFFF;
    border-radius: 32px;
    padding: 0;
    @include flex-center(column, null, center);
    border-radius: 56px;


    &__headtitle {
        font-size: 36px;
        color: #26273C;
        font-weight: 500;
        margin-bottom: 16px;
    }

    &__desc {
        width: 462px;
        font-weight: 400;
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 48px;
        // padding: 0 26px;
        letter-spacing: 0;
    }

    &__btn {
        @include flex-center();
        width: 462px;
        height: 88px;
        font-size: 32px;
        border-radius: 44px;
    }

    &__cancel {
        margin-top: 32px;
        font-size: 32px;
        line-height: 50px;
        text-align: center;
    }
    .common-protocol-modal__img {

    display: flex;
    flex-direction: column;
    align-items: center;
    .banner {
        width: 610px;
        height: 300px;
    }

    .tips {
        font-weight: 500;
        font-size: 30px;
        color: #222833;
        margin-top: 8px;
        text-align: center;
    }
    .download_btn {
        width: 420px;
        height: 82px;
        line-height: 83px;
        background: #222833;
        border-radius: 64.5px;
        font-weight: 500;
        font-size: 30px;
        color: #FFFFFF;
        text-align: center;
        margin-top: 66px;
    }
    .cancel_btn {
        width: 420px;
        height: 82px;
        line-height: 83px;
        margin-top: 12px;
        border-radius: 64.5px;
        text-align: center;
        font-size: 30px;
        padding-bottom: 44px;
    }
        img {
            width: 100%;
        }
    }
}
</style>
