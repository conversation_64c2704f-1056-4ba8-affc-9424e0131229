<!-- 老注册页AB测试方案测试组对应的下载页（推荐方案） -->
<template>
    <div class="recom-info-wrapper">
        <z-image
            :width="750"
            :height="748"
            src="https://photo.zastatic.com/images/common-cms/it/20220916/1663311827975_583.png"
        />
        <div class="list">
            <div
                class="member-card"
                v-for="(item, index) in recomList"
                :key="index"
            >
                <z-image
                    :width="686"
                    :height="80"
                    class="header"
                    src="https://photo.zastatic.com/images/common-cms/it/20220916/1663312236111_396035.png"
                >
                    匹配度：<span>{{ (Math.random() * 4 + 95).toFixed(1) }}%</span>
                </z-image>
                <div class="content">
                    <div
                        class="pic"
                        @click="handleClickItem"
                    >
                        <img :src="`${item.avatar}?imageMogr2/thumbnail/388x388`">
                        <div>
                            <img :src="`${item.photo1}?imageMogr2/thumbnail/184x184`">
                            <img :src="`${item.photo2}?imageMogr2/thumbnail/184x184`">
                        </div>
                        <div>当前APP在线</div>
                    </div>
                    <div class="name">
                        {{ item.nickName }} <img
                            v-if="item.hasVerify"
                            src="https://photo.zastatic.com/images/common-cms/it/20220916/1663313041052_388645.png"
                        >
                    </div>
                    <div class="tag">
                        <span>{{ item.age }}</span>
                        <span>{{ item.profession }}</span>
                        <span>{{ item.height }}</span>
                    </div>
                    <div class="des">
                        <span v-if="item.photoCnt">{{ item.photoCnt }}张照片</span>
                        <span v-if="item.momentCnt">｜{{ item.momentCnt }}条动态</span>
                        <span v-if="item.viewCnt">｜{{ item.viewCnt }}条观点</span>
                    </div>
                </div>
                <div :class="{'left-icon': (index+1) % 2 === 0, 'right-icon': (index+1) % 2 !== 0}"></div>
            </div>

            <div class="empty-recom">
                <img
                    :key="index"
                    v-for="(item, index) in picList"
                    :src="`${item}?imageMogr2/thumbnail/170x170`"
                >
                <div class="mask">
                    <z-image
                        :width="686"
                        :height="222"
                        src="https://photo.zastatic.com/images/common-cms/it/20220916/1663321955256_801122.png"
                    />
                </div>
            </div>
        </div>
        <div class="bottom-button">
            <div
                class="download-button"
                @click="handleDownload(15, '旧落地页(推荐)')"
            >
            </div>
            <div
                class="bottom-link"
                @click="handleViewWap(15, '旧落地页(推荐)')"
            >
                继续访问网页版
            </div>
        </div>
    </div>
</template>

<script>
import ZImage from "@/common/components/z-image";
import { handleDownload, handleViewWap} from '../js/util';
import { _getCommonRecommendCardList } from '../js/api';
import { reportKibana } from "@/common/utils/report.js";
    
export default {
    name: 'RecomInfo',
    data() {
        return {
            recomList: [],
            picList: []
        };
    },
    mounted() {
        reportKibana("旧落地页(推荐)", 15, "推人下载页访问");

        this.handleGetRecomList();
        window.addEventListener("popstate", () => {
            this.handleViewWap();
        });
    },
    destroyed() {
        window.removeEventListener("popstate", this.handleViewWap);
    },
    components: {
        ZImage
    },
    methods: {
        handleDownload,
        handleViewWap,
        async handleGetRecomList() {
            const result = await _getCommonRecommendCardList({count: 16});

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
            
            this.recomList = result.data.objectList;
            this.picList = result.data.picList;
        },

        handleClickItem() {
            reportKibana("旧落地页(推荐)", 15, "推人下载页-人物卡片点击");
            this.$toast('请下载珍爱APP，立即与Ta约会');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.recom-info-wrapper {
    min-height: 100vh;
    background-color: #FFE5F0;
    .list {
        position: relative;
        margin-top: -440px;
        padding-bottom: 298px;
        .member-card {
            position: relative;
            width: 686px;
            margin: 0 auto 40px;
            .header {
                padding: 18px 0px 18px 24px;
                font-weight: 500;
                font-size: 32px;
                color: #18181E;
                span {
                    font-weight: 600;
                    font-size: 44px;
                }
            }
            .content {
                padding: 32px 46px 40px;
                width: 686px;
                height: 652px;
                background: #FFFFFF;
                border-radius: 0 0 40px 40px;
                .pic {
                    position: relative;
                    @include flex-center(row, space-between, null);
                    >img:nth-child(1) {
                        width: 388px;
                        height: 388px;
                        border-radius: 16px;
                        object-fit: cover;
                    }
                    >div:nth-child(2) {
                        @include flex-center(column, space-between, null);
                        img {
                            width: 184px;
                            height: 184px;
                            border-radius: 16px;
                            object-fit: cover;
                        }
                    }
                    >div:nth-child(3) {
                        position: absolute;
                        left: 16px;
                        top: 320px;
                        width: 204px;
                        height: 52px;
                        background: #FBF002;
                        border-radius: 26px;
                        @include flex-center(row, center, center);
                        font-weight: 400;
                        font-size: 24px;
                        color: #18181E;
                    }
                }
                
                .name {
                    margin-top: 30px;
                    font-weight: 600;
                    font-size: 36px;
                    color: #191C32;
                    img {
                        position: relative;
                        top: 8px;
                        left: 4px;
                        width: 108px;
                        height: 40px;
                    }
                }

                .tag {
                    margin-top: 4px;
                    span {
                        display: inline-block;
                        padding: 8px 16px;
                        font-weight: 400;
                        font-size: 28px;
                        color: #2B2D33;
                        background: rgba(194,206,231, 0.2);
                        border-radius: 16px;
                    }
                }

                .des {
                    padding-left: 10px;
                    margin-top: 16px;
                    font-weight: 400;
                    font-size: 28px;
                    color: #828388;
                }
            }

            .left-icon {
                position: absolute;
                left: -30px;
                top: 676px;
                z-index: 0;
                width: 154px;
                height: 192px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663314204200_856975.png")
            }

            .right-icon {
                position: absolute;
                left: 598px;
                top: 708px;
                width: 120px;
                height: 128px;
                z-index: 2;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663313875611_300451.png")
            }
        }
        
        .empty-recom {
            margin: 0 auto;
            width: 686px;
            height: 516px;
            @include flex-center(row, space-between, null);
            border-radius: 40px;
            flex-wrap: wrap;
            position: relative;
            img {
                width: 170px;
                height: 170px;
                object-fit: cover;
            }
            img:nth-child(1){
                border-radius: 40px 0px 0px 0px;
            }
            img:nth-child(4){
                border-radius: 0px 40px 0px 0px;
            }
            img:nth-child(9){
                border-radius: 0px 0px 0px 40px;
            }
            img:nth-child(12) {
                border-radius: 0px 0px 40px 0px;
            }
            .mask {
                position: absolute;
                left: 0;
                top: 0;
                z-index: 1;
                @include flex-center(row, center, center);
                width: 100%;
                height: 100%;
                border-radius: 40px;
                background: rgba(0,0,0,0.33);
            }
        }
    }

    .bottom-button {
        position: fixed;
        right: 0;
        bottom: 40px;
        left: 0;
        text-align: center;
        z-index: 1;
        .download-button {
            width: 622px;
            height: 116px;
            margin: 0 auto;
            @include set-img("https://photo.zastatic.com/images/common-cms/it/20220916/1663318477504_358022.png");
        }
        .bottom-link {
            margin: 32px auto 0px;
            width: 258px;
            height: 58px;
            background: #FFE5F0;
            opacity: 0.9;
            border-radius: 29px;
            @include flex-center(row, center, center);
            font-weight: 400;
            font-size: 30px;
            color: #5B5B6A;
        }
    }
}
</style>