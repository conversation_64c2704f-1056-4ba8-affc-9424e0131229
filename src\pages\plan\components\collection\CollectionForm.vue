<template>
    <div class="collection-form-wrapper">
        <!-- 顶部背景 -->
        <div class="border-image-wrapper">
            <div class="content__space">
                请完善资料
            </div>
        </div>

        <!-- 填写内容 -->
        <div class="content__collection">
            <!-- 注册项 -->
            <div
                v-for="(item, index) in formInfo"
                :key="index"
                class="collection__item"
                @click="openSelect(item)"
            >
                <div class="collection__item__label">
                    {{ item.label }}
                </div>
                <!-- 性别项 -->
                <div
                    v-if="item.index === 'gender'"
                    class="collection__item__select--gender"
                >
                    <button
                        v-for="(item, index) in formInfo[0].selectArr"
                        :key="index"
                        class="gender-item"
                        @click="setGender(item)"
                        :class="
                            item.key === registerInfo.gender ? 'gender-item--selected' : ''
                        "
                    >
                        {{ item.text }}
                    </button>
                </div>

                <!-- 工作地、出生年份、学历、婚况、月收入 -->
                <div
                    v-else
                    class="collection__item__select"
                >
                    <span v-if="item.value">{{ item.value }}</span>
                    <span
                        v-else
                        class="color--9395A4"
                    >{{ "待完善" }}</span>
                </div>
            </div>

            <!-- 手机号 -->
            <div class="collection__phone_top">
                <div class="collection__phone__label">
                    您的手机号
                </div>
            </div>

            <div class="collection__phone_bottom">
                <div class="collection__phone__input">
                    <input
                        ref="refPhoneInput"
                        type="tel"
                        :value="phone"
                        @input="limit"
                        placeholder="请输入11位手机号"
                        maxlength="13"
                    />
                </div>
                <div
                    class="collection__phone__clear"
                    @click="phone = ''"
                ></div>
            </div>

            <!-- 图形验证码 -->
            <div
                v-if="showImgCode"
                class="collection__code"
            >
                <div class="collection__code__input">
                    <input
                        ref="refImgCodeInput"
                        type="text"
                        v-model="imgCode"
                        placeholder="请输入验证码"
                        maxlength="4"
                    />
                </div>
                <div
                    class="collection__code__img"
                    ref="refImgCode"
                ></div>
                <div
                    class="collection__code__refresh"
                    @click="setImgCode"
                ></div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <button
            :class="
                finished
                    ? 'content__submit content__submit--finished'
                    : 'content__submit'
            "
            @click="submitRegisterInfo(finished ? 'buttonIndex' : '')"
        >
            立即脱单
        </button>

        <!-- 协议 -->
        <div class="content__protocal">
            <div class="content__protocal__checked"></div>
            已阅读并同意<span @click="goUrl(1)">《珍爱网服务协议》</span><span @click="goUrl(2)">《个人信息保护政策》</span>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
        />

        <!-- 底部选择框 -->
        <select-panel
            v-if="showSelect"
            @close-select="closeSelect"
            :select-type="selectType"
            :select-param="selectParam"
            :validate-code="validateCode"
        />
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
import { SelectPanel, Modal } from "../common/index.js";
import { reportError, reportMagic } from "@/common/utils/report.js";
import { channelId, subChannelId, oExt9 } from "@/common/js/const.js";
import { reportKibana } from "@/common/utils/report.js";
import oUserSelect from "@/common/ocpx/huichuan.js";
import Api from "@/common/server/base";
import Prototype from "@/common/framework/prototype";
import {
    registerResult,
    pageTypeMap
} from "@/common/config/register-dictionary.js";

export default {
    components: {
        SelectPanel,
        Modal
    },
    data() {
        return {
            progress: 0,
            showModal: false,
            modalType: "modalValidate",
            modalParam: {},
            showSelect: false,
            selectType: "null",
            selectParam: {},
            showImgCode: false,
            lockBaseInfo: false,
            lockPhone: false,
            lockOverwrite: false,
            messageCode: ""
        };
    },
    computed: {
        ...mapState([
            "formInfo",
            "registerInfo",
            "code",
            "cmsConfig",
            "isCover",
            "hasCheckProtocal",
            "materialId"
        ]),
        ...mapGetters(["getProgress", "getNormalPhone"]),
        phone: {
            get() {
                return this.registerInfo.phone;
            },
            set(newVal) {
                // console.log("手机号校验",newVal);
                // newVal = newVal.replace(/[^\d]/g,'');
                this.setRegisterInfo({ phone: newVal });
                // this.phone2 = e.target.value.replace(/[^\d]/g,"");
            }
        },
        imgCode: {
            get() {
                return this.code;
            },
            set(newVal) {
                this.setCode(newVal);
            }
        },
        finished() {
            if (this.$refs.refPhoneInput && this.getProgress === 6) {
                this.$refs.refPhoneInput.scrollIntoView();
            }
            // 完成7项基本资料的填写
            return this.getProgress === 7;
        }
    },
    created() {},
    mounted() {
        this.scrollInput();
    },
    methods: {
        scrollInput() {
            // 安卓端控制软键盘
            if (/Android/i.test(navigator.userAgent)) {
                window.addEventListener("resize", () => {
                    if (
                        document.activeElement.tagName.toUpperCase() === "INPUT" ||
                        document.activeElement.tagName.toUpperCase() === "TEXTAREA"
                    ) {
                        window.setTimeout(() => {
                            document.activeElement.scrollIntoView({
                                block: "center"
                            });
                        }, 0);
                    }
                });
            }
        },
        formatMobile(mobile, e) {
            if (!mobile) return "";

            let result = mobile.replace(/[^(\d|\s)]/g, "");
            if (e.inputType === "insertText") {
                if (result.length === 3 || result.length === 8) {
                    return result.replace(result, result + " ");
                }
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/(\d$)/g, " $1");
                }
            } else if (e.inputType === "deleteContentBackward") {
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/\s$/, "");
                }
            }
            // else if (e.inputType === "insertFromPaste") {
            // 复制粘贴的情况
            result = result.replace(/\D/g, "");

            if (result.length > 3 && result.length < 8) {
                result = result.replace(/^(\d{3})/g, "$1 ");
            } else if (result.length >= 8) {
                result = result.replace(/^(\d{3})(\d{4})/g, "$1 $2 ");
            }
            // }
            // 中间删除的情况

            return result;
        },
        limit(e) {
            const phone = e.target.value.replace(/[^(\d|\s)]/g, "");
            this.phone = phone;

            if (this.phone) {
                this.phone = this.formatMobile(this.phone, e);
            }
        },
        ...mapMutations([
            "setRegisterInfo",
            "setFormInfo",
            "setCmsConfig",
            "setCode",
            "setOverwriteRegistrationSwitch",
            "setRegMemberId",
            "setIsCover"
        ]),
        ...mapActions([
            // "setModelInfo"
        ]),
        closeModal() {
            this.showModal = false;
        },
        closeSelect() {
            this.showSelect = false;
            this.selectParam = {};
        },
        // 页面跳转
        jump(path) {
            this.$router.push({
                path,
                query: {
                    // plan:123
                }
            });
        },
        async submitRegisterInfo(from) {
            let ext17 = [];
            this.formInfo.forEach(item => {
                if (item.value) {
                    ext17.push(item.value);
                }
            });
            if (this.phone) {
                ext17.push(this.phone);
            }
            ext17 = ext17.join(",");
            // 如果是大表单页按钮点击时触发，需要打桩
            if (from === "buttonIndex") {
                reportKibana("脱单计划H5", 21, "大表单页点击注册按钮", {
                    ext16: this.materialId,
                    ext17
                });
            }

            // 锁，已经发送请求
            if (this.lockBaseInfo || this.lockPhone) {
                return;
            }

            // 信息未填完整
            if (!this.finished) {
                return this.$toast("请完善资料再提交");
            }

            // 需要填写图形验证码
            if (this.showImgCode && !this.imgCode) {
                this.$toast("请输入图形验证码");
                return;
            }

            // 老注册页qms上报逻辑
            Z.tj.qms({
                dataType: "stat",
                data: {
                    type: "tag",
                    tname: `click.${channelId || ""}.${subChannelId || ""}`
                }
            });

            // 提交注册信息
            let sendData = { ...this.registerInfo },
                resData = null;
            sendData.height = -1; // 没有身高项，为兼容老注册页直接传-1
            delete sendData.phone; // 基础信息提交时，不提交手机号

            this.lockBaseInfo = true;
            // first参数用于确定是否直接跳老注册的密码页,新注册页没有密码页，该逻辑暂不迁移
            // if (Z.getParam('first')) {
            //     let isCover = Z.getParam('cover');
            //     sendData.phone = Z.getParam('phone');
            //     sendData.isCover = isCover ? 1 : 0;
            //     resData = await _submitWapRegBaseInfoToRegister(sendData);
            // } else {
            resData = await Api.submitWapRegBaseInfo(sendData);

            // }
            this.lockBaseInfo = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                // 老注册页qms上报逻辑
                reportError(
                    "新注册页(脱单计划H5),注册信息提交失败 |" +
                        resData.errorMessage +
                        " | " +
                        JSON.stringify(this.registerInfo)
                );
                // 打桩
                return;
            }

            // 提交手机号 / (手机号+图形验证码)等其他信息
            await this.submitPhoneInfo();
        },
        async submitPhoneInfo() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();

            let sendData = {
                phone: this.getNormalPhone,
                type: 0,
                imgCode: this.imgCode,
                // 极验参数，暂不迁移
                // challenge: self.gtObj.challenge,
                // validate: self.gtObj.validate,
                // seccode: self.gtObj.seccode,
                // 落地页url, 用于记录百度投放跳转到落地页时添加的参数
                landingUrl: document.referrer || undefined
            };

            // 【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype")
            };

            for (const v in toutiaoParamlist) {
                if (toutiaoParamlist[v]) {
                    sendData[v] = toutiaoParamlist[v];
                }
            }

            this.lockPhone = true;
            // let resData = await _submitWapRegNoPasswordInfo(sendData);
            let resData = await Api.sendWapMessageCodeV2(sendData);

            this.lockPhone = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                // 重置图形验证码
                // this.setImgCode();
                return;
            }

            // 走验证码逻辑
            this.goValidate();
        },
        setGender(item) {
            this.setFormInfo({ gender: item.text });
            this.setRegisterInfo({ gender: item.key });

            // 处理回传
            oUserSelect.mark({
                gender: item.key
            });
        },
        openSelect(currentItem) {
            if (["gender"].includes(currentItem.index)) {
                return;
            } else if (
                ["workCity", "birthday", "marriage", "education", "salary"].includes(
                    currentItem.index
                )
            ) {
                this.selectType = "selectSlide";
            }
            this.selectParam = currentItem;
            this.showSelect = true;
        },
        openMessageValid() {
            this.selectParam.label = "验证码已通过短信发到您的手机";
            this.selectType = "messagesType";
            this.showSelect = true;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.showModal = true;
        },
        modifyCustomer(data) {
            Z.ajax({
                type: "POST",
                url:
                    "https://call-api.zhenai.com/workapi/externalController/modifyCustomer",
                dataType: "json",
                opts: {
                    headers: {
                        "Content-Type": "application/json;charset=UTF-8"
                    }
                },
                data,
                success: function(results) {
                    console.log(results);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.log(xhr, textStatus, errorThrown);
                }
            });
        },
        setImgCode() {
            // 重置验证码
            this.imgCode = "";

            this.$nextTick(() => {
                let refImgCode = this.$refs.refImgCode,
                    refImgCodeInput = this.$refs.refImgCodeInput;

                let src =
                    window.location.protocol +
                    "//api.zhenai.com/register/getWapRegisterImgCode.do?t=" +
                    new Date().getTime();

                if (refImgCode) {
                    refImgCode.style.backgroundImage = `url(${src})`;
                    refImgCodeInput.focus();
                    // 输入框位置调整
                    refImgCodeInput.scrollIntoView({ block: "center" });
                }
                return;
            });
        },
        // 覆盖注册
        async coverRegister() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = Prototype.$gather.getOverwriteAccountParams(
                this.messageCode,
                pageTypeMap.PLAN
            );
            const resData = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                return;
            }

            if (resData.data.memberID) {
                // 存vuex
                this.setRegMemberId(resData.data.memberID);
                // 记录ocpc回传状态
                oUserSelect.mark({
                    msgValid: true
                });
            }

            reportKibana("脱单计划H5", 22, "注册成功并生成ID", {
                ext16: this.materialId,
                ext17: "覆盖注册"
            });

            this.closeModal();
            this.onRegisterFinished();
        },
        // 先处理OCPX相关，然后打开验证码弹窗
        goValidate() {
            Prototype.$gather.setBeforeValidateCodeOCPC();

            // 打开验证码窗口
            // this.openModal("modalValidate",{});
            this.openMessageValid();
        },
        goUrl(type) {
            if (type === 1) {
                location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
            } else if (type === 2) {
                location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
            }
        },
        // 核对验证码
        async validateCode(messageCode) {
            this.$gather.setAbtZaTTTCookie();
            const sendData = this.$gather.getValidateCodeParams(
                this.getNormalPhone,
                messageCode,
                pageTypeMap.PLAN
            );
            const resData = await Api.submitWapRegNoPasswordInfoV2(sendData);

            if (!resData.isError) {
                // 取号成功
                let {
                    type = undefined,
                    memberID = undefined,
                    oldMemberID = undefined, // 覆盖注册的旧会员id
                    overwriteRegistrationSwitch = undefined // 开关, 用于控制已注册弹窗中是否允许尝试打开/下载APP
                } = resData.data;

                // 存vuex
                this.setOverwriteRegistrationSwitch(overwriteRegistrationSwitch);

                // 微信朋友圈投放
                const [userId, externalUserId] = [
                    Z.getParam("userId"),
                    Z.getParam("externalUserId")
                ];
                let customId = memberID || oldMemberID;
                if (userId && externalUserId && customId) {
                    this.modifyCustomer({
                        userId,
                        externalUserId,
                        memberId: customId,
                        channelId,
                        subChannelId
                    });
                }

                if (memberID) {
                    // 存vuex
                    this.setRegMemberId(memberID);
                    // 记录ocpc回传状态
                    oUserSelect.mark({
                        msgValid: true
                    });
                }

                this.messageCode = messageCode;

                switch (type) {
                case registerResult.LOGIN_ACCOUNT.value:
                    // 展示已注册弹窗，不提供覆盖注册按钮
                    reportKibana("脱单计划H5", 22, "注册成功并生成ID", {
                        ext16: this.materialId,
                        ext17: "登录"
                    });
                    reportKibana("脱单计划H5", 23, "手机号验证成功", {
                        ext16: this.materialId,
                        ext17: "登录"
                    });
                    this.setIsCover(false);

                    oExt9.set(2);
                    this.openModal("modalRegistered", {
                        showCoverButton: false
                    });
                    break;
                case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                    reportKibana("脱单计划H5", 23, "手机号验证成功", {
                        ext16: this.materialId,
                        ext17: "覆盖注册"
                    });
                    this.setIsCover(true);
                    oExt9.set(1);
                    // 展示已注册弹窗，提供覆盖注册按钮
                    this.openModal("modalRegistered", {
                        showCoverButton: true
                    });
                    break;
                case registerResult.NEW_ACCOUNT.value:
                    // 正常注册
                    reportKibana("脱单计划H5", 22, "注册成功并生成ID", {
                        ext16: this.materialId,
                        ext17: "新注册"
                    });
                    reportKibana("脱单计划H5", 23, "手机号验证成功", {
                        ext16: this.materialId,
                        ext17: "新注册"
                    });

                    oExt9.set(2);
                    // 走验证码逻辑
                    // this.goValidate();
                    this.onRegisterFinished();
                    break;
                case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                    // 一年内未活跃非珍心会员 && 不弹窗 && 覆盖资料
                    reportKibana("脱单计划H5", 22, "注册成功并生成ID", {
                        ext16: this.materialId,
                        ext17: "覆盖注册"
                    });
                    reportKibana("脱单计划H5", 23, "手机号验证成功", {
                        ext16: this.materialId,
                        ext17: "覆盖注册"
                    });

                    this.setIsCover(true);
                    oExt9.set(1);
                    // 走覆盖注册的逻辑
                    // this.coverRegister();
                    this.onRegisterFinished();
                    break;
                default:
                    this.$toast("验证错误，请重试");
                    break;
                }
            }

            return resData;
        },
        onRegisterFinished() {
            // 核对成功
            this.$toast("注册成功");

            this.closeModal();

            this.clearCache();
            setTimeout(() => {
                this.$router.push("result");
            }, 2000);
        },
        clearCache() {
            // 清空注册信息
            localStorage.setItem("localFormInfo", "");
            localStorage.setItem("localRegisterInfo", "");
            localStorage.setItem("defaultBirthday", "");
            localStorage.setItem("defaultWorkCity", "");
            localStorage.setItem("defaultEducation", "");
            localStorage.setItem("defaultSalary", "");
            localStorage.setItem("defaultMarriage", "");
            // 清空脱单计划测试题信息
            localStorage.setItem("za_localPlanInfo", "");
            // 清空协议勾选状态
            localStorage.removeItem("protocolStatus");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";

.collection-form-wrapper {
  padding-bottom: 70px;
}

.border-image-wrapper {
  margin: 0 auto;
  width: 100%;
  height: 236px;
}

.content__space {
  margin: 0 auto;
  width: 100%;
  height: 236px;
  font-size: 44px;
  font-weight: 500;
  color: #191c32;
  text-align: center;
  line-height: 200px;
  @include set-img("../../assets/images/bg-form.png");
}

.content__collection {
  margin: -32px auto 0;
  padding-bottom: 78px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 40px 40px 0 0;
  overflow: hidden;
}

.collection__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 48px;
  width: 100%;
  height: 120px;
  line-height: 120px;
  font-size: 28px;
  font-weight: 400;
}

.collection__item:nth-child(1) {
  margin-top: 46px;
}

.collection__item__label {
  color: #9395a4;
  width: 240px;
}
.collection__item__select {
  position: relative;
  padding-right: 36px;
  font-size: 28px;
  font-weight: 500;
  color: #191c32;
  line-height: 40px;
}

.color--9395A4 {
  color: #9395a4;
}

.collection__item__select::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 23px;
  height: 23px;
  @include set-img("../../assets/images/icon-arrow.png");
}

.collection__item__select--gender {
  display: flex;
  justify-content: space-between;
  align-items: center;

  width: 304px;
  height: 100px;
}

.gender-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 137px;
  height: 65px;
  border: 1px solid #e6e6e6;
  border-radius: 34px;
  font-size: 28px;
  font-weight: 500;
  color: #26273c;
  line-height: 65px;
  text-align: center;
  background-color: #f6f5f8;
}

.gender-item--selected {
  border: none;
  background: #279bae;
  color: #ffffff;
}

.collection__phone_top {
  position: relative;
  padding: 0 48px;
  font-size: 28px;
  font-weight: 400;
  color: #26273c;
}

.collection__phone_bottom {
  position: relative;
  padding: 0 36px;
  font-size: 28px;
  font-weight: 400;
  color: #26273c;
}
.collection__phone__label {
  height: 120px;
  line-height: 120px;
  font-size: 28px;
  color: #9395a4;
}
.collection__phone__input {
  // 兼容处理safari光标漂移，input外多套一层+padding
  margin: 0 auto;
  padding-left: 40px;
  padding-top: 24px;
  width: 100%;
  height: 88px;
  background: #f6f5f8;
  border-radius: 44px;

  input {
    height: 40px;
    background-color: transparent;
    font-size: 28px;
    color: #191c32;
    line-height: 40px;
    text-align: left;
    width: 500px;
  }
}

.collection__phone__clear {
  position: absolute;
  top: 50%;
  right: 75px;
  width: 50px;
  height: 50px;
  transform: translateY(-50%);
  @include set-img("../../assets/images/icon-clear.png");
}

.collection__code {
  position: relative;
  margin: 32px auto 0;
  padding: 0 36px;
  height: 88px;
}
.collection__code__input {
  margin: 0 auto;
  padding-top: 24px;
  padding-left: 40px;
  height: 88px;
  background-color: rgba($color: #26273c, $alpha: 0.05);
  border-radius: 44px;
  text-align: left;

  input {
    display: block;
    height: 40px;
    background-color: transparent;
    font-size: 28px;
    color: #26273c;
    line-height: 40px;
    text-align: left;
  }
}
.collection__code__img {
  display: block;
  position: absolute;
  top: 14px;
  right: 150px;
  width: 144px;
  height: 60px;
  border-radius: 34px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.collection__code__refresh {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 76px;
  width: 48px;
  height: 48px;
  @include set-img("../../assets/images/icon-refresh.png");
}

.content__submit {
  display: block;
  margin: 16px auto 0;
  width: 630px;
  height: 110px;
  line-height: 110px;
  border-radius: 80px;
  font-size: 32px;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  opacity: 0.5;
  background: linear-gradient(180deg, #ff7599 0%, #ff5883 100%);
}

.content__submit--finished {
  opacity: 1;
}

.content__protocal {
  margin: 32px auto 0;
  width: 630px;
  font-size: 24px;
  color: #191c32;
  line-height: 38px;
  text-align: center;

  span {
    color: #191c32;
    font-weight: 500;
  }
}

.content__protocal__checked {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  line-height: 38px;
  transform: translateY(-2px);
  @include set-img("../../assets/images/icon-checked.png");
}

.content__protocal__uncheck {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  @include set-img("../../assets/images/icon-unchecked.png");
}
</style>
