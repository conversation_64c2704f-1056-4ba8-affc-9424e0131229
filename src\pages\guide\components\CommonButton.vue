<template>
    <div
        class="button-wrap"
        :style="{width: computedWidth, height: computedHeight, fontSize: computedFontSize}"
        @click="$emit('click', $event)"
    >
        {{ config.des }}
    </div>
</template>

<script>
export default {
    name: "CommonButton",
    props: {
        config: {
            type: Object,
            default: {
                width: 610,
                height: 116,
                fontSize: 36,
                des: "一键领取"
            }
        }
    },
    computed: {
        computedWidth() {
            return this.$utils.pxToRem(this.config.width);
        },
        computedHeight() {
            return this.$utils.pxToRem(this.config.height);
        },
        computedFontSize() {
            return this.$utils.pxToRem(this.config.fontSize);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.button-wrap {
    @include flex-center(row, center, center);
    background-image: linear-gradient(-60deg, #31A3E0 0%, #4C94FF 34%, #6E77FF 58%, #F591F9 100%);
    border-radius: 56px;
    color: #ffffff;
    font-weight: 500;
}
</style>
