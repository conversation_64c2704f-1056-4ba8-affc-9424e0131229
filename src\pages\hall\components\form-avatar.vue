<template>
    <div class="form-avatars">
        <z-image
            class="form-avatars__banner"
            :src="require('../assets/images/form-banner.png')"
            :width="730"
            :height="270"
            block
        >
            <div class="form-avatars__banner__text">
                已有<span>9</span>位{{registerForm.gender === 0 ? "女生" : "男生"}}想参加你的约会
            </div>
        </z-image>

        <div class="form-avatars__avatars" id="svgaAvatars"></div>
    </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import API from "@/common/server/base.js";

export default {
    name: "form-avatars",
    computed: {
        ...mapState(["registerForm"])
    },
    methods: {
        async setSVGA() {
            let player = new SVGA.Player("#svgaAvatars"),
                parser = new SVGA.Parser("#svgaAvatars");

            let resData = await API.getSpecifyGenderRandomAvatar({
                sex: this.registerForm.gender === 0 ? 1 : 0,
                limit: 7
            });

            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }

            // 后台给的是200x200，压缩至100x100
            let avatarList = resData.data.list;
            avatarList.forEach(item => {
                item.avatar += "?imageMogr2/thumbnail/100x100";
            });

            parser.load(require("../assets/svga-avatar.svga"), videoItem => {
                // 设置头像
                for (let i = 0; i < avatarList.length; i++) {
                    player.setImage(avatarList[i].avatar, `key${i + 1}`);
                }
                player.setVideoItem(videoItem);
                player.loops = 1;
                player.startAnimation();
                player.onFinished(() => {
                    // 动画执行一次，然后只循环48帧之后的上下浮动部分
                    player.startAnimationWithRange({
                        location: 48,
                        length: 48
                    });
                });
            });
        }
    },
    mounted() {
        this.setSVGA();
    }
};
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.form-avatars {
    &__banner {
        margin: 0 auto;
        position: relative;

        &__text {
            margin-top: 194px;
            font-size: 32px;
            color: #ffffff;
            text-align: center;

            span {
                padding: 0 10px;
                font-size: 36px;
                color: #ffb900;
                font-weight: 900;
            }
        }
    }

    &__avatars {
        height: 200px;
        width: 750px;
        overflow: hidden;
    }
}
</style>
