<template>
    <div class="exam_div">
        <navApp
            title="2024年恋爱段位考试"
            type="close"
        />
        <div class="exam_result">
            <img
                src="https://photo.zastatic.com/images/common-cms/it/20240527/1716783992611_634484.png"
                alt=""
                class="bgc_one"
            >
            <img
                src="https://photo.zastatic.com/images/common-cms/it/20240527/1716783871139_475682.png"
                alt=""
                class="bgc_two"
            >
            <!-- 答案页面 -->
            <div class="result_box">
                <!-- 答案内容 -->
                <img
                    :src="examResultPic"
                    alt=""
                    class="result_img"
                >
                <!-- 答案绘制昵称和头像 覆盖-->
                <img
                    :src="avatarURLSlip"
                    alt=""
                    class="avatar_img"
                >

                <div class="nick_name">
                    {{ nickName }}
                </div>

                <!-- 分享按钮-->
                <template>
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20240527/1716783911467_415263.png"
                        alt=""
                        class="share_btn"
                        @click="shareSqare"
                    >
                    <p class="share_text">
                        分享结果到动态广场
                    </p>
                </template>
            </div>
            <!-- 分享生成图片 canvas 绘制 -->
            <canvas
                class="canvas_love"
                id="examCanvas"
                style="display: none;"
            >
            </canvas>
        </div>
    </div>
</template>
<script>
import { debounce } from 'lodash';
import Toast from 'vant/lib/toast';
import navApp from '../../components/nav.vue';
import { reportKibana } from '@/common/utils/report';

export default {
    data() {
        return {
            avatarURL: '', // 头像
            examResultPic: '', // 结果页面
            nickName: '', // 昵称
            avatarURLSlip: '', // 头像压缩
        };
    },
    components: {
        navApp,
    },
    created() {
        // 来源
        this.source = this.$route.query.source;

        this.reportKibanaTotal(114, '测试报告页访问');
    },
    mounted() {
        // 页面闪动
        Toast.loading({ forbidClick: true, });
        this.getloveResult();
    },
    methods: {
        getloveResult() {
            Z.ajax({
                type: 'GET', url: 'https://api.zhenai.com/game/loveExam/saveExamResult.do',
            }, ({ isError, data, errorMessage, }) => {
                if (isError) {
                    Z.client.invoke('ui', 'showToast', { content: errorMessage, });
                    return;
                }
                this.avatarURL = data.avatarURL;
                this.avatarURLSlip = data.avatarURL + '?imageMogr2' + '/quality/85/gravity/center/thumbnail/200x200';

                this.examResultPic = data.examResultPic;
                this.nickName = data.nickName;

                Toast.clear();

            }, error => {
                Z.client.invoke('ui', 'showToast', { content: error, });
            });
        },
        // 分享
        shareSqare: debounce(function() {
            Toast.loading({ forbidClick: true, });
            this.canvasDraw();
            this.reportKibanaTotal(6, '考试结果页点击');

        }, 1000, {
            leading: true,
            trailing: false,
        }),
        // canvas Draw
        canvasDraw() {
            let canvas = document.getElementById('examCanvas');

            if (!canvas.getContext) { return; }
            // 拿到上下文
            let ctx = canvas.getContext('2d');


            // toDataUrl 生成图片模糊 加上设备像素比
            const ratio = window.devicePixelRatio || 1;

            canvas.width = 375 * ratio; // 实际渲染像素
            canvas.height = 613 * ratio; // 实际渲染像素
            canvas.style.width = `${375}px`; // 控制显示大小
            canvas.style.height = `${613}px`; // 控制显示大小

            // 画背景矩形
            let divWidth = 375 * ratio;
            let divHeight = 613 * ratio;


            // 导出图片透明色 所以这里需要填充一个矩形来加一个底色
            ctx.beginPath();
            ctx.fillStyle = '#FFE7E1';
            ctx.fillRect(0, 0, divWidth, divHeight);
            ctx.closePath();

            // 创建img 背景
            let imgOne = new Image(); // 创建一个<img>元素

            // 画布污染
            imgOne.setAttribute('crossOrigin', 'Anonymous');
            imgOne.src = 'https://photo.zastatic.com/images/common-cms/it/20240527/1716783992611_634484.png'; // 设置图片源地址
            imgOne.onload = () => {
                ctx.drawImage(imgOne, 0, 48 * ratio, 375 * ratio, 127 * ratio);
                ctx.save();

                // 创建答案图片
                let answerImg = new Image(); // 创建一个<img>元素

                answerImg.setAttribute('crossOrigin', 'Anonymous');
                answerImg.src = this.examResultPic; // 设置图片源地址
                answerImg.onload = () => {
                    ctx.drawImage(answerImg, 15 * ratio, 35 * ratio, 345 * ratio, 559 * ratio);
                    // 创建第二个背景
                    let imgTwo = new Image(); // 创建一个<img>元素

                    imgTwo.setAttribute('crossOrigin', 'Anonymous');
                    imgTwo.src = 'https://photo.zastatic.com/images/common-cms/it/20240527/1716783871139_475682.png';// 设置图片源地址

                    imgTwo.onload = () => {
                        ctx.drawImage(imgTwo, -1, 0, 376 * ratio, 50 * ratio);
                        ctx.save();
                        // 创建文本 姓名
                        ctx.font = `${14 * ratio}px PingFangSC`;
                        ctx.fillStyle = '#000';
                        ctx.fillText(this.nickName, 106 * ratio, 88 * ratio);
                        // 头像绘制
                        let img = new Image();

                        img.setAttribute('crossOrigin', 'Anonymous');
                        img.src = this.avatarURL;
                        img.onload = () => {
                            let width = 30;
                            let height = 30;
                            let circle = {
                                x: width / 2,
                                y: height / 2,
                                r: width / 2,
                            };

                            ctx.beginPath();
                            ctx.arc(45 * ratio, 85 * ratio, circle.r * ratio, 0, Math.PI * 2, false);
                            ctx.clip(); // 剪切路径
                            ctx.drawImage(img, 30 * ratio, 70 * ratio, 30 * ratio, 30 * ratio);
                            ctx.save();
                            ctx.drawImage(canvas, 0, 0, divWidth, divHeight);
                            // 画完进行缩放
                            ctx.scale(ratio, ratio);

                            setTimeout(() => {
                                this.saveUrl();

                            }, 500);

                        };

                    };

                };

            };


        },
        saveUrl() {
            let canvas = document.getElementById('examCanvas');
            let base64Url = canvas.toDataURL('image/png', 1).split(',')[1];

            Z.ajax({
                type: 'POST',
                url: 'https://api.zhenai.com/game/loveExam/shareMoment.do',
                data: { picBase64: base64Url, },
            }, ({ isError, data, errorMessage, }) => {
                Toast.clear();
                if (isError) {
                    Z.client.invoke('ui', 'showToast', { content: errorMessage, });
                    return;
                }
                Z.client.invoke('ui', 'showToast', { content: data.msg, });
            }, error => {
                Z.client.invoke('ui', 'showToast', { content: error, });
            });
            // 后台接口
        },
        reportKibanaTotal(accessPoint, accessPointDesc) {
            reportKibana({
                resourceKey: 'za_h5_loveTest',
                accessPoint,
                accessPointDesc,
                ext1: this.source || Z.getParam('from'),
            });
        },

    },

};
</script>
<style scoped lang="scss">
.exam_div{
  width: 100vw;
  min-height: 100vh;
  box-sizing: border-box;
}
.exam_result{
  width: 100vw;
  min-height: 90vh;
  background: #FFE7E1;
  position: relative;
  .bgc_one{
    position: absolute;
    top:48px;
    width: 375px;
    height:127px;
    z-index:1;
  }
  .bgc_two{
    position: absolute;
    top:0px;
    width: 375px;
    height:50px;
    z-index: 2;

  }
  .result_box{
    position: relative;
    z-index: 1;
    width:100vw;
    min-height:559px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 35px;

    .result_img{
      width:345px;
      height:559px;
    }
    .share_btn{
      margin-top: 25px;
      width:230px;
      height:50px;
    }
    .share_text{
      margin-top: 9px;
      height: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 10px;
      color: #5C5E66;
      letter-spacing: 0;
      margin-bottom: 20px;

    }
    .avatar_img{
      position: absolute;
      top: 70px;
      left: 30px;
      // background-color: #FFE7E1;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      object-fit: cover;
    }
    .avatar{
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }
    .nick_name{
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      letter-spacing: 0;
      text-align: center;
      position: absolute;
      top: 73px;
      left: 110px;
    }

  }
}
</style>
