<template>
    <div class="ques">
        <button
            class="ques_btn"
            :class="{ active: current === index }"
            v-for="(option, index) in columns"
            :key="index"
            @click="goNext(option, index)"
        >
            {{ option.text }}
        </button>
    </div>
</template>

<script>
import { marriage } from "@/common/config/register-dictionary";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            columns: marriage,
            current: -1
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            7, // 记录点
            "注册-婚况曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    methods: {
        goNext(option, index) {
            this.current = index;
            this.$select.mark({
                marriage: option.key
            });
            this.$storage.saveToStorage("__regInfo__", "marriage", option.key);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
