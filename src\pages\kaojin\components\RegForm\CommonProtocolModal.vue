<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="common-protocol-modal"
    >
        <div class="common-protocol-modal__headtitle">
            温馨提示
        </div>
        <div class="common-protocol-modal__desc">
            已阅读并同意<span @click="goServiceProtocol">《用户协议》</span>
            和<span @click="goPersonDescription">《隐私政策》</span>
        </div>

        <div
            class="common-protocol-modal__btn"
            :style="{ background: styleConfig.confirmButtonBgColor, color: styleConfig.confirmButtonColor}"
            @click="confirm"
        >
            确认
        </div>
        <div
            class="common-protocol-modal__cancel"
            :style="{ color: styleConfig.cancleButtonColor}"
            @click="handleCloseModal"
        >
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { reportKibana,reportLoveKibana } from "@/common/utils/report";

export default {
    name: 'CommonProtocolModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        styleConfig: {
            type: Object,
            default: {
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF',
            }
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(600, "同意协议弹窗访问击", {});
                    this.$report(41, "手机验证页-用户协议弹窗曝光", {});
                }
            },
            immediate: true,
        }
    },
    methods: {
        goServiceProtocol() {
            window.location.href = "https://i.zhenai.com/m/near/agreement/index.html#/user";
        },
        goPersonDescription() {
            window.location.href = "https://i.zhenai.com/m/near/agreement/index.html#/privacy";
        },
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirm() {
            this.$report(601, "同意协议弹窗-同意按钮点击", {});
            this.$report(42, "手机验证页-用户协议弹窗-同意", {});
            this.$emit('confirm');
        },
        handleCloseModal() {
            this.$report(602, "同意协议弹窗-取消按钮点击", {});
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.common-protocol-modal {
    width: 558px;
    background-color: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__headtitle {
        font-size: 36px;
        color: #26273C;
        font-weight: 500;
        margin-bottom: 16px;
    }

    &__desc {
        width: 462px;
        font-weight: 400;
        font-size: 28px;
        text-align: center;
        line-height: 42px;
        margin-bottom: 48px;
        // padding: 0 26px;
        letter-spacing: 0;
        span {
            color: #0092FF;
        }
    }

    &__btn {
        @include flex-center();
        width: 462px;
        height: 88px;
        font-size: 32px;
        border-radius: 44px;
    }

    &__cancel {
        margin-top: 32px;
        font-size: 32px;
        line-height: 50px;
        text-align: center;
    }
}
</style>
