<template>
    <div
        class="modal-wrapper"
        ref="refWrapper"
    >
        <div
            class="modal-mask"
            @click="closeModal"
        ></div>

        <!-- 引导下载弹窗 -->
        <div
            class="modal-content modal-download"
            ref="refDownload"
        >
            <div
                class="modal-content__title"
                :class="openDownload ? '' : 'limit-width'"
            >
                {{ openDownload ? '下载珍爱APP使用此功能' : '请前往各大应用市场搜索 “珍爱网”下载珍爱APP' }}
            </div>
            <div class="modal-download__subtitle">
                快来与Ta相遇，收获您的爱情吧！
            </div>
            <template
                v-if="openDownload"
            >
                <button
                    class="modal-content__button--submit"
                    @click="goDownload()"
                >
                    立即约会
                </button>
                <button
                    class="modal-content__button--cancel"
                    @click="closeModal()"
                >
                    取消
                </button>
            </template>
            <button
                v-else
                class="modal-content__button--submit"
                @click="closeModal()"
            >
                好的
            </button>
        </div>
    </div>
</template>

<script>
import borderStopScroll from "@/common/utils/borderStopScroll.js";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

import { Toast } from "vant";
import { session as Session} from "@/common/utils/storage";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    props: {
        openDownload: {
            type: Boolean
        },
    },
    data() {
        return {
        };
    },
    mounted() {
        // 处理滚动穿透
        borderStopScroll({
            wrapEle: this.$refs.refWrapper
        });
        borderStopScroll({
            wrapEle: this.$refs.refDownload
        });

    },
    methods: {
        closeModal() {
            this.$emit("close-modal");
        },
        goDownload() {
            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
// 弹窗共享样式
.modal-mask {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background: rgba($color: #26273c, $alpha: 0.6);
  z-index: 100;
}

.modal-content {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32px;
  z-index: 999;
}

.modal-content__title {
  margin: 48px auto 0;
  font-size: 36px;
  font-weight: 700;
  color: #26273c;
  text-align: center;
  line-height: 54px;

}

.limit-width {
  width: 408px;
}

.modal-content__button--disabled {
  background: #6c6d75;
  opacity: 0.5;
}

.modal-content__button--submit {
  display: block;
  margin: 48px auto 0;
  width: 462px;
  height: 88px;
  background: #767dff;
  border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 88px;
}

.modal-content__button--cancel {
  display: block;
  margin: 20px auto 0;
  font-size: 32px;
  font-weight: 400;
  color: #767dff;
  text-align: center;
  line-height: 47px;
}

// 引导下载弹窗
.modal-download {
  width: 602px;
  height: 424px;
}

.modal-download__subtitle {
  margin-top: 28px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  line-height: 47px;
  text-align: center;
}
</style>
