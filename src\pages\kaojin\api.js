
import { formatAxios } from './lib/index';
export const getList = (data) => {
    return formatAxios("/api/common/evaluate/start-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const answerResultId = (data) => {
    return formatAxios("/api/common/evaluate/answer-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const getAnswerResult = (data) => {
    return formatAxios("/api/common/evaluate/get-evaluate-by-type.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};
export const bindEvaluate = (data) => {
    return formatAxios("/api/common/evaluate/bind-evaluate.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};

export const registeredH5CheckReport = (data) => {
    return formatAxios("/api/common/ad/account/report/registeredH5CheckReport.do",  data, 'post', true, false,{'Content-Type': 'application/json;charset=utf-8'});
};

