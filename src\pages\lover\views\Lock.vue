<template>
    <div class="lock-wrapper">
        <div class="title">
            恭喜！
        </div>
        <div class="sub-title">
            大数据已为你匹配到适合你的理想恋人，<br />
            快去完善信息解锁{{ genderText }}的联系方式吧！
        </div>
        <z-image
            :width="750"
            :height="778"
            :src="lockPhoto"
        />
        <common-button
            class="lock"
            :config="{ width: 610, height: 116, fontSize: 36, des: '去解锁' }"
            @click="handleLock"
        />
    </div>
</template>

<script>
import CommonButton from "../components/CommonButton";
import { session } from "@/common/utils/storage.js";

export default {
    name: "Lock",
    components: {
        CommonButton
    },
    computed: {
        lockPhoto() {
            return session.getItem("gender") === 0
                ? "https://photo.zastatic.com/images/common-cms/it/20220902/1662105347026_874504_t.png"
                : "https://photo.zastatic.com/images/common-cms/it/20220905/1662367628602_848963_t.png";
        },
        genderText() {
            return session.getItem("gender") === 1 ? '他' : '她';
        }
    },
    mounted() {
        this.$report(12, "剪影页-访问");
    },
    methods: {
        handleLock() {
            this.$report(12, "剪影页-去解锁按钮点击");
            this.$router.push("/form");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.lock-wrapper {
    position: relative;
    z-index: 1;
    padding-top: 53px;
    text-align: center;
    min-height: 100vh;
    background: linear-gradient(180deg, #f6f6fb 40%, #d4d1ff);
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        left: 0;
        bottom: 0;
        width: 750px;
        height: 550px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220905/1662349717190_46242_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .title {
        font-weight: 500;
        font-size: 64px;
        color: #26273c;
    }
    .sub-title {
        margin-top: 38px;
        line-height: 54px;
        font-weight: 400;
        font-size: 36px;
        color: #6c6d75;
    }
    .lock {
        margin: 56px auto 0px;
        animation: OpacityBreath 1.2s ease-in-out infinite;
    }
}
@keyframes OpacityBreath {
    0% {
        transform: scale(0.8);
    }

    50% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.8);
    }
}
</style>
