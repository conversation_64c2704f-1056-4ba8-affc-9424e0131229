<template>
    <div class="collection-wrapper" :class="rate ? '' : 'small-screen'">
        <div class="header-img">
            <img src="https://photo.zastatic.com/images/common-cms/it/20240402/1712049935951_817893_t.png" alt="">
        </div>
        <div class="text-content" v-if="currentId < 1">
            <p>
                你想找什么样的人？<br/>
                你对未来的另一半有什么要求？<br/>
                更清晰的择偶标准<br/>
                会让你在爱情路上走得更顺<br/>
                <span class="bold">要求越多，大数据推送越精准</span><br/>
                <span class="bold">可以匹配更多符合你意向的异性</span>
            </p>
            <div class="fill-in">
                <img @click="choose('', 0)" src="https://photo.zastatic.com/images/common-cms/it/20240408/1712547710629_283270_t.png" alt="">
            </div>
        </div>
        <div class="text-content text-result" v-else-if="currentId > 5">
            <img class="success" src="https://photo.zastatic.com/images/common-cms/it/20240408/1712568777347_651121_t.png" alt="">
            <p>
                <span class="bold">感谢你的填写</span><br/>
                <span class="late">稍后会有红娘老师致电您，<br/>
                给你一对一推荐符合你的异性，<br/>
                请注意接听</span>
                <span class="late">你也可以下载APP先提前了解呦～</span>
            </p>
            <div class="fill-in" v-if="showButton">
                <img @click="handleDownload(1)" src="https://photo.zastatic.com/images/common-cms/it/20240408/1712569607686_282896_t.png" alt="">
            </div>
        </div>
        <div class="question-content" v-else>
            <span class="title">单选题</span>
            <p class="question">{{currentQuestion.title}}</p>
            <ul class="answer">
                <li
                    v-for="(item, index) in currentQuestion.question"
                    :key="index"
                    :class="index === selectId ? 'active' : ''"
                    @click="choose(item, index)"
                >
                    {{item}}</li>
            </ul>
        </div>
        <div class="footer-content" v-if="showButton">
            <div class="footer-left">
                <img src="https://photo.zastatic.com/images/common-cms/it/20240408/1712547945498_453762_t.png" alt="">
                <span>点击下载珍爱APP</span>
            </div>
            <div class="footer-right" @click="handleDownload">
                立即下载
            </div>
        </div>
    </div>
</template>

<script>

import {reportKibana} from '@/common/utils/report.js';
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { session as Session } from "@/common/utils/storage";
import { Toast } from "vant";
import Api from "@/common/server/base";
import { channelId, subChannelId } from '@/common/js/const';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { mapState } from "vuex";
export default {
    data() {
        return {
            rate: true,
            type: 0,
            dialogStatus: false,
            list: {},
            selectId:'',
            currentId: 0,
            currentQuestion: {},
            showButton: true,
            questionList: [
                {
                    title: '你是否介意对方的收入比你低？',
                    question: ['介意', '不介意']
                },
                {
                    title: '你希望对方的学历为？',
                    question: ['我不在意对方的学历', '大专及以上', '大学本科及以上', '硕士及以上', '博士']
                },
                {
                    title: '你是否介意对方是离异？',
                    question: ['介意', '不介意']
                },
                {
                    title: '你能接受与对方的年龄差为几岁？',
                    question: ['0-3岁', '0-6岁', '爱情无关年龄']
                },
                {
                    title: '你之前谈过几次恋爱？',
                    question: ['没有谈过', '1次', '2次', '3次及以上']
                }

            ],
        };
    },
    computed: {
        ...mapState([
            "registerInfo",
            "cmsConfig",
        ]),
    },
    async created(){
        this.rate = (window.innerHeight / window.innerWidth) > 1.95
        this.type = this.$route.query.type
        this.currentId = +this.$route.params.id
        this.currentQuestion = this.questionList[this.currentId-1]
        this.showButton = this.cmsConfig.downloadApp === 0;

    },
    watch: {
        '$route'(to, from) {
            this.currentId = +this.$route.params.id
            this.currentQuestion = this.questionList[this.currentId-1]
            if (this.currentId === 1) {
                reportKibana("线下大表单", 3, '择偶收入页访问', {
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 2) {
                reportKibana("线下大表单", 5, '择偶学历页访问', {
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 3) {
                reportKibana("线下大表单", 7, '择偶婚况页访问', {
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 4) {
                reportKibana("线下大表单", 9, '择偶年龄页访问', {
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 5) {
                reportKibana("线下大表单", 11, '择偶恋爱次数页访问', {
                    ext29: Session.getItem('reg_memberid')
                });
            }
        }
    },
    mounted(){
        // 打桩
        reportKibana("线下大表单", 1, '择偶首页访问', {
            ext29: Session.getItem('reg_memberid')
        });
    },
    methods:{
        async choose(item, index) {
            if (this.currentId === 0) {
                reportKibana("线下大表单", 2, '择偶首页-立即填写', {
                    ext29: Session.getItem('reg_memberid')
                });
                this.$router.push({
                    path: "/mateSelection/"+(this.currentId+1)
                });
                return
            } else if (this.currentId === 1) {
                reportKibana("线下大表单", 4, '择偶收入页-按钮点击', {
                    ext28: item,
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 2) {
                reportKibana("线下大表单", 6, '择偶学历页-按钮点击', {
                    ext28: item,
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 3) {
                reportKibana("线下大表单", 8, '择偶婚况页-按钮点击', {
                    ext28: item,
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 4) {
                reportKibana("线下大表单", 10, '择偶年龄页-按钮点击', {
                    ext28: item,
                    ext29: Session.getItem('reg_memberid')
                });
            } else if (this.currentId === 5) {
                reportKibana("线下大表单", 12, '择偶恋爱次数页-按钮点击', {
                    ext28: item,
                    ext29: Session.getItem('reg_memberid')
                });
            }
            this.selectId = index
            const params = {
                phone: Session.getItem('advertPhone'),
                question: this.currentQuestion.title,
                answer: item
            }
            const resData = await Api.hookAnswerNotes(params)
            if (resData.code === 0) {
                this.$router.push({
                    path: "/mateSelection/"+(this.currentId+1)
                });
                this.selectId = ''
            } else {
                reportKibana("线下大表单", 20, '保存大表单答题异常', {
                    ext1: resData && JSON.stringify(resData),
                    ext29: Session.getItem('reg_memberid')
                });
            }
        },
        handleDownload(type) {
            if (type === 1) {
                reportKibana("线下大表单", 14, '感谢填写页-按钮点击', {
                    ext29: Session.getItem('reg_memberid')
                });
            } else {
                reportKibana("线下大表单", 15, '底部下载引导-立即下载', {
                    ext29: Session.getItem('reg_memberid')
                });
            }

            if (this.cmsConfig.jumpULoveCupid == '1') {
                this.$router.push({
                    path: "/successResult"
                });
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({ value: true });
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }

        },
    }
};
</script>

<style lang="scss" scoped>

@keyframes scaleDraw {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
.collection-wrapper{
    width: 750px;
    min-height: 100vh;
    background: url("https://photo.zastatic.com/images/common-cms/it/20240408/1712546873833_223658_t.png") no-repeat;
    background-size: 100% 100%;
    text-align: center;
    padding: 0 50px 100px 50px;
    .header-img {
        img {
            width: 682px;
            height: auto;
            margin-top: 66px;
        }
    }
    .text-content {
        color: #000000;
        font-size: 28px;
        font-weight: 300;
        line-height: 60px;
        margin-top: 236px;
        .bold {
            font-weight: bold;
        }
        .fill-in {
            margin-top: 279px;
            img {
                width: 391px;
                height: auto;
            }
        }
        .success {
            width: 90px;
            height: 90px;
        }
        .late {
            display: block;
            margin-top: 20px;
        }
        &.text-result {
            .fill-in {
                margin-top: 142px;
            }
        }
    }

    .question-content {
        width: 667px;
        height: 817px;
        margin-top: 105px;
        background: url("https://photo.zastatic.com/images/common-cms/it/20240408/1712561930854_923228_t.png") no-repeat;
        background-size: 100% 100%;
        padding: 70px 30px 0;
        text-align: left;
        .title {
            margin-top: 89px;
            margin-left: 16px;
            font-weight: 600;
            font-size: 32px;
            color: #999999;
            line-height: 50px;
        }
        .question {
            margin-left: 16px;
            font-weight: 600;
            font-size: 36px;
            color: #000000;
            line-height: 50px;
            margin-top: 18px;
        }
        .answer {
            margin-top: 90px;
            li {
                width: 590px;
                height: 80px;
                background: #F1EDFF;
                border-radius: 8px;
                font-weight: 500;
                font-size: 28px;
                color: #000000;
                text-align: center;
                line-height: 80px;
                margin-bottom: 20px;
                &.active {
                    background: #8F8BFF;
                    color: #FFFFFF;
                }
            }
        }
    }
    .footer-content {
        display: flex;
        height: 96px;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        .footer-left {
            width: 473px;
            height: 96px;
            text-align: left;
            background: #ffffff;
            display: flex;
            align-items: center;
            img {
                width: 54px;
                height: 54px;
                margin-left: 27px;
            }
            span {
                font-weight: 400;
                font-size: 24px;
                color: #333333;
                margin-left: 19px;
            }
        }
        .footer-right {
            height: 96px;
            line-height: 96px;
            flex: 1;
            text-align: center;
            background-image: linear-gradient(90deg, #F97CC4 0%, #FF9ECB 97%);
            font-weight: 500;
            font-size: 28px;
            color: #FFFFFF;
            align-self: center;
        }
    }
}
.small-screen {
    .text-content {
        margin-top: 91px;
        .fill-in {
            margin-top: 238px;
        }
    }
    .question-content {
        margin-top: 55px;
    }
}
</style>
