import { createApp } from '@/common/framework';
import { PAGE_TYPE } from "./config";
import App from './App.vue';
import router from './router';
import { reportLoveKibana1 } from "@/common/utils/report";

Vue.prototype.$report = (accessPoint, accessPointDesc, options) => {
    const key = '新注册路径手机前置';
    return reportLoveKibana1(key, accessPoint, accessPointDesc, options);
};
createApp({
    router,
    render: h => h(App),
    resourceKey: PAGE_TYPE
});
