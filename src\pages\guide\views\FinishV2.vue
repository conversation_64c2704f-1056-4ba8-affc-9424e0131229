<template>
    <div class="finish">
        <div class="panel">
            <!-- header -->
            <div class="panel-result">
                <div class="title">脱单概率</div>
                <div class="rate">{{ score.percent }}<span class="subfix">%</span></div>
            </div>


            <div class="panel-title">· 请领取指南,立即提升脱单成功率 ·</div>
            <!-- footer -->
            <Phone
                ref="refPhone"
                class="panel-phone"
                @submit="handleSubmit"
            />
            <common-submit
                ref="refCommonSubmit"
                :page-type="PAGE_TYPE"
                :style-config="{
                    modalConfig: {
                        confirmButtonColor: '#FFFFFF',
                        confirmButtonBgColor: '#17263D',
                        cancleButtonColor: '#000000',
                    },
                    protocolConfig: {
                        textColor: '#222833',
                        protocolColor: '#222833',
                        protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20230516/1684220210896_758809_t.png'
                    }
                }"
                :handle-after-regisiter="handleJump"
                :handle-login="handleJump"
                :handle-cancle="handleJump"
            >
                <div
                    class="panel-submit"
                    @click="handleReport"
                >
                    领取指南
                </div>
            </common-submit>
        </div>
    </div>
</template>
<script>
import CommonSubmit from '@/common/business/CommonSubmit';
import Phone from "../components/RegForm/phone.vue";
import { PAGE_TYPE, countScore } from "../config";

export default {
    name: "Finish",
    components: {
        Phone,
        CommonSubmit,
    },
    data() {
        return {
            isShow: true,
            PAGE_TYPE,
            score: null
        };
    },
    created(){
        const score = countScore();
        score.percent = score.percent.replace('%', '');
        this.score = score;
    },
    methods: {
        handleJump() {
            this.$router.push({ path: '/result'});
        },
        handleReport(e) {
            this.$report(42, '手机验证页-点击立即解锁');
            if(this.$refs.refPhone.inputValue.length !== 11){
                this.$toast("输入手机号解锁你的单身自救指南");
                e.stopPropagation();
            } 
        },
        handleSubmit() {
            // 触发提交手机号逻辑
            this.$refs.refCommonSubmit.handleSubmit(false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.finish{
    width: 100vw;
    height: 100vh;
    min-height: 1400px;
    @include set-img('https://photo.zastatic.com/images/common-cms/it/20230517/1684317888122_52616_t.png');
    background-size: cover;
    .panel{
        position: absolute;
        top: 106px;
        left: 34px;
        width: 666.2px;
        height: 1218.68px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20230518/1684381317795_180865_t.png");
        text-align: center;

        &-result{
            margin-top: 96px;
            position: relative;
            display: inline-block;
            
            .title {
                font-weight: 400;
                font-size: 40px;
                color: #03355E;
                height: 56px;
                line-height: 56px;
                margin: 0 auto;
                position: relative;
                z-index: 2;

                &::before{
                    content: '';
                    position: absolute;
                    bottom: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 168px;
                    height: 10px;
                    background-image: linear-gradient(90deg, #99f4ffde 0%, #16abfbab 100%);
                    z-index: -1;
                }
            }

            .rate {
                font-size: 140px;
                font-weight: 700;
                letter-spacing: 0;
                text-align: center;
                background-image: linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                -webkit-background-clip: text;
                color: transparent;
                position: relative;

                .subfix {
                    font-size: 60px;
                    position: absolute;
                    right: -52px;
                    bottom: 14px;
                    background-image: linear-gradient(-200deg, #31A3E0, #4C94FF);
                    background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF);
                    -webkit-background-clip: text;
                }
            }
        }

        &-title {
            margin-top: 162px;
            font-weight: 400;
            font-size: 32px;
            color: #043762;
            text-align: center;
        }

        &-phone{
            margin-top: 48px;
            margin-left: 46px;
            background: #EAF1FA;
            border: 2px solid #91B3D6;
            border-radius: 24px;
        }

        &-submit{
            margin: 32px auto 0;
            width: 590px;
            height: 100px;
            background-image: linear-gradient(-90deg, #31A3E0 0%, #4C94FF 34%, #6E77FF 58%, #F591F9 100%, #F591F9 100%);
            box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
            border-radius: 55px;
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;
            line-height: 100px;
        }
    }
    /deep/ .common-protocol {
        align-items: flex-start;
        margin: 68px 24px 0 50px;

        .common-protocol__checke-box {
            flex: 0 0 auto;
            width: 22px;
            height: 22px;
        }

        .common-protocol__text {
            font-size: 24px;
            line-height: 30px;
            a {
                font-weight: 500;
            }
        }
    }
}
</style>
