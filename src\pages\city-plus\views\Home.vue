<template>
    <div class="home">
        <div
            class="home-banner"
            :style="{
                backgroundImage: `url(${cmsConfig.homeHeadImg})`
            }"
        ></div>
        <!-- E群聊 -->
        <template v-if="cmsConfig.planName === '同城脱单群聊(A)'">
            <div class="home-list">
                <div
                    class="home-list-item"
                    v-for="entity in cmsConfig.entity"
                >
                    <span
                        class="hli-nail"
                        :style="{
                            background: `${entity.groupChatButtonColor}`
                        }"
                    ></span>
                    <div class="hli-img">
                        <strong><b></b><b></b><b></b></strong>
                        <span
                            v-for="avatar in entity.groupChatAvatars"
                            :style="{
                                backgroundImage: `url(${avatar}?imageMogr2/thumbnail/36x36)`
                            }"
                        ></span>
                    </div>
                    <div class="hli-info">
                        <h4>{{ entity.groupChatName }}</h4>
                        <p>[99条] {{ entity.groupChatLatestNews }}</p>
                    </div>
                    <span
                        class="hli-back back"
                        @click="handleJoin(1)"
                    ></span>
                </div>
            </div>
            <div class="home-bt">
                <div
                    class="home-bt-button"
                    :style="{ background: `${cmsConfig.homeBackgroundColor}` }"
                    @click="handleJoin(2)"
                >
                    {{ cmsConfig.homeButtonText }}
                </div>
                <common-protocol
                    class="home-wrapper__protocol"
                    :is-checked.sync="isCheckProtocol"
                    :agreement-status="cmsConfig.agreementStatus"
                    :style-config="{
                        textColor: '#6C7175',
                        protocolColor: '#5368F0',
                        protocolCheckedUrl:
                            'https://photo.zastatic.com/images/common-cms/it/20220808/1659942723638_477294_t.png'
                    }"
                />
                <div
                    class="home-company"
                >
                    粤ICP备09157619号-1 深圳市珍爱网信息技术有限公司
                </div>
            </div>
        </template>
        <!-- D活动 -->
        <template v-else>
            <div class="home-group">
                <div
                    class="home-group-item"
                    v-for="entity in cmsConfig.entity"
                    :style="{
                        backgroundImage: `url(${entity.homeActivityImg})`
                    }"
                    @click="handleJoin(3)"
                >
                    <div class="hgi-info">
                        <h4>{{ entity.homeActivityText }}</h4>
                        <span class="hgi-back back"></span>
                    </div>
                </div>
            </div>
            <div class="home-ft">
                <div
                    class="home-btn"
                    @click="handleJoin(4)"
                >
                    <span>+</span>查看更多活动
                </div>
                <common-protocol
                    class="home-wrapper__protocol"
                    :is-checked.sync="isCheckProtocol"
                    :agreement-status="cmsConfig.agreementStatus"
                    :style-config="{
                        textColor: '#6C7175',
                        protocolColor: '#5969E0',
                        protocolCheckedUrl:
                            'https://photo.zastatic.com/images/common-cms/it/20220808/1659942723638_477294_t.png'
                    }"
                />
                <div
                    class="home-company"
                >
                    粤ICP备09157619号-1 深圳市珍爱网信息技术有限公司
                </div>
            </div>
        </template>
        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :page-type="cmsConfig.planName"
            :style-config="{
                confirmButtonColor: '#fff',
                confirmButtonBgColor: '#5969e0',
                cancleButtonColor: '#5969e0'
            }"
        />
    </div>
</template>

<script>
import CommonProtocol from "@/common/business/CommonProtocol.vue";
import CommonProtocolModal from "@/common/business/components/CommonProtocolModal.vue";
import { storage as Storage } from "@/common/utils/storage";

export default {
    name: "Home",
    data() {
        return {
            isCheckProtocol: false,
            isShowModal: false,
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false,
                sixthScreen: false
            },
            screenHeight: 0
        };
    },
    inject: ["cmsConfig"],
    components: {
        CommonProtocol,
        CommonProtocolModal
    },
    mounted() {
        console.log("9999", this.cmsConfig);
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                this.isCheckProtocol = value === 0;
            },
            {
                immediate: true
            }
        );

        this.$report(1, "首页访问");
        this.$report(3000, "首页访问（监控）");

        window.addEventListener("scroll", this.handleExposure);
        this.screenHeight = document.documentElement.clientHeight;
        Storage.removeItem(`cachedRegisterForm-${this.cmsConfig.planName}`);
    },
    destroyed() {
        window.removeEventListener("scroll", this.handleExposure);
    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.isShowModal = true;
            setTimeout(() => {
                this.$router.push({
                    path: "/about"
                });
            }, 300);
        },
        handleJoin(type) {
            const REPORTENUM = {
                1: "首页-群聊卡片点击",
                2: "首页-加入群聊按钮点击",
                3: "首页-活动卡片点击",
                4: "首页-查看更多活动点击"
            };

            this.$report(3, REPORTENUM[type]);

            if (!this.isCheckProtocol) {
                this.isShowModal = true;
            } else {
                this.$router.push({
                    path: "/about"
                });
            }
        },

        handleExposure() {
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$report(2, "首页第2屏曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$report(2, "首页第3屏曝光");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$report(2, "首页第4屏曝光");
                this.reportLock.fourthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 4 &&
                !this.reportLock.fifthScreen
            ) {
                this.$report(2, "首页第5屏曝光");
                this.reportLock.fifthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 5 &&
                !this.reportLock.sixthScreen
            ) {
                this.$report(2, "首页第6屏曝光");
                this.reportLock.sixthScreen = true;
            }
            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleExposure);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.home {
    padding-bottom: 234px;
    background: #f3f4f6;
    &-banner {
        height: 246px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
    .back {
        width: 50px;
        height: 50px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220804/1659608592473_144663_t.png)
            no-repeat;
        background-size: 100% 100%;
        animation-name: bounceIn;
        animation-duration: 1.2s;
        animation-delay: 0.2s;
        animation-timing-function: ease-in;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
    }
    &-list {
        margin: 20px 14px 64px;
        &-item {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            padding: 16px 32px 16px 16px;
            margin-bottom: 24px;
            border-radius: 24px;
            background: #fff;
            overflow: hidden;
        }
        .hli-nail {
            position: absolute;
            z-index: -1;
            top: 0;
            right: 0;
            width: 166px;
            height: 100%;
            transform: scaleX(-1);
        }

        .hli-img {
            flex-shrink: 0;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: space-between;
            flex-wrap: wrap;
            width: 130px;
            padding: 6px 6px 2px 6px;
            border-radius: 16px;
            background: #ecedef;
            > span {
                font-size: 0;
                width: 36px;
                height: 36px;
                margin-bottom: 4px;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                border-radius: 6px;
            }
            > strong {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: absolute;
                top: -12px;
                right: -12px;
                color: #fff;
                width: 38px;
                height: 22px;
                padding: 8px;
                background: #fe4f06;
                border-radius: 12px;
                > b {
                    flex-shrink: 0;
                    width: 6px;
                    height: 6px;
                    background: #ffffff;
                    border-radius: 50%;
                }
            }
        }
        .hli-info {
            flex: 1;
            flex-wrap: nowrap;
            margin-left: 22px;
            > h4 {
                color: #333333;
                font-size: 36px;
                font-weight: 600;
            }
            > p {
                max-width: 56vw;
                margin-top: 10px;
                color: #757575;
                font-size: 28px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .hli-back {
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }
    }
    &-group {
        margin: 20px 40px 40px;
        &-item {
            display: flex;
            align-items: flex-end;
            height: 270px;
            margin-bottom: 30px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            border-radius: 16px;
            overflow: hidden;
        }
        .hgi-info {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            height: 102px;
            padding-top: 8px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000 100%);
            > h4 {
                margin-right: 16px;
                color: #fff;
                font-size: 40px;
                font-weight: 700;
            }
            .hgi-back {
                margin-right: 32px;
            }
        }
    }
    &-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 104px;
        margin: 48px 140px 32px;
        color: #5368f0;
        font-size: 40px;
        font-weight: 500;
        background: #fff;
        border: 2px solid rgba(92, 94, 102, 0.1);
        border-radius: 52px;
        > span {
            width: 36px;
            height: 36px;
            margin-right: 16px;
            line-height: 36px;
            text-align: center;
            color: #fff;
            background: #5368f0;
            border-radius: 50%;
        }
    }
    &-bt {
        position: fixed;
        z-index: 1;
        left: 0;
        right: 0;
        bottom: 0;
        height: 274px;
        background: linear-gradient(
            180deg,
            rgba(250, 250, 251, 0) 0%,
            #f3f4f6 34%
        );
        &-button {
            height: 104px;
            margin: 24px 144px 48px;
            text-align: center;
            line-height: 104px;
            color: #fff;
            font-size: 40px;
            font-weight: 500;
            border-radius: 54px;
        }
    }
    &-ft {
        position: fixed;
        z-index: 1;
        left: 0;
        right: 0;
        bottom: 0;
        height: 274px;
        background: linear-gradient(
            180deg,
            rgba(250, 250, 251, 0) 0%,
            #f3f4f6 34%
        );
    }
    &-company {
        color: #333;
        font-size: 22px;
        padding-top: 8px;
        text-align: center;
    }
}
@keyframes bounceIn {
    from,
    20%,
    40%,
    60%,
    80%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }

    20% {
        transform: scale3d(1.1, 1.1, 1.1);
    }

    40% {
        transform: scale3d(0.9, 0.9, 0.9);
    }

    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }

    80% {
        transform: scale3d(0.97, 0.97, 0.97);
    }

    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}
</style>
