<template>
    <van-popup
        class="proposal-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="proposal-modal__title">
            <div>珍爱APP红娘老师将给你来电</div>
            <div>为你提供专业爱情答疑，避开恋爱盲点找到恋爱优势，助你早日脱单</div>
        </div>

        <div
            class="proposal-modal__btn"
            @click="confirmModal"
        >
            收下锦囊
        </div>
        <div
            class="proposal-modal__cancel"
            @click="closeModal"
        >
            暂时放弃
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import Api from '@/common/server/base';
import { session as Session } from '@/common/utils/storage.js';
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";

export default {
    name: 'ProposalModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: <PERSON>olean,
            default: false,
        },
    },

    inject: ["cmsConfig"],

    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        confirmModal() {
            this.handleDealClick(1);
            this.$emit('input', false);
            this.$report(12, "引导下载页-锦囊弹窗-接受按钮点击");
        },
        closeModal() {
            this.handleDealClick(2);
            this.$emit('input', false);
            this.$report(12, "引导下载页-锦囊弹窗-拒绝按钮点击");
        },

        async handleDealClick(behavior) {
            const sendData = {
                memberId: Session.getItem("reg_memberid"),
                behavior,
                source: pageTypeChnMap[this.cmsConfig.planName]
            };

            const result = await  Api.recordProposalClickBehavior(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.proposal-modal {
    width: 586px;
    height: 917px;
    background: url("https://photo.zastatic.com/images/common-cms/it/20220806/1659782655925_632526_t.png") no-repeat;
    background-size: 100% 100%;
    padding-top: 20px;
    @include flex-center(column, null, center);

    &__title {
        margin-top: 427px;
        padding-top: 40px;
        width: 504px;
        height: 192px;
        border-radius: 22px;
        background: rgba(255,255,255,0.77);
        color: #4828A2;
        text-align: center;
        >div:nth-child(1) {
            font-weight: 500;
            font-size: 32px;
        }
        >div:nth-child(2) {
            width: 448px;
            margin: 20px auto 0px;
            line-height: 34px;
            font-weight: 400;
            font-size: 24px;
        }
    }


    &__btn {
        margin-top: 33px;
        width: 504px;
        height: 88px;
        font-size: 32px;
        background: #5368F0;
        color: #ffffff;
        border-radius: 77px;
        @include flex-center();
    }

    &__cancel {
        width: 504px;
        height: 88px;
        border: 2px solid #5368F0;
        font-weight: 400;
        font-size: 32px;
        color: #5368F0;
        margin-top: 24px;
        border-radius: 77px;
        @include flex-center();
    }
}
</style>
