export const storage = {
    setItem(k, v) {
        try {
            return localStorage.setItem(k, JSON.stringify(v));
        } catch (ex) {
            localStorage.clear();
            return false;
        }
    },
    getItem(k) {
        try {
            const jsonString = localStorage.getItem(k) || '';
            return JSON.parse(jsonString);
        } catch (ex) {
            return null;
        }
    },
    removeItem(k) {
        try {
            return localStorage.removeItem(k);
        } catch (ex) {
            return null;
        }
    }
};


// from Roger 处理输入手机号保留3-4-4格式
export const formatPhone = (e) => {
    // 存储当前光标位置用于之后重置
    let position = e.target.selectionStart;

    if (e.inputType === "deleteContentBackward") {
        // 删除的情况
        // 删到空格时 需要多删除空格前一位数
        if (position === 3 || position === 8) {
            let temArr = e.target.value.split('');
            temArr.splice(position - 1, 1);
            e.target.value = temArr.join('');
            // 光标也要跟着前移一位
            position -= 1;
        }
        // 格式化
        e.target.value = e.target.value.replace(/\D/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3').trim();
        // 重置光标,setTimeout用于兼容苹果手机
        setTimeout(() => {
            e.target.selectionStart = e.target.selectionEnd = position;
        }, 0);
        // e.target.selectionStart = e.target.selectionEnd = position;
    } else if (e.inputType === "insertText") {
        // 插入的情况
        if (e.target.value.length < 9) {
            e.target.value = e.target.value.replace(/\D/g, '').replace(/(\d{3})(\d{0,4})/, '$1 $2');
        } else {
            e.target.value = e.target.value.replace(/\D/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3');
        }
        if (position < e.target.value.length) {
            // 输入空格后第一位数时 光标要往后移一位
            if (position === 4 || position === 9) {
                position += 1;
            }
            // 重置光标
            setTimeout(() => {
                e.target.selectionStart = e.target.selectionEnd = position;
            }, 0);

        }
    } else {
        // 复制粘贴的情况
        let pasteStr = e.target.value.replace(/\D/g, '');
        // maxlenth为13，所以这里要限制位数大于11时截取
        if (pasteStr > 11) {
            e.target.value = pasteStr.substr(0, 11).replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3');
        }
    }

    return e.target.value;

};
