<template>
    <div class="city">
        <van-picker
            ref="refPicker"
            class="m_register-picker"
            title="标题"
            value-key="text"
            :item-height="55"
            :visible-item-count="5"
            :show-toolbar="false"
            :columns="columns"
        />
        <button
            class="city_btn"
            @click="goNext"
        >
            下一步
        </button>
    </div>
</template>

<script>
import { Picker } from "vant";
import "vant/lib/picker/style";

import { locationMixin } from "@/common/utils/mixin";
import { storage as Storage } from "@/common/utils/storage";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js"; 

export default {
    mixins:[locationMixin],
    name: "WorkCity",
    components: {
        vanPicker: Picker,
    },
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            columns: Z.workCity,
            selectParam:{
                selectArr:Z.workCity
            }
            // areaIndexes: [2, 1, 0]
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            3, // 记录点
            "注册-工作地曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    mounted() {
        // 无缓存，直接调用定位
        this.handleLocate();
        // this.$refs.picker.setIndexes(this.areaIndexes);

    },
    methods: {
        goNext() {
            const picker = this.$refs.refPicker;
            const values = picker.getValues();
            this.$select.mark({
                workCity: values[2].key ? values[2].key :  values[1].key ? values[1].key : ""
            });
            this.$storage.saveToStorage(
                "__regInfo__",
                "workCity",
                values[2].key ? values[2].key :  values[1].key ? values[1].key : ""
            );
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss">
@import "../assets/css/common.scss";

.city_btn {
    @include btn-style;
    margin-bottom: 40px;
    color: #767dff;
    box-shadow: 0px 4px 22px 0px #e8e5fe;
}
.m_register-picker {
    background: transparent;
    overflow: hidden;
    padding: 0 100px 120px 100px;
}
.m_register-picker .van-picker-column__item {
    color: #fff;
    opacity: 0.5;
}

.m_register-picker .van-picker__mask {
    background-image: linear-gradient(180deg, transparent),
        linear-gradient(0deg, transparent);
}

.m_register-picker .van-hairline--top-bottom:after,
.van-hairline-unset--top-bottom:after {
    border-width: 2px 0;
    border-color: rgba(255, 255, 255, 0.6);
}

.m_register-picker .van-picker__frame {
    left: 0;
}
.m_register-picker .van-picker-column__item--selected {
    opacity: 1;
}

.m_register-picker
    .van-picker-column__item--selected
    + .van-picker-column__item {
    opacity: 0.6;
}
</style>
