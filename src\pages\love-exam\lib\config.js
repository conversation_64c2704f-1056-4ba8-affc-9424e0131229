/**
 * 配置文件
 * 根据前端域名匹配接口地址
 */
const CONFIG = {
    API_HOST_NAME: function() { // 获取接口域名
      let host = ''

      if (window.location.host === 'i-test.zhenai.com') { // 测试环境
        host = 'api-test.zajiebao.com'
      } else if (window.location.host === 'i-pre.zhenai.com') { // 预发布环境
        host = 'api-pre.zajiebao.com'
      } else if (window.location.host === 'i.zhenai.com') { // 线上环境
        host = 'api.zajiebao.com'
      } else {
        host = 'api-test.zajiebao.com'
      }
      return `${host}`
    },
  }

  export default CONFIG
