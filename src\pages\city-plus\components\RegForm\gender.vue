<template>
    <div class="gender">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}
        </div>
        <div class="gender-info">
            <div
                class="gender-info-item"
                v-for="item in list.options"
                @click="goNext(item.key)"
            >
                <div
                    class="gii-img"
                    :class="{active: chIndex === item.key}"
                ></div>
                <p>{{ item.text }}</p>
            </div>
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
export default {
    name: "Gender",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    inject: ['cmsConfig'],
    mounted() {
        this.$report(4, '性别页访问');
    },
    data() {
        return {
            chIndex: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) && Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`).gender
        };
    },
    methods: {
        goNext(val) {
            const params = {
                key: "gender",
                value: val
            };
            const REPORTENUM = {
                0: '性别页-选择男',
                1: '性别页-选择女'
            };

            this.$report(4, REPORTENUM[val]);
            this.chIndex = val;
            setLocalRegisterForm(params, this.cmsConfig.planName);
            setTimeout(() => {
                this.$emit("val-updated", val);
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
.gender {
    padding: 0 48px;
    .title {
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-top: 26px;
        color: #0f1122;
        font-size: 32px;
    }
    &-info {
        margin: 86px 38px 0;
        display: flex;
        justify-content: space-around;
        &-item {
            width: 258px;
            > p {
                margin-top: 32px;
                color: #26273c;
                font-size: 36px;
                text-align: center;
            }
        }
        .gii-img {
            width: 100%;
            height: 258px;
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659685156462_692310_t.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
        .active {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659685151916_704016_t.png);
        }

        &-item:nth-of-type(1) {
            margin-right: 62px;
            .gii-img {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659685158819_79283_t.png);
            }
            .active {
                background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659685149595_972971_t.png);
            }
        }
    }
}
</style>
