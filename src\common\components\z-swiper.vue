<template>
    <div class="z-swiper"
         :style="{
            height: $utils.pxToRem(height)
         }">
        <div class="z-swiper__btn"
             v-if="useLeft"
             @click="clickLeft"
             :style="customBtnStyle">
            <slot name="left"/>
        </div>

        <div class="z-swiper__list-wrapper"
             ref="swiperBody"
             :style="{
                width: $utils.pxToRem(innerWidth)
             }"
             @touchstart="touchstart"
             @touchmove="touchmove"
             @touchend="touchend"
             @touchcancel="touchcancel">
            <div class="z-swiper__list"
                 ref="swiperWrapper"
                 style="transform: translateX(0px)">
                <div
                    class="z-swiper__item"
                    ref="swiperItems"
                    :style="{
                       marginRight: $utils.pxToRem(spanGap),
                    }"
                    v-for="(item, index) in doubleList"
                    :data-index="index"
                    :key="index">
                    <slot
                        :item="item"
                        :index="index"/>
                </div>
            </div>
        </div>

        <div class="z-swiper__btn"
             v-if="useRight"
             @click="clickRight"
             :style="customBtnStyle">
            <slot name="right"/>
        </div>
    </div>
</template>

<script>
import { createComponent } from "@/common/framework";

export default createComponent({
    name: 'z-swiper',
    props: {
        height: {
            type: Number,
            required: true,
        },
        innerWidth: {
            type: Number,
            required: true,
        },
        // Todo 动态计算 spanGap，而不是由传进来
        spanGap: {
            type: Number,
            default: 0,
        },
        sideGap: {
            type: Number,
            default: 0,
        },
        // 文档注明，list 不支持响应式，开发确保 list 加载完成再渲染组件
        list: {
            type: Array,
            required: true,
        },
        autoPlay: {
            type: Boolean,
            default: false,
        },
        step: {
            type: Number,
            default: -0.5,
        },
        playDelay: {
            type: Number,
            default: 2000,
        },
        slideAnimationDuration: {
            type: Number,
            default: 300,
        },
    },
    data() {
        return {
            halfLen: 0,
            leftBorder: 0,
            rightBorder: 0,
            itemFullWidth: 0,
            lastX: 0,
            intersectionRatioThreshold: 0.95,
            replayTimer: null,
            animationInterval: null,
            doubleList: [],
        }
    },
    computed: {
        customBtnStyle() {
            return {
                padding: `0 ${ this.$utils.pxToRem(this.sideGap) }`
            }
        },
        useLeft() {
            return this.$slots['left']
        },
        useRight() {
            return this.$slots['right']
        },
    },
    methods: {
        getObserveEntries() {
            return new Promise((resolve) => {
                const observer = new IntersectionObserver((entries) => {
                    observer.disconnect();

                    resolve(entries);
                }, {
                    root: this.$refs.swiperBody,
                });

                this.$z_.forEach(this.$refs.swiperItems, (el) => {
                    observer.observe(el);
                });
            })
        },
        getDomTranslateX() {
            // TODO 封装获取样式方法
            return Number(this.$refs.swiperWrapper.style.transform.split('(')[1].split('px')[0])
        },
        setDomTranslateX(translateX) {
            this.$refs.swiperWrapper.style.transform = `translateX(${ translateX }px)`;
        },
        initDoubleList() {
            const mid = Math.floor((this.list.length / 2));
            this.halfLen = mid;
            this.doubleList = this.$z_.flatten([ this.list.slice(mid), this.list, this.list.slice(0, mid) ])
        },
        initItemFullWidth() {
            const itemDom = this.$refs.swiperItems[0];
            const itemWidth = Number(window.getComputedStyle(itemDom).getPropertyValue('width').replace('px', ''));

            // clientWidth 算出来宽度的数会有一点偏差，用 window.getComputedStyle 最精确
            const renderedSpanGap = Number(window.getComputedStyle(itemDom).getPropertyValue('margin-right').replace('px', ''));
            this.itemFullWidth = itemWidth + renderedSpanGap;
        },
        initTranslateX() {
            const translateX = this.getDomTranslateX() - this.itemFullWidth * this.halfLen;
            this.setDomTranslateX(translateX);

            return Math.abs(translateX);
        },
        play() {
            this.translateX = this.getDomTranslateX();

            const animationCallback = () => {
                this.setMove(this.step);
                this.animationInterval = requestAnimationFrame(animationCallback);
            }

            this.animationInterval = requestAnimationFrame(animationCallback);
        },
        replay() {
            clearTimeout(this.replayTimer);

            this.replayTimer = setTimeout(() => {
                this.play();
            }, this.playDelay);
        },
        // TODO setMove、slideLeft、slideRight 逻辑复用
        setMove(xDiff) {
            this.translateX += xDiff;
            const translateXOpt = Math.abs(this.translateX);

            if (translateXOpt >= this.rightBorder) {
                this.translateX = -(this.leftBorder + (translateXOpt - this.rightBorder));
            } else if (translateXOpt <= this.leftBorder) {
                this.translateX = -(this.rightBorder - (this.leftBorder - translateXOpt));
            }

            this.setDomTranslateX(this.translateX);
        },
        async slideLeft() {
            const entries = await this.getObserveEntries();

            const firstVisibleIndex = this.$z_.findIndex(entries, (item) => {
                // 某些浏览器在计算位置时跟预期会有一点点偏差，原来期望完全相交 1 的元素可能相交 0.99，所以将完全相交判定设置比 1 低一点点。
                return item.intersectionRatio >= this.intersectionRatioThreshold;
            });

            const isAllVisible = this.$z_.filter(entries, (item) => {
                return item.intersectionRatio >= this.intersectionRatioThreshold
            }).length === this.halfLen;

            const targetIndex = isAllVisible ? firstVisibleIndex + 1 : firstVisibleIndex;
            const target = entries[targetIndex];

            const xDiff = target.boundingClientRect.left - target.rootBounds.left;

            this.translateX -= xDiff;
            const translateXOpt = Math.abs(this.translateX);

            setTimeout(() => {
                if (translateXOpt >= this.rightBorder) {
                    // 比如 13 - 8 后得到真实索引 5，视口第一个元素就是索引为 5 的元素
                    this.translateX = -(this.itemFullWidth * (targetIndex - this.list.length));
                    this.setDomTranslateX(this.translateX);
                }
            }, this.slideAnimationDuration);

            this.setDomTranslateX(this.translateX);
        },
        async slideRight() {
            const entries = await this.getObserveEntries();

            const lastVisibleIndex = this.$z_.findLastIndex(entries, (item) => {
                return item.intersectionRatio >= this.intersectionRatioThreshold;
            });

            const isAllVisible = this.$z_.filter(entries, (item) => {
                return item.intersectionRatio >= this.intersectionRatioThreshold;
            }).length === this.halfLen;

            const targetIndex = isAllVisible ? lastVisibleIndex - 1 : lastVisibleIndex;
            const target = entries[targetIndex];

            const xDiff = target.rootBounds.right - target.boundingClientRect.right;

            this.translateX += xDiff;
            const translateXOpt = Math.abs(this.translateX);

            setTimeout(() => {
                if (translateXOpt <= this.leftBorder) {
                    // targetIndex - this.halfLen 拿到前置位的索引，也就是将 targetIndex - this.halfLen 放到左边界的位置，此时 targetIndex 刚好在右边界
                    this.translateX = -(this.itemFullWidth * (targetIndex - this.halfLen + 1 + this.list.length));
                    this.setDomTranslateX(this.translateX);
                }
            }, this.slideAnimationDuration);

            this.setDomTranslateX(this.translateX);
        },
        /*
         * event handle
         */
        touchstart(evt) {
            // 兼容某些安卓手机只触发一次 touchmove 的问题
            // https://blog.csdn.net/cdnight/article/details/50625391
            evt.preventDefault();
            cancelAnimationFrame(this.animationInterval);

            const touch = evt.targetTouches[0];

            this.lastX = touch.pageX;
            this.translateX = this.getDomTranslateX();
        },
        touchmove(evt) {
            // 修复某些安卓手机左滑时页面后退的问题
            // https://www.cnblogs.com/Miracle-ZLZ/p/7852421.html
            evt.preventDefault();

            const touch = evt.targetTouches[0];
            const xDiff = touch.pageX - this.lastX;

            this.lastX = touch.pageX;
            this.setMove(xDiff);
        },
        touchend() {
            if (this.autoPlay) {
                this.replay();
            }
        },
        touchcancel() {
            // 兼容小米手机不触发 touchend 问题
            this.touchend();
        },
        clickLeft() {
            this.handleSlide(true);
        },
        clickRight() {
            this.handleSlide();
        },
    },
    created() {
        this.initDoubleList();
    },
    mounted() {
        this.initItemFullWidth();
        const initialTranslateX = this.initTranslateX();
        this.leftBorder = initialTranslateX;
        this.rightBorder = this.itemFullWidth * this.list.length + initialTranslateX;

        if (this.autoPlay) {
            this.replay();
        }

        this.handleSlide = this.$z_.throttle((isLeft) => {
            cancelAnimationFrame(this.animationInterval);

            Object.assign(this.$refs.swiperWrapper.style, {
                'transition-duration': `${ this.slideAnimationDuration }ms`,
            });

            this.translateX = this.getDomTranslateX();

            if (isLeft) {
                this.slideLeft();
            } else {
                this.slideRight();
            }

            setTimeout(() => {
                Object.assign(this.$refs.swiperWrapper.style, {
                    'transition-duration': '0ms',
                });

                if (this.autoPlay) {
                    this.replay();
                }
            }, this.slideAnimationDuration);

        }, this.slideAnimationDuration + 300, {
            trailing: false,
        });
    },
});
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.z-swiper {
    position: relative;
    user-select: none;
    @include flex-center(row, null, null);

    &__list-wrapper {
        overflow: hidden;
    }

    &__list {
        width: 100%;
        @include flex-center(row, null, null);
    }

    &__item {
        flex-shrink: 0;
    }

    &__btn {
        @include flex-center(row, null, center);
    }
}
</style>
