<template>
    <div class="result">
        <z-image
            class="result-code"
            :width="750"
            :height="778"
            :src="styleConfig.resultTopImg"
        >
            <img
                @touchstart="handleTouchStart"
                @touchend="handleTouchEnd"
                @touchcancel="handleTouchEnd"
                :src="wxInfo.code"
            />
            <div
                class="result-code-btn"
                @click="handleJumpMiniProgram"
            />
        </z-image>
        <img
            class="result-title"
            :src="styleConfig.resultMiddleImg"
        >
        <result-info 
            :memberInfo="memberList.promotionsMemberInfoVo"
        />
        <result-info
            v-for="(item, index) in memberList.recomList" 
            :key="index"
            :memberInfo="item"
        />
    </div>
</template>

<script>
import ResultInfo from '../components/ResultInfo';
import { getMiniPath, getWxCode } from '@/common/business/utils/wecom';

export default {
    name: "Result",
    data() {
        return {
            timer: null,
            wxInfo: {
                link: '', //小程序链接
                code: '', //企微二维码
                userId: '' //工号id
            }
        };
    },
    components: {
        ResultInfo,
    },
    computed: {
        bottomCode() {
            return this.cmsConfig.reportViewType === 1 ? 'https://photo.zastatic.com/images/cms/assistant/20220815/2136019362125257.png' : 'https://photo.zastatic.com/images/cms/banner/20220817/94447058823397514.png';
        }
    },
    inject: ["cmsConfig","memberList", "styleConfig"],
    async mounted() {
        const pageType = this.cmsConfig.reportViewType === 1 ? 30 : 39;
        const res = await getWxCode(pageType);
        this.wxInfo.code = (res && res.qrCode) || this.bottomCode;
        this.wxInfo.userId = res && res.userId;
        this.$report(5, '引导下载页访问', {
            ext18: res.userId
        });
        this.wxInfo.link = await getMiniPath(this.wxInfo.code, this.cmsConfig.reportViewType);
    },

    methods: {
        handleJumpMiniProgram() {
            this.$report(8, '引导下载页-去添加微信点击', {
                ext18: this.wxInfo.userId
            });
            // 跳转小程序
            setTimeout(() => { window.location.href = this.wxInfo.link; }, 300);
        },

        handleTouchStart() {
            this.timer = setTimeout(() => {
                // 模拟长按事件
                this.$report(9, '引导下载页-二维码长按', {
                    ext18: this.wxInfo.userId
                });
            }, 500);
        },

        handleTouchEnd() {
            clearTimeout(this.timer);
        },
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.result{
    padding-bottom: 50px;
    @include flex-center(column, null, center);
    &-code{
        position: relative;
        img {
            position: absolute;
            top: 322px;
            left: 270px;
            transform: rotate(-4deg);
            width: 224px;
            height: 224px;
            border-radius: 8px;
        }
        &-btn {
            top: 570px;
            left: 170px;
            position: absolute;
            transform: rotate(-4deg);
            width: 454px;
            height: 120px;
        }
    }

    &-title{
        margin: 50px auto 0;
        width: 490px;
        height: 108px;
    }
}
</style>
