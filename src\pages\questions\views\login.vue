<template>
    <div class="login">
        <h3 class="login_title">
            恭喜你完成测试！
        </h3>
        <van-field
            v-model.trim="phone"
            class="login_input"
            type="tel"
            placeholder="请输入手机号"
            maxlength="11"
            ref="fieldPhone"
            @focus="onFocus"
            @blur="onBlur"
        />
        <!-- 验证码暂时不需要 -->
        <van-field
            v-if="showImgCode"
            v-model="imgCode"
            class="login_input"
            center
            clearable
            placeholder="请输入验证码"
            right-icon="https://photo.zastatic.com/images/common-cms/it/20220105/1641354567084_218892_t.png"
        >
            <template slot="button">
                <img
                    class="login_img"
                    :src="imgBg"
                    @click="setloginImg"
                />
            </template>
        </van-field>
        <van-field
            v-model.trim="messageCode"
            class="login_input"
            center
            clearable
            placeholder="请输入验证码"
            maxlength="4"
        >
            <template slot="button">
                <van-button
                    class="login_btn"
                    :class="{ disabled: phone.length < 11 }"
                    @click="handleSendCode"
                >
                    获取验证码
                </van-button>
            </template>
        </van-field>

        <div class="login_info">
            <button
                class="login_info_btn"
                @click="submit"
            >
                立即解锁测试结果
            </button>
            <p class="login_info_txt">
                已阅读并同意
                <a
                    href="https://i.zhenai.com/m/portal/register/prDeal.html"
                    @click="goPolicy"
                >珍爱网服务协议</a>
                和
                <a
                    href="https://i.zhenai.com/m/portal/register/serverDeal.html"
                    @click="goPolicy"
                >个人信息保护政策</a>
            </p>
        </div>
    </div>
</template>

<script>
import { Field, Button } from "vant";
import "vant/lib/field/style";
import "vant/lib/button/style";
import { post } from "@/common/utils/ajax.js";
import Modal from "../components/modal.vue";
const phoneRegExp = /^1\d{10}/;
import { session } from "@/common/utils/storage.js";
import { pageTypeMap } from "@/common/config/register-dictionary.js";
export default {
    name: "Login",
    components: {
        VanField: Field,
        VanButton: Button,
        Modal
    },
    data() {
        return {
            phone: "",
            messageCode: "",
            imgCode: "",
            imgBg: "//api.zhenai.com/register/getWapRegisterImgCode.do",
            showImgCode: false,
            confirmText: `继续<span class='tips'>（原有账号的资料将被覆盖）</span>`,
            scrollTop: 0
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            9, // 记录点
            "填写手机号注册页面曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    activated() {
        document.querySelector("#trigger")
            ? (document.querySelector("#trigger").style.opacity = 0)
            : "";
    },
    mounted() {
        this.$nextTick(() => {
            this.$refs.fieldPhone.focus();
        });
    },
    methods: {
        setloginImg() {
            const loginImgEl = this.$el.querySelector(".login_img");
            loginImgEl.src = `${this.imgBg}?t=${Date.now()}`;
        },
        // 重新发送验证码
        async handleSendCode(e) {
            e.preventDefault();
            if (!phoneRegExp.test(this.phone)) {
                return this.$toast("请输入正确的手机号");
            }
            e.target.disabled = true;
            const COUNT_DOWN_TIME = 60;
            let timeLeft = COUNT_DOWN_TIME;
            const timeSubduction = () => {
                if (timeLeft > 0) {
                    e.target.innerText = `重新发送(${timeLeft}s)`;
                    timeLeft--;
                    setTimeout(timeSubduction, 1000);
                } else {
                    e.target.disabled = false;
                    e.target.innerText = `获取验证码`;
                }
            };
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                3100, // 记录点
                "提交手机号（监控）", // 点描述
            );
            const res = await post("/register/sendWapMessageCode.do", {
                phone: this.phone,
                type: 0
            });
            if (res.isError) {
                e.target.disabled = false;
                return this.$toast(res.errorMessage);
            }
            timeSubduction();
            this.$toast(res.data.msg);
        },
        async submit() {
            if (!phoneRegExp.test(this.phone)) {
                return this.$toast("请输入正确的手机号");
            }
            if (this.messageCode.length < 4) return this.$toast("请输入正确的验证码");
            const formData = {
                phone: this.phone,
                messageCode: this.messageCode,
                pageType: pageTypeMap.QUESTIONS
            };
            this.$select.mark({
                submitPhone: true
            });
            const res = await post("/register/validatePhoneCodeFirst.do", formData);
            if (res.isError) return this.$toast(res.errorMessage);
            const { result } = res.data;
            let ext17 = "新注册";
            // 覆盖注册
            if (result === 2) {
                ext17 = "覆盖注册";
                this.toRegister();
            } else if (result === 3) {
                ext17 = "登录";
                this.$router.push({ path: "/blindinfo" });
            } else {
                // 正常注册
                this.toRegister(0);
            }
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                50, // 记录点
                "手机号验证成功", // 点描述
                {
                    ext17
                }
            );
            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                10, // 记录点
                "填写手机号注册页-点击解锁测试结果按钮", // 点描述
                {
                    ext1: Z.getParam("materialId")
                }
            );
        },
        /**
         * 注册逻辑
         * @param {Number} 是否覆盖，1为覆盖，0为正常注册
         */
        async toRegister(isCover = 1) {
            const formData = JSON.parse(localStorage.getItem("__regInfo__"));
            formData.phone = this.phone;
            formData.isCover = isCover;
            // 【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype"),
                pageType: pageTypeMap.QUESTIONS
            };
            Object.entries(toutiaoParamlist).forEach(([key, value]) => {
                value && (formData[key] = value);
            });
            const api = "/register/submitWapRegBaseInfoToRegister.do";
            const res = await post(api, formData);
            if (res.isError) return this.$toast(res.errorMessage);

            if (res.data.memberID) {
                session.setItem("reg_memberid", res.data.memberID);
                this.$select.mark({
                    msgValid: true
                });
            }

            this.$reportKibana(
                "h5-test-recommend", // 资源标识
                51, // 记录点
                "注册成功并生成ID", // 点描述
                {
                    ext17: isCover ? "覆盖注册" : "新注册"
                }
            );

            this.$router.push({
                path: "/blindinfo"
            });
        },
        goPolicy() {
            window.localStorage.setItem("policy", 1);
        },
        preventScroll(e) {
            e.preventDefault();
        },
        onFocus() {
            this.scrollTop = document.documentElement.scrollTop;
            window.addEventListener("touchmove", this.preventScroll, {
                passive: false
            });
        },
        onBlur() {
            document.documentElement.scrollTop = this.scrollTop;
            window.removeEventListener("touchmove", this.preventScroll);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../assets/css/common.scss";
.login {
  padding: 240px 48px 0;
  color: #fff;
  text-align: center;
  overflow: hidden;
  &_title {
    padding-bottom: 92px;
    font-size: 36px;
    font-weight: 500;
  }
  &_input {
    position: relative;
    display: block;
    height: 108px;
    padding: 30px 0 30px 60px;
    margin-bottom: 24px;
    border-radius: 60px;
    caret-color: #8c7afe;
    overflow: hidden;
    // &::before {
    //     content: "";
    //     position: absolute;
    //     left: 48px;
    //     top: 38px;
    //     bottom: 38px;
    //     width: 4px;
    //     background: #8c7afe;
    // }
  }
  &_info {
    &_btn {
      @include btn-style;
      margin: 230px auto 32px;
      color: #767dff;
      box-shadow: 0px 4px 22px 0px #e8e5fe;
    }
    &_txt {
      color: #fff;
      font-size: 26px;
    }
    &_txt > a {
      text-decoration: none;
      color: #ada2ff;
    }
  }
  &_btn {
    position: absolute;
    z-index: 1;
    top: -32px;
    right: -4px;
    height: 114px;
    color: #fff;
    font-size: 36px;
    background: #707095;
    border-radius: 60px 0 0 60px;
    transition: none;
  }
  &_btn.disabled {
    opacity: 0.5;
  }
  &_img {
    position: absolute;
    z-index: 1;
    top: -16px;
    right: 78px;
    width: 200px;
    height: 84px;
    border-radius: 42px;
    background-size: 100% 100%;
    background-origin: center center;
    background-repeat: no-repeat;
  }
}
</style>
