import { getRandomInt } from '@/common/utils/tools';
import { session as Session, storage as Storage } from '@/common/utils/storage.js';
import * as dict from "@/common/config/register-dictionary";
import Api from '@/common/server/base';
import z_ from "@/common/zdash";
import { reportMagic, reportKibana,reportLoveKibana1 } from "@/common/utils/report";

export const PAGE_TYPE = (function(){
    return '新注册路径手机前置';
})();



const educationScoreMap={
    3: 5, // 高中及以下 ：5分
    2: 4, // 中专
    4: 4, // 大专
    5: 3, // 大学本科
    6: 2, // 硕士
    7: 1, // 博士
};

const salaryScoreMap = {
    3: 5, //3000以下 ： 5分
    4: 5, //3001-5000
    5: 4, //5001-8000
    6: 3, //8001-12000
    7: 2, //12001-20000
    8: 1, //20001-50000
    9: 1, //50000以上
};


// (注册项) 性别 工作地 出生日期  身高 学历 婚姻状况  月收入
export const QUIZ_LIST_REGISTER = [
    {
        type:'外在魅力',
        comp:'Gender',
        title: '完善资料',
        title1: '为你匹配最合适的人',
        options: dict.genderV3
    },
    {
        type:'外在魅力',
        comp:'Birthday',
        title: '你是哪一年出生的?',
        options: dict.birthday
    },
    {
        type:'外在魅力',
        comp:'WorkCity',
        title: '你的工作地在哪里',
        options: Z.workCity
    },
    {
        type:'外在魅力',
        comp:'Marriage',
        title: '你的婚姻状况',
        options: dict.marriage
    },
    {
        type:'外在魅力',
        comp:'Education',
        title: '你的学历',
        options: dict.education
    },
    {
        type:'外在魅力',
        comp:'Salary',
        title: '你的月收入',
        options: dict.salary
    },
    {
        type:'外在魅力',
        comp:'Height',
        title: '你的身高',
        options: dict.heightV2
    },

];

export async function changeProfile (target) {
    const key = z_.get(target, "key");
    const value = z_.get(target, "value");
    const param = {
        [key]: value,
        token: target.token || sessionStorage.getItem('token'),
        memberId: sessionStorage.getItem('memberId'),
    }
    const data = await Api.updateProfileItem(param)
    if (data.isError) {
        reportLoveKibana1('新注册路径手机前置', 100,'修改资料失败', {
            ext3: JSON.stringify(param)
        });
    }
}
