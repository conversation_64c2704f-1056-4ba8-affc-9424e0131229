/*
 * @Desc: 提交规范配置文件
 * @Author: ya<PERSON><PERSON>.<EMAIL>
 * @Date: 2022-05-09 12:29:06
 * @Last Modified by: ya<PERSON><PERSON>.<EMAIL>
 * @Last Modified time: 2022-05-23 11:50:36
 */
module.exports = {
  types: [
    {
      value: 'feat',
      name: 'feat: 新增功能',
    },
    {
      value: 'fix',
      name: 'fix: 修复bug',
    },
    {
      value: 'docs',
      name: 'docs: 文档',
    },
    {
      value: 'style',
      name: 'style: 不影响代码含义的修改，比如空格、格式化、缺失的分号等',
    },
    {
      value: 'refactor',
      name: 'refactor: 代码重构(既不是修复bug,也不是添加新功能)',
    },
    {
      value: 'test',
      name: 'test: 增加测试',
    },
    {
      value: 'build',
      name: 'build: 对构建系统或者外部依赖项进行了修改（webpack，npm等）',
    },
    {
      value: 'ci',
      name: 'ci: 对CI配置文件和脚本的更改',
    },
    {
      value: 'revert',
      name: 'revert: 回退',
    },
    {
      value: 'config',
      name: 'config: 构建过程或辅助工具的变动',
    },
    {
      value: 'chore',
      name: 'chore: 其他改动',
    },
  ],
  messages: {
    type: '请选择提交类型(必填)',
    customScope: '请输入文件修改范围(必填)',
    subject: '请简要描述提交(必填)',
    body: '请输入详细描述(可选)',
    confirmCommit: '确定提交此说明吗？',
  },

  scopes: [ // 根据项目添加 scope
  { name: '公共模块' },
  { name: '业务钩子：大表单翻牌' },
  { name: '业务钩子：大表单盲盒' },
  { name: '业务钩子：脱单计划' },
  { name: '业务钩子：测试题+推荐对象' },
  { name: '业务钩子：宠物钩子' },
  { name: '业务钩子：恋爱基因' },
  { name: '业务钩子：mbti测试' },
  { name: '业务钩子：脱单营业厅' },
  { name: '业务钩子：同城交友' },
  { name: '业务钩子：锵锵锵咚咚咚' },
  { name: '业务钩子：脱单游戏'},
  { name: '业务钩子：同城交友(二期)' },
  { name: '业务钩子：脱单游戏'},
  { name: '业务钩子：理想恋人'},
  { name: '业务钩子：恋爱人格测试'}
  ],
  allowCustomScopes: 'custom', // 允许用户自己输入模块
  allowBreakingChanges: ['feat', 'fix'],
  skipQuestions: ['footer', 'skip', 'breaking'], // 跳过这几个问题
  subjectLimit: 120,
};
