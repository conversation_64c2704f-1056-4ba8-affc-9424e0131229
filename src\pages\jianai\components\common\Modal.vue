<template>
    <div
        class="modal-wrapper"
        ref="refWrapper"
    >
        <div
            class="modal-mask"
            @click="closeMask"
        >
            <!-- 验证码弹窗 -->
            <div
                class="modal-content modal-validate"
                v-if="modalType === 'modalValidate'"
                ref="refValidate"
            >
                <div class="modal-content__title">
                    验证码已通过短信发到您的手机
                </div>
                <div class="modal-validate__phone">
                    {{ registerInfo.phone }}
                </div>
                <div class="modal-validate__row">
                    <!-- UI为4个框 -->
                    <div class="modal-validate__inputs">
                        <div
                            v-for="(item, index) in 4"
                            :key="index"
                            class="modal-validate__inputs__item"
                        ></div>
                    </div>
                    <!-- 实际为1个框 -->
                    <input
                        ref="refCode"
                        type="tel"
                        v-model="code"
                        class="modal-validate__input"
                        maxlength="4"
                        @input="checkCode"
                        pattern="[0-9]*"
                        autocomplete="new-password"
                    />
                </div>

                <div
                    v-if="showWarning"
                    class="modal-validate__warning"
                >
                    {{ warningMessage }}
                </div>
                <button
                    class="modal-content__button--submit"
                    :style="{ background: isValidating ? '#B5B6BA' : '#FC77A8' }"
                    @click="resendValidateCode"
                >
                    {{ validateSubmit }}
                </button>
                <button
                    class="modal-content__button--cancel"
                    @click="closeModal"
                >
                    取消
                </button>
            </div>

            <!-- 引导下载弹窗 -->
            <div
                class="modal-content modal-download"
                v-if="modalType === 'modalDownload'"
                ref="refDownload"
            >
                <div
                    class="modal-content__title"
                    :style="{
                        width: +this.cmsConfig.downloadStatus === 0 ? '5.43rem' : ''
                    }"
                >
                    {{ downloadTitle }}
                </div>
                <div class="modal-download__subtitle">
                    请前往“优恋空间”小程序
                </div>
                <div class="modal-download__subtitle">
                    Ta在等你
                </div>
                <button
                    v-if="+this.cmsConfig.downloadStatus === 0"
                    class="modal-content__button--submit"
                    @click="closeModal(401, '引导去市场的弹窗访问-按钮点击')"
                >
                    好的
                </button>
            </div>

            <!-- 协议弹窗 -->
            <div
                class="modal-content modal-protocol"
                v-if="modalType === 'modalProtocol'"
                ref="refProtocol"
            >
                <div class="modal-protocol__subtitle">
                    已阅读并同意<span @click="goUrl(1)">《珍爱网服务协议》</span>和<span
                        @click="goUrl(2)"
                    >《个人信息保护政策》</span>
                </div>
                <button
                    class="modal-content__button--submit"
                    @click="confirmProtocol"
                >
                    确认
                </button>
                <button
                    class="modal-content__button--cancel"
                    @click="closeModal(602, '同意协议弹窗-取消按钮点击')"
                >
                    取消
                </button>
            </div>

            <!-- 已注册弹窗 -->
            <div
                class="modal-content modal-registered"
                v-if="modalType === 'modalRegistered'"
                ref="refRegistered"
            >
                <div class="modal-registered__banner"></div>
                <div class="modal-content__title">
                    该手机已在珍爱网APP注册过！
                </div>
                <button
                    v-if="modalParam.showCoverButton"
                    class="modal-registered__button"
                    @click="goCoverRegister"
                >
                    继续<span>(原有账号的资料将被覆盖)</span>
                </button>
                <button
                    class="modal-content__button--submit"
                    @click="goDownload"
                >
                    登录原有账号
                </button>
                <button
                    class="modal-content__button--cancel"
                    @click="closeModal"
                >
                    取消
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from "vuex";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { IS_SAPP } from "@/common/js/const.js";
import { reportKibana } from "@/common/utils/report.js";
import borderStopScroll from "@/common/utils/borderStopScroll.js";
import Prototype from "@/common/framework/prototype";
import Api from "@/common/server/base";

export default {
    props: {
        modalType: {
            type: String,
            required: true
        },
        modalParam: {
            type: Object
        },
        validateCode: {
            type: Function,
            required: true
        }
    },
    components: {},
    data() {
        return {
            inputValueList: ["", "", "", ""],
            code: "",
            showWarning: false,
            warningMessage: "",
            validateSubmit: "获取验证码",
            isValidating: false,
            downloadTitle: "",
            backTimer: null,
            leaveTime: null,
            backTime: null,
            lockValidate: false
        };
    },
    computed: {
        ...mapState([
            "registerInfo",
            "isCover",
            "cmsConfig",
            "overwriteRegistrationSwitch"
        ]),
        ...mapGetters(["getNormalPhone"])
    },
    created() {
        if (this.modalType === "modalValidate") {
            reportKibana("简爱大表单", 300, "验证短信弹窗访问", {});
            // 打开的是验证弹窗，打开倒计时
            this.countDown();

            this.$nextTick(() => {
                // 优化前
                // document.getElementsByClassName("modal-validate__inputs__item")[0].firstChild.focus();

                // 优化后
                this.$refs.refCode.focus();
            });
        } else if (this.modalType === "modalDownload") {
            reportKibana("简爱大表单", 400, "引导去市场的弹窗访问", {});
        } else if (this.modalType === "modalProtocol") {
            reportKibana("简爱大表单", 600, "同意协议弹窗访问", {});
        } else if (this.modalType === "modalRegistered") {
            reportKibana("简爱大表单", 310, "已注册提醒弹窗访问", {});
        }
    },
    mounted() {
        this.downloadTitle =
            +this.cmsConfig.downloadStatus === 0
                ? "对方已接受你的好友请求"
                : "下载珍爱APP使用此功能";

        // 处理滚动穿透
        borderStopScroll({
            wrapEle: this.$refs.refWrapper
        });

        // 所有弹窗此处统一处理，新增弹窗类型时此处需补充
        borderStopScroll({
            wrapEle: this.$refs.refValidate
        });
        borderStopScroll({
            wrapEle: this.$refs.refDownload
        });
        borderStopScroll({
            wrapEle: this.$refs.refProtocol
        });
        borderStopScroll({
            wrapEle: this.$refs.refRegistered
        });

    // 兼容ios fixed光标偏移问题
    // document.body.style.position = "fixed";
    // this.limitScroll();
    },
    methods: {
        ...mapMutations(["setRegMemberId"]),
        // 限制滚动, 有bug
        limitScroll() {
            document.documentElement.style.overflow = "hidden";
            document.body.style.overflow = "hidden";
        },
        // 取消限制滚动
        unlimitScroll() {
            document.documentElement.style.overflow = "visible";
            document.body.style.overflow = "auto";
        },
        countDown() {
            // 从60s开始倒计时
            this.validateSubmit = 60;

            // 锁，倒计时期间不能再点击
            this.isValidating = true;

            let timer = setInterval(() => {
                this.validateSubmit -= 1;
                if (this.validateSubmit <= 0) {
                    this.validateSubmit = "获取验证码";
                    this.isValidating = false;
                    clearInterval(timer);
                }
            }, 1000);

            // 如果已经有监听，无需重复绑定
            if (this.backTimer) {
                return;
            }

            this.backTimer = document.addEventListener("visibilitychange", () => {
                // 用户进入后台
                if (document.visibilityState === "hidden") {
                    this.leaveTime = new Date().getTime();
                } else if (document.visibilityState === "visible") {
                    this.backTime = new Date().getTime();
                    let diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                    // 在后台期间已经超过当前的剩余秒数
                    if (
                        diff > this.validateSubmit ||
                        this.validateSubmit === "获取验证码"
                    ) {
                        this.validateSubmit = "获取验证码";
                        this.isValidating = false;
                        clearInterval(timer);
                    } else {
                        // 否则将后台期间的差值计入倒计时
                        this.validateSubmit -= diff;
                    }
                }
            });
        },
        confirmProtocol() {
            reportKibana("简爱大表单", 601, "同意协议弹窗-同意按钮点击", {});
            // 确认协议后直接走 提交注册 的逻辑
            this.$parent.submitRegisterInfo("buttonProtocol");
        },
        async checkCode() {
            this.code = this.code.replace(/[^\d]/g, "");

            if (this.code.length === 4) {
                this.$refs.refCode.blur();

                const result = await this.validateCode(this.code);

                if (result.isError) {
                    this.code = "";
                    this.$refs.codeInput.focus();

                    switch (result.errorCode) {
                    case "-8002005":
                        this.errorMessage = "验证码错误，请重新输入";
                        break;
                    case "-8002006":
                        this.errorMessage = "验证码已过期";
                        break;
                    case "-8002004":
                        this.errorMessage = result.errorMessage;
                        break;
                    }

                    return;
                }

                Prototype.$gather.setValidateCodeSuccessOCPC();
            }
        },
        checkInputValue(event) {
            // 获取当前input框
            let currentTarget = event.target,
                currentIndex = +currentTarget.dataset.index;

            // 限制只能输入数字
            this.inputValueList[currentIndex] = this.inputValueList[
                currentIndex
            ].replace(/[^\d]/g, "");

            // 非最后一个框,且当前框填了数字，输入后自动跳转下一个框
            if (currentIndex !== 3 && this.inputValueList[currentIndex] !== "") {
                let nextTarget = currentTarget.parentNode.nextSibling.firstChild;
                if (!nextTarget.value) {
                    nextTarget.focus();
                }
            }

            // 非第一个框，删除时自动回退到上一个框
            if (currentIndex !== 0 && event.key === "Backspace") {
                let preTarget = currentTarget.parentNode.previousSibling.firstChild;
                // if(!nextTarget.value && currentIndex !== 0){
                //     preTarget = preTarget.parentNode.previousSibling.firstChild;
                // }
                preTarget.focus();
            }

            if (this.inputValueList.findIndex(item => item === "") === -1) {
                // 确保输完四个数字，即遍历数字数组找不到''时(findIndex返回-1)，调用验证码接口
                let fullCode = "";
                this.inputValueList.forEach((item, index) => {
                    fullCode = fullCode + item;
                });

                this.getValidate(fullCode);
            }
        },
        async resendValidateCode() {
            reportKibana("简爱大表单", 301, "验证短信弹窗-重新获取短信按钮点击", {});

            if (this.lockValidate) {
                return;
            }

            if (this.isValidating) {
                console.log(`已发送验证请求，请${this.validateSubmit}秒后重试`);
                return;
            }

            // 清空警告
            this.showWarning = false;

            // 锁
            this.lockValidate = true;
            // 重新发送验证码
            // 【归因】头条
            const sendData = {
                phone: this.getNormalPhone,
                type: 0
            };
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype")
            };

            for (const v in toutiaoParamlist) {
                if (toutiaoParamlist[v]) {
                    sendData[v] = toutiaoParamlist[v];
                }
            }
            let resData = await Api.sendWapMessageCodeV2(sendData);
            this.lockValidate = false;

            if (resData.isError) {
                return this.$toast(resData.errorMessage);
            }
            this.$toast(resData.data.msg);
            this.countDown();
        },
        jump(path) {
            this.$router.push({
                path,
                query: {
                    // plan:123
                }
            });
        },
        parseQueryString(url) {
            if (url.indexOf("#") !== -1) {
                url = url.split("#")[0];
            }
            var obj = {};
            var keyvalue = [];
            var key = "",
                value = "";
            var paraString = url
                .substring(url.indexOf("?") + 1, url.length)
                .split("&");
            for (var i in paraString) {
                keyvalue = paraString[i].split("=");
                key = keyvalue[0];
                value = keyvalue[1];
                if (key) {
                    obj[key] = value;
                }
            }
            return obj;
        },
        closeMask(e) {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        },
        closeModal(accessPoint, description) {
            // this.unlimitScroll();
            if (accessPoint && description) {
                reportKibana("简爱大表单", accessPoint, description, {});
            }

            this.$emit("close-modal");
        },
        // 兼容ios光标乱跳问题
        // fixIodCursor(){
        //     document.getElementsByClassName('modal-mask')[0].style.position = "absolute";
        // },
        goUrl(type) {
            if (type === 1) {
                location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
            } else if (type === 2) {
                location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
            }
        },
        // 后台控制的，跳转/下载APP
        goDownload() {
            // SAPP
            if (IS_SAPP) {
                this.closeModal();
                Z.client.invoke("ui", "logout", { canBack: true });
                return;
            }

            reportKibana("简爱大表单", 311, "已注册提醒弹窗-登录按钮点击", {});

            // if(this.isCover){
            //     reportKibana(
            //         "宠物钩子",
            //         311,
            //         '询问覆盖弹窗-登录按钮',
            //         {}
            //     );
            // }else{
            //     reportKibana(
            //         "宠物钩子",
            //         314,
            //         '引导登录弹窗-登录按钮',
            //         {}
            //     );
            // }

            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            if (this.overwriteRegistrationSwitch) {
                visibilityChangeDelay(function() {
                    downloadApp();
                }, 500);
                openApp();
                return;
            }

            // 【兜底】：跳wap登录页
            window.location.href = `${
                location.protocol
            }//i.zhenai.com/m/portal/login.html`;
        },
        // 始终跳转/下载APP
        goDownloadAnyway(accessPoint, description) {
            // 打桩
            if (accessPoint && description) {
                reportKibana("简爱大表单", accessPoint, description, {});
            }

            // 【后台开关:是否允许尝试打开/下载app】：尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                downloadApp();
            }, 500);
            openApp();
        },
        // 覆盖注册
        goCoverRegister() {
            reportKibana("简爱大表单", 312, "已注册提醒弹窗-覆盖注册按钮点击", {});
            this.closeModal();
            this.$parent.coverRegister();
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
// 弹窗共享样式
.modal-mask {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  // margin: auto;
  background: rgba($color: #26273c, $alpha: 0.6);
  z-index: 100;
}

.modal-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 32px;
  z-index: 999;
}

.modal-content__title {
  margin: 48px auto 0;
  font-size: 36px;
  font-weight: 700;
  color: #26273c;
  text-align: center;
  line-height: 54px;
}

.modal-content__button--disabled {
  background: #6c6d75;
  opacity: 0.5;
}

.modal-content__button--submit {
  display: block;
  margin: 48px auto 0;
  width: 462px;
  height: 88px;
  background: #fc77a8;
  border-radius: 55px;
  font-size: 32px;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 88px;
}

.modal-content__button--cancel {
  display: block;
  margin: 20px auto 0;
  font-size: 32px;
  font-weight: 400;
  color: #fc77a8;
  text-align: center;
  line-height: 47px;
}

// 验证弹窗
.modal-validate {
  width: 600px;
  height: 625px;
}

.modal-validate__phone {
  margin-top: 16px;
  font-size: 32px;
  font-weight: 400;
  color: #92939d;
  text-align: center;
  line-height: 47px;
}

.modal-validate__row {
  position: relative;
}

.modal-validate__inputs {
  @include set-flex(space-between, center);
  margin-top: 46px;
  margin-bottom: 81px;
  padding: 0 72px;
  // border: 1px solid black;
}

.modal-validate__inputs__item {
  position: relative;
  width: 102px;
  height: 130px;
  background: #eaebec;
  border-radius: 55px;

  // input{
  //     position: absolute;
  //     left: 50%;
  //     top: 50%;
  //     transform: translate(-50%,-50%);
  //     width: 22px;
  //     height: 43px;
  //     font-size: 29px;
  //     font-weight: 400;
  //     background: #EAEBEC;
  //     color: #26273C;
  //     line-height: 43px;
  // }
}

.modal-validate__input {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding-left: 116px;
  height: 60px;
  width: 600px;
  font-size: 30px;
  line-height: 30px;
  background: transparent;
  letter-spacing: 101px;
  // -webkit-appearance:none;
  // border: 1px solid ;
}

// input:-webkit-autofill {
// background-color: transparent;
// background-image: none;

// color: #000;
// box-shadow:0 0 0px 1000px transparent inset !important;
// -webkit-text-fill-color: #333333;
// }

// iphone12 自动输入验证码黄色背景的兼容
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  // -webkit-transition: background-color 99999s ease-out;
}

.modal-validate__warning {
  position: absolute;
  top: 377px;
  font-size: 28px;
  font-weight: 400;
  color: #f04086;
  width: 100%;
  text-align: center;
  line-height: 41px;
}

// 引导下载弹窗
.modal-download {
  width: 602px;
  height: 474px;
}

.modal-download__subtitle {
  margin: 28px auto 0;
  width: 500px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;
  line-height: 47px;
  text-align: center;
}

// 确认协议弹窗
.modal-protocol {
  width: 602px;
  height: 390px;
}

.modal-protocol__subtitle {
  margin: 48px auto 0;
  width: 530px;
  font-size: 32px;
  font-weight: 400;
  color: #6c6d75;

  line-height: 47px;
  span {
    color: #fc77a8;
  }
}

// 已注册弹窗
.modal-registered {
  width: 602px;
  padding-bottom: 44px;
  // height: 437px;
}

.modal-registered__banner {
  position: relative;
  top: -100px;
  @include set-img("../../assets/imgs/modal-registered-banner.png");
  margin: 0 auto -100px;
  width: 602px;
  height: 264px;
}

.modal-registered__button {
  @include set-flex(space-between, center);
  flex-direction: column;
  padding-bottom: 6px;
  margin: 20px auto 0;
  width: 462px;
  height: 88px;
  background: #fc77a8;
  border-radius: 55px;

  font-size: 32px;
  color: #ffffff;
  line-height: 47px;

  span {
    font-size: 24px;
    color: #ffffff;
    line-height: 36px;
  }
}
</style>
