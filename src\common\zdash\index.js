import pick from "lodash-es/pick";
import pickBy from "lodash-es/pickBy";
import isFunction from "lodash-es/isFunction";
import get from "lodash-es/get";
import omit from "lodash-es/omit";
import chain from "lodash-es/chain";
import isArray from "lodash-es/isArray";
import isEmpty from "lodash-es/isEmpty";
import clone from "lodash-es/clone";
import cloneDeep from "lodash-es/cloneDeep";
import flatten from "lodash-es/flatten";
import flattenDeep from "lodash-es/flattenDeep";
import map from "lodash-es/map";
import find from "lodash-es/find";
import findIndex from "lodash-es/findIndex";
import forEach from "lodash-es/forEach";
import some from "lodash-es/some";
import every from "lodash-es/every";
import includes from "lodash-es/includes";
import isNil from "lodash-es/isNil";
import isUndefined from "lodash-es/isUndefined";
import isNull from "lodash-es/isNull";
import isNumber from "lodash-es/isNumber";
import isString from "lodash-es/isString";
import compact from "lodash-es/compact";
import startsWith from "lodash-es/startsWith";
import slice from "lodash-es/slice";
import sample from "lodash-es/sample";
import shuffle from "lodash-es/shuffle";
import debounce from "lodash-es/debounce";
import throttle from "lodash-es/throttle";
import findLastIndex from "lodash-es/findLastIndex";
import filter from "lodash-es/filter";
import drop from "lodash-es/drop";
import each from "lodash-es/each";

const _ = {
    pick,
    pickBy,
    isFunction,
    get,
    omit,
    chain,
    isArray,
    isEmpty,
    clone,
    cloneDeep,
    flatten,
    flattenDeep,
    map,
    find,
    findIndex,
    forEach,
    some,
    every,
    includes,
    isNil,
    isUndefined,
    isNull,
    isNumber,
    isString,
    compact,
    startsWith,
    slice,
    sample,
    shuffle,
    debounce,
    throttle,
    findLastIndex,
    filter,
    drop,
    each
};

function toFixed(number, precision) {
    const multiplier = Math.pow(10, precision + 1),
        wholeNumber = Math.floor(number * multiplier);
    return (Math.round(wholeNumber / 10) * 10) / multiplier;
}

export default {
    ..._,
    toFixed,
};
