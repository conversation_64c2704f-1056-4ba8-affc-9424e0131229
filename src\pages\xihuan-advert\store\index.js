import Vuex from 'vuex';
import * as dict from "@/common/config/register-dictionary";
import {
    _getMaterial,
} from '../js/api.js';
import { session } from '@/common/utils/storage.js';

const formInfo = [{
        index: "gender",
        label: "您的性别",
        value: "",
        selectArr: dict.gender
    },
    {
        index: "workCity",
        label: "您的工作地",
        value: "",
        selectArr: Z.workCity
    },
    {
        index: "birthday",
        label: "您的出生年份",
        value: "",
        selectArr: dict.birthday
    },
    {
        index: "education",
        label: "您的学历",
        value: null,
        selectArr: dict.education
    },
    {
        index: "marriage",
        label: "您的婚姻状况",
        value: null,
        selectArr: dict.marriage
    },
    {
        index: "salary",
        label: "您的月收入",
        value: null,
        selectArr: dict.salary
    }
];

const store = new Vuex.Store({
    state: {
        // 用于表单显示
        formInfo,
        // 用于注册提交
        registerInfo: {
            gender: "",
            workCity: "",
            birthday: "",
            marriage: "",
            education: "",
            salary: "",
            phone: ""
        },
        // cms的配置
        cmsConfig: {
            formImg: '', //大表单页头图 crm
            subButtonUrl: '', // 提交按钮图片 crm
            textThemeColor: {
                color: '',
            },// 文字主题 crm
            formType: 1 ,// 表单类型 0 平铺 1 是弹出 crm
            pageColor:'', // 页面背景色 crm
            showAgreeButton: 0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认) crm
            downloadApp: 0, //是否可以直接跳转下载APP开关 1为可以 0为不可以(默认) crm

            downloadImg: require("../assets/imgs/new/success.jpg"), //下载页头图
            downloadContentImg: require("../assets/imgs/new/text.png"), //下载页宣传区图片 crm
            downloadUserText: '缺省', //来信页用户区域文字
            downloadButtonText: '回应Ta', //下载页主按钮文

        },
        // 图形验证码
        code: '',
        // 是否覆盖资料
        isCover: false,
        // 是否允许尝试打开/下载APP
        overwriteRegistrationSwitch: false,
        // 后台返回的memberId
        regMemberId: '',
        // 老注册页打桩使用
        EXT9: '',
        // 协议勾选状态，默认勾选
        hasCheckProtocal: true,
        // 控制是否打开引导刷新
        showError: false
    },
    mutations: {
        setFormInfo(state, target) {
            state.formInfo.forEach(item => {
                Object.keys(target).forEach(key => {
                    if (item.index === key) {
                        return item.value = target[key];
                    }
                })
            })

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localFormInfo', JSON.stringify(state.formInfo));
        },
        setRegisterInfo(state, target) {
            state.registerInfo = Object.assign(state.registerInfo, target);

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localRegisterInfo', JSON.stringify(state.registerInfo));
        },
        setCmsConfig(state, target) {
            state.cmsConfig = Object.assign(state.cmsConfig, target);
        },
        setCode(state, target) {
            state.code = target;
        },
        setIsCover(state, target) {
            state.isCover = target;
        },
        setOverwriteRegistrationSwitch(state, target) {
            state.overwriteRegistrationSwitch = target;
        },
        setRegMemberId(state, target) {
            state.regMemberId = target;
            session.setItem('reg_memberid', target);
        },
        setEXT9(state, target) {
            state.EXT9 = target;
        },
        setHasCheckProtocal(state, target) {
            state.hasCheckProtocal = target;
        },
        setShowError(state, target) {
            state.showError = target;
        }
    },
    actions: {
        async setCmsConfig({ state,commit }, target) {
            // 默认配置
            let defaultConfig = {
                formImg: require("../assets/imgs/new/top.png"), //大表单页头图 crm
                subButtonUrl: {'background-image': `url(${require("../assets/imgs/new/button.png")})`}, // 提交按钮图片 crm
                textThemeColor: {
                    color: '',
                },// 文字主题 crm
                formType: 1 ,// 表单类型 0 平铺 1 是弹出 crm
                pageColor: {
                    background: '#fdd1d6',
                }, // 页面背景色 crm
                showAgreeButton: 0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认) crm
                downloadApp: 0, //是否可以直接跳转下载APP开关 1为可以 0为不可以(默认) crm


                downloadImg: require("../assets/imgs/new/success.jpg"), //下载页头图
                downloadContentImg: require("../assets/imgs/new/text.png"), //下载页宣传区图片 crm
                downloadUserText: '缺省', //来信页用户区域文字
                downloadButtonText: '回应Ta', //下载页主按钮文

            }


            if (!Z.getParam("materialId")) {
                // 没有素材id则采用默认配置
                commit("setCmsConfig", defaultConfig);
                return;
            }

            // 拉取CMS配置
            let resData = await _getMaterial({
                serialNo: Z.getParam("materialId"),
            });

            if (resData.code !== 0) {
                // 取配置失败也使用默认配置
                // 不能直接写在cmsConfig中否则会出现切换
                commit("setCmsConfig", defaultConfig);

                // 此处的this指向store不是vue
                return this._vm.$toast(resData.errorMessage);
            }

            target = resData.data;

            // 处理页面底色和按钮底色
            function handleColor(color) {
                return {
                    background: color
                };
            }
            target.pageColor = handleColor(target.backGroundColor);//页面背景色

            //文字主题色 主题色设置成sass变量统一修改
            let body = document.getElementsByTagName('body')[0];
            body.style.setProperty('--color',`${target.textThemeColor}`);

            // 头图压缩
            target.formImg = target.formPageUrl && target.formPageUrl + '?imageMogr2/thumbnail/750x';
            target.downloadContentImg = target.downPageUrl && target.downPageUrl + '?imageMogr2/thumbnail/750x';
            target.subButtonUrl = {'background-image': `url(${target.subButtonUrl && target.subButtonUrl + '?imageMogr2/thumbnail/750x'})`};

            // 设置协议默认值
            commit("setHasCheckProtocal", target.showAgreeButton === 0);
            commit("setCmsConfig", target);
        },
    },
    getters: {
        // 根据registerInfo返回已填写的表单项个数
        getProgress: state => {
            let infoArr = Object.keys(state.registerInfo),
                count = 0;

            infoArr.forEach(item => {
                // 手机号需填满11位才算填写完整
                if (item === 'phone') {
                    state.registerInfo.phone.length === 13 ? count++ : "";
                } else if (state.registerInfo[item] !== '') {
                    count++;
                }
            })
            return count;
        },
        // 返回正常格式的手机号
        getNormalPhone: state => {
            return state.registerInfo.phone.replace(/[^(\d)]/g, "")
        }
    }
})

export default store;
