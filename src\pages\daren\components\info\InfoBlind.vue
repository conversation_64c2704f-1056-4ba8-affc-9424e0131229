<template>
    <div class="info-blind-wrapper">
        <div
            v-for="(item,index) in blindArr"
            :key="index"
            class="blind__item"
        >
            <!-- 头像 -->
            <!-- <div
                class="blind__item__img"
                :style="{backgroundImage:hasFinished && selectedId===index?`url(${modelInfo.avatar})`:''}"
            ></div> -->

            <!-- 头像 -->
            <img
                v-if="hasFinished && selectedId===index"
                class="blind__item__img"
                :src="hasFinished && selectedId===index?modelInfo.avatar:''"
            >

            <!-- 动效 -->
            <div
                v-if="!hasFinished || selectedId!==index"
                :id="'itemBlur'+index"
                class="blind__item__img--svga"
            ></div>

            <!-- 按钮 -->
            <button
                class="blind__item__button"
                :style="cmsConfig.buttonColor"
                :class="hasFinished && selectedId===index?'blind__item__block__button--disabled':''"
                @click="selectBlind(item,index)"
            >
                {{cmsConfig.flopButtonText}}
            </button>
        </div>
    </div>
</template>

<script>
import {mapState,mapMutations,mapActions} from 'vuex';
import {_getSpecifyGenderRandomAvatar} from '../../js/api.js';
import {reportKibana} from '@/common/utils/report.js';

export default {
    props:{
        hasFinished:{
            type:Boolean,
            // required: true,
        },
    },
    components:{
    },
    data() {
        const blindArr=[
            {},
            {},
            {}
        ];
        return {
            blindArr,
            selectedId:null,
            // hasFinished:false
        };
    },
    computed:{
        ...mapState([
            'cmsConfig',
            'registerInfo',
            'modelInfo'
        ]),
    },
    created(){
        // this.getAvatars();
    },
    mounted(){

    },
    watch:{
        "modelInfo.avatar"(val, oldval){
            // 获取到头像之后，构造svga参数
            let currentBlurId = "#itemBlur"+this.selectedId;
            this.setSVGA(currentBlurId);
        }
    },
    methods:{
        ...mapMutations([
            'setShowError'
        ]),
        ...mapActions([
            'setModelInfo',
        ]),
        async getAvatars(){
            // 计算用户的年龄
            let age = new Date().getFullYear() - new Date(this.registerInfo.birthday).getFullYear();

            let sendData = {
                sex:+this.registerInfo.gender === 0? 1 : 0, // 当前性别为男(0),则传女(1)
                limit: 3, // 返回3个
                age //输入哪个年龄段就返回哪个年龄段段，如25返回30岁以下，35返回30-40，45返回40以上
            };
            let resData = await _getSpecifyGenderRandomAvatar(sendData);
            if(resData.isError){
                // 打开引导刷新
                if(resData.errorMessage === "当前网络异常"){
                    return this.setShowError(true);
                }
                return this.$toast(res.errorMessage);
            }

            this.setShowError(false);

            this.blindArr = resData.data.list;

            // 图片压缩
            this.blindArr.forEach((item)=>{
                item.avatar += '?imageMogr2/thumbnail/200x200';
            })
        },
        async selectBlind(item, index){
            // 打桩
            reportKibana(
                "导量H5大表单翻牌",
                7,
                "翻牌下载页-翻牌按钮点击",
                {
                    ext16: 2, // 1 投放版 2 达人版
                    ext18:this.$route.path === '/info'? 1 : 2 // 1 无盲盒 2 有盲盒
                }
            );

            if(typeof this.selectedId === 'number'){
                // 已选
                if(this.selectedId === index){
                    return;
                }

                // 非当前选择，下载APP的逻辑
                this.$emit('open-modal','modalDownload',{})
                return;
            }

            // 设置当前选中项
            this.selectedId = index;

            // 请求详细信息
            await this.setModelInfo();
        },
        setSVGA(dom){
            let player = new SVGA.Player(dom),
                parser = new SVGA.Parser(dom);

            parser.load(require('../../assets/imgs/svgaBox.svga'), (videoItem)=>{
                player.loops = 1;
                player.setVideoItem(videoItem);
                player.startAnimation();
                player.onFinished(()=>{
                    this.$emit("select-blind");
                    // this.hasFinished = true;
                })
            })
        }
    },

};
</script>

<style lang="scss" scoped>
@import "../../index.scss";

.info-blind-wrapper{
    margin-top: -50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
}

.blind__item{
    position: relative;
    width: 200px;
    height: 306px;
    border-radius: 16px 16px 16px 16px;
    // overflow: hidden;
    // border: 1px solid red;
}

.blind__item__img{
    position: absolute;
    bottom: 72px;
    margin-top: 34px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 200px;
    height: 200px;
    border-radius: 16px 16px 16px 16px;
    object-fit: cover;
    border: 2px solid #000;
}

.blind__item__img--svga{
    @include set-img('../../assets/imgs/box.png');
    position: absolute;
    left: -17px;
    top: 4px;
    width: 233px;
    height: 264px;
    z-index: 2;
}

.blind__item__button{
    position: absolute;
    bottom: 0;
    display: block;
    width: 200px;
    height: 72px;
    background: #A5AFB1;
    border: 2px solid #000000;
    border-radius: 36px;

    font-size: 32px;
    font-weight: 700;
    color: #FFFFFF;
    line-height: 72px;
    text-shadow: 0px 0px 6px rgba(255, 150, 9, 0.6);
    box-shadow: inset 0 0 6px 1px #fff;
    z-index: 3;

}

.blind__item__block__button--disabled{
    background: #A5AFB1 !important;
    text-shadow: 0px 0px 0px !important;

}

</style>
