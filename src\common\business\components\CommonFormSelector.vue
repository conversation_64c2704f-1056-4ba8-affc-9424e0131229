<template>
    <div class="selector-wrapper">
        <van-popup
            :value="value"
            @input="handleVisibleChange"
            duration="0"
            position="bottom"
            class="selector-panel"
        >
            <div>
                <div
                    class="selector-panel__subtitle"
                    v-if="isNeedSubTitle"
                >
                    完善资料，立即匹配对象
                </div>

                <div
                    class="selector-panel__title"
                    :class="{'selector-panel__title-margin': !isNeedSubTitle}"
                >
                    {{ selectorParam.label }}
                </div>

                <!-- Picker类型：工作地、出生年份-->
                <van-picker
                    v-if="selectorType === 'selectorPicker'"
                    ref="refPicker"
                    show-toolbar
                    :columns="selectorParam.options"
                    @confirm="onConfirm"
                    @cancel="onCancel"
                    toolbar-position="bottom"
                    item-height="1.25rem"
                    visible-item-count="5"
                >
                    <div
                        slot="confirm"
                        class="selector-panel__picker__confirm"
                        :style="{background:styleConfig.selectorColor,color:styleConfig.selectorFontColor}"
                    >
                        提交
                    </div>
                    <div
                        slot="cancel"
                        :style="{color:styleConfig.selectorColor}"
                    >
                        取消
                    </div>
                </van-picker>

                <!-- Board类型：婚姻状况、学历、收入 -->
                <template v-if="selectorType === 'selectorBoard'">
                    <div
                        class="selector-panel__board"
                        ref="refBoard"
                    >
                        <div
                            v-for="(item, index) in selectorParam.options"
                            :key="index"
                            @click="onBoardClick(item)"
                            class="selector-panel__board__item"
                            :style="
                                item.key === registerForm[selectorParam.type]
                                    ? {background:styleConfig.selectorColor,color:styleConfig.selectorFontColor,borderColor:styleConfig.selectorColor}
                                    : {color:styleConfig.selectorColor,borderColor:styleConfig.selectorColor}
                            "
                        >
                            {{ item.text }}
                        </div>
                    </div>
                    <button
                        class="selector-panel__board__cancel"
                        :style="{ color: styleConfig.selectorColor }"
                        @click="closeSelector"
                    >
                        取消
                    </button>
                </template>
            </div>
        </van-popup>
    </div>
</template>

<script>
import { Popup, Picker } from "vant";
import { storage as Storage } from "@/common/utils/storage";
import { setLocalRegisterForm, keyToValue, findWorkCity } from "@/common/business/utils/localRegisterForm.js";

export default {
    name: "CommonFormSelector",
    components: {
        [Picker.name]: Picker,
        [Popup.name]: Popup
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        pageType: {
            type: String,
            default: "导量H5大表单翻牌"
        },
        styleConfig: {
            type: Object,
            required: true
        },
        selectorType: {
            type: String,
            required: true
        },
        selectorParam: {
            type: Object,
            required: true
        },
        isNeedSubTitle: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            loopLock: false,
            registerForm:{
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },
    watch: {
        async selectorParam() {
            // nextTick用于解决vant兼容问题
            await this.$nextTick();
            this.initSelector();
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit("input", value);
        },
        setRegisterForm(target) {
            // 同步至缓存
            const cachedRegisterForm = setLocalRegisterForm(target, this.pageType);
            // 并更新data
            Object.assign(
                this.registerForm,
                cachedRegisterForm
            );
        },
        initRegisterForm(){
            // 处理回显
            const cachedRegisterForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            );
            if (cachedRegisterForm) {
                Object.assign(this.registerForm, cachedRegisterForm);
            }
        },
        initSelector() {
            this.initRegisterForm();

            let key = this.registerForm[this.selectorParam.type];
            switch (this.selectorParam.type) {
            case "workCity": {
                if(key){
                    let arr = findWorkCity(key);
                    this.$refs.refPicker.setValues(arr);
                } else {
                    // 有缓存优先读缓存，否则走定位逻辑
                    this.handleLocate();
                    // this.$refs.refPicker.setValues(['广东', '肇庆', '端州区']); // 缺省地区
                }
                break;
            }
            case "birthday": {
                if(key){
                    let year = keyToValue('birthday', key);
                    this.$refs.refPicker.setValues([year]);
                } else {
                    this.$refs.refPicker.setValues([1990]); // 缺省年份
                }
                break;
            }
            case "education": {
                this.$refs.refBoard.scrollTo(0, 200);
                break;
            }
            case "salary": {
                //TODO: 可能需要根据屏幕尺寸调整
                this.$refs.refBoard.scrollTo(0, 52);
                break;
            }
            }
        },
        closeSelector() {
            this.$emit("input", false);
        },
        handleMultiClick() {
            // autoloop是异步的，需要防重点击
            if (this.loopLock) {
                return;
            }
            this.loopLock = true;
            setTimeout(() => {
                this.loopLock = false;
                this.$emit("autoloop");
            }, 300);
        },
        onBoardClick(item) {
            this.setRegisterForm({
                key: this.selectorParam.type,
                value: item.key
            });

            this.handleMultiClick();
        },
        onConfirm(area) {
            const picker = this.$refs.refPicker;
            let key = "";

            if (this.selectorParam.type === "birthday") {
                let year = picker.getColumnValue(0).key;
                //实际传给后台的数据为 年/1/1 对应的毫秒值
                key = new Date(year + "/" + 1 + "/" + 1).getTime();
            } else if (this.selectorParam.type === "workCity") {
                const currentCity = area[0];
                if (["北京", "上海", "重庆", "天津"].includes(currentCity)) {
                    key = picker.getColumnValue(1).key;
                    sessionStorage.setItem('workCityReport', picker.getColumnValue(0).key)
                } else {
                    key = picker.getColumnValue(2).key;
                    sessionStorage.setItem('workCityReport', picker.getColumnValue(1).key)
                }

            }

            // 设置数据
            const registerItem = {
                key: this.selectorParam.type,
                value: key
            };

            if (this.selectorParam.type === "birthday") {
                registerItem.getMarkValue = () => {
                    return {
                        year: keyToValue('birthday', key)+"",
                        month: "1",
                        day: "1"
                    };
                };
            }

            this.setRegisterForm(registerItem);
            this.handleMultiClick();
        },
        onCancel() {
            this.closeSelector();
        },
        handleLocate() {
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                    // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {  //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([result.addressComponent.province, result.addressComponent.city, result.addressComponent.district]);
                            this.$refs.refPicker.setValues(areaArr);
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if(status === 'complete'){
                        const areaArr = this.handleLocationPair([result.province, result.city, '']);
                        this.$refs.refPicker.setValues(areaArr);
                    } else {
                        this.$refs.refPicker.setValues(['广东', '肇庆', '端州区']); // 缺省地区
                    }
                });
            });
        },
        handleLocationPair(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            const targetProvince = this.$z_.find(this.selectorParam.options, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });
            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(targetCity.children, district => {
                return district.text.indexOf(sliceDistrict) >= 0;
            });

            return[targetProvince.text, targetCity.text, targetDistrict.text];
        }
    },
};
</script>

<style lang="scss" scoped>
// @import "../../index.scss";
@import "~@/common/styles/common.scss";
.selector-wrapper {
    // position: fixed;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}

.select-mask {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    // margin: auto;
    background: rgba($color: #26273c, $alpha: 0.6);
    z-index: 100;
}

.selector-panel {
    width: 750px;
    height: 998px;
    background: #ffffff;
    border-radius: 60px 60px 0px 0px;
}

.selector-panel__subtitle {
    margin-top: 60px;
    font-size: 29px;
    color: #92939d;
    text-align: center;
    line-height: 43px;
}

.selector-panel__title {
    margin-top: 16px;
    font-size: 36px;
    font-weight: 700;
    color: #26273c;
    text-align: center;
    line-height: 54px;
    &-margin {
        padding-top: 60px;
    }
}

.selector-panel__board {
    margin-top: 50px;
    padding: 4px;
    position: relative;
    @include flex-center(row, flex-start, center);
    flex-direction: column;
    width: 750px;
    height: 670px;
    overflow: scroll;
}

.selector-panel__board--blur {
    position: absolute;
    width: 560px;
    height: 0px;
    box-shadow: 0 0 40px 20px #ffffff;
    z-index: 3;
}

.top {
    top: 260px;
    left: 95px;
}

.bottom {
    bottom: 164px;
    left: 95px;
}

.selector-panel__picker__confirm {
    width: 100%;
    height: 100%;
    height: 110px;
    line-height:110px;
    border-radius: 55px;
}

.selector-panel__board__item {
    width: 560px;
    height: 80px;
    margin-top: 15px;
    flex-shrink: 0;
    border-radius: 55px;
    border-width: 2px;
    border-style: solid;
    font-size: 32px;
    font-weight: 400;
    text-align: center;
    line-height: 80px;
}

.selector-panel__board__item:nth-child(1) {
    margin-top: 0;
}

.selector-panel__board__cancel {
    display: block;
    margin: 25px auto 0;
    font-size: 32px;
    font-weight: 400;
    text-align: center;
}
</style>

<style lang="scss">
/* 覆盖vant样式 */

.van-picker {
    margin-top: 77px;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}

.van-picker__columns {
    margin-top: -30px;
    // height: 430px;
}

.van-picker-column {
    // height: 475px;
    font-size: 36px;
    font-weight: 400;
    color: #26273c;
    // background: yellow;
}

.van-picker-column__item {
    font-size: 30px;
    font-weight: 400;
    color: #26273c;
    line-height: 95px;
}

.van-picker-column__item--selected {
    font-size: 36px;
    font-weight: 400;
    color: #26273c;
    line-height: 95px;
}

.van-picker__toolbar {
    margin-top: 60px;
    flex-direction: column;
    align-content: flex-start;
}

.van-picker__cancel {
    flex-shrink: 0;
    order: 1;
    width: 654px;
    height: 110px;
    font-size: 32px;
    font-weight: 400;
    line-height: 47px;
}

.van-picker__confirm {
    flex-shrink: 0;
    order: 0;
    margin: 0 auto;
    padding: 0;
    width: 654px;
    height: 110px;
    background: #fff;
    border-radius: 55px;
    font-size: 32px;
    font-weight: 400;
    color: #ffffff;
    line-height: 47px;
}
</style>
