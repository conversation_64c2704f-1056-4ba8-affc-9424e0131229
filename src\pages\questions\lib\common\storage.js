const saveToStorage = function (obj, key, val) {
    let regInfo = localStorage.getItem(obj);
    if (!regInfo) {
        regInfo = {};
    } else {
        regInfo = JSON.parse(regInfo);
    }
    regInfo[key] = val;
    localStorage.setItem(obj, JSON.stringify(regInfo));
};


const loadFromStorage = function (obj, key, def) {
    let regInfo = localStorage.getItem(obj);
    if (!regInfo) {
        return def;
    }
    regInfo = JSON.parse(regInfo);
    return regInfo[key];
};

const removeFromStorage = function(obj) {
    localStorage.removeItem(obj);
}

const loadRegInfo = function (obj) {
    let regInfo = localStorage.getItem(obj);
    if (!regInfo) {
        return {};
    }
    return JSON.parse(regInfo)
};

export default {
    saveToStorage,
    loadFromStorage,
    removeFromStorage,
    loadRegInfo
}
