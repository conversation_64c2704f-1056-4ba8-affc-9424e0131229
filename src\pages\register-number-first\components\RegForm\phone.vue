<template>
    <div class="phone">
        <input
            ref="refInput"
            type="tel"
            placeholder="请输入手机号"
            maxlength="11"
            :value="value"
            @input="handleInputPhone"
        />
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage} from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: 'Phone',
    data() {
        return {
            value: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) ? Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).phone :'',
            isReport: false,
            hasReportInput: false,
            inputValue:''
        };
    },
    mounted() {
        this.inputValue = this.$refs.refInput.value;
    },
    methods: {
        handleInputPhone(e) {
            this.inputValue = this.$refs.refInput.value;

            if(!this.hasReportInput) {
                this.$report(37, '手机验证页-手机号填充');
                this.hasReportInput = true;
            }

            const isCorrect = this.checkMobile(e.target.value);
            if(!isCorrect && e.target.value.length === 11) {
                return this.$toast("请输入正确手机号");
            }
            const params = {
                key: "phone",
                value: e.target.value,
                isMark: false
            };
            setLocalRegisterForm(params, PAGE_TYPE);

            this.$emit('submit');


        },
        checkMobile(s) {
            if (s && s.length === 11 && /^1[3-9]\d{9}$/.test(s)) {
                return true;
            } else {
                return false;
            }
        },
    }
};

</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.phone {
    margin: 0 auto;
    padding-left: 32px;
    width: 540px;
    height: 100px;
    background-color: #EAF1FA;
    border-radius: 24px;
    @include flex-center(row, null, center);

    >input {
        width: 100%;
        background-color: transparent;
        font-weight: 400;
        font-size: 32px;
        color: #26273C;
        caret-color: #26273C;
    }

    ::-webkit-input-placeholder {
        color:#AEB1B6;
    }

    :-moz-placeholder {
        color:#AEB1B6;
    }

    ::-moz-placeholder {
        color: #AEB1B6;
    }

    :-ms-input-placeholder {
        color: #AEB1B6;
    }

}
</style>
