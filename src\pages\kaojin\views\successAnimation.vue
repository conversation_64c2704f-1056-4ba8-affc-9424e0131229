<template>
    <div class="wrapper">
        <img :src="url" alt="" :key="key">
    </div>
</template>

<script>
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../config";

export default {
    name: "SuccessAnimation",

    data() {
        return {
            url: '',
            gender: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender,
            timer: null,
            key: Date.now()
        };
    },
    activated() {
        // this.goNext()
        this.$report(306, '动画页访问');
    },
    mounted () {

    },
    beforeRouteEnter (to, from, next) {
        next(vm => {
            if (from.path !== '/message') {
                vm.url = ''
                vm.key = Date.now()
                vm.goNext()
            }
        })
    },
    // beforeRouteUpdate (to, from, next) {
    //     // 在当前路由改变，但是该组件被复用时调用
    //     // 举例来说，对于一个带有动态参数的路径 /foo/:id，在 /foo/1 和 /foo/2 之间跳转的时候，
    //     // 由于会渲染同样的 Foo 组件，因此组件实例会被复用。而这个钩子就会在这个情况下被调用。
    //     // 可以访问组件实例 `this`
    // },
    // beforeRouteLeave (to, from, next) {
    //
    // },
    methods: {
        goNext() {
            let normalMatchGif = [
                'https://photo.zastatic.com/images/common-cms/it/20190516/1558009656269_229597.gif',
                'https://photo.zastatic.com/images/common-cms/it/20190516/1558009656193_592766.gif'
            ];
            this.url = normalMatchGif[this.gender] ? normalMatchGif[this.gender] : normalMatchGif[0]
            this.timer = setTimeout(() => {
                this.$router.push({
                    path:`/message`
                });
                return false
            }, 5000)
        }
    },
    destroyed() {
        clearTimeout(this.timer)
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
   img {
       width: 100%;
   }
}
</style>
