<template>
    <div class="container">
        <div class="simple-button"
             :style="customStyle"
             @click="$emit('click', $event)">
            <slot/>
        </div>
    </div>
</template>

<script>
export default {
    name: "SimpleButton",
    props: {
        width: {
            type: Number,
        },
        height: {
            type: Number,
            default: 96,
        },
        background: {
            type: String,
            default: '#FFB900',
        },
        disable: {
            type: Boolean,
            default: false,
        },
        fixBottom: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        customStyle() {
            const result = {
                background: this.disable ?  'rgba(255,185,0,0.6)' : this.background,
                width: this.$utils.pxToRem(this.width),
                height: this.$utils.pxToRem(this.height),
                'line-height': this.$utils.pxToRem(this.height),
            }

            if (this.fixBottom) {
                Object.assign(result, {
                    position: 'fixed',
                    bottom: this.$utils.pxToRem(48),
                })
            }

            return result;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.container {
    width: 100%;

    // 使用 flex 居中兼容性没 relative 好
    .simple-button {
        @include relative-center();
        text-align: center;
        color: #000;
        font-weight: 400;
        font-size: 28px;
        border-radius: 77px;
    }
}
</style>
