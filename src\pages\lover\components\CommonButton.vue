<template>
    <div
        class="button-wrap"
        :style="{width: computedWidth, height: computedHeight, fontSize: computedFontSize}"
        @click="$emit('click', $event)"
    >
        {{ config.des }}
    </div>
</template>

<script>
export default {
    name: "CommonButton",
    props: {
        config: {
            type: Object,
            default: {
                width: 610,
                height: 116,
                fontSize: 36,
                des: "一键领取"
            }
        }
    },
    computed: {
        computedWidth() {
            return this.$utils.pxToRem(this.config.width);
        },
        computedHeight() {
            return this.$utils.pxToRem(this.config.height);
        },
        computedFontSize() {
            return this.$utils.pxToRem(this.config.fontSize);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.button-wrap {
    @include flex-center(row, center, center);
    background: linear-gradient(154deg, #7566eb 0%, #4a3bc0 100%);
    border: 2px solid #4a3bc0;
    border-radius: 56px;
    color: #ffffff;
}
</style>
