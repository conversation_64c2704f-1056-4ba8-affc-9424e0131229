import { getRandomInt } from '@/common/utils/tools';
import { session as Session, storage as Storage } from '@/common/utils/storage.js';
import * as dict from "@/common/config/register-dictionary";

// 首页分流，1为对照组，2测试组
export const PAGE_TYPE = (function(){
    const abTest = +Session.getItem('guide_abtest') || getRandomInt(1,3);
    Session.setItem('guide_abtest', abTest);

    if(abTest === 1){
        // EXT30=64
        return '单身自救指南(APP活跃)';
    } else if(abTest === 2) {
        // EXT30=79
        return '单身自救指南(新APP活跃)';
    }
    // EXT30=65
    return '单身自救指南(小程序活跃)';
})();


const scoreMap = {
    "非常不符合": 1, // 1分
    "不太符合": 2,
    "不确定": 3,
    "有点符合": 4,
    "非常符合": 5
};

const educationScoreMap={
    3: 5, // 高中及以下 ：5分
    2: 4, // 中专
    4: 4, // 大专
    5: 3, // 大学本科
    6: 2, // 硕士
    7: 1, // 博士
};

const salaryScoreMap = {
    3: 5, //3000以下 ： 5分
    4: 5, //3001-5000
    5: 4, //5001-8000
    6: 3, //8001-12000
    7: 2, //12001-20000
    8: 1, //20001-50000
    9: 1, //50000以上
};

const DEFAULT_OPTIONS = Object.keys(scoreMap);

// 测试题quiz(五个维度)
// 维度一：脱单意识
const QUIZ_LIST_MOTIVE = [
    {
        id: 1,
        type: '脱单意识',
        title: '我至今单身的最主要原因是什么？',
        options:[
            '工作太忙，没有时间交友',
            '社交圈子小，没有机会交友',
            '性格内向被动，不习惯主动交友',
            '没有紧迫感，拖着拖着就到现在了',
            '我也不太清楚，挺迷茫的'
        ]
    },
    {
        id: 2,
        type: '脱单意识',
        title: '我现在努力想脱单的原因是什么？',
        options:[
            '父母催促',
            '自己的规划，想找个伴了',
            '生育孩子的理想年龄',
            '周围环境，如：朋友同事都已婚已育了',
            '我也不太清楚，挺迷茫的'
        ]
    },
];

// 维度二：内在安全感
const QUIZ_LIST_SAFETY = [
    {
        id: 3,
        type: '内在安全感',
        title: '我很难自然地告诉恋人，我很在乎TA。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 4,
        type: '内在安全感',
        title: '在恋人身边，我可以轻轻松松地做我自己。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 5,
        type: '内在安全感',
        title: '我担心无法和恋人一直保持亲密关系。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 6,
        type: '内在安全感',
        title: '如果恋人依赖于从我这里获得情感支持，我会感到不自在。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 7,
        type: '内在安全感',
        title: '我可能会害怕向恋人吐露内心深处的情感。',
        options: DEFAULT_OPTIONS
    },
];

// 维度三：独处能力
const QUIZ_LIST_ALONE = [
    {
        id: 8,
        type: '独处能力',
        title: '我经常会感到寂寞。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 9,
        type: '独处能力',
        title: '我觉得无法和身边的人分享自己的兴趣和想法。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 10,
        type: '独处能力',
        title: '我经常想要与人来往、结交朋友。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 11,
        type: '独处能力',
        title: '虽然我身边有一些朋友，但我觉得他们不是真正关心我。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 12,
        type: '独处能力',
        title: '我觉得自己和别人的交往没有意义。',
        options: DEFAULT_OPTIONS
    },
];

// 维度四：脱单行动力
const QUIZ_LIST_ACTION  = [
    {
        id: 13,
        type: '脱单行动力',
        title: '在选择面前，我通常会迅速作出决定。',
        options: DEFAULT_OPTIONS
    },{
        id: 14,
        type: '脱单行动力',
        title: '在晚上休息之前，我通常会处理好所有必须完成的任务。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 15,
        type: '脱单行动力',
        title: '看到对方的信息或未接电话，我总是迅速回复。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 16,
        type: '脱单行动力',
        title: '我经常花费时间去做其他事情，而不是去结交新的朋友或加入新的圈子。',
        options: DEFAULT_OPTIONS
    },
    {
        id: 17,
        type: '脱单行动力',
        title: '去赴约时，我一般都会提前出发。',
        options: DEFAULT_OPTIONS
    },
];

// 维度五：外在魅力(注册项)
const QUIZ_LIST_REGISTER = [
    {
        type:'外在魅力',
        comp:'Gender',
        title: '我的性别是？',
        options: dict.genderV3
    },
    {
        type:'外在魅力',
        comp:'WorkCity',
        title: '我的生活地是？',
        options: Z.workCity
    },
    {
        type:'外在魅力',
        comp:'Marriage',
        title: '我的情感状态是？',
        options: dict.marriage
    },
    {
        type:'外在魅力',
        comp:'Education',
        title: '我的学历是？',
        options: dict.education
    },
    {
        type:'外在魅力',
        comp:'Birthday',
        title: '我是哪一年的？',
        options: dict.birthday
    },
    {
        type:'外在魅力',
        comp:'Salary',
        title: '我的收入是？',
        options: dict.salary
    },
];

// 测试组 性别页已提前 删除quiz中的性别页
if(PAGE_TYPE==='单身自救指南(APP活跃)' || PAGE_TYPE==='单身自救指南(新APP活跃)'){
    QUIZ_LIST_REGISTER.shift();
}

// quiz列表
export const QUIZ_LIST = [
    ...QUIZ_LIST_MOTIVE,
    ...QUIZ_LIST_SAFETY,
    ...QUIZ_LIST_ALONE,
    ...QUIZ_LIST_ACTION,
    ...QUIZ_LIST_REGISTER
];

// 使用QuizItem组件的quiz类型
export const NORMAL_QUIZ_TYPES = [
    '脱单意识',
    '内在安全感',
    '独处能力',
    '脱单行动力'
];

// 计算quiz分数
export function countScore(){
    // 计算实际得分
    const answerList = Session.getItem('sessionAnswerList');
    const score = {
        answerList:[], // 分数list，用于请求测试结果
        total:0, // 总分 ps:指数=118-总分
        result:'', // 指数对应结果文案
        percent:'', // 指数对应脱单概率

        // 四个分项得分
        safety:0, 
        alone:0,
        action:0,
        register:0
    };

    // 计算普通题得分
    answerList.forEach(item=>{
        // 匹配题号，计算得分
        if([5,6,7,8,9,10,11,12,16].includes(item.questionId)){
            // 正向计分
            item.optionContent = scoreMap[item.optionContent];
        } 
        else if([3,4,13,14,15,17].includes(item.questionId)){
            // 反向计分
            item.optionContent = 6 - scoreMap[item.optionContent];
        }

        [1,2].includes(item.questionId)?'':score.total += item.optionContent;
        [3,4,5,6,7].includes(item.questionId)?score.safety+=item.optionContent:'';
        [8,9,10,11,12].includes(item.questionId)?score.alone+=item.optionContent:'';
        [13,14,15,16,17].includes(item.questionId)?score.action+=item.optionContent:'';
    });
    score.answerList = answerList;

    // 计算注册项得分
    const {education,salary,birthday} = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`);
    const age = new Date().getFullYear() - new Date(birthday).getFullYear();
    let ageScore;
    if(age < 24){
        ageScore = 5;
    } else if(age > 45){
        ageScore = 3;
    } else {
        ageScore = 1;
    }    
    score.register = educationScoreMap[education] + salaryScoreMap[salary] + ageScore;
    score.total += score.register;

    // 计算总分对应结果
    const TOTAL = 118;
    if(TOTAL - score.total >= 81 ){
        score.result = '新恋情已经在路上';
        score.percent = '99.99%';
    } else if (TOTAL  - score.total >= 50){
        score.result = '就差捅破窗户纸';
        score.percent = '88.88%';
    } else if (TOTAL  - score.total >= 39){
        score.result = '柠檬汁单身';
        score.percent = '78.88%';
    } else if (TOTAL  - score.total >= 28){
        score.result = '还差亿点点脱单';
        score.percent = '66.66%';
    }

    return score;
}