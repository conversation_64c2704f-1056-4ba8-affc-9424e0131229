import { createApp } from '@/common/framework';
import App from './App';
import router from './router';
import store from "./store/index.js";
import Vue from 'vue';
import SimpleButton from "@/pages/mbti/components/SimpleButton";
import { reportKibana } from '@/common/utils/report.js';


Vue.prototype.$reportKibana = reportKibana;
Vue.component('SimpleButton', SimpleButton);

createApp({
    router,
    store,
    render: h => h(App),
});
