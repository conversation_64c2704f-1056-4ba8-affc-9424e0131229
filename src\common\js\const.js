// 兼容处理Z.getParam无法取到hash后URL参数的情况
// let tmpFunc = Z.getParam;
// Z.getParam = function(name){
//     if(location.href.indexOf("#")>-1){
//         // 获取hash前参数
//         let paramBeforeHash = tmpFunc(name);

//         // 获取hash后参数
//         const u = location.href.split(`#`)[1]; // 取hash后面
//         const r = new RegExp(`(\\?|&)` + name + `=(.*?)(#|&|$)`, `i`);
//         const m = u.match(r);
//         let paramAfterHash = decodeURIComponent(m ? m[2] : ``);

//         return paramBeforeHash || paramAfterHash;
//     }
// }
import { getRandomInt } from "@/common/utils/tools.js";


export const PROTOCOL = window.location.protocol;


// 渠道号和子渠道号
export let channelId = Z.getParam('channelId');
export let subChannelId = Z.getParam('subChannelId');

// 测试类型，来源于落地页的性别选择，用于区分男女
export let TEST_TYPE = Z.getParam('testType');

// 页面来源
export let FROM = Z.getParam('from');

// 页面来源，是否在sapp中
export let IS_SAPP = FROM === 'sapp';

// 是否春晚注册渠道
export var IS_SPRING_CHANNEL = ['915313'].indexOf(channelId) !== -1;

// 是否走覆盖流程，若走覆盖流程，记为1；若不走覆盖流程，记为2
export let oExt9 = (function () {
    let res = '';
    return {
        set: function (val) {
            res = val;
        },
        get: function () {
            return res;
        }
    };
})();

//头条AB测试，0：走弹窗引导，1: 跳应用市场/浏览器下载
export const TOUTIAO_DOWNLOAD  = (function () {
    const url = window.decodeURIComponent(localStorage.getItem('AD_URL'));
    const searchUrl = url.split('?')? url.split('?')[1]: [];
    const params = new URLSearchParams(searchUrl);
    const channelId = Number(params.get('channelId'));
    const channelIdList = [924088,925171,927111,926988
        ,924075,927138,924085,924479,924865,917866
        ,918486,923101,921839,922453,924845,920044
        ,925620,918638,924703,922362,923810,924707
        ,924268,921377,925366,926961,924708,927130
        ,927315,923530,927343,924257,926752,924665
        ,927007,926749,925706,926647,926697,925925
        ,927340,926648,926596,926865,927339,925621
        ,923621,927344,920254,923910,926940,926637];
    if(channelIdList.includes(channelId)){
        return 1;
    }else{
        //localStorage.setItem("resourceKey", "大表单翻牌(新流程)");
        return 0;
    }
})();