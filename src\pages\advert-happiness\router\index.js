import VueRouter from "vue-router";

import Collection from "../views/Collection.vue";
import Info from "../views/Info.vue";
import SuccessResult from "../views/SuccessResult.vue";
import YoulianActivity from "../views/YoulianActivity.vue";
import DownApp from "../views/downApp.vue";
import MateResult from "../views/mateResult.vue";
import MateSelection from "../views/mateSelection.vue";
import YoulianMakefriends from "../views/YoulianMakefriends.vue";
import Service from "../views/service.vue";
import Privacy from "../views/privacy.vue";
import XfhService from "../views/XfhService.vue";
import XfhPrivacy from "../views/XfhPrivacy.vue";


const router = new VueRouter({
    mode: "hash",
    routes: [{
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            component: Collection
        },
        {
            path: "/info",
            component: Info
        },
        {
            path: "/successResult",
            component: SuccessResult
        },
        {
            path: "/youlianActivity",
            component: YoulianActivity
        },
        {
            path: "/downApp",
            component: DownApp
        },
        {
            path: "/mateResult",
            component: MateResult
        },
        {
            path: "/mateSelection/:id",
            component: MateSelection
        },
        {
            path: "/youlianMakefriends",
            component: YoulianMakefriends
        },
        {
            path: "/service",
            component: Service
        },
        {
            path: "/privacy",
            component: Privacy
        },
        {
            path: "/XfhService",
            component: XfhService
        },
        {
            path: "/XfhPrivacy",
            component: XfhPrivacy
        }
    ],
    scrollBehavior(to, from, savedPosition) {
        return { x: 0, y: 0 }
    }
});

export default router;
