<template>
    <div class="quiz">
        <div class="panel">
            <img src="https://photo.zastatic.com/images/common-cms/it/20230812/1691831200192_482309_t.png" alt="" class="sreach">
            <p class="search_text">恭喜你，完成测试！</p>
            <p class="success">你的【恋爱人格测试报告】已生成</p>
            <img src="https://photo.zastatic.com/images/common-cms/it/20230812/1691831204574_368115_t.png" alt="" class="text_dialog">
            <div @click="openLock()" class="lock">去解锁</div>
        </div>
    </div>
</template>
<script>
export default {
    name:"TestResult",
    data(){
        return {

        };
    },
    // created() {
    //     this.$report(17,'完成测试页访问');
    // },
    activated(){
        this.$report(17,'完成测试页访问');
    },
    mounted() {
    },
    methods:{
        openLock() {
            this.$router.push({name:'<PERSON><PERSON><PERSON><PERSON>'});
            this.$report(18,'完成测试页-按钮点击');
        },

    }
};
</script>
<style scoped lang="scss">
.quiz{
    // width: 100vw;
    // height: 100vh;
    // background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825084549_711274_t.png');
    // background-repeat: no-repeat;
    // margin:0px;
    // background-size:100% 100%;
    // background-attachment:fixed;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

    .panel{
        padding: 0 88px;
        width: 99vw;
        height: 83vh;
        background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825172826_472496_t.png');
        background-repeat: no-repeat;
        margin: 0;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-self: center;
    //    margin-top: 128px;
        .sreach{
            width: 300px;
            height: 300px;
            object-fit: contain;
            margin-top: 100px;

        }
        .search_text{
            margin-top: 35px;
            color: #8a54f8;
            font-family: 'love';
            font-size: 60px;
            text-align: center;
            margin-bottom: 20px;

        }
        .success{
            font-weight: 400;
            height: 48px;
            font-size: 30px;
            text-align: center;
            color: #2E2E2E ;
            letter-spacing: 3px;
        }
        .text_dialog{
            width: 540px;
            height: 226px;
            object-fit: contain;
        }
        .lock{
            width: 575px;
            height: 100px;
            background: linear-gradient(180deg,#5243FE, #9A55F0);
            display:flex;
            align-items: center;
            justify-content: center;
            font-family: 'love';
            line-height: 54px;
            font-size: 52px;
            color: #FFFFFF;
            letter-spacing: 4px;
            box-shadow: 0 3px 3px 0 #bd92eb66;
            border-radius: 10px;
            margin-top: 60px;

        }

    }
    .top{
        position: fixed;
        top: 0px;
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 34px 25px 0 25px;
    }
    .panel-back{
        width: 147px;
        height: 56px;

    }
    .music{
        width: 74px;
        height: 74px;
    }
    .hidden_mis{
        justify-content: flex-end;
    }
    .flex_box{
        display: flex;
        font-weight: Oblique;
            height: 27px;
            font-size: 20px;
            text-align: left;
            color: #8A54F8 ;
            letter-spacing: 4px;
    }

}
</style>
