<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="download">
            <div class="download-title">
                {{ downloadText.title }}
            </div>
            <div class="download-info" :class="{spe: downloadText.isLeft}">
                <p class="download-info-txt">
                    {{ downloadText.text1 }}
                </p>
                <p class="download-info-txt">
                    {{ downloadText.text2 }}
                </p>
            </div>
            <div
                class="download-btn"
                @click="closeModal"
            >
                好的
            </div>
        </div>
    </van-popup>
</template>

<script>
import { Popup } from "vant";
const textList = [
    {
        title: "开启与Ta聊天",
        text1: "1.请到应用市场搜索下载【珍爱APP】",
        text2: "2.在【珍爱APP】按条件搜索便可找到心仪的Ta",
        isLeft: true
    },
    {
        title: "查看微信号",
        text1: "查看完整微信号的功能仅限APP",
        text2: "请前往应用市场搜索下载【珍爱APP】",
        isLeft: false
    },
    {
        title: "查看手机号",
        text1: "查看完整手机号的功能仅限APP",
        text2: "请前往应用市场搜索下载【珍爱APP】",
        isLeft: false
    }
];
export default {
    name: "Download",
    components: {
        VanPopup: Popup
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        downloadNum: {
            type: Number,
            default: 0
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    computed: {
        downloadText() {
            return textList[this.downloadNum-1];
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit("input", value);
        },
        closeModal() {
            this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit("input", false);
        }
    }
};
</script>
<style lang="scss" scoped>
.download {
    width: 558px;
    padding: 48px 40px;
    text-align: center;
    background: #fff;
    border-radius: 32px;
    &-title {
        color: #000000;
        font-size: 36px;
        font-weight: 500;
    }
    &-info {
        padding-top: 16px;
        text-align: center;
        &-txt {
            color: #6C6D75;
            font-size: 28px;
            line-height: 40px;
        }
        &.spe {
            text-align: left;
        }
    }
    &-btn {
        margin: 48px auto 0;
        width: 462px;
        height: 88px;
        color: #fff;
        font-size: 32px;
        font-weight: 500;
        line-height: 88px;
        text-align: center;
        background: #5368f0;
        border-radius: 78px;
        &:active {
            opacity: 0.7;
        }
    }
}
</style>
