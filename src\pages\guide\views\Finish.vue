<template>
    <div class="finish">
        <div class="panel">
            <!-- header -->
            <div class="panel-title">
                专属单身自救指南
            </div>
            <div class="panel-subtitle">
                四大维度全方位分析你当前的单身现状
            </div>
            
            <!-- content -->
            <radar 
                class="panel-radar" 
                :width="504"
                :height="426"
                :show-score="false"
                :radius="{
                    radiusSmall: 140/2,
                    radiusMid: 244/2,
                    radiusBig: 280/2
                }"
            />
            <div class="panel-result">
                <span>{{ `“${score.result}”` }}</span>
            </div>
            <div class="panel-general">
                <div class="panel-general-rate">
                    {{ 118 - score.total }}
                </div>
                <div class="panel-general-rate">
                    ??
                </div>
                <div class="panel-general-line"></div>
            </div>

            <!-- footer -->
            <Phone
                ref="refPhone"
                class="panel-phone"
                @submit="handleSubmit"
            />
            <common-submit
                ref="refCommonSubmit"
                :page-type="PAGE_TYPE"
                :style-config="{
                    modalConfig: {
                        confirmButtonColor: '#FFFFFF',
                        confirmButtonBgColor: '#17263D',
                        cancleButtonColor: '#000000',
                    },
                    protocolConfig: {
                        textColor: '#FFFFFF',
                        protocolColor: '#5BB5ED',
                        protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20221115/1668493498703_827687_t.png'
                    }
                }"
                :handle-after-regisiter="handleJump"
                :handle-login="handleJump"
                :handle-cancle="handleJump"
            >
                <div
                    class="panel-submit"
                    @click="handleReport"
                >
                    领取指南
                </div>
            </common-submit>
        </div>
    </div>
</template>
<script>
import CommonSubmit from '@/common/business/CommonSubmit';
import Phone from "../components/RegForm/phone.vue";
import Radar from "../components/Radar.vue";
import { PAGE_TYPE, countScore } from "../config";

export default {
    name: "Finish",
    components: {
        Radar,
        Phone,
        CommonSubmit,
    },
    data() {
        return {
            isShow: true,
            PAGE_TYPE,
            score: null
        };
    },
    created(){
        this.score = countScore();
    },
    methods: {
        handleJump() {
            this.$router.push({ path: '/result'});
        },
        handleReport(e) {
            this.$report(42, '手机验证页-点击立即解锁');
            if(this.$refs.refPhone.inputValue.length !== 11){
                this.$toast("输入手机号解锁你的单身自救指南");
                e.stopPropagation();
            } 
        },
        handleSubmit() {
            // 触发提交手机号逻辑
            this.$refs.refCommonSubmit.handleSubmit(false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.finish{
    width: 100vw;
    height: 100vh;
    min-height: 1400px;
    @include set-img('https://photo.zastatic.com/images/common-cms/it/20221115/1668479548301_858916_t.png');
    background-size: cover;
    .panel{
        padding-right: 48px;
        position: absolute;
        top: 0;
        right: 0;
        width: 702px;
        height: 1370px;
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20221115/1668479687314_898283_t.png');
        text-align: center;

        &-title{
            margin-top: 104px;
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;
        }

        &-subtitle{
            margin-top: 12px;
            font-weight: 400;
            font-size: 28px;
            color: #E2F0FE;
            text-align: center;
        }

        &-radar{
            margin: 48px auto 0;
        }

        &-result{
            margin-top: 32px;
            position: relative;
            display: inline-block;
            
            > span{
                position: relative;
                font-weight: 600;
                font-size: 66px;
                color: #E2F0FE;
                z-index: 2;
            }

            &::after{
                content: '';
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -8px;
                width: 90%;
                height: 16px;
                background-image: linear-gradient(90deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 100%);
                z-index: 1;
            }
        }

        &-general{
            position: relative;
            margin-top: 90px;
            padding: 0 100px 18px;
            @include flex-center(row, space-between, center);
            border-bottom: 2px dashed #C9C8CE;
            &-rate{
                position: relative;
                font-weight: 600;
                font-size: 48px;
                color: #E2F0FE;
                text-align: center;

                &::before{
                    content: '';
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    top: -50px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #E2F0FE;
                    text-align: center;
                }
            }

            :nth-child(1){
                &::before{
                    content: '脱单指数';
                    width: 100px;
                }
            }

            :nth-child(2){
                &::before{
                    content: '今年脱单概率';
                    width: 150px;
                }
            }

            &-line{
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: -40px;
                width: 2px;
                height: 84px;
                border: 2px solid rgba(151,151,151,0.19);
            }
        }

        &-phone{
            margin-top: 70px;
        }

        &-submit{
            margin: 32px auto 0;
            width: 590px;
            height: 100px;
            background-image: linear-gradient(104deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 81%);
            box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
            border-radius: 55px;
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;
            line-height: 100px;
        }
    }
    /deep/ .common-protocol {
        align-items: flex-start;
    }
}
</style>
