<template>
    <div class="wrapper">
        <img src="https://photo.zastatic.com/images/common-cms/it/20240103/1704279476000_997315_t.jpg" alt="">
        <button class="down" @click="handleDownload" v-if="showButton">打开珍爱APP</button>
        <!--<p class="url">
            <a :href="url">继续访问网页版</a>
        </p>-->
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import {reportKibana} from '@/common/utils/report.js';
import {
    pageTypeMap
} from "@/common/config/register-dictionary.js";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { Toast } from "vant";
import { mapState } from "vuex";
export default {
    name: "DownApp",

    data() {
        return {
            url: 'https://i.zhenai.com/m/wap/index/index.html' + location.search,
            showButton: true,
        };
    },
    computed: {
        ...mapState([
            "registerInfo",
            "cmsConfig",
        ]),
    },
    created () {
        this.showButton = this.cmsConfig.downloadApp === 0;
    },
    mounted () {

    },

    methods: {
        handleDownload() {
            reportKibana("线下大表单", 111, '结果页弹窗-点击下载APP按钮', {});
            if (this.cmsConfig.jumpULoveCupid == '1') {
                this.$router.push({
                    path: "/successResult"
                });
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({ value: true });
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }

        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    text-align: center;
   img {
       width: 100%;
   }
    .down {
        width: 80%;
        background: #00c7b1;
        height: 100px;
        line-height: 100px;
        margin-top: 40px;
        border-radius: 10px;
        color: #fff;
        text-decoration: none;
        font-size: 40px;
    }
    .url {
        text-decoration: underline;
        color: #aaa;
        a {
            color: #aaa;
            font-size: 32px;
        }
    }
}
</style>
