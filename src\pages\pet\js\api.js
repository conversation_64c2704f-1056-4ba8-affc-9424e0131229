import { get, post, ajax } from '@/common/utils/ajax.js';

// 字典
export const _getDictionary = (data) => post("/system/getDictionary.do", data);

// 提交注册信息 
export const _submitWapRegBaseInfo = (data) => post("/register/submitWapRegBaseInfo.do", data);

// 提交注册信息(特定参数触发)
export const _submitWapRegBaseInfoToRegister = (data) => post("/register/submitWapRegBaseInfoToRegister.do", data);

// 手机号提交 下发验证码
export const _submitWapRegNoPasswordInfo = (data) => post("/register/submitWapRegNoPasswordInfo.do", data);

// 覆盖注册
export const _sendWapMessageCodeAndSubmitNoPasswordInfo = (data) => post("/register/sendWapMessageCodeAndSubmitNoPasswordInfo.do", data);

// 核对验证码(GET)
export const _validatePhoneCodeGET = (data) => get("/register/validatePhoneCode.do", data);

// 核对验证码(POST)
export const _validatePhoneCodePOST = (data) => post("/register/validatePhoneCode.do", data);

// 核对验证码(特定情况触发)
export const _validAndRegNew = (data) => post("/register/validAndRegNew.do", data);

// 重新发送验证码
export const _sendWapMessageCode = (data) => post("/register/sendWapMessageCode.do", data);

// 获取CMS配置的素材
export const _getMaterial = (data) => get("/register/getMaterial.do", data);

// 获取大表单页头部的7个随机头像
export const _getRandomAvatar = (data) => get("/register/getRandomAvatar.do", data);

// 获取指定性别的3个随机头像
export const _getSpecifyGenderRandomAvatar = (data) => get("/register/getSpecifyGenderRandomAvatar.do", data);

// 获取推荐详细信息
// export const _getModelInfo = (data) => post("/register/getModelInfo.do", data);
export const _getWapRecommendMember = (data) => post("/register/getWapRecommendMember.do", data);

// 获取兴趣爱好
export const _getInterestAnswerRecordListForUser = (data) => post("/marriageView/getInterestAnswerRecordListForUser.do", data);

// 宠物钩子标记用户
export const _petMark = (data) => post("/register/advertisement/petMark.do", data);


