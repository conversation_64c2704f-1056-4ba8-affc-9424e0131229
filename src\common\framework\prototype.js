import Vue from "vue";
import z_ from "@/common/zdash";
import { reportKibana } from "@/common/utils/report";
import { storage } from "@/common/utils/storage";
import { channelId, PROTOCOL, subChannelId } from '@/common/js/const';
import oUserSelect from '@/common/ocpx/huichuan';
import { downloadApp, openApp, visibilityChangeDelay } from '@/common/utils/download_app';

const prototype = {
    $z_: z_,
    $report: (accessPoint, accessPointDesc, options) => {
        return reportKibana(window._zconfig.resourceKey, accessPoint, accessPointDesc, options);
    },
    $utils: {
        pxToRem(pxValue) {
            if (!pxValue) {
                return 0;
            }

            const rootValue = 75;
            const unitPrecision = 2;
            const pixels = parseFloat(pxValue);
            const fixedVal = z_.toFixed(pixels / rootValue, unitPrecision);
            return fixedVal === 0 ? '0' : fixedVal + 'rem';
        },
        formatImgUrl(url, width, height) {
            if (z_.includes(url, 'imageMogr2')) {
                return url;
            }

            const params = [
                width > 0 ? width : '',
                height > 0 ? height : '',
            ];

            return `${ url }?imageMogr2/thumbnail/${ params.join('x') }`;
        },
        parseQueryString(url) {
            if (url.indexOf('#') !== -1) {
                url = url.split('#')[0];
            }
            var obj = {};
            var keyvalue = [];
            var key = "", value = "";
            var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
            for (var i in paraString) {
                keyvalue = paraString[i].split("=");
                key = keyvalue[0];
                value = keyvalue[1];
                if (key) {
                    obj[key] = value;
                }
            }
            return obj;
        },
        handleDownload() {
            visibilityChangeDelay( ()=> {
                downloadApp();
            }, 500);

            openApp();
        },
        getScale(){
            // 计算当前屏幕css宽度（offsetWidth）和UI稿(750px)和元素宽度的比例
            return 750/document.documentElement.offsetWidth;
        }
    },
    $gather: {
        _getLandingUrl() {
            try {
                let bd_vid = '';
                let uc_vid = '';
                let tt_vid = '';
                let qh_vid = '';

                try {
                    bd_vid = Z.getParam('bd_vid');
                    uc_vid = Z.getParam('uctrackid');
                    tt_vid = Z.getParam('adid');
                    qh_vid = Z.getParam('qhclickid');
                } catch (err) {
                    console.error('_getLandingUrl getParam error', err);
                }

                // 新注册页没有落地页，此处的landingUrl为由新注册页生成的abt URL
                // let landingUrl = document.referrer || undefined;   // 旧逻辑
                let landingUrl = location.href.replace(location.hash, '');  // 新逻辑

                if (bd_vid || uc_vid || tt_vid) {
                    try {
                        // 当拿不到referrer时，手动拼接落地页Url，以减少后台数据与注册数据的误差
                        if (!landingUrl) {
                            var pageKey = Z.getParam('pageKey');
                            var za_ttt = encodeURIComponent(Z.getParam('za_ttt'));
                            // link.do情况
                            if (pageKey && za_ttt) {
                                if (bd_vid) {
                                    landingUrl = PROTOCOL + '//a.zhenai.com/abt/link.do?channelId=' + channelId + '&subChannelId=' + subChannelId + '&pageKey=' + pageKey + '&za_ttt=' + za_ttt + '&bd_vid=' + encodeURIComponent(bd_vid);
                                }
                                if (uc_vid) {
                                    landingUrl = PROTOCOL + '//a.zhenai.com/abt/link.do?channelId=' + channelId + '&subChannelId=' + subChannelId + '&pageKey=' + pageKey + '&za_ttt=' + za_ttt + '&uctrackid=' + encodeURIComponent(uc_vid);
                                }
                                if (tt_vid) {
                                    var ttParams = {
                                        adid: tt_vid,
                                        creativeid: Z.getParam('creativeid'),
                                        creativetype: Z.getParam('creativetype'),
                                        clickid: Z.getParam('clickid')
                                    };
                                    landingUrl = PROTOCOL + '//a.zhenai.com/abt/link.do?channelId=' + channelId + '&subChannelId=' + subChannelId + '&pageKey=' + pageKey + '&za_ttt=' + za_ttt + '&adid=' + ttParams.adid + '&creativeid=' + ttParams.creativeid + '&creativetype=' + ttParams.creativetype + '&clickid=' + ttParams.clickid;
                                }
                                // from.do情况,什么情况会生成from.do？
                            } else {
                                if (bd_vid) {
                                    landingUrl = PROTOCOL + '//a.zhenai.com/abt/from.do?channelId=' + channelId + '&subChannelId=' + subChannelId + '&pageKey=' + pageKey + '&bd_vid=' + encodeURIComponent(bd_vid);
                                }
                            }
                        }
                    } catch (err) {
                        console.error('get landingUrl error', err);
                    }
                }

                return landingUrl;
            } catch (err) {
                console.error('setAbtZaTTTCookie err', err);
            }
        },
        setAbtPageKeyCookie() {
            const pageKey = Z.getParam('pageKey');

            if (!pageKey) {
                return;
            }

            let abtParams = Z.cookie.get('abt_params') || '';

            const arr = abtParams.split('|');
            if (arr.length !== 5) {
                abtParams = '';
            }

            if (!abtParams) {
                const planId = 0;
                const schemeId = 0;
                const tmp = [ pageKey, channelId || 0, subChannelId || 0, planId, schemeId ].join('|');
                Z.cookie.set('abt_params', tmp, '.zhenai.com', '/', 24);
            }

            console.log('setAbtPageKeyCookie...');
        },
        setAbtZaTTTCookie() {
            try {
                let bd_vid = '';
                let uc_vid = '';
                let tt_vid = '';
                let qh_vid = '';

                try {
                    bd_vid = Z.getParam('bd_vid');
                    uc_vid = Z.getParam('uctrackid');
                    tt_vid = Z.getParam('adid');
                    qh_vid = Z.getParam('qhclickid');
                } catch (err) {
                    console.error('setAbtZaTTTCookie getParam error', err);
                }

                const landingUrl = this._getLandingUrl();

                if (landingUrl) {
                    const encodeUrl = encodeURIComponent(landingUrl);

                    if (bd_vid) {
                        Z.cookie.set('bd_url', encodeUrl, '.zhenai.com', '', 1);
                    }
                    if (uc_vid) {
                        Z.cookie.set('uc_link', encodeUrl, '.zhenai.com', '', 1);  // 文档要求落地页链接进行url编码
                    }
                    if (tt_vid) {
                        Z.cookie.set('toutiao_url', encodeUrl, '.zhenai.com', '', 1);
                    }
                }

                if (qh_vid) { // 奇虎只需传广告id，不需要传url
                    Z.cookie.set('qhclickid', qh_vid, '.zhenai.com', '', 1);
                }

            } catch (err) {
                console.error('setAbtZaTTTCookie err:', err);
            }

            console.log('setAbtZaTTTCookie...');
        },
        setBeforeValidateCodeOCPC() {
            try {
                // 记录OCPC回传状态
                oUserSelect.mark({
                    submitPhone: true,
                });

                // 小米ocpc上报
                // 跳到验证码页时提交
                if (
                    [
                        '909457',
                        '909458',
                        '910105',
                        '910106',
                        '910107',
                        '910108',
                        '910109',
                    ].indexOf(channelId) !== -1
                ) {
                    try {
                        window.mi_tracker &&
                        window.mi_tracker.log &&
                        window.mi_tracker.log('form', { conversionId: '627' });
                    } catch (e) {
                        console.log(e);
                    }
                }

                // 网易ocpc上报
                if ([ '910743' ].indexOf(channelId) !== -1) {
                    try {
                        window._nfe &&
                        typeof window._nfe.report === 'function' &&
                        window._nfe.report({
                            convert_id: '685',
                            convert_method: '0',
                            convert_type: '4',
                        });
                    } catch (err) {
                    }
                }

                if ([ '914463' ].indexOf(channelId) !== -1) {
                    try {
                        window._nfe &&
                        typeof window._nfe.report === 'function' &&
                        window._nfe.report({
                            convert_id: '3895',
                            convert_method: '1',
                            convert_type: '4',
                        });
                    } catch (err) {
                    }
                }

                // 快手ocpc上报
                if ([ '914334' ].indexOf(channelId) !== -1) {
                    try {
                        window._ks_trace &&
                        typeof window._ks_trace.push === 'function' &&
                        window._ks_trace.push({ event: 'click3' });
                    } catch (err) {
                    }
                }

                // 接入vivo ocpc上报
                if ([ '914418' ].indexOf(channelId) !== -1) {
                    try {
                        var actName = 'submit';
                        var actProp = { act: 'submit', name: '转化组件' };
                        window.VAD_EVENT &&
                        typeof window.VAD_EVENT.sendAction === 'function' &&
                        window.VAD_EVENT.sendAction(actName, actProp);
                    } catch (err) {
                    }
                }

                // 接入爱奇艺 api回传
                if ([ '914545' ].indexOf(channelId) !== -1) {
                    try {
                        var url = 'http://tc.cupid.iqiyi.com/dsp_lpapi?link=__LINK__&conv_time=__CONVTIME__&event_type=200';
                        var reportUrl = url
                            .replace('__LINK__', encodeURIComponent(window.document.referrer))
                            .replace('__CONVTIME__', Math.ceil(+new Date() / 1000));
                        var img = new Image();
                        img.onload = img.onerror = function () {
                            img = null;
                        };
                        img.src = reportUrl;
                    } catch (err) {
                    }
                }
            } catch (err) {
            }

            console.log('setBeforeValidateCodeOCPC...');
        },
        // OCPX相关，在手机验证码验证成功后调用。统计百度信息流回传数据，以排查数据误差
        setValidateCodeSuccessOCPC() {
            try {
                // oUserSelect.mark({
                //     msgValid: true,
                // });

                let bd_vid = '';
                let uc_vid = '';
                let tt_vid = '';
                let qh_vid = '';

                try {
                    bd_vid = Z.getParam('bd_vid');
                    uc_vid = Z.getParam('uctrackid');
                    tt_vid = Z.getParam('adid');
                    qh_vid = Z.getParam('qhclickid');
                } catch (err) {
                    console.error(err);
                }

                const landingUrl = this._getLandingUrl();

                if (bd_vid) {
                    Z.tj.kibana({
                        resourceKey: '注册页百度信息流api回传',
                        ext10: bd_vid,
                        ext11: document.referrer || '0',
                        ext12: landingUrl,
                        ext13: Z.platform.isIos ? 'ios' : 'android',
                        ext14: window.navigator.userAgent,
                    });
                }

                // 统计uc信息流回传数据，以排查数据误差
                if (uc_vid) {
                    Z.tj.kibana({
                        resourceKey: '注册页uc汇川api回传',
                        ext10: uc_vid,
                        ext11: document.referrer || '0',
                        ext12: landingUrl,
                        ext13: Z.platform.isIos ? 'ios' : 'android',
                        ext14: window.navigator.userAgent,
                    });
                }

                // 微博ocpc回传 - start
                var reportIdMap = {
                    '913271': '207911',
                    '913272': '207912',
                    '913273': '207922',
                    '913274': '207924',
                    '913275': '207925',
                };

                if (reportIdMap[channelId]) {
                    window.wbadmt && typeof window.wbadmt.send === 'function' && window.wbadmt.send({
                        'eventid': reportIdMap[channelId],
                        'eventtype': 'form',
                        'eventvalue': ''
                    });
                }
            } catch (err) {
                console.error('setValidateCodeOCPC err:', err);
            }

            console.log('setValidateCodeSuccessOCPC...');
        },
        getValidateCodeParams(phone, messageCode, pageType) {
            const data = {
                phone,
                messageCode,
                bdVid: Z.getParam('bd_vid'),
                qywxId: Z.getParam('qywxId'),
                pageType,
            };

            const iqiyi_id = Z.getParam('impress_id');
            if (iqiyi_id) {
                data.advReportJsonString = JSON.stringify(prototype.$utils.parseQueryString(location.href));
            }

            return data;
        },
        getOverwriteAccountParams(messageCode, pageType) {
            const data = {
                messageCode,
                bdVid: Z.getParam('bd_vid'),
                qywxId: Z.getParam('qywxId'),
                pageType
            };

            const landingUrl = this._getLandingUrl();

            if (landingUrl) {
                data.landingUrl = landingUrl;
            }

            const iqiyi_id = Z.getParam('impress_id');
            if (iqiyi_id) {
                const iqiyi_params = prototype.$utils.parseQueryString(location.href);
                data.advReportJsonString = JSON.stringify(iqiyi_params);
            }

            return data;
        },
    },
    $storage: storage,
};

// $开头表示这是挂载在原型上的属性
Object.assign(Vue.prototype, prototype);

export default prototype;
