<template>
    <question-panel
        title="您想要匹配哪个年龄段的异性"
        :src="cmsConfig.questionBodyImg2"
        :height="736">
        <radio-list
            :list="list"
            :value="requirement.ageRange"
            @change="handleChange"/>
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import RadioList from './radio-list';
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-age",
    components: {
        QuestionPanel,
        RadioList,
    },
    data() {
        return {
            list: [
                {
                    label: '23岁以下',
                    accessPoint: 6,
                    accessPointDesc: '问题页2-点击“选项1”',
                },
                {
                    label: '24-30岁',
                    accessPoint: 7,
                    accessPointDesc: '问题页2-点击“选项2”',
                },
                {
                    label: '31-35岁',
                    accessPoint: 8,
                    accessPointDesc: '问题页2-点击“选项3”',
                },
                {
                    label: '36-40岁',
                    accessPoint: 9,
                    accessPointDesc: '问题页2-点击“选项4”',
                },
                {
                    label: '41-45岁',
                    accessPoint: 10,
                    accessPointDesc: '问题页2-点击“选项5”',
                },
                {
                    label: '46岁以上',
                    accessPoint: 11,
                    accessPointDesc: '问题页2-点击“选项6”',
                },
            ]
        }
    },
    computed: {
        ...mapState([
            'cmsConfig',
            'requirement'
        ]),
    },
    methods: {
        ...mapMutations([
            'setRequirement',
        ]),
        handleChange(item) {
            this.$report(item.accessPoint, item.accessPointDesc);

            if (this.lock) {
                return;
            }

            this.lock = true;

            this.setRequirement({
                ageRange: item.label,
            });

            setTimeout(() => {
                this.lock = false;

                this.$router.push({
                    path: "/questions/2",
                });
            }, 1000);
        }
    }
}
</script>
