import Toast from 'vant/lib/toast';
import axios from './axios';
import config from './config';
// axios.defaults.timeout = 1000 // 25s 超时

const domain = config.API_HOST_NAME();
const nearDomain = config.NEAR_API_HOST_NAME();


/**
 * 封装axios
 * @param  methods 默认post
 * @param havacode 是否返回code码
 */
export const formatAxios = (url, params, methods = 'post', havacode = false, isLoading = true, header = {}) => {   // pad接口--'Content-Type': 'application/x-www-form-urlencoded'
    if (isLoading) {
        Toast.loading({forbidClick: true});
    }
    if (methods === 'post') {
        return new Promise((resolve, reject) => {
            let headers = {
                ...header,
            };
            axios[methods](('https://' + domain + url), params, {headers})
                .then((res = {}) => {
                    if (havacode || !res.isError) {
                        Toast.clear();
                        resolve(havacode ? res : res.data);
                    } else {
                        Toast(res.errorMessage);
                    }
                })
                .catch(error => {
                    if(error.message === 'Network Error'){
                        Toast('网络有问题，请稍后重试');
                    } else {
                        Toast(error);
                    }
                    reject(error);
                });
        });
    } else if (methods === 'get') {
        return new Promise((resolve, reject) => {
            axios[methods](('https://' + domain + url), {params: params,
                headers: {
                    ...header
                }})
                .then((res = {}) => {
                    if (havacode || !res.isError) {
                        Toast.clear();
                        resolve(havacode ? res : res.data);
                    } else {
                        Toast(res.errorMessage);
                    }
                })
                .catch(error => {
                    Toast(error);
                    reject(error);
                });
        });
    }
};
/**
 * 封装axios
 * @param  methods 默认post
 * @param havacode 是否返回code码
 */
export const formatNearAxios = (url, params, methods = 'post', havacode = false, isLoading = true, header = {}) => {   // pad接口--'Content-Type': 'application/x-www-form-urlencoded'
    if (isLoading) {
        Toast.loading({forbidClick: true});
    }
    const ua = Z.getUA();
    if (methods === 'post') {
        return new Promise((resolve, reject) => {
            Z.ajax({
                type: methods,
                url: 'https://' + nearDomain + url,
                data: params,
                opts: {
                    headers: {
                        ...header,
                        ua,
                    },
                },

            }, response => {
                Toast.clear();
                if (response.isError === true) {
                    if (response.errorCode !== "-11507001") {
                        Toast(response.errorMessage);
                    }
                    resolve(havacode ? response : response.data);
                } else {
                    resolve(havacode ? response : response.data);
                }
            }, error => {
                Toast.clear();
                Toast(error);
                reject(error);
            });
        });
    } else if (methods === 'get') {
        return new Promise((resolve, reject) => {
            axios[methods](('https://' + nearDomain + url), {params: params,
                headers: {
                    ...header,
                    ua,
                }})
                .then((res = {}) => {
                    if (havacode || !res.isError) {
                        Toast.clear();
                        resolve(havacode ? res : res.data);
                    } else {
                        Toast(res.errorMessage);
                    }
                })
                .catch(error => {
                    Toast(error);
                    reject(error);
                });
        });
    }
};

