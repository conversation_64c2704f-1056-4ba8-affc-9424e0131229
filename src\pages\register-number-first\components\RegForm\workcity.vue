<template>
    <div class="workcity">
        <div class="workcity-info">
            <van-picker
                ref="refPicker"
                item-height="1.25rem"
                :visible-item-count="5"
                :show-toolbar="false"
                :columns="list.options"
                class="workcity-info-picker"
            />
        </div>
        <div
            class="btn"
            @click="goNext"
        >
            下一步
        </div>
    </div>
</template>
<script>
import { Picker } from "vant";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import "vant/lib/picker/style";
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { PAGE_TYPE } from "../../config";
import Api from '@/common/server/base';
import { pageTypeChnMap, registerResult } from "@/common/config/register-dictionary.js";
import { Toast } from "vant";
export default {
    name: "WorkCity",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        vanPicker: Picker
    },
    data() {
        return {
            dict: Z.workCity
        };
    },
    mounted() {
        // 初始化定位
        this.handleInitArea();
    },
    methods: {
        async goNext() {
            const picker = this.$refs.refPicker;
            const values = picker.getValues();

            const workCity = values[2].key ? values[2].key : values[1].key ? values[1].key : "";
            // const workCity = values[2].key ? values[1].key : values[1].key ? values[1].key : "";
            const params = {
                key: "workCity",
                value: workCity,
                isMark: false
            };

            if (["北京", "上海", "重庆", "天津"].includes(values[0].text)) {
                sessionStorage.setItem('workCityReport', values[0].key)
            } else {
                sessionStorage.setItem('workCityReport', values[1].key ? values[1].key : '')
            }
            setLocalRegisterForm(params, PAGE_TYPE);
            const registerForm = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) || {};
            const sendData = {
                phone: registerForm.phone.replace(/[^(\d)]/g, ""),
                pageType: pageTypeChnMap[PAGE_TYPE],
                clickId: localStorage.getItem('AD_URL'),
                gender: registerForm.gender,
                birthday: registerForm.birthday,
                workCity: workCity,
            }
            const result = await Api.complementRegInfo(sendData);
            if (result.code === 0) {
                oCookie.set('token', result.data.token, '.zhenai.com' )
                Session.setItem('token', result.data.token)
                Session.setItem('memberId', result.data.memberId)
            } else if (result.code == '109') { // 停留时间过长跳回首页
                this.$router.push({
                    path:`/message`
                });
                return
            } else {
                Toast(result.msg)
                this.$report(30070, '三步注册失败', {
                    ext3: JSON.stringify(result)
                });
            }
            this.$report(3007, '选择工作地');
            this.$emit('go-next');
        },
        handleInitArea() {
            const workCity =
                (Storage.getItem(
                    `cachedRegisterForm-${PAGE_TYPE}`
                ) &&
                    Storage.getItem(
                        `cachedRegisterForm-${PAGE_TYPE}`
                    ).workCity) ||
                "";
            if (workCity) {
                const cityArr = findWorkCity(workCity);
                this.$refs.refPicker.setValues(cityArr);
            } else {
                // 有缓存优先读缓存，否则走定位逻辑
                this.handleLocate();
            }
        },
        handleLocate() {
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                    // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {
                    //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([
                                result.addressComponent.province,
                                result.addressComponent.city,
                                result.addressComponent.district
                            ]);
                            this.$refs.refPicker.setValues(areaArr);
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if (status === "complete") {
                        const areaArr = this.handleLocationPair([
                            result.province,
                            result.city,
                            ""
                        ]);
                        this.$refs.refPicker.setValues(areaArr);
                    } else {
                        this.$refs.refPicker.setValues([
                            "广东",
                            "肇庆",
                            "端州区"
                        ]); // 缺省地区
                    }
                });
            });
        },
        handleLocationPair(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            const targetProvince = this.$z_.find(this.dict, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });

            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(
                targetCity.children,
                district => {
                    return district.text.indexOf(sliceDistrict) >= 0;
                }
            );

            return [targetProvince.text, targetCity.text, targetDistrict.text];
        },
    }
};
</script>

<style lang="scss">
.workcity {
    &-info {
        margin-top: 90px;
        &-picker {
            background: transparent;
            .van-picker__mask {
                background: linear-gradient(180deg, transparent),
                    linear-gradient(0deg, transparent);
            }
            .van-picker-column__item {
                opacity: 0.7;
                color: #222222;
                font-size: 30px;
                // font-family: 'love';
                > div{
                    line-height: 1.4;
                }

                &--selected {
                    opacity: 1;
                    color: #a98ce6;
                    font-size: 36px;
                }
            }
        }
    }
    .btn {
        margin: 110px auto 0;
        width: 558px;
        height: 100px;
        background-color: #A98CE6;
        border-radius: 55px;
        font-size: 32px;
        color: #FFFFFF;
        line-height: 100px;
        text-align: center;
        // font-family: 'love';

    }
}
</style>
