<template>
    <div
        :class="`home page${pageType} ${!isBigScreen ? 'normal-screen' : ''}`"
    >
        <template v-if="pageType === '80' || pageType === '86'">
            <div class="header">
                <div class="logo"></div>
            </div>
            <div class="banner">
                <div
                    v-if="!isShowResult"
                    class="searching"
                >
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20230519/1684480422468_564993_t.png"
                        @load="imgLoad"
                    />
                </div>
                <div
                    :style="{ opacity: isShowResult ? 1 : 0 }"
                    class="avatars"
                ></div>
            </div>
            <div class="title">
            </div>
            <div class="btn-wrap">
                <a
                    class="btn"
                    @click="goNext"
                ></a>
            </div>
            <div class="des">
                <div class="des-title">
                    活动规则：
                </div>
                <ul>
                    <li>1.本活动是由珍爱组织的多人线上限时聊天相亲活动</li>
                    <li>2.我们会根据精准的推荐算法为你匹配同城的单身异性</li>
                </ul>
            </div>
            <div class="foot">
                <p class="copyright">
                    4001-520-520 粤ICP备09157619号-1
                    <br />深圳市珍爱网信息技术有限公司
                </p>
            </div>
        </template>
        <template v-else-if="pageType === '81'">
            <div
                class="btn"
                @click="goNext"
            ></div>
            <!-- <common-protocol
                class="home-protocol"
                :is-checked.sync="isCheckProtocol"
                :agreement-status="1"
                :style-config="{
                    textColor: '#5C5B5B',
                    protocolColor: '#804655',
                    protocolCheckedUrl:
                        'https://photo.zastatic.com/images/common-cms/it/20230524/1684916028391_789035_t.png'
                }"
            /> -->
            <p class="copyright">
                4001-520-520&emsp;粤ICP备09157619号-1&emsp;深圳市珍爱网信息技术有限公司
            </p>
        </template>
        <template v-else-if="pageType === '82'">
            <div
                class="btn"
                @click="goNext"
            ></div>
            <!-- <common-protocol
                class="home-protocol"
                :is-checked.sync="isCheckProtocol"
                :agreement-status="1"
                :style-config="{
                    textColor: '#EEEEEE',
                    protocolColor: '#FF5E5E',
                    protocolCheckedUrl:
                        'https://photo.zastatic.com/images/common-cms/it/20230524/1684899974570_773676_t.png'
                }"
            /> -->
            <p class="copyright">
                4001-520-520&emsp;粤ICP备09157619号-1&emsp;深圳市珍爱网信息技术有限公司
            </p>
        </template>
        <template v-else-if="pageType === '83'">
            <div
                class="btn"
                @click="goNext"
            ></div>
            <!-- <common-protocol
                class="home-protocol"
                :is-checked.sync="isCheckProtocol"
                :agreement-status="1"
                :style-config="{
                    textColor: '#FFFFFF',
                    protocolColor: '#FFFFFF',
                    protocolCheckedUrl:
                        'https://photo.zastatic.com/images/common-cms/it/20220602/1654159772231_16933_t.png'
                }"
            /> -->
            <p class="copyright">
                4001-520-520&emsp;粤ICP备09157619号-1&emsp;深圳市珍爱网信息技术有限公司
            </p>
        </template>

        <common-protocol-modal
            v-model="isShowModal"
            @confirm="handleConfirmProtocol"
            :page-type="planName"
            :style-config="{
                confirmButtonColor: '#ffffff',
                confirmButtonBgColor: '#767DFF',
                cancleButtonColor: '#767DFF'
            }"
        />
    </div>
</template>

<script>
import { session as Session, storage as Storage } from "@/common/utils/storage";
// import CommonProtocol from "@/common/business/CommonProtocol.vue";
import CommonProtocolModal from "@/common/business/components/CommonProtocolModal.vue";

export default {
    name: "Home",
    components: {
        // CommonProtocol,
        CommonProtocolModal
    },
    data() {
        return {
            isShowResult: false,
            planName: Session.getItem('planName'),
            pageType: Session.getItem('ext30'),
            isCheckProtocol: false,
            isShowModal: false,
        };
    },
    computed: {
        isBigScreen() {
            //这里根据返回值 true 或false ,返回true的话 则为全面屏
            var result = false;
            var rate = window.screen.height / window.screen.width;
            var limit = window.screen.height == window.screen.availHeight ? 1.8 : 1.65; // 临界判断值
            // window.screen.height为屏幕高度
            //  window.screen.availHeight 为浏览器 可用高度
            if (rate > limit) {
                result = true;
            }
            return result;
        }
    },
    mounted() {
        this.$report(1, "落地页");
        this.$report(3000, "首页访问（监控）");
        Storage.removeItem(`cachedRegisterForm-${this.planName}`);
    },
    methods: {
        goNext() {
            this.$report(1, "落地页-按钮点击");
            this.$report(3001, "首页点击（监控）");
            this.$router.push({
                path: "/about/0"
            });
        },
        handleConfirmProtocol() {
            this.goNext();
        },
        imgLoad() {
            setTimeout(() => {
                this.isShowResult = true;
            }, 2500);
        }
    }
};
</script>
<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.home {
    position: relative;

    &.page80,
    &.page86 {
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684480422473_830332_t.png');
        background-size: 100% 1624px;
        background-position: center center;
        height: 100vh;
        max-height: 1624px;
        overflow-x: hidden;
        .header {
            padding: 16px 0 0 40px;
            .logo {
                width: 68px;
                height: 68px;
                @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684480421793_300039_t.png');
            }
        }

        .banner {
            height: 428px;
            position: relative;
            .searching {
                width: 432px;
                height: 426px;
                @include set-img('https://photo.zastatic.com/images/common-cms/it/20230529/1685358401528_479211_t.png');
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);

                img {
                    display: block;
                    width: 216px;
                    height: 220px;
                    @include set-img('https://photo.zastatic.com/images/common-cms/it/20230529/1685358401369_41736_t.png');
                    position: absolute;
                    top: 134px;
                    left: 80px;
                    animation: spin 1.5s infinite linear;

                    @keyframes spin {
                        from {
                            transform:  rotate(0turn)
                                        translateY(-160px) translateY(50%)
                                        rotate(1turn);
                        }
                        to {
                            transform:  rotate(1turn)
                                    translateY(-160px) translateY(50%)
                                    rotate(0turn);
                        }
                    }
                }
            }
            .avatars {
                width: 620px;
                height: 364px;
                margin: 64px auto 0;
                @include set-img('https://photo.zastatic.com/images/common-cms/it/20230524/1684920722957_181907_t.png');
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                transition: 1s all ease-in;
            }
        }

        .title {
            width: 684px;
            height: 297px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684480422472_818913_t.png');
            margin: 0 auto;
        }

        .btn-wrap {
            width: 764px;
            height: 216px;
            padding: 34px 0 0 56px;
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230519/1684480421767_212017_t.png');
            .btn {
                display: block;
                width: 654px;
                height: 110px;
            }
        }

        .des {
            margin-top: 72px;
            padding: 0 40px;
            .des-title {
                color: #26273cff;
                font-size: 32px;
                font-weight: 400;
                line-height: 52px;
            }
            ul {
                color: #26273cff;
                font-size: 28px;
                font-weight: 400;
                line-height: 52px;
            }
        }

        .foot {
            margin: 82px auto 0;
            padding-bottom: 48px;
            .copyright {
                color: #9395A4;
                font-size: 22px;
                line-height: 32px;
                text-align: center;
            }
        }
    }

    &.page81 {
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230529/1685332288343_413838_t.png');
        background-size: 100% 1624px;
        background-position: 0 0;
        background-color: #000;
        height: 100vh;
        max-height: 1624px;
        &.normal-screen {
            background-image: url('https://photo.zastatic.com/images/common-cms/it/20230529/1685332247292_376482_t.png');
            background-size: 100% 1334px;
            min-height: 1334px;
            .btn {
                bottom: 90px;
            }
            .copyright {
                bottom: 32px;
            }
        }
        .btn {
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230524/1684898592178_740856_t.png');
            width: 654px;
            height: 110px;
            position: fixed;
            bottom: 106px;
            left: 48px;
        }
        .home-protocol {
            margin-top: 60px;
        }
        .copyright {
            color: #5c5b5b;
            font-size: 22px;
            font-weight: 400;
            line-height: 28px;
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: 48px;
        }
    }

    &.page82 {
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230529/1685331291250_707563_t.png');
        background-size: 100% 1624px;
        background-position: 0 0;
        height: 100vh;
        max-height: 1624px;
        &.normal-screen {
            background-image: url('https://photo.zastatic.com/images/common-cms/it/20230529/1685332098251_348631_t.png');
            background-size: 100% 1334px;
            min-height: 1334px;
            .btn {
                bottom: 90px;
            }
            .copyright {
                bottom: 32px;
            }
        }
        .btn {
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230524/1684898592084_627078_t.png');
            width: 654px;
            height: 110px;
            position: fixed;
            bottom: 106px;
            left: 48px;
        }
        .home-protocol {
            margin-top: 20px;
        }
        .copyright {
            color: #EEEEEE;
            font-size: 22px;
            font-weight: 400;
            line-height: 28px;
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: 48px;
        }
    }

    &.page83 {
        @include set-img('https://photo.zastatic.com/images/common-cms/it/20230529/1685342653477_773691_t.png');
        background-size: 100% 1624px;
        background-position: 0 0;
        height: 100vh;
        max-height: 1624px;
        &.normal-screen {
            background-image: url('https://photo.zastatic.com/images/common-cms/it/20230529/1685342653479_951301_t.png');
            background-size: 100% 1334px;
            min-height: 1334px;
            .btn {
                bottom: 110px;
            }
            .copyright {
                bottom: 42px;
            }
        }
        .btn {
            @include set-img('https://photo.zastatic.com/images/common-cms/it/20230524/1684898690094_630366_t.png');
            width: 612px;
            height: 119px;
            position: fixed;
            bottom: 165px;
            left: 70px;
        }
        .home-protocol {
            margin-top: 64px;
        }
        .copyright {
            opacity: 0.4;
            font-weight: 400;
            font-size: 20px;
            color: #FFFFFF;
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: 87px;
        }
    }
}
</style>
