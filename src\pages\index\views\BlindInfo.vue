<template>
    <div
        class="blindinfo-wrapper"
        :style="cmsConfig.pageColor"
    >
        <info-header />

        <info-blind 
            :has-selected="hasSelected"
            @open-modal="openModal(arguments)"
            @select-blind="selectBlind"
        />

        <info-avatar 
            @open-modal="openModal(arguments)"
        />

        <info-content 
            v-if="hasSelected" 
            @open-modal="openModal(arguments)"
        />

        <info-footer
            @open-modal="openModal(arguments)"
        />

        <info-default />

        <!-- 弹窗 -->
        <modal 
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
        />        
    </div>
</template>

<script>
import {mapState,mapMutations,mapActions} from 'vuex';
import {Modal} from '../components/common/index.js';
import {InfoHeader,InfoBlind,InfoAvatar,InfoContent,InfoFooter,InfoDefault} from '../components/info/index.js';
export default {
    components:{
        InfoHeader,
        InfoBlind,
        InfoAvatar,
        InfoContent,
        InfoFooter,
        InfoDefault,
        Modal
    },
    data() {
        return {
            hasSelected:false,
            showModal:false,
            modalType:"modalDownload",
            modalParam:{},
        };
    },
    computed:{
        ...mapState([
            'cmsConfig'
        ]),
    },
    async created(){
        let flagFilled = localStorage.getItem("flagFilled");

        // 没有注册态，跳回大表单页
        if(flagFilled !== '1'){
            this.$router.push({
                path:'index'
            });
        }
        // 注册态只能用一次
        localStorage.setItem("flagFilled", "0");

        // 清空注册信息
        localStorage.setItem("localFormInfo", "");
        localStorage.setItem("localRegisterInfo", "");
        localStorage.setItem("defaultBirthday","");
        localStorage.setItem("defaultWorkCity","");
    },
    mounted(){
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
    },
    methods:{
        selectBlind(){
            this.hasSelected = true;
        },
        openModal(args){
            this.modalType = args[0];
            this.modalParam = args[1];
            this.showModal = true;
        },
        closeModal(){
            this.showModal = false;
        },
    }
};
</script>

<style lang="scss" scoped>
.blindinfo-wrapper{
    background: lightblue;
    padding-bottom: 100px;
}
</style>
