<template>
    <div class="birthday">
        <div class="birthday-info">
            <van-picker
                ref="refPicker"
                item-height="1.25rem"
                :visible-item-count="5"
                :show-toolbar="false"
                :columns="list.options"
                class="birthday-info-picker"
            />
        </div>
        <div
            class="btn"
            @click="goNext"
        >
            下一步
        </div>

        <!-- <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(option, name) in list.options"
            >
                <h4 class="birthday-info-title">
                    {{ name }}后
                </h4>
                <div
                    class="birthday-info-card"
                    :class="{ spe: name === '00' }"
                >
                    <span
                        class="bic-num"
                        :class="{ active: item === curBirthday }"
                        v-for="item in option"
                        @click="goNext(item)"
                    >{{ item }}</span>
                </div>
            </div>
        </div> -->
    </div>
</template>
<script>
import { Picker } from "vant";
import {
    setLocalRegisterForm,
    keyToValue
} from "@/common/business/utils/localRegisterForm.js";
import "vant/lib/picker/style";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";

export default {
    name: "Birthday",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        vanPicker: Picker
    },
    data() {
        return {
            lock:false,
            
        };
    },
    mounted() {
        this.$report(32, "出生年份页访问");
        // 初始化出生日期
        this.initBirthday();
    },
    methods: {
        initBirthday(){
            const curBirthday = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).birthday || "";
            let curYear = 1990;
            if(curBirthday){
                curYear = keyToValue('birthday', curBirthday);
            } 
            this.$refs.refPicker.setValues([curYear]);
        },
        goNext() {
            if(this.lock){
                return;
            }
            this.lock=true;

            // 展示的格式 xx年
            const yearValue = this.$refs.refPicker.getValues()[0].key;
            // 存本地的格式 时间戳
            const dateValue = new Date(yearValue + "/" + 1 + "/" + 1).getTime();
            const params = {
                key: "birthday",
                value: dateValue,
                getMarkValue:()=>{
                    // 构造回传给后台的时间格式 
                    return {
                        year: keyToValue('birthday', dateValue)+"",
                        month: "1",
                        day: "1"
                    };
                }
            };
            this.$report(33, "出生年份页-按钮点击");
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    },
   
};
</script>

<style lang="scss" scoped>
.birthday {
    &-info {
        margin-top: 90px;
        &-picker {
            background: transparent;
            .van-picker__mask {
                background: linear-gradient(180deg, transparent),
                    linear-gradient(0deg, transparent);
            }
            .van-picker-column__item {
                opacity: 0.7;
                color: #222222;
                font-size: 30px;
                &--selected {
                    opacity: 1;
                    color: #0f1122;
                    font-size: 36px;
                }
            }
        }
    }
    .btn {
        margin: 180px auto 0;
        width: 558px;
        height: 100px;
        background: #17263D;
        border-radius: 55px;
        font-size: 32px;
        color: #FFFFFF;
        line-height: 100px;
        text-align: center;
                
    }
}
</style>
