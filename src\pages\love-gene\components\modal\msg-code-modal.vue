<template>
    <van-popup
        class="love-gene-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange">

        <h1 class="love-gene-modal__title">
            验证码已通过短信<br>
            发到您手机
        </h1>

        <p class="love-gene-modal__desc">
            {{ phone }}
        </p>

        <div class="love-gene-modal__code">
            <div class="love-gene-modal__code-input-items">
                <div
                    v-for="(item, index) in 4"
                    :key="index"
                    class="love-gene-modal__code-input-item"/>
            </div>
            <input
                ref="codeInput"
                type="tel"
                class="love-gene-modal__code-input"
                maxlength="4"
                :value="code"
                @input="checkCode"
                autocomplete="new-password">
        </div>

        <div class="love-gene-modal__code-error">{{ errorMessage }}</div>

        <div class="love-gene-modal__btn"
             :style="{ background: isLock ?'#B5B6BA':'#767dff'}"
             @click="clickSendCode">
            {{ btnText }}
        </div>

        <div class="love-gene-modal__cancel"
             @click="closeModal">
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from "vuex";
import Api from '@/common/server/base';
import { reportMagic } from "@/common/utils/report";

export default {
    name: 'msg-code-modal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        validateCode: {
            type: Function,
            required: true,
        }
    },
    data() {
        return {
            errorMessage: '',
            btnText: '',
            isLock: false,
            code: '',
            leaveTime: null,
            isListenVisibilitychange: null,
        }
    },
    computed: {
        ...mapState([
            'registerForm',
        ]),
        phone() {
            return (this.$z_.get(this.registerForm, 'phone') || '').replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3');
        }
    },
    watch: {
        value: {
            handler(value) {
                if (!value) {
                    return;
                }

                this.code = '';
                this.errorMessage = '';

                this.$report(300, '验证短信弹窗访问');

                this.$gather.setBeforeValidateCodeOCPC();

                this.countDown();

                this.$nextTick().then(() => {
                    this.$refs.codeInput.focus();
                });
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        async checkCode(evt) {
            this.errorMessage = ''
            this.code = evt.target.value.replace(/\D/g, '');
            evt.target.value = this.code;

            if (this.code.length === 4) {
                this.$refs.codeInput.blur();
                const result = await this.validateCode(this.code);

                if (result.isError) {
                    this.code = '';
                    this.$refs.codeInput.focus();

                    switch (result.errorCode) {
                        case '-8002005':
                            this.errorMessage = '验证码错误，请重新输入';
                            break;
                        case '-8002006':
                            this.errorMessage = '验证码已过期';
                            break;
                        case '-8002004':
                            this.errorMessage = result.errorMessage;
                            break;
                    }

                    return;
                }

                this.closeModal();
                this.$gather.setValidateCodeSuccessOCPC();
            }
        },
        closeModal() {
            this.$emit('input', false);
        },
        async sendCode() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();

            const sendData = {
                phone: this.registerForm.phone,
                type: 0,
            };

            //【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam('clickid'),
                adid: Z.getParam('adid'),
                creativeid: Z.getParam('creativeid'),
                creativetype: Z.getParam('creativetype')
            };

            for (const key in toutiaoParamlist) {
                if (toutiaoParamlist[key]) {
                    sendData[key] = toutiaoParamlist[key];
                }
            }

            const sendMsgResult = await Api.sendWapMessageCodeV2(sendData);

            if (sendMsgResult.isError) {
                this.errorMessage = sendMsgResult.errorMessage;
            } else {
                this.$toast('验证码已发送，请注意查收');
            }
        },
        clickSendCode() {
            this.$report(301, '验证短信弹窗-重新获取短信按钮点击');
            this.countDown();
        },
        async countDown() {
            if (this.isLock) {
                return;
            }

            this.isLock = true;

            await this.sendCode();

            // 从60s开始倒计时
            this.btnText = 60;

            this.timer = setInterval(() => {
                this.btnText -= 1;
                if (this.btnText <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }, 1000);
        },
    },
    mounted() {
        const callback = () => {
            // 用户进入后台
            if (document.visibilityState === "hidden") {
                this.leaveTime = new Date().getTime();
            } else if (document.visibilityState === "visible") {
                if (!this.isLock) {
                    return;
                }

                this.backTime = new Date().getTime();
                const diff = Math.floor((this.backTime - this.leaveTime) / 1000);

                this.btnText -= diff;

                if (this.btnText <= 0) {
                    this.btnText = '获取验证码';
                    this.isLock = false;
                    clearInterval(this.timer);
                }
            }
        }

        document.addEventListener('visibilitychange', callback);
        this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('visibilitychange', callback)
        });
    }
}
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

// TODO 所有 Modal 样式放到一个 css 文件

$width: 600px;

.love-gene-modal {
    width: $width;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 72px;
    overflow: hidden;
    @include flex-center(column, null, center);

    &.van-popup--center {
        // 某些浏览器出现软键盘后会覆盖输入框，所以将弹窗位置放上点
        top: 40%;
    }

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        color: #6C6D75;
        text-align: center;
        line-height: 42px;
        margin-bottom: 24px;
        padding: 0 26px;
    }

    &__btn {
        @include flex-center(column);
        flex-shrink: 0;
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #767DFF;
        border-radius: 44px;
        margin-top: 24px;
        margin-bottom: 32px;

        &-continue {
            padding-top: 3px;

            > div:last-child {
                font-size: 24px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        flex-shrink: 0;
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #767DFF;
        text-align: center;
    }

    &__code {
        position: relative;
        color: #26273C;
        width: 100%;
        margin-bottom: 16px;

        &-input-items {
            @include flex-center(row, space-between, center);
        }

        &-input-item {
            width: 102px;
            height: 102px;
            opacity: 0.2;
            background: #6C6D75;
            border-radius: 8px;
        }

        &-error {
            width: 100%;
            text-align: center;
            bottom: -38px;
            font-size: 24px;
            color: #FF4881;
            line-height: 42px;
        }

        &-input {
            box-sizing: border-box;
            width: $width;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            height: 60px;
            font-size: 30px;
            line-height: 30px;
            background: transparent;
            letter-spacing: 101px;
            padding-left: 116px;
            overflow: hidden;
        }
    }
}
</style>
