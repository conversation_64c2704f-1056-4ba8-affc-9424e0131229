<template>
    <div
        class="collection-wrapper"
        :style="cmsConfig.pageColor"
    >
        <!-- CMS配置的头图 -->
        <img
            class="collection-banner"
            :src="cmsConfig.formImg"
            :style="{
                visibility:cmsConfig.formImg?'visible':'hidden',
                height:cmsConfig.formImg?'auto':'0px'
            }"
        />
        <!-- <div class="test"></div> -->
        <div class="collection-header">
            <div
                class="header__avatars"
                id="svgaAvatars"
            ></div>
            <div class="header__online">
                已经有<span id="aaa">{{ onlineNumber }}</span>位小伙伴领取盲盒对象
            </div>
        </div>

        <!-- 表单部分 -->
        <collection-form />
    </div>
</template>

<script>

import {mapState,mapMutations,mapActions} from 'vuex';
import {CollectionForm} from '../components/collection/index.js';
import {getRandomInt} from "@/common/utils/tools.js";
import {_saveWechatAdCode,_getRandomAvatar} from "../js/api.js";
import {reportKibana} from '@/common/utils/report.js';

export default {
    components:{
        CollectionForm
    },
    data() {
        return {
            onlineNumber:null,
            player:null,
            testPhone:''
        };
    },
    computed:{
        ...mapState([
            'formInfo',
            'registerInfo',
            'cmsConfig'
        ]),
    },
    async created(){
        //获取微信授权code
        let wxCode = Z.getParam('code');
        if(Z.platform.isWeiXin){
            if(wxCode){
                this.saveWechatAdCode(wxCode);
                localStorage.removeItem('wxCount');
            }else{
                //防止失败死循环鉴权
                let wxCount = localStorage.getItem('wxCount') || 1;
                if(wxCount <= 3){
                    localStorage.setItem('wxCount',++wxCount);
                    let redirect_uri = encodeURIComponent(window.location.href);
                    let url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx9010cdfe96709e2c&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
                    window.location.href = url;
                }else{
                    localStorage.removeItem('wxCount');
                }
            }
        }
        // 随机生成在线人数
        this.setOnlineNumber();
        // 获取CMS配置
        await this.setCmsConfig();

    },
    mounted(){
        // 打桩
        reportKibana(
            "导量H5大表单翻牌",
            1,
            "大表单页访问",
            {
                ext16: 2,
            }
        );

        // 定位到页面顶部
        document.body.scrollTop = document.documentElement.scrollTop = 0;
        document.body.scrollIntoView();
        window.scrollTo(0, 0);

        // banner动效
        this.setSVGA();
    },
    methods:{

        ...mapMutations([
            "setFormInfo"
        ]),
        ...mapActions([
            "setCmsConfig",

        ]),
        setOnlineNumber(){
            this.onlineNumber = getRandomInt(360000,499997);
            // let timer = setInterval(()=>{
            //     let count = getRandomInt(1,3)
            //     this.onlineNumber += count;
            //     if(this.onlineNumber > 499997 ){
            //             clearInterval(timer);
            //         }
            // },2000);
        },
        async setSVGA(){
            let player = new SVGA.Player('#svgaAvatars'),
                parser = new SVGA.Parser('#svgaAvatars');

            let resData = await _getRandomAvatar({});
            if(resData.isError){
                return this.$toast(resData.errorMessage);
            }

            // 后台给的是200x200，压缩至100x100
            let avatarList = resData.data.list;
            avatarList.forEach((item)=>{
                item.avatar += '?imageMogr2/thumbnail/100x100';
            });

            parser.load(require('../assets/imgs/svgaAvatar1.svga'), (videoItem)=>{
                // 设置头像
                for(let i=0;i<avatarList.length;i++){
                    player.setImage(avatarList[i].avatar,`key${i+1}`);
                }
                player.setVideoItem(videoItem);
                player.loops = 1;
                player.startAnimation();
                player.onFinished(()=>{
                    // 动画执行一次，然后只循环48帧之后的上下浮动部分
                    player.startAnimationWithRange({location:48,length:48});
                });
            });
        },
        closeModal(){
            this.showModal = false;
        },
        closeSelect(){
            this.showSelect = false;
        },
        checkRegisterInfo(){
            return true;
        },
        async saveWechatAdCode(code){
            await _saveWechatAdCode({code});
        }
    }

    // beforeRouteUpdate(to, from, next) {
    //     console.log(111,to);
    //     console.log(222,from);
    //     next(true)
    // }
};
</script>

<style lang="scss" scoped>


.collection-wrapper{
    font-family: Source Han Sans SC;
    width: 750px;
    background-color: #ffffff;
    // height: ;
}
.collection-banner{
    width: 750px;
    visibility: hidden;
    margin-bottom: -160px;
}

.collection-header{
    // margin-top: -5px;
    position: relative;
    width: 750px;
    height: 294px;
}

.header__avatars{
    position: absolute;
    top: -20px;
    left: 0px;
    height: 240px;
    width: 750px;
    overflow: hidden;
}

.header__online{
    position: absolute;
    top: 190px;
    left: 85px;
    width: 580px;
    height: 54px;
    line-height: 56px;
    background: #C5FC01;
    border: 2px solid #000000;
    border-radius: 27px;
    font-size: 28px;
    font-weight: 400;
    color: #000000;
    text-align: center;

    span{
        margin: 0 6px;
        font-weight: 700;
        color: #4437E9;
    }
}

</style>
