import {
    IS_SAPP,
    TEST_TYPE,
    channelId,
    subChannelId,
    FROM,
    oExt9,
} from "../js/const";

import { pageTypeChnMap } from '../config/register-dictionary';

// qms错误上报
export const reportError = function (msg, custom) {
    Z.tj.qms({
        dataType: 'error',
        data: {
            file: window.location.href,
            msg,
            custom,
        }
    });
};

// 指纹安全组的魔方(罗盘)上报
export const reportMagic = function () {
    Z.tj.reportMagic({
        data: {
            iid: 't_dc_00064',
            biz: '3014',
            data5: window.__B_DATA__,
        },
    });
};

// kibana上报
export const reportKibana = function (resourceKey, accessPoint, accessPointDesc, options= {}) {
    let params = {
        resourceKey: "新注册流程2.0",
        accessPoint: encodeURIComponent(accessPoint),
        accessPointDesc: accessPointDesc,
        ext30: pageTypeChnMap[resourceKey]
    };

    Object.assign(params, options);
    Z.tj.kibanaV2(params);
};

// 老注册页的kibana上报
export const tongji = (accessPoint, accessPointDesc, options)=>{
    var platform = 2;
    if (IS_SAPP) {
        platform = 19;
    }

    if (Object.prototype.toString.call({}).toLowerCase() !== '[object object]') {
        options = {};
    }

    var params = {
        resourceKey: '新注册流程2.0',
        accessPoint: encodeURIComponent(accessPoint),
        accessPointDesc: accessPointDesc,
        platform: platform,
        ext1: TEST_TYPE,
    };

    Object.assign(params, options);

    if (FROM === 'logintoregister' || FROM === 'covertoregister') {
        params.ext7 = FROM;
    }

    if (oExt9.get()) {
        params.ext9 = oExt9.get();
    }

    Z.tj.kibana({
        ...params
    });
};

// 恋爱人格测试 131 注册流程
export const reportLoveKibana = function (key, accessPoint, accessPointDesc, options) {
    let params = {
        resourceKey: "新注册流程2.0",
        accessPoint: encodeURIComponent(accessPoint),
        accessPointDesc: accessPointDesc,
        ext30: pageTypeChnMap[key]
    };
    Object.assign(params, options);
    Z.tj.kibanaV2(params);
};
// 手机前置 131 注册流程
export const reportLoveKibana1 = function (key, accessPoint, accessPointDesc, options) {
    let params = {
        resourceKey: key,
        accessPoint: encodeURIComponent(accessPoint),
        accessPointDesc: accessPointDesc,
        ext30: pageTypeChnMap[key]
    };
    Object.assign(params, options);
    Z.tj.kibanaV2(params);
};
