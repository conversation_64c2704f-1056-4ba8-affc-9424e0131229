<template>
    <div class="figure">
        <div
            class="figure-item"
            v-for="option in options"
            :key="option.key"
            @click="goNext(option)"
        >
            <div
                class="figure-item-img"
                :style="{
                    backgroundImage: `url(${option.url}`
                }"
            ></div>
            <div
                class="figure-item-text"
                :class="{ active: selectIndex === option.text }"
            >
                {{ option.text }}
            </div>
        </div>
    </div>
</template>
<script>
import { session as Session } from "@/common/utils/storage";
import { reportKibana } from "@/common/utils/report";
export default {
    name: "Figure",
    props: {
        type: {
            type: String
        },
        options: {
            type: Array,
            default: () => []
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            selectIndex: Session.getItem(this.type) || ""
        };
    },
    methods: {
        goNext(option) {
            this.selectIndex = option.text;
            Session.setItem(this.type, option.text);
            if (this.type === "Figure") {
                reportKibana(this.pageType, 4, "定制身材页-具体身材点击");
            } else {
                reportKibana(this.pageType, 5, "定制穿搭页-具体穿搭点击");
            }

            setTimeout(() => {
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss" scoped>
.figure {
    display: flex;
    flex-wrap: wrap;
    margin: 0 92px;
    &-item {
        width: 250px;
        text-align: center;
        &-img {
            height: 330px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            border: 2px solid #4a3bc0;
            border-radius: 24px 24px 0 0;
        }
        &-text {
            color: #4a3bc0;
            font-size: 36px;
            line-height: 76px;
            background: linear-gradient(118deg, #f2f9ff 2%, #d9d4ff 100%);
            border-radius: 0 0 24px 24px;
            border: 2px solid #4a3bc0;
            border-top: none;
            &.active {
                color: #fff;
                background: linear-gradient(154deg, #7566eb 0%, #4a3bc0 100%);
            }
        }
    }
    &-item:nth-of-type(2n-1) {
        margin-right: auto;
        margin-bottom: 60px;
    }
}
</style>
