<template>
    <van-popup
        class="download-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div class="title">
            {{ guideConfig.title }}
        </div>

        <div class="subtitle">
            {{ guideConfig.subtitle }}
        </div>

        <div
            class="submit"
            @click="goNext"
        >
            {{ guideConfig.submitText }}
        </div>

        <div
            class="cancel"
            @click="closeModal"
        >
            {{ guideConfig.cancelText }}
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { PAGE_TYPE } from "../../config";

export default {
    name: 'GuideModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        guideConfig: {
            type: Object,
            required: true
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                        this.$report(62, "测试报告页-app弹窗曝光");
                    } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                        this.$report(63, "测试报告页-引导小程序弹窗曝光");
                    }
                }
            },
            immediate: true,
        }
    },
    mounted(){
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            // this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        },
        goNext(){
            if(PAGE_TYPE === '单身自救指南(APP活跃)'){
                this.$emit('go-download');
            } else if(PAGE_TYPE === '单身自救指南(小程序活跃)'){
                this.$emit('go-mini');
            }
        },
        
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.download-modal {
    padding: 0 50px;
    width: 562px;
    height: 458px;
    background-color: transparent;
    @include flex-center(column, flex-start, center);
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221116/1668571300223_821670_t.png");

    .title {
        margin-top: 50px;
        font-weight: 500;
        font-size: 36px;
        color: #FFFFFF;
        text-align: center;
        line-height: 1.2;
    }

    .subtitle{
        margin-top: 26px;
        font-size: 28px;
        color: #FFFFFF;
        text-align: left;
        line-height: 36px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.50);
        opacity: 0.7;
    }

    .submit{
        margin-top: 64px;
        width: 462px;
        height: 88px;
        background-image: linear-gradient(104deg, rgba(153,244,255,0.87) 0%, rgba(22,171,251,0.67) 81%);
        box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
        border-radius: 44px;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        line-height: 88px;
    }

    .cancel{
        margin-top: 40px;
        opacity: 0.7;
        font-size: 28px;
        color: #FFFFFF;
        text-align: center;
        line-height: 36px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.50);
    }
}
</style>
