<template>
    <div class="lover">
        <router-view :key="$route.fullPath" v-if="canRender" />
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import Api from "@/common/server/base";
import { _getChannelConfig } from "./api";
import { getRandomInt } from "@/common/utils/tools.js";
import { session, storage } from "@/common/utils/storage.js";
import { judgeIfInToutiaoIos } from "@/common/business/utils/channel";
import { girlVoice, boyVoice } from "./voice.js";

export default createRoot({
    name: "App",
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    data() {
        return {
            cmsConfig: {
                planName: "定制恋人（导APP）",
                downloadStatus: 0,
                agreementStatus: 0,
                isNewUI: false
            },
            canRender: false,
        };
    },
    async created() {
        await this.getChannelConfig();
    },
    async mounted() {
        await this.handleInitCmsConfig();
        this.handleSplitPlan();
        judgeIfInToutiaoIos();
        const audios = [
            ...girlVoice.map(v => v.url),
            ...boyVoice.map(v => v.url),
        ];
        setTimeout(() => {
            const getAudios = (function() {
                const audios = [];
                return function(src) {
                    const audioEle = document.createElement("audio");
                    audios.push(audioEle);
                    audioEle.src = src;
                };
            })();
            audios.forEach(item => {
                getAudios(item);
            });
        });
    },
    methods: {
        async handleInitCmsConfig() {
            const id = Z.getParam("materialId");

            if (!id) {
                return;
            }

            const result = await Api.getHookMaterialInfo({ id });

            if (result.isError) {
                return;
            }

            const data = this.$z_.get(result, "data");
            this.cmsConfig = Object.assign(
                this.cmsConfig,
                this.$z_.pick(data, Object.keys(this.cmsConfig))
            );
        },

        handleSplitPlan() {
            // const planName = session.getItem("loverPlanName");
            // if (planName) {
            //     this.cmsConfig.planName = planName;
            //     window._zconfig.resourceKey = planName;
            //     this.canRender = true;
            //     return;
            // }

            // const tftype = Z.getParam("tftype");
            // if (tftype) {
            //     if (tftype === "0") {
            //         window._zconfig.resourceKey = "定制恋人（导APP）";
            //         storage.setItem("ext30", 42);
            //     } else if (tftype === "1") {
            //         window._zconfig.resourceKey = "定制恋人（导小程序）";
            //         storage.setItem("ext30", 43);
            //     } else if (tftype === "2") {
            //         window._zconfig.resourceKey = "订制理想恋人（新注册导APP）";
            //         storage.setItem("ext30", 78);
            //     }

            // } else {
            //     const num = getRandomInt(1, 9);
            //     if (num <= 3) {
            //         window._zconfig.resourceKey = "定制恋人（导APP）";
            //         storage.setItem("ext30", 42);
            //     } else if (num <= 6) {
            //         window._zconfig.resourceKey = "定制恋人（导小程序）";
            //         storage.setItem("ext30", 43);
            //     } else {
            //         window._zconfig.resourceKey = "订制理想恋人（新注册导APP）";
            //         storage.setItem("ext30", 78);
            //     }
            // }
            window._zconfig.resourceKey = "定制恋人（导APP）";
            storage.setItem("ext30", 42);

            this.cmsConfig.planName = window._zconfig.resourceKey;
            session.setItem("loverPlanName", window._zconfig.resourceKey);
            this.canRender = true;
        },
        async getChannelConfig() {
            const channelId = Z.getParam("channelId"), subChannelId = Z.getParam("subChannelId");
            const res = await _getChannelConfig({
                channel: channelId,
                subChannel: subChannelId
            });
            if(!res.isError) {
                const mediaGroupType = res.data.mediaGroupType;
                if(mediaGroupType.includes(10)) {
                    session.setItem("mediaGroupType", 10);
                }else if(mediaGroupType.includes(11)) {
                    session.setItem("mediaGroupType", 11);
                }else if(mediaGroupType.includes(12)) {
                    session.setItem("mediaGroupType", 12);
                }else {
                    session.setItem("mediaGroupType", false);
                }
            }

        }
    }
});
</script>

<style lang="scss">
.lover {
    min-height: 100vh;
}
</style>
