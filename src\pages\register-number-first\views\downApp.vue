<template>
    <div class="wrapper">
        <img src="https://photo.zastatic.com/images/common-cms/it/20240103/1704279476000_997315_t.jpg" alt="">
        <button class="down" @click="handleDownload">打开珍爱APP</button>
        <p class="url">
            <a :href="url">继续访问网页版</a>
        </p>
        <p class="phone">
            请保持手机畅通，后续会有工作人员与你联系呦~
        </p>
    </div>
</template>

<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../config";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { session as Session } from "@/common/utils/storage";
import { Toast } from "vant";
export default {
    name: "DownApp",

    data() {
        return {
            url: 'https://i.zhenai.com/m/wap/index/index.html' + location.search
        };
    },
    mounted () {

    },
    methods: {
        handleDownload() {
            this.$report(44, "报告页-点击下载APP按钮");
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({ value: true });
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    text-align: center;
   img {
       width: 100%;
   }
    .down {
        width: 80%;
        background: #00c7b1;
        height: 100px;
        line-height: 100px;
        margin-top: 40px;
        border-radius: 10px;
        color: #fff;
        text-decoration: none;
        font-size: 40px;
    }
    .url {
        text-decoration: underline;
        color: #aaa;
        a {
            color: #aaa;
            font-size: 32px;
        }
    }
    .phone {
        margin-top: 30px;
        color: #A5AFB1;
        font-size: 28px;
    }
}
</style>
