<template>
    <div class="birthday">
        <div
            class="birthday-scroll"
            ref="birthScroll"
        >
            <div
                class="birthday-info"
                v-for="(option, name) in heightV2"
                :key="name"
            >
                <h4 class="birthday-info-title">
                    {{ name }}cm
                </h4>
                <div
                    class="birthday-info-card"
                >
                    <span
                        class="bic-num"
                        :class="{ active: item === curHeight }"
                        v-for="item in option"
                        :key="item"
                        @click="goNext(item)"
                    >
                        <img
                            src="https://photo.zastatic.com/images/common-cms/it/20240521/1716282359883_359550.png"
                            alt=""
                            class="active_img"
                        >
                        {{ item }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    setLocalRegisterForm,
} from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { heightV2 } from "@/common/config/register-dictionary";
import { PAGE_TYPE, changeProfile } from "../../config";
export default {
    name: "Height",
    data() {
        const form = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`);
        return {
            heightV2,
            curHeight: form ? form.height : '',
        };
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;
            this.curHeight = val;
            const params = {
                key: "height",
                value: val,
                isMark: false,
            };
            setLocalRegisterForm(params, PAGE_TYPE);
            // changeProfile({...params, token: oCookie.get('token')})
            this.$report(29, "身高页-按钮点击", {
                ext3: val
            });
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);

        }
    },
    mounted() {

        this.$refs.birthScroll.scrollTo(0, 900);
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    margin-bottom: 40px;
    position: relative;

    .banner {
        width: 690px;
        height: 120px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);

    }

    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }

    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }

    .birthday-scroll {
      overflow-y: scroll;
      margin-top: 160px;
      height: calc(100vh - 364px);
    }

    &-info {
        display: flex;
        &-title {
            text-wrap: nowrap;
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            margin-left: 26px;
            font-weight: 600;
            font-size: 40px;
            color: #222833;

            &::after {
                content: " ";
                margin-top: 5px;
                height: 8px;
                width: 100%;
                background: #D7204A;
                border-radius: 6px;
            }
        }

        &-card {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding: 8px 6px;
            margin: 0 0px 40px 21px;
            border-radius: 60px;

            .bic-num {
                position: relative;
                border-radius: 45px;
                flex: 0 0 auto;
                display: block;
                color: #26273C;
                font-size: 28px;
                line-height: 62px;
                width: 132px;
                height: 60px;
                border-radius: 50px;
                text-align: center;
                margin-right: 12px;
                margin-bottom: 24px;
                border: 1px solid #B8BCCC;
            }

            .bic-num:nth-of-type(4n) {

                margin-right: 0;
            }

            .active_img {
                position: absolute;
                right: 29px;
                top: 0;
                transform: translateY(-50%);
                width: 28px;
                height: 28px;
                display: none;
            }

            .active {
                // background-image: linear-gradient(90deg, #D7204A 0%, #f06787 100%);
                background: #f06787;

                .active_img {
                }
            }
        }
    }
}
</style>
