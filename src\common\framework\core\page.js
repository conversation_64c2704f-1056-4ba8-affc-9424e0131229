import isPlainObject from 'lodash-es/isPlainObject';
import omit from "lodash-es/omit";

export default function createPage(options) {
    const extendsProps = [ 'visitReport' ];
    const mixins = options.mixins || [];

    mixins.push({
        created() {
            if (isPlainObject(options.visitReport)) {
                const { accessPoint, accessPointDesc, options: reportOptions } = options.visitReport;
                this.$report(accessPoint, accessPointDesc, reportOptions);
            }
        },
    });

    options.mixins = mixins;

    return omit(options, extendsProps);
}
