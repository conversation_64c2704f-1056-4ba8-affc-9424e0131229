<template>
    <div
        class="mobi-test-form"
        :class="{ spe: isSpe }"
    >
        <template v-if="isSpe">
            <Caption content="请完善个人信息，查看报告" />
            <div class="tips">
                <span>根据报告结果：</span>已为你匹配到<strong></strong>几位异性好友
            </div>
        </template>
        <template v-else>
            <div class="title"></div>
            <div class="result"></div>
        </template>
        <div class="collection-container">
            <div
                class="collection-container-nail"
                v-if="isSpe"
            >
                完善资料即可查看报告，并与这几位好友联系
            </div>
            <!-- 注册项 -->
            <div
                v-for="(item, index) in formItems"
                :key="index"
                class="collection__item"
                @click="openSelect(item)"
            >
                <div class="collection__item-label">
                    {{ item.label }}
                </div>

                <!-- 性别 -->
                <div
                    v-if="item.key === 'gender'"
                    class="collection__item-select-gender"
                >
                    <div
                        v-for="(_item, _index) in item.options"
                        class="collection__item-gender-item"
                        :key="_index"
                        @click="handleSetGender(_item)"
                        :class="
                            _item.key === registerForm.gender
                                ? 'collection__item-gender-item--selected'
                                : ''
                        "
                    >
                        {{ _item.text }}
                    </div>
                </div>

                <!-- 工作地、出生年份、学历、婚况、月收入 -->
                <div
                    v-else
                    class="collection__item-select"
                >
                    <span v-if="item.value">{{ item.value }}</span>
                    <span
                        v-else
                        class="collection__item-todo"
                    >{{
                        "待完善"
                    }}</span>
                </div>
            </div>

            <!-- 手机号 -->
            <div class="collection__input">
                <div class="collection__input-label">
                    您的手机号
                </div>
                <input
                    class="collection__input-input"
                    placeholder="请输入11位手机号"
                    :value="registerForm.phone"
                    @input="inputPhone"
                    maxlength="13"
                    type="tel"
                />
                <div
                    class="collection__input-clear"
                    @click="clearPhone"
                />
            </div>
        </div>

        <template v-if="isSpe">
            <div
                class="collection-btn"
                :class="{ disabled: !finished }"
                @click="submit"
            >
                立即查看报告
            </div>
        </template>
        <template v-else>
            <simple-button
                class="mobi-test-form__button"
                :width="590"
                :height="116"
                :disable="!finished"
                @click="submit"
            >
                立即查看报告
            </simple-button>
        </template>

        <!-- 虎扑合规要求 -->
        <div
            v-if="isHupu"
            class="mobi-test-form__app-info"
        >
            开发者：深圳市珍爱网信息技术有限公司
            <br />
            应用名称：珍爱 | 应用版本：7.30.0
        </div>

        <!-- 底部选择框 -->
        <form-selector
            :visible.sync="selectorVisible"
            :select-type="selectType"
            :next-cb="openSelect"
            :select-param="selectParam"
        />

        <register-overwrite-modal
            v-model="modalVisible.registerOverwriteModal"
            :type="registerOverwriteModalType"
            @select-overwrite="overwriteAccount"
            @select-origin="loginOriginAccount"
        />
        <msg-code-modal
            v-model="modalVisible.msgCodeModal"
            :validate-code="validateCode"
        />
    </div>
</template>

<script>
import { createPage } from "@/common/framework";
import RegisterOverwriteModal from "../components/modal/RegisterOverwriteModal";
import MsgCodeModal from "../components/modal/MsgCodeModal";
import FormSelector from "../components/FormSelector";
import Caption from "../components/Caption.vue";
import { mapMutations, mapState } from "vuex";
import { reportError } from "@/common/utils/report";
import Api from "@/common/server/base";
import { registerResult } from "../enum";
import { formatPhone } from "../lib/utils";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { getWxCode } from '@/common/business/utils/wecom';
import { storage } from "../lib/utils.js";

export default createPage({
    name: "Form",
    components: {
        FormSelector,
        RegisterOverwriteModal,
        MsgCodeModal,
        Caption
    },
    data() {
        return {
            registerOverwriteModalType: "",
            modalVisible: {
                registerOverwriteModal: false,
                msgCodeModal: false
            },
            selectorVisible: false,
            selectType: "",
            selectParam: {},
            isCheckProtocol: false,
            validateAccountResult: {
                memberInfoVo: {},
                oldMemberID: null,
                overwriteRegistrationSwitch: "",
                type: null
            },
            lockSubmit: false,
            lockOverwrite: false,
            messageCode: "",
            single: Z.getParam("single") || storage.getItem("single"),
        };
    },

    mounted() {
        this.$reportKibana(this.resourceKey, 90, "大表单注册页访问");
    },

    computed: {
        ...mapState([
            "resourceKey",
            "registerForm",
            "formItems",
            "cmsConfig",
            "regMemberId",
            "wxInfo"
        ]),
        isHupu() {
            return ["924829", "924830"].includes(Z.getParam("channelId"));
        },
        allFillIn() {
            return this.$z_.every(this.registerForm, value => {
                return !this.$z_.isNil(value) && value !== "";
            });
        },
        validatedPhone() {
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                this.registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },
        finished() {
            return this.allFillIn && this.validatedPhone;
        },
        pageType() {
            return pageTypeChnMap[this.resourceKey];
        },
        isShare() {
            return Z.getParam("from") === "share";
        },
        isSpe() {
            return this.cmsConfig.reportViewType === 4;
        }
    },

    methods: {
        ...mapMutations([
            "setRequirement",
            "setFormItems",
            "setRegisterForm",
            "setRegMemberId"
        ]),
        formatPhone,

        inputPhone(e) {
            e.target.value = e.target.value.replace(/[^(\d|\s)]/g, "");

            this.setRegisterForm({
                key: "phone",
                value: this.formatPhone(e),
                isMark: false
            });
        },
        clearPhone() {
            this.setRegisterForm({
                key: "phone",
                value: "",
                isMark: false
            });
        },
        async submit(isReport = true) {
            if (isReport) {
                this.$reportKibana(
                    this.resourceKey,
                    91,
                    "大表单注册页-提交按钮点击"
                );
            }

            if (this.lockSubmit) {
                return;
            }

            if (!this.allFillIn) {
                this.$toast("请完善资料再提交");
                return;
            }

            if (!this.validatedPhone) {
                return this.$toast("请输入正确的手机号");
            }

            if (isReport) {
                this.$reportKibana(
                    this.resourceKey,
                    92,
                    "大表单注册页-提交按钮点击（有效点击）"
                );
            }

            console.log(this.cmsConfig.reportViewType);
            const sendData = {
                // C方案的话提交表单的身高，AB方案传-1兼容
                ...this.registerForm,
                height:
                    this.cmsConfig.reportViewType === 3
                        ? this.registerForm.height
                        : "-1"
            };
            delete sendData.phone;

            this.lockSubmit = true;

            try {
                const baseInfoPostResult = await Api.submitWapRegBaseInfo(
                    sendData
                );

                if (baseInfoPostResult.isError) {
                    this.$toast(baseInfoPostResult.errorMessage);
                    // 老注册页qms上报逻辑
                    reportError(
                        "新注册页(大表单H5),注册信息提交失败 |" +
                            baseInfoPostResult.errorMessage +
                            " | " +
                            JSON.stringify(this.registerForm)
                    );
                    return;
                }

                this.modalVisible.msgCodeModal = true;
            } finally {
                this.lockSubmit = false;
            }
        },

        handleSetGender(item) {
            // 设置性别
            this.setRegisterForm({
                key: "gender",
                value: item.key
            });

            this.setFormItems({
                gender: item.text
            });
        },

        openSelect(currentItem) {
            if (currentItem.key === "gender") {
                return;
            }

            if (["workCity", "birthday", "height"].includes(currentItem.key)) {
                this.selectType = "selectSlide";
            } else if (
                ["marriage", "education", "salary"].includes(currentItem.key)
            ) {
                this.selectType = "selectBoard";
            }

            this.selectParam = currentItem;
            this.selectorVisible = true;
        },

        async overwriteAccount() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = this.$gather.getOverwriteAccountParams(
                this.messageCode,
                this.pageType
            );
            const result = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }

            if (result.data.memberID) {
                this.setRegMemberId(result.data.memberID);
            }

            this.closeRegisterOverwriteModal();
            this.finishedRegister(
                registerResult.MANUAL_OVERWRITE_ACCOUNT.label
            );
        },
        loginOriginAccount() {
            this.$router.push("/result");
        },
        async validateCode(messageCode) {
            this.$gather.setAbtZaTTTCookie();
            let sendData = this.$gather.getValidateCodeParams(
                this.registerForm.phone,
                messageCode,
                this.pageType
            );
            // 由于用了3-4-4结构 所以还要把空格换掉
            sendData = {
                ...sendData,
                phone: sendData.phone.replace(/[^(\d)]/g, "")
            };
            const result = await Api.submitWapRegNoPasswordInfoV2(sendData);

            if (!result.isError) {
                if (result.data.memberID) {
                    this.setRegMemberId(result.data.memberID);
                }

                setTimeout(() => {
                    this.messageCode = messageCode;
                    this.checkOverrideAccount(result.data);
                }, 0);
            }

            return result;
        },
        checkOverrideAccount(validateAccountResult) {
            const { type } = validateAccountResult;

            this.validateAccountResult = validateAccountResult;

            switch (type) {
            case registerResult.LOGIN_ACCOUNT.value:
                this.registerOverwriteModalType =
                    registerResult.LOGIN_ACCOUNT.value;
                this.showRegisterOverwriteModal();
                break;
            case registerResult.MANUAL_OVERWRITE_ACCOUNT.value:
                this.registerOverwriteModalType =
                    registerResult.MANUAL_OVERWRITE_ACCOUNT.value;
                this.showRegisterOverwriteModal();
                break;
            case registerResult.NEW_ACCOUNT.value:
                this.finishedRegister(registerResult.NEW_ACCOUNT.label);
                break;
            case registerResult.AUTO_OVERWRITE_ACCOUNT.value:
                this.finishedRegister(
                    registerResult.AUTO_OVERWRITE_ACCOUNT.label
                );
                break;
            }

            const resultType = this.$z_.find(registerResult, {
                value: type
            });

            if (this.$z_.get(resultType, "label")) {
                this.$reportKibana(this.resourceKey, 50, "手机号验证成功", {
                    ext17: resultType.label,
                    ext20: this.isShare ? "分享链接" : "原链接"
                });
            }
        },
        showRegisterOverwriteModal() {
            this.modalVisible.registerOverwriteModal = true;
        },
        closeRegisterOverwriteModal() {
            this.modalVisible.registerOverwriteModal = false;
        },

        async handlegudgeonMark() {
            const sendData = {
                memberId: this.regMemberId || null,
                memberTaskType: 128
            };
            const result = await Api.gudgeonMark(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        },

        finishedRegister(ext) {
            this.$reportKibana(this.resourceKey, 51, "注册成功并生成ID", {
                ext17: ext,
                ext20: this.isShare ? "分享链接" : "原链接"
            });
            // h5注册标记 （D方案不用调接口）
            if (this.cmsConfig.reportViewType !== 4) {
                this.handlegudgeonMark();
            }

            this.$toast("注册成功");
            this.$storage.setItem("registerFinished", true);

            // MBTI(D方案+企微)优化：请求完二维码接口后再跳转结果页
            if(this.resourceKey === 'MBTI钩子D方案(企微)' && this.single === 1){
                return this.handleQrCode();
            }

            this.$router.push({
                path: "/result"
            });

        },

        async handleQrCode(){
            const res = await getWxCode(pageTypeChnMap[this.resourceKey]);
            this.wxInfo.code = res.qrCode;
            this.wxInfo.workerId = res.workerId;

            const image = new Image();
            image.onload = () => {
                this.$router.push({
                    path: "/result"
                });
            };
            image.onerror = () => {
                this.$router.push({
                    path: "/result"
                });
            };
            image.src =  res.qrCode;
        }

    }
});
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.mobi-test-form {
    position: relative;
    z-index: 1;
    .title {
        width: 686px;
        height: 54px;
        margin: 32px auto 64px;
        background: url(../assets/images/form-title.png) no-repeat;
        background-size: 100% 100%;
    }

    .result {
        width: 686px;
        height: 100px;
        margin: 0px auto 40px;
        background: url(../assets/images/form-bg1.png) no-repeat;
        background-size: 100% 100%;
    }

    .collection {
        &-btn {
            width: 620px;
            height: 120px;
            margin: 0 auto 70px;
            color: #fff;
            font-size: 36px;
            line-height: 120px;
            text-align: center;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656932169404_974831_t.png)
                no-repeat;
            background-size: 100% 100%;
            &.disabled {
                background: url(https://photo.zastatic.com/images/common-cms/it/20220701/1656659067946_511972_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
        }
        &-container {
            width: 686px;
            @include relative-center();
            font-size: 28px;
            background: url(../assets/images/form-bg2.png) no-repeat;
            background-size: 100% 100%;
            padding: 76px 32px 0px;
            margin-bottom: 48px;
            &-nail {
                position: absolute;
                top: 20px;
                left: 116px;
                color: #fff;
                font-size: 26px;
            }
        }

        &__item {
            @include flex-center(row, space-between, center);
            height: 100px;

            &-todo {
                color: #00ffff;
            }
        }

        &__item-label {
            width: 240px;
            color: #ffffff;
            font-size: 32px;
            font-weight: 400;
        }

        &__item-select-gender {
            @include flex-center(row, space-between, center);
            width: 304px;
            height: 100px;
            color: #ffffff;
        }

        &__item-gender-item {
            @include flex-center(row, center, center);
            width: 138px;
            height: 60px;
            border: 2px solid #00ffff;
            border-radius: 30px;
            font-size: 32px;
            font-weight: 400;
            color: #00ffff;
            line-height: 60px;
            text-align: center;
        }

        &__item-gender-item--selected {
            border: none;
            background: #00ffff;
            color: #222833;
        }

        &__item-select {
            color: #ffffff;
            font-size: 32px;
            position: relative;
            padding-right: 48px;
        }

        &__item-select::after {
            content: "";
            position: absolute;
            top: -11%;
            right: 0;
            width: 32px;
            height: 32px;
            background: url("../assets/images/icon-arrow-right.png") center
                center no-repeat;
            background-size: contain;
        }

        &__input {
            position: relative;
        }

        &__input-label {
            font-size: 32px;
            height: 100px;
            line-height: 100px;
            font-weight: 400;
            color: #ffffff;
        }

        &__input-input {
            text-align: center;
            position: relative;
            height: 88px;
            width: 622px;
            margin-bottom: 40px;
            border-radius: 60px;
            color: #ffffff;
            background: rgba(0, 0, 0, 0.2);
            border: 2px #00ffff solid;
        }

        &__input-clear {
            position: absolute;
            bottom: 64px;
            right: 48px;
            width: 32px;
            height: 32px;
            background: url("../assets/images/icon-x.png") center center
                no-repeat;
            background-size: 100% 100%;
        }
    }

    &__app-info {
        opacity: 0.6;
        font-size: 22px;
        line-height: 40px;
        padding: 40px 39px 0;
        text-align: center;
    }

    &__button {
        margin-bottom: 88px;
    }
    .tips {
        margin: 0 32px 64px;
        color: #ffffff;
        font-size: 34px;
        > span {
            color: #c9c8ce;
            font-size: 28px;
        }
        > strong {
            color: #5ad9ff;
            font-size: 46px;
            font-weight: 600;
        }
    }
    &.spe {
        .collection-container {
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579479727_582408_t.png)
                    no-repeat,
                linear-gradient(
                    0deg,
                    rgba(119, 148, 255, 0.55) 0%,
                    rgba(83, 110, 255, 0) 100%
                );
            background-size: 686px 64px, 100% 100%;
            border-radius: 32px;
        }
        .collection__item-todo {
            color: #5ad9ff;
        }
        .collection__item-gender-item {
            border: 2px solid #5ad9ff;
            color: #5ad9ff;
        }
        .collection__item-gender-item--selected {
            background: #5ad9ff;
            color: #222833;
        }
        .collection__input-input {
            border: 2px #5ad9ff solid;
        }
    }
}
</style>
