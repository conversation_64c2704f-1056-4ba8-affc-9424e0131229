<template>
    <van-popup
        class="rate-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <div
            class="close-btn"
            @click="closeModal"
        ></div>
        <div class="title">
            恋爱成功率是如何计算的？
        </div>

        <div class="description">
            恋爱成功率根据你的基本资料情况与当前在库异性的数量、恋爱意愿、基本资料、择偶条件等多因子计算得出。
        </div>

        <div class="table">
            <div class="table-row">
                <div>恋爱成功率等级</div>
                <div class="font-big">说明</div>
            </div>
            <div class="table-row">
                <div>很高(😊你在这里)</div>
                <div>资料与大量的异性匹配，在短时间内你可以成功恋爱</div>
            </div>
            <div class="table-row">
                <div>高</div>
                <div>资料与部分异性匹配，通过努力可能脱单</div>
            </div>
            <div class="table-row">
                <div>中</div>
                <div>匹配的异性很少，近期不太容易脱单</div>
            </div>
            <div class="table-row">
                <div>低</div>
                <div>个人基本情况太差，较难成功恋爱</div>
            </div>
        </div>

        <div class="tips">
            你的幸福掌握在自己手里，成功恋爱率越高，越应该主动把握机会。
        </div>

        <div class="bottom">
            <common-button
                :config="{ width: 462, height: 88, fontSize: 32, des: '好的' }"
                @click="closeModal"
            />
        </div>
    </van-popup>
</template>

<script>
import { Popup } from "vant";
import CommonButton from "./CommonButton";

export default {
    name: 'RateModal',
    components: {
        VanPopup: Popup,
        CommonButton,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(63, "测试报告页-引导小程序弹窗曝光");
                }
            },
            immediate: true,
        }
    },
    mounted(){
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            // this.$report(401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        },
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.rate-modal {
    padding: 48px 40px;
    width: 558px;
    background-color: transparent;
    @include flex-center(column, flex-start, center);
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20220530/1653899214724_758097_t.png");
    background-color: #fff;
    background-position: 0 0;
    border-radius: 32px;
    overflow: initial;

    .title {
        font-weight: 500;
        font-size: 36px;
        color: #26273C;
        line-height: 50px;
    }

    .description {
        margin-top: 16px;
        font-size: 28px;
        color: #26273C;
        line-height: 40px;
    }

    .tips {
        margin-top: 46px;
        font-size: 28px;
        color: #6C6D75;
        line-height: 40px;
    }

    .table {
        margin-top: 40px;
        .table-row {
            position: relative;
            display: flex;
            align-items: center;
            font-size: 26px;
            line-height: 36px;
            color: #6C6D75;
            &:nth-of-type(2n-1) {
                background: rgba(174, 177, 182, .4);
            }
            &:first-of-type {
                height: 56px;
                color: #fff;
                background: #CAC9FF;
                text-align: center;
                & > div:last-child {
                    text-align: center;
                    font-size: 26px;
                }
            }
            & > div {
                padding: 16px 0 16px 4px;
                &:first-child {
                    flex: 0 0 auto;
                    width: 232px;
                    text-align: center;
                    border-right: 2px solid #fff;
                }
                &:last-child {
                    flex: 1;
                    text-align: left;
                    font-size: 20px;
                    line-height: 24px;
                }
            }
        }
    }

    .bottom {
        margin-top: 40px;
        /deep/ .button-wrap {
            background: #767DFF;
        }
    }

    .close-btn {
        width: 48px;
        height: 48px;
        background-image: url('https://photo.zastatic.com/images/common-cms/it/20221019/1666168277613_235412_t.png');
        background-size: 48px 48px;
        background-position: center center;
        background-repeat: no-repeat;
        position: absolute;
        right: -32px;
        top: -60px;
    }
}
</style>
