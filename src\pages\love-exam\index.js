import { createApp } from '@/common/framework';
import { PAGE_TYPE } from "./config";
import App from './App.vue';
import router from './router';
import { reportLoveKibana } from "@/common/utils/report";

Vue.prototype.$report = (accessPoint, accessPointDesc, options) => {
    const key = '恋爱段位考试';
    return reportLoveKibana(key, accessPoint, accessPointDesc, options);
};
createApp({
    router,
    render: h => h(App),
    resourceKey: PAGE_TYPE
});
