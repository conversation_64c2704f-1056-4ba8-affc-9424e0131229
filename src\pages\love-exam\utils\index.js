import { findWorkCity } from "@/common/business/utils/localRegisterForm";

export function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    })
}
export function getTypeImg(data) {
    let form = {
        '完美' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823798172_381409_t.png',
        '助人' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691824131244_722449_t.png',
        '成就' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823819430_362168_t.png',
        '自我' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823832540_757444_t.png',
        '理智' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823854195_32637_t.png',
        '疑惑' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823867929_180069_t.png',
        '活跃' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823880589_400713_t.png',
        '领袖' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823894740_197053_t.png',
        '平和' : 'https://photo.zastatic.com/images/common-cms/it/20230812/1691823911940_783667_t.png',
    };
    return form[data];

}
// 表单项Key到value映射
export function keyToValue(type, key){
    let value = '';
    switch(type){
    case 'gender':
    case 'education':
    case 'marriage':
    case 'salary':
        value = dict[type].find(item => {
            return item.key === key;
        }).text;
        break;
    case 'workCity':
        value = findWorkCity(key).join('/');
        break;
    case 'birthday':
        value = new Date(key).getFullYear();
        break;
    case 'phone':
        value = key.replace(/(\d{3})(\d{4})/, '$1 $2 ');
        break;
    }
    return value;
}