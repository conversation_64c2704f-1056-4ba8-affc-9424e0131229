<template>
    <div class="collection-wrapper" :style="{'background-image': `url(${bg[type]})`}">
        <div class="wrapper-item" v-for="(item, index) in list[type]" :key="index">
            <img class="banner-img" :src="item.url" alt="">
            <img class="button-img" @click="applyJoin" :src="item.button" alt="">
        </div>
        <div class="footer-button">
            <img @click="handleDownload"  v-if="showButton" :src="downImg[type]" alt="">
        </div>
        <div class="dialog-content" v-show="dialogStatus">
            <div class="dialog-img">
                <img class="img-banner" :src="dialogImg[type]" alt="">
                <img class="close-icon" @click="closeDialog" src="https://photo.zastatic.com/images/common-cms/it/20240318/1710749322740_476269_t.png" alt="">
                <img class="down-img" v-if="showButton" @click="handleDownload" :src="buttonImg[type]" alt="">
            </div>

        </div>
    </div>
</template>

<script>

import {reportKibana} from '@/common/utils/report.js';
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { session as Session } from "@/common/utils/storage";
import { Toast } from "vant";
import Api from "@/common/server/base";
import { channelId, subChannelId } from '@/common/js/const';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import { mapState } from "vuex";
export default {
    data() {
        return {
            type: 0,
            dialogStatus: false,
            list: {
                0: [
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710734208431_261052_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710735557706_910675_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754647094_710017_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710735557706_910675_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754673419_324886_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710735557706_910675_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754686977_309992_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710735557706_910675_t.png'
                    }
                ],
                1: [
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754933164_990259_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755020995_986649_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754939817_262452_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755020995_986649_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754947963_396886_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755020995_986649_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710754956706_607238_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755020995_986649_t.png'
                    }
                ],
                2: [
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755532111_662502_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755621216_566280_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755539972_112998_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755621216_566280_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755546239_413170_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755621216_566280_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755552534_683604_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755621216_566280_t.png'
                    }
                ],
                3: [
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755731946_35849_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755771566_746394_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755738305_827888_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755771566_746394_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755745586_269404_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755771566_746394_t.png'
                    },
                    {
                        url: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755752052_329539_t.png',
                        button: 'https://photo.zastatic.com/images/common-cms/it/20240318/1710755771566_746394_t.png'
                    }
                ]
            },
            bg: [
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710743566828_928284_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755109734_60799_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755451377_270200_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755716282_949679_t.png',
            ],
            downImg: [
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710746189395_147244_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755249660_18453_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755342032_870449_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710755362627_550295_t.png',
            ],
            dialogImg: [
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710754054824_675117_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710753326604_900432_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710754054824_675117_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710753326604_900432_t.png',
            ],
            buttonImg: [
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710754431425_138980_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710752455120_341414_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710754431425_138980_t.png',
                'https://photo.zastatic.com/images/common-cms/it/20240318/1710752455120_341414_t.png',
            ],
            showButton: true,
        };
    },
    computed: {
        ...mapState([
            "registerInfo",
            "cmsConfig",
        ]),
    },
    async created(){
        this.type = this.$route.query.type
        this.showButton = this.cmsConfig.downloadApp === 0;
    },
    mounted(){
        // 打桩
        reportKibana("线下大表单", 111, '跳转小程序uv', {});
    },
    methods:{
        applyJoin() {
            this.dialogStatus = true
            reportKibana("线下大表单", 3008, '申请加入', {});
        },
        closeDialog() {
            this.dialogStatus = false
        },
        handleDownload() {
            reportKibana("线下大表单", 111, '结果页弹窗-点击下载APP按钮', {});
            if (this.cmsConfig.jumpULoveCupid == '1') {
                this.$router.push({
                    path: "/successResult"
                });
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({ value: true });
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }

        },
    }
};
</script>

<style lang="scss" scoped>

@keyframes scaleDraw {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
.collection-wrapper{
    width: 750px;
    padding: 30px 20px 160px 20px;
    position: relative;
    .wrapper-item {
        position: relative;

        .banner-img {
            width: 100%;
            height: auto;
        }
        .button-img {
            position: absolute;
            top: 160px;
            right: 210px;
            width: 214px;
            height: 62px;
        }
    }
    .footer-button {
        position: fixed;
        bottom: 40px;
        width: 100%;
        text-align: center;
        img {
            width: 460px;
            height: 120px;
        }
    }
    .dialog-content {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        .dialog-img {
            text-align: center;
            position: absolute;
            top: 130px;
            left: 53px;
            .img-banner {
                width: 644px;
                height: auto;
            }
            .close-icon {
                position: absolute;
                width: 40px;
                height: 40px;
                top: 220px;
                right: 40px;
            }
            .down-img {
                width: 440px;
                height: 100px;
                position: absolute;
                bottom: 100px;
                left: 100px;
            }

        }
    }

}

</style>
