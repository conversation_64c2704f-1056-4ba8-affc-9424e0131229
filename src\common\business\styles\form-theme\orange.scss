@import "~@/common/styles/common.scss";
.orange{
    .form-item {
        &--normal {
            margin-top: 50px;
            font-size: 29px;
            font-weight: 400;
            color: #6C6D75;
            
            &__label {
                display: inline-block;
                position: relative;
                &::after {
                    content: '*';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translate(15px,-50%);
                    font-weight: 400;
                    font-size: 29px;
                    color: #FF0000;
                }
            }

            &__value {
                margin-top: 16px;
                position: relative;
                padding-bottom: 10px;
                font-size: 28px;
                font-weight: 400;
                line-height: 40px;
                border-bottom: 2px solid #D8D8D8;

                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 0;
                    transform: translateY(-50%);
                    width: 18px;
                    height: 31px;
                    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221010/1665374348378_631463_t.png");
                    // https://photo.zastatic.com/images/common-cms/it/20221010/1665374059026_246208_t.png
                }
            }

            .filled{
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 0;
                    transform: translateY(-50%);
                    width: 30px;
                    height: 30px;
                    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221010/1665374059026_246208_t.png");
                }
            }

        }

        &--gender {
            &__value {
                margin-top: 16px;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                height: 54px;
                &__item {
                    margin-right: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 148px;
                    height: 54px;
                    border: 1px solid #AEAFB3 !important;
                    border-radius: 27px;
                    font-size: 29px;
                    font-weight: 400;
                    line-height: 60px;
                    text-align: center;
                    &--selected {
                        border: none;
                        color: #ffffff;
                    }
                }
            }
        }

        &--phone {
            margin-top: 16px;
            position: relative;
            font-size: 28px;
            font-weight: 400;
            color: #26273c;
            &__label {
                height: 100px;
                line-height: 100px;
            }
            &__value {
                margin: 0 auto;
                height: 100px;
                background-color: rgba($color: #26273c, $alpha: 0.05);
                border-radius: 44px;
                &__input {
                    width: 654px;
                    height: 100px;
                    background-color: transparent;
                    font-size: 30px;
                    color: #26273c;
                    line-height: 100px;
                    text-align: center;
                }
            }
            &__clear {
                position: absolute;
                top: 130px;
                right: 24px;
                width: 32px;
                height: 32px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20220517/1652782303916_599182_t.png");
            }
        }
    }
}