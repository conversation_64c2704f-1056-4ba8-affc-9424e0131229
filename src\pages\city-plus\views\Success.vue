<template>
    <div class="success">
        <Back @on-back="goBack" />
        <div class="success-card">
            <h4>恭喜！已成功匹配优质同城异性</h4>
            <Phone
                class="success-phone"
            />
            <common-submit
                :page-type="cmsConfig.planName"
                :is-need-protocol="false"
                :style-config="{
                    modalConfig: {
                        confirmButtonColor: '#FFFFFF',
                        confirmButtonBgColor: '#5368F0',
                        cancleButtonColor: '#000000',
                    }
                }"
                :handle-after-regisiter="handleJump"
                :handle-login="handleDownload"
                :handle-cancle="handleJump"
            >
                <div
                    class="success-submit"
                    @click="handleReport"
                >
                    立即查看同城异性
                </div>
            </common-submit>
        </div>
    </div>
</template>
<script>
import Back from "../components/Back.vue";
import CommonSubmit from '@/common/business/CommonSubmit';
import Phone from "../components/RegForm/phone.vue";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

import { Toast } from "vant";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { session as Session } from "@/common/utils/storage";


export default {
    name: "Success",
    components: {
        Back,
        Phone,
        CommonSubmit,
    },
    data() {
        return {
            isShow: true
        };
    },
    inject: ['cmsConfig'],
    methods: {
        goBack() {
            this.$report(11, '手机验证页-返回按钮点击');
            this.$router.push({ path: "/anim" });
        },

        handleJump() {
            this.$router.push({ path: '/rcommd'});
        },

        handleDownload() {
            Toast({
                message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                duration: 5000
            });
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({value: true});
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },

        handleReport() {
            this.$report(11, '手机验证页-立即验证按钮点击');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.success {
    min-height: 100vh;
    background-image: linear-gradient(-30deg, #b7bfff 46%, #fafbff 86%);
    &-card {
        height: 744px;
        margin: 0 38px 0;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659689913022_641637_t.png) no-repeat;
        background-size: 100% 100%;
        >h4 {
            padding-top: 100px;
            padding-left: 32px;
            color: #26273C;
            font-size: 32px;
            font-weight: 400;
        }
    }
    &-phone {
        margin-top: 292px;
        margin-left: 32px;
    }

    &-submit {
        @include flex-center(row, center, center);
        margin-top: 40px;
        width: 612px;
        height: 110px;
        border-radius: 55px;
        background: #5368F0;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
    }
}
</style>
