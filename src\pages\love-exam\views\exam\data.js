export const subjectList = [
    {problem: '你觉得小A的哪个特点，最让你有想要和ta在一起的冲动？', options: [
        {label:'小A和你兴趣一致，能聊也能玩到一起', labelName: 'A'},
        {label:'你难过时，可以毫无顾忌地和小A倾诉感受', labelName: 'B'},
        {label:'小A对你每条动态都点赞或评论', labelName: 'C'},
        {label:'你和小A认识很多年，是很了解彼此的老友', labelName: 'D'}
    ]},
    {problem: '这是你第一次发消息给暗恋的小A，你决定用一个表情包作为开场，你的选择是:', options: [
        {label:'表情包A', labelName: 'A'},
        {label:'表情包B', labelName: 'B'},
        {label:'表情包C', labelName: 'C'},
        {label:'表情包D', labelName: 'D'}
    ], imgUrl: 'https://photo.zastatic.com/images/common-cms/it/20240801/1722479028924_146619.png'},

    {problem: '聊到哪个话题时，最能展现出小A的责任感与真诚态度，不会给人留下不良伴侣的印象?', options: [
        {label:'感情经历带来的伤', labelName: 'A'},
        {label:'前任的各种优点', labelName: 'B'},
        {label:'评价共同认识的异性', labelName: 'C'},
        {label:'未来结婚生娃的计划', labelName: 'D'}
    ]},
    
    {problem: '小A的哪种行为最有可能让你感受到自己处于备选的位置？', options: [
        {label:'和你聊天不咸不淡，总是用嗯、哦、好回复', labelName: 'A'},
        {label:'喜欢三更半夜找你聊天，说一些情感上的私密话题', labelName: 'B'},
        {label:'和你聊天时不时蹦出几句骚话，故意营造暧昧气氛', labelName: 'C'},
        {label:'对你十分温柔耐心，始终保持联系但也没有进一步的进展', labelName: 'D'}
    ]},
    {problem: '你鼓足勇气和暗恋很久的小A表白，结果小A说：“抱歉，我现在不想谈恋爱。”你认为小A的心理活动最有可能是：', options: [
        {label:'觉得有点突然，还没准备好', labelName: 'A'},
        {label:'忙于事业，不方便谈恋爱', labelName: 'B'},
        {label:'死缠烂打继续追我，说不定有机会', labelName: 'C'},
        {label:'不是我的菜，不喜欢', labelName: 'D'}
    ]},
    {problem: '你想约小A出来，最合适的理由是？', options: [
        {label:'新电影快上映了，想不想看？', labelName: 'A'},
        {label:'朋友办生日party，来一起玩', labelName: 'B'},
        {label:'有一家餐馆很好吃，一起去尝尝', labelName: 'C'},
        {label:'一起去浪漫的土耳其旅游', labelName: 'D'}
    ]},
    {problem: '小A答应交往并赴约，你会选择哪种约会穿搭？', options: [
        {label:'当下比较流行的', labelName: 'A'},
        {label:'有自己风格的', labelName: 'B'},
        {label:'穿起来比较年轻的', labelName: 'C'},
        {label:'自己喜欢的合适的就行，不管那么多', labelName: 'D'}
    ]},
    {problem: '小A花了很多钱买了一件你完全无法理解的东西，你应该对小A说:', options: [
        {label:'亲爱的你好有眼光，买什么都好看', labelName: 'A'},
        {label:'只要你花自己的钱，买什么我都开心', labelName: 'B'},
        {label:'认识你这么久，你总能给我带来新鲜感', labelName: 'C'},
        {label:'我们都穷得揭不开锅了，你还买这个？', labelName: 'D'}
    ]},
    {problem: '你因为做错事惹小A生气了，你决定用一个表情包打破僵局，你选择是:', options: [
        {label:'表情包A', labelName: 'A'},
        {label:'表情包B', labelName: 'B'},
        {label:'表情包C', labelName: 'C'},
        {label:'表情包D', labelName: 'D'}
    ], imgUrl: 'https://photo.zastatic.com/images/common-cms/it/20240801/1722479095529_698831.png'},
    {problem: '小A生日，你打算送ta一个生日礼物加深你们感情，你会选择？', options: [
        {label:'贴身衣物', labelName: 'A'},
        {label:'一套房子', labelName: 'B'},
        {label:'新款苹果手机', labelName: 'C'},
        {label:'亲手做的饭菜', labelName: 'D'}
    ]},



];