<template>
    <div class="plan-question">
        <div class="inner">
            <div class="title">
                「恭喜！您已完成报名」
            </div>
            <p class="text">稍后将有红娘与您联系</p>
            <p class="text">祝您早日脱单</p>
        </div>
    </div>
</template>

<script>
import { mapMutations } from "vuex";
export default {
    components: {},
    data() {
        return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.plan-question {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    @include set-img("../assets/images/bg-success.png");
    background-size: cover;
    background-position: 0% 50%;
    .inner {
        position: absolute;
        top: 50%;
        right: 48px;
        left: 48px;
        height: 584px;
        transform: translateY(-60%);
        text-align: center;
        // css 类点九图实现
        border-image-source: url("../assets/images/bg-question-card.png");
        border-image-slice: 48 36 36 36 fill; // 每个区域截取宽度为 10px
        border-image-width: 48px 36px 36px 36px; // 设置各个区域的图片宽度
        border-image-repeat: repeat; // 图片重复或拉伸模式
        .title {
            font-size: 44px;
            font-weight: 500;
            color: #191c32;
            line-height: 38px;
            margin-top: 182px;
            &::after {
                content: "";
                display: block;
                width: 274px;
                height: 24px;
                background-color: #fed9e2;
                margin: -6px auto 0;
            }
        }
        .text {
            margin-top: 90px;
            font-size: 32px;
            font-weight: 400;
            color: #26273c;
            line-height: 48px;
            & + .text {
                margin-top: 0;
            }
        }
    }
}
</style>
