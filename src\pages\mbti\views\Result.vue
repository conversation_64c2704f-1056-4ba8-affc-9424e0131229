<template>
    <!--
        cmsConfig.reportViewType
        1: A方案
        2: B方案
        3：C方案
        4: D方案
      -->
    <div>
        <div
            class="result"
            ref="result"
        >
            <!-- 标题 -->
            <template v-if="cmsConfig.reportViewType === 4">
                <div class="tips-d"></div>
            </template>
            <template v-else>
                <div class="tips">
                    专业版
                </div>
                <div class="title"></div>
            </template>

            <!-- 类型 -->
            <template v-if="cmsConfig.reportViewType === 4">
                <div class="info-d">
                    <div>
                        {{ isShareResult ? 'Ta': '你' }}是一个
                        <div>
                            <span>[{{ result }}] 型</span><span></span>
                        </div>
                    </div>
                </div>
                <p class="desc-d">
                    {{ mbtiResult.proverbs }}
                </p>
            </template>
            <div
                class="info"
                v-else
            >
                <h3><span></span>【{{ result }}】型</h3>
                <p>{{ mbtiResult.proverbs }}</p>
            </div>

            <!-- 企微导量(D方案测试组) -->
            <qr-code
                v-if="resourceKey === 'MBTI钩子D方案(企微)' && single === 1"
                :result="result"
                :proverbs="mbtiResult.proverbs"
            />

            <!-- 图表 -->
            <bar-chart
                v-if="!isShareResult && cmsConfig.reportViewType !== 4"
            />

            <!-- 圆球动画 -->
            <div
                class="panel"
                :class="{
                    'panel-share': isShareResult,
                    'panel-d': cmsConfig.reportViewType === 4
                }"
            >
                <div
                    class="title-d"
                    v-if="cmsConfig.reportViewType === 4"
                >
                    <span></span>
                    <span>{{ mbtiResult.name[0] }}</span>
                    <span>{{ mbtiResult.name[1] }}</span>
                </div>
                <div
                    class="space"
                    :class="{ 'space-d': cmsConfig.reportViewType === 4 }"
                >
                    <div
                        class="star"
                        v-if="cmsConfig.reportViewType !== 4"
                    >
                        <anim-svga>
                            <div class="star-text">
                                <p>{{ mbtiResult.name[0] }}</p>
                                <h4>{{ mbtiResult.name[1] }}</h4>
                            </div>
                        </anim-svga>
                    </div>
                    <div
                        class="line"
                        v-if="cmsConfig.reportViewType !== 4"
                    ></div>
                    <div
                        class="circle"
                        :class="[
                            `circle${i + 1}`,
                            { 'circle-d': cmsConfig.reportViewType === 4 }
                        ]"
                        v-for="(word, i) in mbtiResult.words"
                        v-html="word"
                        :key="i"
                    ></div>
                </div>
                <div class="desc">
                    {{ isShareResult ? mbtiResult.typeDesc.replace(/你/g, 'Ta') : mbtiResult.typeDesc }}
                </div>
            </div>

            <!-- 推人卡片 + 红娘锦囊 + APP课程赠送 （适用于D方案）-->
            <result-for-d
                v-if="cmsConfig.reportViewType === 4"
                :result="{
                    type: result,
                    ...mbtiResult,
                    modelList,
                    matchTypeList: MBTITypes,
                    gender: registerForm.gender
                }"
            />

            <!-- 报告（A，C, D方案）-->
            <div
                class="analyse"
                :class="{ 'analyse-d': cmsConfig.reportViewType === 4 }"
                v-if="
                    cmsConfig.reportViewType === 1 ||
                        cmsConfig.reportViewType === 3 ||
                        cmsConfig.reportViewType === 4
                "
            >
                <template v-if="cmsConfig.reportViewType !== 4">
                    <div class="subtitle1"></div>
                    <div class="analyse-line"></div>
                </template>
                <template v-else>
                    <common-title>
                        {{ this.isShareResult ? 'Ta' : '你' }}的爱情习性
                    </common-title>
                </template>
                <template
                    v-for="n in 5"
                    v-if="mbtiResult['feature' + n]"
                >
                    <div class="note">
                        <span
                            :class="{
                                'note-d-icon': cmsConfig.reportViewType === 4
                            }"
                        ></span>
                        <span
                            :class="{
                                'note-d': cmsConfig.reportViewType === 4
                            }"
                        >{{ calc(n) }}</span>
                    </div>
                    <p class="note-info">
                        {{ calc(n, "desc") }}
                    </p>
                </template>
                <template v-if="cmsConfig.reportViewType !== 4">
                    <div class="subtitle2"></div>
                    <div class="analyse-line"></div>
                    <div class="note">
                        <span></span>{{ mbtiResult.matchType }}
                    </div>
                    <p class="note-info">
                        {{ mbtiResult.matchTypeDesc }}
                    </p>
                </template>
            </div>

            <!-- 报告（B方案）-->
            <!--<div
                class="essay"
                v-if="cmsConfig.reportViewType === 2"
            >
                <div class="essay-title">
                    专业版报告解读须下载APP，在APP内查看
                </div>
                <div class="essay-line"></div>
                <div class="essay-lock">
                    <div
                        class="essay-lock-img"
                        @click="handleGoDownloadAfter"
                    ></div>
                    <button @click="handleGoDownloadAfter">
                        立即解锁完整版报告
                    </button>
                </div>
                <div class="essay-subtitle"></div>
                <div class="essay-info">
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20220424/1650794054541_130279_t.png"
                        :style="{ width: '145px' }"
                        alt=""
                    />
                    <p>(帮助你了解自己是怎样的人)</p>
                </div>
                <div class="essay-info">
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20220424/1650794311220_425507_t.png"
                        :style="{ width: '174px' }"
                        alt=""
                    />
                    <p>(帮助你了解恋爱时的状态和特点)</p>
                </div>
                <div class="essay-info">
                    <img
                        src="https://photo.zastatic.com/images/common-cms/it/20220424/1650794847490_885770_t.png"
                        :style="{ width: '157px' }"
                        alt=""
                    />
                    <p>(帮助你了解哪些人格与你最默契，最适合当你伴侣)</p>
                </div>
                <div class="essay-final"></div>
            </div>-->

            <!-- 推荐人模块（单身用户）（适用于A , B , C方案） -->
            <div
                class="chance"
                v-if="single === 1 && cmsConfig.reportViewType !== 4"
            ></div>
            <!--<div
                class="recommend"
                v-if="single === 1 && cmsConfig.reportViewType !== 4"
            >
                <section>
                    <div class="recommend-title">
                        <p
                            v-if="
                                cmsConfig.reportViewType === 1 ||
                                    cmsConfig.reportViewType === 3
                            "
                        >
                            <span>Ta</span>
                            是：{{
                                MBTITypes[currentIndex] &&
                                    MBTITypes[currentIndex].name
                            }}

                            （<span>{{
                                MBTITypes[currentIndex] &&
                                    MBTITypes[currentIndex].type
                            }}</span>）
                        </p>
                        <div v-else>
                            <span>Ta</span>是与你最匹配的（<span>XXXX</span>）型
                            <p class="spe">
                                请前往APP解锁完整报告，即可获知Ta的类型
                            </p>
                        </div>
                    </div>
                    <img
                        class="recommend-img"
                        :src="
                            currentModel &&
                                (currentModel.mainImg || currentModel.avatar)
                        "
                    />
                    <div class="recommend-desc">
                        <h4>{{ currentModel && currentModel.name }}</h4>
                        <p>
                            年龄：{{ currentModel && currentModel.ageString }}
                        </p>
                        <p>
                            工作地：{{
                                currentModel && currentModel.workCityString
                            }}
                        </p>
                        <div
                            class="lock"
                            v-if="!isLock"
                        >
                            <p>
                                学历：{{
                                    currentModel && currentModel.educationString
                                }}
                            </p>
                            <p>
                                月收入：{{
                                    currentModel && currentModel.salaryString
                                }}
                            </p>
                            <p>
                                手机号：{{ currentModel && currentModel.phone }}
                                <span
                                    @click="handleViewProfile('phone')"
                                >查看完整手机号</span>
                            </p>
                            <p>
                                微信号：{{
                                    currentModel && currentModel.weChat
                                }}
                                <span
                                    @click="handleViewProfile('wechat')"
                                >查看完整微信号</span>
                            </p>
                        </div>
                    </div>
                    <div class="recommend-btn">
                        <button @click="handleOperation(0)"></button>
                        <button @click="handleOperation(1)"></button>
                    </div>
                </section>
            </div>-->
            <div
                class="explain"
                v-if="single === 1 && cmsConfig.reportViewType !== 4"
            >
                {{
                    isLock
                        ? "选择你喜欢的对象，解锁Ta的全部资料"
                        : "Ta当前APP在线，期待与你相遇！"
                }}
            </div>

            <!-- 按钮区域 -->
            <button
                class="main"
                :class="{
                    'main-sticky':
                        cmsConfig.reportViewType === 1 ||
                        cmsConfig.reportViewType === 3 ||
                        cmsConfig.reportViewType === 4,
                    'main-d': cmsConfig.reportViewType === 4,
                    bounceInDown: showDownStatus
                }"
                @click="handleGoDownloadBeforeOrShare(single)"
                v-show="showDownStatus"
                v-if="!isShareResult"
            >
                {{
                    cmsConfig.reportViewType === 1 ||
                        cmsConfig.reportViewType === 3 ||
                        cmsConfig.reportViewType === 4
                        ? `${single === 1 ? expertJumpULoveApplet ? '点击按钮跳转优恋空间' : "下载APP立即约会" : "太准了！分享给单身朋友吧！"}`
                        : "下载APP查看完整报告，与Ta约会吧"
                }}
            </button>

            <mutual-like-modal
                v-model="modalVisible.mutualLikeModal"
                :current-model="currentModel"
                :handle-go-download="handleGoDownload"
            />

            <download-guide-modal v-model="modalVisible.downloadGuideModal" />

            <download-guide-d-modal v-model="modalVisible.downloadGuideDModal" />

            <share-modal v-model="modalVisible.shareModal" />
        </div>

        <!-- 分享模块 -->
        <div
            class="main-share"
            :class="{ 'main-share-d': cmsConfig.reportViewType === 4 }"
            v-if="isShareResult"
        ></div>

        <!-- SVGA悬浮窗 -->
        <div
            class="love"
            id="svgaLove"
            v-show="(cmsConfig.reportViewType === 1 || cmsConfig.reportViewType === 3 ) && single === 1 && isShowSvga"
            @click="handleScroll"
        ></div>

        <!-- 红娘的悬浮窗 -->
        <div
            class="call"
            :style="{'transform': `translateX(${this.distance})`}"
            @click="handleClickCall"
            v-if="single === 1 && cmsConfig.reportViewType === 4"
        >
            <img
                src="https://photo.zastatic.com/images/common-cms/it/20220704/1656900980757_396085_t.png"
                alt="红娘"
            >
            <div>
                你获得一次珍爱红娘电话沟通的机会
            </div>
        </div>

        <a
            id="mbti_href"
            style="display: none;"
        ></a>
    </div>
</template>

<script>
import BarChart from "../components/result/BarChart.vue";
import MutualLikeModal from "../components/modal/MutualLikeModal";
import DownloadGuideModal from "../components/modal/DownloadGuideModal";
import DownloadGuideDModal from "../components/modal/DownloadGuideDModal";
import ShareModal from '../components/modal/ShareModal.vue';
import ResultForD from '../components/result/ResultForD.vue';
import QrCode from "../components/result/QrCode";
import CommonTitle from '../components/CommonTitle.vue';
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import Api from '@/common/server/base';
import { Toast } from "vant";
import { mapState } from "vuex";
import { storage } from "../lib/utils.js";
import { _getModelInfos } from "../api";
import { post } from "@/common/utils/ajax.js";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import AnimSvga from "../components/AnimSvga.vue";
import { CryptoDes } from '@/common/utils/crypto';
const cryptoDes = new CryptoDes('rlWU9Uty8atXZVE8')

export default {
    name: "Result",
    components: {
        BarChart,
        MutualLikeModal,
        DownloadGuideModal,
        DownloadGuideDModal,
        ShareModal,
        AnimSvga,
        ResultForD,
        QrCode,
        CommonTitle
    },
    data() {
        return {
            result: Z.getParam("type") || "",
            isLock: true,
            modelList: [],
            modalVisible: {
                mutualLikeModal: false,
                downloadGuideModal: false,
                downloadGuideDModal: false,
                shareModal: false
            },
            currentIndex: 0,
            mbtiResult: {
                advantage: "",
                inferiority: "",
                proposal: "",
                feature1: "",
                feature1Desc: "",
                feature2: "",
                feature2Desc: "",
                feature3: "",
                feature3Desc: "",
                feature4: "",
                feature4Desc: "",
                feature5: "",
                feature5Desc: "",
                keyWord: "",
                matchType: "",
                matchTypeDesc: "",
                name: "",
                proverbs: "",
                typeDesc: "",
                words: []
            },
            single: Z.getParam("single") || storage.getItem("single"),
            MBTITypes: [],
            isShowSvga: true,
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
            },
            screenHeight: 0, // 一屏高度,
            distance: '0px', //滑动距离
            showDownStatus: false,

            expertJumpULoveApplet: false, // 是否跳优恋空间
            youlianLink: '', // 跳优恋空间链接
        };
    },
    computed: {
        ...mapState(["registerForm", "cmsConfig", "resourceKey"]),
        currentModel() {
            return this.modelList && this.modelList[this.currentIndex];
        },
        isShareResult() {
            return Z.getParam("from") === "share" && Z.getParam("type");
        }
    },
    created() {
        // 结果分享页
        if (this.isShareResult) {
            this.$watch("resourceKey", value => {
                if (value) {
                    this.$reportKibana(value, 110, "分享页访问");
                }
            });
        } else {
            this.getResult();
            if(this.single === 1){
                this.$reportKibana(this.resourceKey, 93, "报告页访问-单身", {
                    ext17: navigator.userAgent,
                });
            } else {
                this.$reportKibana(this.resourceKey, 93, "报告页访问-非单身", {
                    ext17: navigator.userAgent,
                });
            }

        }
        this.getModelInfos();
        this.getMBITResult();
        this.isJumpYoulian();

    },
    mounted() {
        // 钩子A，C方案且为单身，才有悬浮球
        if (
            (this.cmsConfig.reportViewType === 1 ||
                this.cmsConfig.reportViewType === 3) &&
            this.single === 1
        ) {
            this.handleSetSVGA();

            window.addEventListener("scroll", () => {
                const nav = document.querySelector(".nav");
                const target = document.querySelector(".result .chance");
                const scrollTop =
                    window.pageYOffset ||
                    document.documentElement.scrollTop ||
                    document.body.scrollTop;
                // 看到左右滑模块时，将悬浮球置为消失态
                if (scrollTop >= nav.offsetHeight + target.offsetTop) {
                    this.isShowSvga = false;
                }
            });
        }
        window.addEventListener("scroll", this.handleExposure);
        this.screenHeight = document.documentElement.clientHeight;

        // 控制浮球位置
        if (this.cmsConfig.reportViewType === 4 && this.single === 1) {
            setTimeout(() => {
                this.distance = '71%';
            }, 3000);
        }


    },
    methods: {
        async isJumpYoulian() {
            const res = await Api.getRegChainConfig({
                channelId: Z.getParam('channelId'),
                subChannelId: Z.getParam('subChannelId'),
            })
            if (res.code === 0) {
                this.expertJumpULoveApplet = res.data.results.expertJumpULoveApplet
            }
        },
        async getYoulianLink() {

            if (this.expertJumpULoveApplet) {
                const memberId = sessionStorage.getItem('reg_memberid')
                this.$reportKibana(this.resourceKey, 123, "测试结果页-短链生成进入", {});
                let res1 = {}
                if (memberId) {
                    const demem = cryptoDes.encode(memberId)
                    res1 = await Api.getValidToken({memberId: demem})
                    this.$reportKibana(this.resourceKey, 123, "测试结果页-换取token", {
                        ext1: JSON.stringify(res1)
                    });
                }

                const linkUrl = Api.domain().includes('api-test.zajiebao.com') ? encodeURIComponent('https://mp.weixin.qq.com/s/MODFT629m9B3w9v8H15Gig') : encodeURIComponent('https://mp.weixin.qq.com/s/QeszlKRc_D3oKYCoCDE-4w')
                const params = {
                    path: 'pages/main/meet/index',
                    query: `channelId=${Z.getParam('channelId')}&subChannelId=${Z.getParam('subChannelId')}&zaAPPToken=${res1.data ? res1.data : ''}&linkUrl=${linkUrl}`,
                };
                const res2 = await Api.goYouLianMini(params)
                if (res2.code === 0) {
                    this.$reportKibana(this.resourceKey, 123, "测试结果页-短链生成", {
                        ext1: JSON.stringify(res2)
                    });
                    this.youlianLink = res2.data
                    location.href = this.youlianLink
                }
            }
        },
        getResult() {
            let answer = storage.getItem("answer");
            let result = [
                {
                    E: answer.filter(item => item.type === "E").length,
                    I: answer.filter(item => item.type === "I").length
                },
                {
                    N: answer.filter(item => item.type === "N").length,
                    S: answer.filter(item => item.type === "S").length
                },
                {
                    F: answer.filter(item => item.type === "F").length,
                    T: answer.filter(item => item.type === "T").length
                },
                {
                    J: answer.filter(item => item.type === "J").length,
                    P: answer.filter(item => item.type === "P").length
                }
            ];
            if (result) {
                result = result.map(k => {
                    const [a, b] = Object.keys(k);
                    return k[a] > k[b] ? a : b;
                });
            }
            this.result = result.join("");
        },

        async getModelInfos() {
            if (this.single !== 1) {
                return;
            }
            const {
                salary,
                workCity,
                education,
                gender,
                birthday
            } = this.registerForm;
            const age =
                new Date().getFullYear() - new Date(birthday).getFullYear();
            const sendData = {
                limit: this.cmsConfig.reportViewType === 4 ? 12 : 8,
                salary,
                workCity,
                education,
                sex: gender,
                age,
                ageFloor: this.cmsConfig.reportViewType === 4 ? 18 : null,
                ageCeiling: this.cmsConfig.reportViewType === 4 ? 36 : null
            };
            // const result = await _getModelInfos(sendData);
            //
            // if (result.isError) {
            //     this.$toast(result.errorMessage);
            //     return;
            // }
            //
            // this.modelList = result.data.modelInfoVos;
        },

        handleOperation(isLike) {
            if (this.currentIndex === 7 && !isLike) {
                Toast(
                    "当前推荐的8位对象已用完，请前往APP继续匹配更多对象，APP内名额不限"
                );
                return;
            }

            // 0 ：不喜欢 1：喜欢
            if (isLike || this.currentIndex === 7) {
                this.isLock = false;
                this.modalVisible.mutualLikeModal = true;
            } else {
                this.isLock = true;
                this.currentIndex++;
            }
        },

        handleViewProfile(type) {
            this.$reportKibana(
                this.resourceKey,
                type === "wechat" ? 95 : 96,
                `报告页-查看${type === "wechat" ? "微信" : "手机"}号按钮点击`
            );

            this.handleGoDownload();
        },

        handleGoDownload() {
            const { downloadStatus, reportViewType } = this.cmsConfig;
            const toastDescription =
                reportViewType === 1 || reportViewType === 3 || reportViewType === 4
                    ? "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！"
                    : "正在下载APP，进APP查看完整版报告，与你最匹配的Ta在珍爱等你！";
            if (this.expertJumpULoveApplet) {
                this.getYoulianLink()
                return false
            }
            // cms开关打开时才允许下载
            if (downloadStatus === 1) {
                visibilityChangeDelay(function() {
                    if (storage.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: toastDescription,
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();

                return;
            }

            if (reportViewType === 4) {
                this.modalVisible.downloadGuideDModal = true;
            } else {
                this.modalVisible.downloadGuideModal = true;
            }
        },
        async getMBITResult() {
            const res = await post("/register/getMBITResult.do", {
                type: this.result,
                gender:
                    this.single === 1
                        ? this.registerForm.gender
                        : Math.round(Math.random())
            });
            if (!res.isError) {
                this.mbtiResult = res.data;
                let words = res.data.keyWord.split(/,|，/);
                words = words.map(word => {
                    word = word.trim();
                    if (word.length > 3) {
                        return word.slice(0, 3) + "<br/>" + word.slice(3);
                    } else {
                        return word;
                    }
                });
                this.mbtiResult.words = words;
                let name = this.mbtiResult.name;
                let temp = [];
                temp[0] = name.slice(0, name.indexOf("的") + 1);
                temp[1] = name.slice(name.indexOf("的") + 1);
                this.mbtiResult.name = temp;

                let matchTypes = res.data.matchTypes;
                matchTypes = matchTypes.map(matchType => {
                    matchType = matchType.trim();
                    let index = matchType.indexOf("（");
                    return {
                        name: matchType.slice(0, index),
                        type: matchType.slice(index + 1, matchType.length - 1)
                    };
                });

                this.MBTITypes = matchTypes;
            }
        },
        calc(n, desc) {
            if (n && desc) {
                return this.isShareResult ? this.mbtiResult["feature" + n + "Desc"].replace(/你/g, 'Ta') : this.mbtiResult["feature" + n + "Desc"];
            } else {
                return this.mbtiResult["feature" + n];
            }
        },
        handleGoDownloadBeforeOrShare(single) {
            if (single === 2 && this.cmsConfig.reportViewType !== 2) {
                const search =
                    location.search.indexOf("&from") > -1
                        ? `${location.search}&type=${this.result}&single=2`
                        : `${location.search}&from=share&type=${
                            this.result
                        }&single=2`;
                const url =
                    location.protocol +
                    "//" +
                    location.hostname +
                    ":" +
                    location.port +
                    location.pathname +
                    search +
                    location.hash;
                const input = document.createElement("input"); //
                input.value = `我正在参加珍爱MBTI恋爱版测试题，解锁最适合我的对象类型，大家快来测试你的最佳对象类型吧 ${url}`; // 设置复制内容
                document.body.appendChild(input); //
                input.select();
                document.execCommand("Copy");
                document.body.removeChild(input);
                this.$reportKibana(
                    this.resourceKey,
                    94,
                    "报告页-分享按钮点击（选择非单身引导分享）"
                );
                this.modalVisible.shareModal = true;
                return;
            }
            this.$reportKibana(this.resourceKey, 94, "报告页-下载按钮点击");
            this.handleGoDownload();
        },
        handleGoDownloadAfter() {
            this.$reportKibana(
                this.resourceKey,
                99,
                "报告页-查看完整报告按钮点击"
            );
            this.handleGoDownload();
        },

        handleScroll() {
            const nav = document.querySelector(".nav");
            const target = document.querySelector(".result .chance");
            this.$reportKibana(this.resourceKey, 94, "报告页-悬浮球点击");
            window.scrollTo({
                top: target.offsetTop + nav.offsetHeight,
                behavior: "smooth"
            });
            this.isShowSvga = false;
        },

        async handleSetSVGA() {
            let player = new SVGA.Player("#svgaLove"),
                parser = new SVGA.Parser("#svgaLove");

            const { gender } = this.registerForm;
            const path =
                gender === 0
                    ? require("../assets/images/love-her.svga")
                    : require("../assets/images/love-him.svga");

            parser.load(path, videoItem => {
                player.setVideoItem(videoItem);
                player.loops = 0;
                player.startAnimation();
            });
        },
        handleExposure() {
            const { top } = this.$refs.result.getBoundingClientRect();
            if (-top >= screen.height * 1.5) {
                this.showDownStatus = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$reportKibana(this.resourceKey, 100, "报告页-第二屏曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$reportKibana(this.resourceKey, 100, "报告页-第三屏曝光");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$reportKibana(this.resourceKey, 100, "报告页-第四屏幕曝光");
                this.reportLock.fourthScreen = true;
            }
            // 如果已经触发四次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleExposure);
            }
        },

        handleClickCall() {
            this.$reportKibana(
                this.resourceKey,
                94,
                "报告页-悬浮球点击"
            );
            this.distance = '0px';

            setTimeout(() => {
                this.distance = '71%';
            }, 3000);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.result {
    position: relative;
    z-index: 1;
    margin-top: -24px;
    padding: 0 32px 90px;

    .tips-d {
        width: 654px;
        height: 240px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656556975291_546948_t.png)
            no-repeat;
        background-size: 100% 100%;
        margin: 0 auto;
    }

    .tips {
        width: 196px;
        height: 40px;
        padding-left: 16px;
        margin-left: 72px;
        color: #000;
        font-size: 26px;
        font-weight: 500;
        line-height: 40px;
        background: linear-gradient(
            270deg,
            rgba(20, 45, 63, 0) 4%,
            #3be0ef 52%
        );
        border-radius: 200px 0 0 200px;
    }
    .title {
        width: 632px;
        height: 50px;
        margin-top: 8px;
        margin-left: 26px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220422/1650610599709_198070_t.png)
            no-repeat;
        background-size: 100% 100%;
    }

    .info-d {
        width: 686px;
        height: 98px;
        margin-top: 54px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656557957855_540148_t.png)
            no-repeat;
        background-size: 100% 100%;
        @include flex-center(row, center, null);
        font-size: 36px;
        color: #ffffff;
        font-weight: 400;
        padding-top: 28px;
        > div {
            width: 436px;
            display: flex;
            > div {
                height: 50px;
                margin-left: 40px;
                position: relative;
                > span:first-child {
                    font-weight: 600;
                    padding-left: 26px;
                }
                > span:last-child {
                    display: inline-block;
                    width: 254px;
                    height: 22px;
                    background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656558440391_326820_t.png)
                        no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    left: 0px;
                    bottom: 10px;
                    z-index: -1;
                }
            }
        }
    }
    .desc-d {
        width: 689px;
        height: 110px;
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        border-radius: 0 0 32px 32px;
        font-weight: 400;
        font-size: 32px;
        color: #ffffff;
        @include flex-center(row, center, center);
        margin-bottom: 24px;
    }

    .info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 228px;
        margin-top: 16px;
        color: #fff;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220422/1650610161236_269250_t.png)
            no-repeat;
        background-size: 100% 100%;
        > h3 {
            display: flex;
            height: 38px;
            align-items: center;
            font-size: 36px;
            font-weight: bold;
            color: #00ffff;
            > span {
                display: inline-block;
                width: 152px;
                height: 38px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220422/1650610232578_569021_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
        }
        > p {
            padding-top: 24px;
            font-size: 32px;
        }
    }

    .panel,
    .analyse,
    .essay {
        margin-top: 32px;
        border: 2px solid #00ffff;
        border-radius: 16px;
        background: rgba(0, 255, 255, 0.2);
        box-shadow: inset 0 0 48px rgba(255, 255, 255, 0.5);
    }
    .panel {
        margin-top: 0;
        padding-top: 80px;
        border-top: none;
        border-radius: 0 0 16px 16px;
    }
    .panel-share {
        margin-top: 32px;
        border: 2px solid #00ffff;
        border-radius: 16px;
    }
    .panel-d {
        padding-top: 34px;
        box-shadow: none;
        background: none;
        border: none;
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        border-radius: 0 0 32px 32px;
        .title-d {
            color: #ffffff;
            padding-left: 32px;
            margin-bottom: 70px;
            > span:nth-child(1) {
                display: inline-block;
                width: 40px;
                height: 38px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577698934_433293_t.png)
                    no-repeat;
                background-size: 100% 100%;
                position: relative;
                top: 6px;
            }
            > span:nth-child(2) {
                font-weight: 400;
                font-size: 30px;
            }
            > span:nth-child(3) {
                font-weight: 500;
                font-size: 52px;
            }
        }
    }
    .space {
        position: relative;
        width: 560px;
        height: 560px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px auto 70px;
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 50%;
        .line {
            position: absolute;
            top: 40px;
            left: 40px;
            width: 480px;
            height: 480px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
        }
        .line::before {
            content: "";
            position: absolute;
            top: 40px;
            left: 40px;
            width: 400px;
            height: 400px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
        }
        .line::after {
            content: "";
            position: absolute;
            top: 70px;
            left: 70px;
            width: 340px;
            height: 340px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
        }
        .circle {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 164px;
            height: 164px;
            padding: 20px;
            color: #fff;
            font-size: 28px;
            line-height: 32px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220426/1650956538552_697139_t.png)
                no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
        }
        .circle-d {
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656573865737_683629_t.png)
                no-repeat;
            background-size: 100% 100%;
            box-sizing: border-box;
        }
        .circle1 {
            position: absolute;
            top: 52px;
            left: 14px;
            animation-delay: 2s;
            animation: shake1 4s linear infinite;
        }
        .circle2 {
            position: absolute;
            top: -12px;
            right: 154px;
            animation: shake1 4s linear infinite;
        }
        .circle3 {
            position: absolute;
            top: 150px;
            right: -28px;
            animation-delay: 2s;
            animation: shake2 4s linear infinite;
        }
        .circle4 {
            position: absolute;
            right: 36px;
            bottom: 62px;
            animation-delay: 2s;
            animation: shake2 5s linear infinite;
        }
        .circle5 {
            position: absolute;
            left: 148px;
            bottom: -18px;
            animation-delay: 0;
            animation: shake2 4s linear infinite;
        }
        .circle6 {
            position: absolute;
            left: -34px;
            bottom: 140px;
            animation-delay: 2s;
            animation: shake1 5s linear infinite;
        }
    }

    .space-d {
        border: none;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220701/1656681640518_638651_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .desc {
        padding: 0 32px 64px;
        color: #fff;
        font-size: 32px;
        line-height: 48px;
    }

    .analyse-d {
        border-radius: 0 0 16px 16px;
        box-shadow: none;
        background: none;
        border: none;
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
    }

    .subtitle1 {
        margin-left: 32px;
        margin-top: 28px;
        width: 192px;
        height: 38px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220426/1650969428968_240326_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .subtitle2 {
        margin-left: 32px;
        margin-top: 28px;
        width: 344px;
        height: 38px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220426/1650969431953_111223_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .analyse-line,
    .essay-line {
        position: relative;
        width: 654px;
        height: 2px;
        margin-top: 20px;
        background: #00ffff;
        box-shadow: 0 0 48px #fff;
        &::after {
            content: "";
            position: absolute;
            right: 0;
            top: -5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ffff;
        }
    }
    .essay-title {
        padding: 22px 32px 0;
        color: #00ffff;
        font-size: 32px;
    }
    .essay-lock {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 622px;
        margin: 40px 32px 48px;
        padding-top: 186px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220425/1650866952652_880035_t.png)
            no-repeat;
        background-size: 100% 100%;
        &-img {
            width: 116px;
            height: 116px;
            margin-bottom: 74px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220424/1650792039899_386519_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        > button {
            width: 430px;
            height: 110px;
            line-break: 110px;
            color: #000000;
            font-size: 32px;
            font-weight: 500;
            background: #3be0ef;
            border-radius: 55px;
        }
    }
    .essay-subtitle {
        width: 596px;
        height: 34px;
        margin-top: 44px;
        margin-left: 44px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220424/1650793941343_782692_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .essay-info {
        margin-top: 40px;
        padding: 0 32px;
        > img {
            height: 34px;
        }
        > p:last-child {
            margin-top: 20px;
            text-indent: 20px;
            color: #aeafb3;
            font-size: 28px;
        }
    }
    .essay-final {
        width: 620px;
        height: 82px;
        margin: 32px 32px 64px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220424/1650794850194_662369_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .note {
        position: relative;
        display: flex;
        align-items: center;
        margin-top: 32px;
        padding-left: 64px;
        color: #fff;
        font-size: 32px;
        font-weight: 600;
        line-height: 44px;
        > span:nth-child(1) {
            position: absolute;
            left: 34px;
            top: 14px;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
        }
        &-d-icon {
            display: inline-block !important;
            position: relative;
            width: 38px !important;
            height: 34px !important;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577698934_433293_t.png)
                no-repeat !important;
            background-size: 100% 100% !important;
            top: 10px !important;
            border-radius: 0 !important;
        }
        &-d {
            display: inline-block !important;
            margin-left: 24px;
            // padding-left: 24px;
        }
    }
    .note-info {
        padding: 16px 32px 40px;
        color: #fff;
        font-size: 32px;
        line-height: 44px;
    }
    .chance {
        position: relative;
        left: -16px;
        width: 720px;
        height: 244px;
        margin-top: 24px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220526/1653557162220_288487_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .recommend {
        position: relative;
        margin-top: 32px;
        border: 2px solid #4a9cfb;
        border-radius: 16px;
        background: rgba(245, 145, 249, 0.1);
        box-shadow: inset 0 0 48px rgba(255, 255, 255, 0.5);

        > section:first-child {
            position: relative;
        }

        &-title {
            margin: 16px 0;
            text-align: center;
            color: rgba(245, 145, 249, 1);
            font-size: 32px;
            > p {
                > span {
                    font-size: 32px;
                    font-weight: 600;
                }
            }
            > div {
                > span {
                    font-weight: 500;
                }
            }
            .spe {
                margin-top: 16px;
                color: #f591f9;
                font-size: 28px;
            }
        }
        &-img {
            width: 100%;
            height: auto;
        }
        &-desc {
            padding: 32px;
            color: #fff;
            > h4 {
                padding-bottom: 8px;
                font-size: 44px;
                font-weight: 500;
            }
            > p,
            .lock > p {
                height: 80px;
                line-height: 80px;
                font-size: 32px;
                font-weight: 400;
                color: #c9c8ce;
            }
            .lock span {
                margin-top: 4px;
                display: inline-block;
                text-align: center;
                width: 260px;
                height: 64px;
                position: absolute;
                right: 32px;
                line-height: 64px;
                background: #f591f9;
                color: #fff;
                font-size: 32px;
                border-radius: 32px;
            }
        }
        &-btn {
            display: flex;
            justify-content: center;
            padding: 32px 0;
            border-top: 2px solid #4a9cfb;
            > button {
                width: 112px;
                height: 112px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220422/1650618073299_773277_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }

            > button:last-child {
                margin-left: 128px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220422/1650618075765_678589_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    .explain {
        margin-top: 32px;
        color: #f591f9;
        font-size: 32px;
        text-align: center;
    }
    .main {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 88px auto 0;
        width: 622px;
        height: 116px;
        color: #000000;
        font-size: 36px;
        font-weight: 500;
        background: #00ffff;
        border-radius: 58px;
        &-d {
            background: url("https://photo.zastatic.com/images/common-cms/it/20220701/1656646128530_578401_t.png")
                no-repeat;
            background-size: 100% 100%;
            width: 620px;
            height: 120px;
            color: #ffffff;
        }
    }
    .main-sticky {
        position: sticky;
        bottom: 84px;
        z-index: 100;
    }
    @keyframes shake1 {
        0% {
            transform: translateY(0);
            opacity: 1;
        }
        50% {
            transform: translateY(-40px);
            opacity: 0.7;
        }
        100% {
            transform: translateY(0);
            opacity: 0.7;
        }
    }

    @keyframes shake2 {
        0% {
            transform: translateY(0);
            opacity: 1;
        }
        50% {
            transform: translateY(30px);
            opacity: 0.7;
        }
        100% {
            transform: translateY(0);
            opacity: 0.7;
        }
    }
}

.main-share {
    width: 100%;
    z-index: 100;
    height: 356px;
    position: sticky;
    bottom: 0px;
    background: url("https://photo.zastatic.com/images/common-cms/it/20220519/1652934124634_952331_t.png")
        no-repeat;
    background-size: 100% 100%;
    &-d {
        background: url("https://photo.zastatic.com/images/common-cms/it/20220701/1656657248913_619040_t.png")
            no-repeat;
        background-size: 100% 100%;
    }
}

.love {
    width: 160px;
    height: 210px;
    position: fixed;
    right: -16px;
    top: 55%;
    z-index: 100;
}

.call {
    width: 378px;
    height: 112px;
    background-image: linear-gradient(-58deg, #6E77FF 14%, #F591F9 100%);
    border-radius: 200px 0 0 200px;
    position: fixed;
    right: 0px;
    top: 70%;
    z-index: 100;
    @include flex-center(row, center, center);
    transition: all 0.5s ease-in-out;
    >img {
        width: 88px;
        height: 88px;
    }
    >div {
        width: 258px;
        height: 72px;
        font-weight: 400;
        font-size: 28px;
        color: #FFFFFF;
        line-height: 36px;
        margin-left: 12px;
    }
}

@keyframes bounceInDown {
    from,
    60%,
    75%,
    90%,
    to {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    0% {
        opacity: 0;
        transform: translate3d(0, 500px, 0) scaleY(3);
    }

    60% {
        opacity: 1;
        transform: translate3d(0, 25px, 0) scaleY(0.9);
    }

    75% {
        transform: translate3d(0, -10px, 0) scaleY(0.95);
    }

    90% {
        transform: translate3d(0, 5px, 0) scaleY(0.985);
    }

    to {
        transform: translate3d(0, 0, 0);
    }
}

.bounceInDown {
    animation-name: bounceInDown;
    animation-duration: 1.5s;
}
</style>
