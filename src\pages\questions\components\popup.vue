<template>
    <div
        class="popup"
        v-if="show"
    >
        <div class="popup_con">
            <h3
                class="popup_con_title"
                v-html="downloadTitle"
            ></h3>
            <p class="popup_con_subtitle">
                快来与Ta相遇，收获你的爱情吧！
            </p>
            <div
                class="popup_con_btn spe"
                @click="handleConfirm"
            >
                {{ channelType === 1 ? "好的" : "立即约会" }}
            </div>
            <div
                class="popup_con_btn"
                @click="handleCancel"
                v-if="!channelType"
            >
                取消
            </div>
        </div>
    </div>
</template>

<script>
import {openApp, visibilityChangeDelay, downloadApp} from "@/common/utils/download_app.js";
import { Toast } from "vant";
import { session as Session} from "@/common/utils/storage";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "Popup",
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            downloadTitle: "",
            channelType: 0
        };
    },
    created() {
        const channelType = sessionStorage.getItem("is_toutiao");
        this.channelType = +channelType;
        this.downloadTitle =
            +channelType === 1
                ? "请前往各大应用市场搜索<br/> “珍爱网”下载珍爱APP"
                : "下载珍爱APP使用此功能";
    },
    methods: {
        handleConfirm() {
            this.$emit("handleConfirm");
            if (!this.channelType) {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (Session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }
        },
        handleCancel() {
            this.$emit("handleCancel");
        }
    }
};
</script>

<style lang="scss">
.popup {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    &_con {
        padding: 48px 70px;
        margin: 0 74px;
        text-align: center;
        white-space: nowrap;
        background: #fff url("../assets/images/bg.png") no-repeat;
        background-size: 100% 220px;
        border-radius: 32px;
        &_title {
            color: #26273c;
            font-size: 36px;
            font-weight: 600;
            line-height: 54px;
        }
        &_subtitle {
            padding-top: 28px;
            color: #6c6d75;
            font-size: 32px;
        }
        &_btn {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 88px;
            color: #767dff;
            font-weight: 400;
            font-size: 32px;
            &.spe {
                color: #fff;
                margin-top: 32px;
                background: #767dff;
                border-radius: 44px;
            }
        }
        .tips {
            padding-top: 8px;
            font-size: 24px;
        }
    }
}
</style>
