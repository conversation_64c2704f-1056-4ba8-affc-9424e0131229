{"name": "newregister", "version": "0.0.1", "description": "", "scripts": {"dev": "za-dev-vue", "prod": "za-prod-vue", "lint": "eslint --ext .js,.vue src", "lintfix": "eslint --fix --ext .js,.vue src", "prepare": "husky install", "cz": "git-cz"}, "keywords": ["vue", "build", "ZA"], "repository": {"type": "git", "url": ""}, "author": "ZAFE", "license": "MIT", "devDependencies": {"@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^9.1.2", "@za-build/project-vue": "^1.8.9", "babel-plugin-import": "^1.13.3", "commitizen": "^4.2.4", "crypto-js": "^4.2.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "husky": "^7.0.0", "postcss": "8.2.2"}, "dependencies": {"@za/vue-za-swiper": "^1.0.4", "axios": "^1.4.0", "html2canvas": "^1.4.1", "intersection-observer": "^0.12.0", "lodash-es": "^4.17.21", "vant": "^2.12.35"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "config": {"commitizen": {"path": "node_modules/cz-customizable"}}}