<template>
    <div class="anim" id="animSvga">
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: "AnimSvga",
    mounted() {
        this.setSVGA();
    },
    methods: {
        async setSVGA() {
            let player = new SVGA.Player("#animSvga"),
                parser = new SVGA.Parser("#animSvga");

            parser.load(require("../assets/images/circle.svga"), videoItem => {
                player.setVideoItem(videoItem);
                player.loops = 0;
                player.startAnimation();
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.anim {
    position: relative;
    width: 680px;
    height: 680px;
    margin: 0 auto;
    overflow: hidden;
    > span {
        position: absolute;
        width: 200px;
        height: 40px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        text-align: center;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .star-text {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        > p {
            padding-bottom: 16px;
            color: #00ffff;
            font-size: 32px;
            white-space: nowrap;
        }
        > h4 {
            color: #fff;
            font-size: 56px;
            font-weight: 600;
            line-height: 60px;
            max-width: 300px;
        }
    }
}
</style>
