<template>
<div class="ALERT" v-show="show">
    <div class="mask"></div>
    <div class="box">
        <div class="ctn">   
            <div class="top f-flex-box dir-col justify-center">
                <div class="title" v-if="title">{{title}}</div>
                <p class="content" v-if="content">{{content}}</p>
            </div>
            <div class="btns f-flex-box">
                <div class="confirm flex-item f-flex-box align-center justify-center" @click="handleConfirm">{{confirmText}}</div>
                <div class="cancel flex-item f-flex-box align-center justify-center" @click="handleCancel" v-if="cancel">{{cancelText}}</div>
            </div>
        </div>
    </div>
</div>  
</template>

<script>
let $vm = null;

export default {
    name: "alert",
    install(Vue) {
        Vue.component("alert", this)

        /**
         *  
         * @param  {String}  options.title          [标题，默认为'']
         * @param  {String}  options.content        [内容，默认为'', 如果不传，弹窗不会显示]
         * @param  {String}  options.confirmText    [确认按钮文案，默认为 '确认']
         * @param  {String}  options.cancelText     [取消按钮文案，默认为 '取消']
         * @param  {Boolean} options.cancel         [是否显示取消按钮，默认为 true]
         * @param  {[type]}  options.onClickConfirm [点击确认按钮回调，默认undefined]
         * @param  {[type]}  options.onClickCancel  [点击取消按钮回调，默认undefined]
         * @return {[type]}                         [description]
         */
        Vue.prototype.$alert = ({ title='', content='', cancel=true, onClickConfirm=undefined, onClickCancel=undefined, confirmText='确认', cancelText='取消' }) => {
            $vm.title = title || ''
            $vm.content = content || ''
            $vm.cancel = !!cancel
            $vm.confirmText = confirmText || '确认'
            $vm.cancelText = cancelText || '取消'
            $vm.onClickConfirm = onClickConfirm
            $vm.onClickCancel = onClickCancel
            if($vm.content || $vm.title){
                $vm.show = true
            }
            return $vm;
        };
    },
    data() {
        $vm = this;
        return {
            show: false,
            title: '普通弹框',
            content: '告知当前状态，信息和解决方法',
            confirmText: '确认',
            cancelText: '取消',
            cancel: true,
            fadeIn: false,
            onClickConfirm: undefined,
            onClickCancel: undefined,
        };
    },
    // watch: {
    //     show(newVal){
    //         setTimeout(() => {
    //             this.fadeIn = newVal
    //         })
    //     },
    // },
    methods: {
        handleConfirm(){
            this.show = false;
            if(typeof this.onClickConfirm==='function'){
                this.onClickConfirm()
                this.onClickConfirm = undefined
            }
        },
        handleCancel(){
            this.show = false;
            if(typeof this.onClickCancel==='function'){
                this.onClickCancel()
                this.onClickCancel = undefined
            }
        }
    },
};
</script>



<style lang="scss" scoped>
[data-dpr="2"]{
    .ALERT{
        .ctn{
            .btns{
                border-top: 0.5px solid #e6e6e6; /* no */
                .cancel{
                    border-left: 0.5px solid #e6e6e6; /* no */
                }
            }
        }
    }
}
[data-dpr="3"]{
    .ALERT{
        .ctn{
            .btns{
                border-top: 0.3333px solid #e6e6e6; /* no */
                .cancel{
                    border-left: 0.3333px solid #e6e6e6; /* no */
                }
            }
        }
    }
}
.ALERT{
    position: fixed;
    z-index: 999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background:rgba(0,0,0,0.5);
    transition: opacity .5s;
    .box{
        position: absolute;
        width: 560px;
        z-index: 2;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .ctn{
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        .top{
            min-height: 190px;
            padding: 50px 50px 30px;
        }
        .title{
            font-size:36px;
            color: #221C33;
            line-height:50px;
            & + .content{
                margin-top: 18px;
            }
        }
        .content{
            font-size:30px;
            color: #B5B4B8;
            line-height:42px;
        }
        .btns{
            height: 100px;
            font-size: 36px;
            color: #007AFF;
            border-top: 1px solid #e6e6e6; /* no */
            .confirm,.cancel{
                height: 100%;
                &:active{
                    background-color: #fcfcfc;
                }
            }
            .cancel{
                border-left: 1px solid #e6e6e6; /* no */
            }
        }
    }
}
</style>