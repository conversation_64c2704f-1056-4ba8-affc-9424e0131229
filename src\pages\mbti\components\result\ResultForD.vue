<template>
    <div class="result-for-d">
        <!--<div
            class="match"
            v-if="single === 1"
        >
            <div class="title">
                <span>珍爱网APP提供</span>
                <span>[{{ result.type }}]型最佳爱情配对类型为</span>
                <span>{{ result.matchType }}</span>
            </div>
            &lt;!&ndash; 推人模块 &ndash;&gt;
            <div class="people">
                <div class="desc">
                    <span></span>{{
                        result.matchTypeList[currentIndex] &&
                            result.matchTypeList[currentIndex].name
                    }}[{{
                        result.matchTypeList[currentIndex] &&
                            result.matchTypeList[currentIndex].type
                    }}]
                </div>
                <div class="avatar">
                    <img
                        v-if="currentModel && currentModel.mainImg"
                        :src="
                            `${currentModel.mainImg}`
                        "
                    />
                    <div>
                        <img
                            v-if="currentModel && currentModel.momentImg1"
                            :src="
                                `${currentModel.momentImg1}`
                            "
                        />
                        <img
                            v-if="currentModel && currentModel.momentImg2"
                            :src="
                                `${currentModel.momentImg2}`
                            "
                        />
                    </div>
                    <span class="tag">当前APP在线</span>
                </div>
                <div class="info">
                    <div>{{ currentModel && currentModel.name }}</div>
                    <div>
                        <img
                            src="https://photo.zastatic.com/images/common-cms/it/20220606/1654496113842_419193_t.png"
                        />已实名
                    </div>
                    <div>已购车</div>
                    <div>已购房</div>
                </div>
                <div class="detail">
                    <div>
                        年龄: <span>{{ currentModel && currentModel.age }}岁</span>
                    </div>
                    <div>
                        工作地: <span>{{ currentModel && currentModel.workCityString }}</span>
                    </div>
                    <div>
                        学历: <span>{{ currentModel && currentModel.educationString }}</span>
                    </div>
                    <div>
                        手机号:
                        <span>{{ currentModel && currentModel.phone }}</span><span
                            @click="handleView('phone')"
                        >查看完整手机号</span>
                    </div>
                    <div>
                        微信号:
                        <span>{{ currentModel && currentModel.weChat }}</span><span @click="handleView('wx')">查看完整微信号</span>
                    </div>
                </div>
                <div class="button">
                    <div
                        @click="handleSelect('last')"
                        :class="{ 'button-disabled': currentIndex === 0 }"
                    >
                        上一个
                    </div>
                    <div @click="handleInvite">
                        <span></span>邀请Ta约会
                    </div>
                    <div
                        @click="handleSelect('next')"
                        :class="{ 'button-disabled': currentIndex === 11 }"
                    >
                        下一个
                    </div>
                </div>
            </div>
        </div>-->
        <!-- 爱情盲点 -->
        <div
            class="features"
            :class="{'features-no-single': single === 2}"
        >
            <common-title>{{ this.isShareResult ? 'Ta' : '你' }}的爱情盲点</common-title>
            <div class="advantage">
                <div><span></span>优势</div>
                <div>
                    {{ this.isShareResult? result.advantage.replace(/你/g, 'Ta'): result.advantage }}
                </div>
            </div>
            <div class="disadvantage">
                <div><span></span>盲点</div>
                <div>
                    {{ this.isShareResult? result.inferiority.replace(/你/g, 'Ta'): result.inferiority }}
                </div>
            </div>
            <div
                class="proposal"
                v-if="single === 1"
            >
                <div>
                    珍爱红娘锦囊
                </div>
                <div>
                    {{ result.proposal }}
                </div>
                <img
                    src="https://photo.zastatic.com/images/common-cms/it/20220701/1656645690452_411867_t.png"
                    alt="锦囊"
                />
                <div>恭喜你收到1个珍爱官方的 <span>升级锦囊</span></div>
                <main-button @click.native="handleOpen">
                    打开看看
                </main-button>
            </div>
        </div>
        <!-- 爱情课程 -->
        <!--<div
            class="course"
            v-if="single === 1"
        >
            <common-title>
                限时赠送<span class="profess">&nbsp;专业版脱单&nbsp;</span>爱情课程
            </common-title>
            <div>
                免费领取倒计时：<span>{{ timeText }}</span>
            </div>
            <div>{{ result.gender === 1 ? '怎样判断他是不是真心喜欢我？' : '《8个两性交往技巧，让她情不自禁爱上你》' }}</div>
            <ul>
                <li><span></span>售价：<span>限时免费赠送</span></li>
                <li>
                    <span></span>适合用户：专属<span>[{{ result.type }}]型</span>用户的脱单课程
                </li>
                <li><span></span>如何领取：下载珍爱APP即可自动获赠课程</li>
                <li><span></span>如何学习：在珍爱APP内学习课程</li>
            </ul>
            <main-button @click.native="goGetClass">
                立即领取课程
            </main-button>
        </div>-->

        <!-- 邀请弹窗 -->
        <invite-modal
            v-model="modalVisible.inviteModal"
            :current-model="currentModel"
        />
        <!-- 微信号手机号弹窗 -->
        <wechat-phone-modal
            v-model="modalVisible.wechatPhoneModal"
            :is-wechat="isWechat"
        />
        <!-- 镜囊弹窗 -->
        <proposal-modal v-model="modalVisible.proposalModal" />
        <!-- 课程弹窗 -->
        <class-modal
            v-model="modalVisible.classModal"
            :timeText="timeText"
        />
    </div>
</template>

<script>
import MainButton from "../MainButton.vue";
import CommonTitle from "../CommonTitle.vue";
import InviteModal from "../modal/InviteModal.vue";
import WechatPhoneModal from "../modal/WechatPhoneModal.vue";
import ClassModal from "../modal/ClassModal.vue";
import ProposalModal from "../modal/ProposalModal.vue";
import { storage } from "../../lib/utils.js";
import { Toast } from "vant";
import { mapState } from "vuex";

import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";

import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";

export default {
    name: "ResultForD",
    components: {
        MainButton,
        CommonTitle,
        InviteModal,
        WechatPhoneModal,
        ClassModal,
        ProposalModal
    },
    computed: {
        ...mapState(["cmsConfig", "resourceKey"]),
        currentModel() {
            return this.result.modelList && this.result.modelList[this.currentIndex];
        },
        isShareResult() {
            return Z.getParam("from") === "share" && Z.getParam("type");
        }
    },
    props: {
        result: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            currentIndex: 0,
            modalVisible: {
                inviteModal: false,
                wechatPhoneModal: false,
                proposalModal: false,
                classModal: false
            },
            isWechat: false,
            timeDelay: 30 * 60,
            timeText: "30分00秒",
            single: +Z.getParam("single") || storage.getItem("single"),
            isToutiaoIos: storage.getItem("isToutiaoIos")
        };
    },
    mounted() {
        setInterval(() => {
            if (this.timeDelay > 0) {
                this.timeDelay--;
                let m = Math.floor(this.timeDelay / 60);
                let s = Math.floor(this.timeDelay % 60);
                this.timeText = `${m}分${s < 10 ? "0" + s : s}秒`;
            }
        }, 1000);
    },
    methods: {
        handleSelect(type) {
            if (type === "next") {
                this.$reportKibana(
                    this.resourceKey,
                    95,
                    "报告页-查看下个按钮点击"
                );
                this.currentIndex === 11
                    ? (this.currentIndex = 11)
                    : this.currentIndex++;
            }

            if (type === "last") {
                this.$reportKibana(
                    this.resourceKey,
                    95,
                    "报告页-查看上个按钮点击"
                );
                this.currentIndex === 0
                    ? (this.currentIndex = 0)
                    : this.currentIndex--;
            }
        },

        handleView(type) {
            this.isWechat = type === "wx";
            this.$reportKibana(
                this.resourceKey,
                type === "wx" ? 95 : 96,
                type === "wx"
                    ? "报告页-查看微信号按钮点击"
                    : "报告页-查看手机号按钮点击"
            );
            this.handleGoDownload("wechatPhoneModal");
        },

        handleGoDownload(key) {
            const { downloadStatus } = this.cmsConfig;
            if (this.$parent.expertJumpULoveApplet) {
                location.href = this.$parent.youlianLink
                return false
            }
            if (downloadStatus === 1) {

                visibilityChangeDelay(function() {
                    visibilityChangeDelay(function() {
                        if (storage.getItem("isToutiaoIos")) {
                            CommonDownloadGuideModalV2({value: true});
                        } else {
                            Toast({
                                message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                                duration: 5000
                            });
                            downloadApp();
                        }
                    }, 500);
                }, 500);
                openApp();
                return;
            }
            this.modalVisible[key] = true;
        },

        handleInvite() {
            this.$reportKibana(this.resourceKey, 95, "报告页-邀请约会按钮点击");
            this.handleGoDownload("inviteModal");
        },

        goGetClass() {
            this.$reportKibana(this.resourceKey, 97, "报告页-领取课程按钮点击");
            this.handleGoDownload("classModal");
        },

        handleOpen() {
            this.$reportKibana(this.resourceKey, 97, "报告页-打开锦囊按钮点击");
            this.handleGoDownload("proposalModal");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "~@/common/styles/common.scss";

.result-for-d {
    .match {
        position: relative;
        width: 686px;
        min-height: 1170px;
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        border-radius: 0 0 32px 32px;
        margin-top: 48px;
        padding-bottom: 32px;
        .title {
            position: relative;
            min-height: 180px;
            width: 686px;
            padding-bottom: 24px;
            > span:nth-child(1) {
                display: inline-block;
                width: 224px;
                height: 52px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656579116549_561316_t.png)
                    no-repeat;
                background-size: 100% 100%;
                font-size: 26px;
                color: #ffffff;
                font-weight: 400;
                @include flex-center(row, center, center);
            }
            > span:nth-child(2) {
                font-weight: 400;
                font-size: 28px;
                color: #ffffff;
                margin-left: 28px;
                display: inline-block;
            }
            > span:nth-child(3) {
                display: inline-block;
                color: #5ad9ff;
                font-weight: 600;
                font-size: 32px;
                margin-left: 28px;
                margin-top: 20px;
                line-height: 50px;
                > span {
                    color: #ffffff;
                    font-weight: 400;
                }
            }
        }

        .title:before {
            content: "";
            position: absolute;
            left: 66px;
            top: 66px;
            right: 66px;
            bottom: 66px;
            border-image-source: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656587225083_482345_t.png); // 图片路径
            border-image-slice: 66 66 66 66 fill; // 每个区域截取宽度为 10px
            border-image-width: 66px 66px 66px 66px; // 设置各个区域的图片宽度
            border-image-repeat: no-repeat; // 图片重复或拉伸模式
            border-image-outset: 66px 66px 66px 66px; //
        }

        .people {
            margin-top: 24px;
            .desc {
                font-weight: 500;
                font-size: 34px;
                color: #ffffff;
                margin-top: 40px;
                > span {
                    display: inline-block;
                    width: 38px;
                    height: 34px;
                    background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577029972_719710_t.png)
                        no-repeat;
                    background-size: 100% 100%;
                    margin-left: 28px;
                    margin-right: 12px;
                    position: relative;
                    top: 10px;
                }
            }
            .avatar {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: 24px 20px 38px;
                position: relative;
                > img:nth-child(1) {
                    width: 428px;
                    height: 428px;
                    border-radius: 16px 0 0 16px;
                    object-fit: cover;
                }
                > span {
                    position: absolute;
                    display: inline-block;
                }
                > div:nth-child(2) {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    img {
                        width: 210px;
                        height: 210px;
                        border-radius: 0 16px 16px 0;
                        object-fit: cover;
                    }
                }
                .tag {
                    position: absolute;
                    display: inline-block;
                    width: 224px;
                    height: 62px;
                    top: 390px;
                    left: 20px;
                    background: url("https://photo.zastatic.com/images/common-cms/it/20220701/1656661210391_333188_t.png")
                        no-repeat;
                    background-size: 100% 100%;
                    font-weight: 500;
                    font-size: 26px;
                    color: #ffffff;
                    @include flex-center(row, center, center);
                }
            }

            .info {
                padding: 0px 20px;
                display: flex;
                > div:nth-child(1) {
                    font-weight: 600;
                    font-size: 40px;
                    color: #ffffff;
                    margin-left: 8px;
                }
                > div:nth-child(2) {
                    width: 108px;
                    height: 40px;
                    font-size: 20px;
                    color: #ffffff;
                    position: relative;
                    background: rgba(142, 253, 255, 0.28);
                    line-height: 40px;
                    border-radius: 8px;
                    padding-left: 40px;
                    margin-right: 8px;
                    margin-left: 24px;
                    img {
                        width: 24px;
                        height: 24px;
                        position: absolute;
                        left: 8px;
                        top: 6px;
                    }
                }
                > div:nth-child(3),
                > div:nth-child(4) {
                    width: 72px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 20px;
                    border-radius: 8px;
                    color: #ffffff;
                    margin-right: 8px;
                    background: rgba(142, 253, 255, 0.28);
                }
            }

            .detail {
                margin-top: 32px;
                margin-bottom: 68px;
                padding: 0 28px;
                font-weight: 400;
                font-size: 26px;
                color: #c9c8ce;
                > div {
                    margin-bottom: 32px;
                    > span {
                            color: #ffffff;
                            font-size: 28px;
                    }
                }
                > div:nth-child(4),
                > div:nth-child(5) {
                    > span:nth-child(1) {
                        font-size: 30px;
                        color: #ffffff;
                        display: inline-block;
                        width: 180px;
                    }
                    > span:nth-child(2) {
                        font-size: 30px;
                        color: #5ad9ff;
                        text-decoration: underline;
                        padding-left: 16px;
                    }
                }
                > div:nth-child(5) {
                    margin-bottom: 0px;
                }
            }

            .button {
                padding: 0 20px;
                display: flex;
                justify-content: space-between;
                > div {
                    @include flex-center(row, center, center);
                }
                > div:nth-child(1),
                > div:nth-child(3) {
                    width: 150px;
                    height: 100px;
                    font-weight: 500;
                    font-size: 28px;
                    color: #484663;
                    background: #ffffff;
                    border-radius: 50px;
                }
                > div:nth-child(2) {
                    span {
                        display: inline-block;
                        width: 42.54px;
                        height: 36px;
                        margin-right: 16.74px;
                        background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656932237934_881238_t.png)
                            no-repeat;
                        background-size: 100% 100%;
                    }
                    width: 314px;
                    height: 100px;
                    background: url(https://photo.zastatic.com/images/common-cms/it/20220701/1656642943579_594191_t.png)
                        no-repeat;
                    background-size: 100% 100%;
                    font-weight: 500;
                    font-size: 34px;
                    color: #ffffff;
                }
                &-disabled {
                    opacity: 0.5;
                }
            }
        }
    }

    .features {
        margin-top: 48px;
        border-radius: 0 0 32px 32px;
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        &-no-single {
            margin-bottom: 48px;
            padding-bottom: 32px;
        }
        .advantage,
        .disadvantage {
            margin-top: 40px;
            padding: 0 28px;
            > div:nth-child(1) {
                font-weight: 500;
                font-size: 34px;
                color: #ffffff;
                span {
                    width: 38px;
                    height: 34px;
                    display: inline-block;
                    background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577698934_433293_t.png)
                        no-repeat;
                    background-size: 100% 100%;
                    position: relative;
                    top: 10px;
                    margin-right: 12px;
                }
            }
            > div:nth-child(2) {
                font-weight: 400;
                font-size: 30px;
                color: #ffffff;
                margin-top: 24px;
                line-height: 44px;
            }
        }
        .proposal {
            padding: 0 28px 33px;
            margin-top: 40px;
            > div:nth-child(1) {
                width: 472px;
                height: 72px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220701/1656646893132_503954_t.png)
                    no-repeat;
                background-size: 100% 100%;
                font-weight: 500;
                font-size: 34px;
                color: #ffffff;
                padding-left: 78px;
                @include flex-center(row, null, center);
            }
            > div:nth-child(2) {
                font-weight: 400;
                font-size: 30px;
                color: #ffffff;
                line-height: 44px;
                margin-bottom: 40px;
                margin-top: 24px;
            }
            img {
                width: 296px;
                height: 218px;
                position: relative;
                left: 176px;
            }
            > div:nth-child(4) {
                width: 100%;
                text-align: center;
                // width: 442px;
                margin: 32px 0px 40px;
                font-weight: 400;
                font-size: 28px;
                color: #ffffff;

                span {
                    color: #5ad9ff;
                    font-weight: 600;
                }
            }
        }
    }

    .course {
        background-image: linear-gradient(
            0deg,
            rgba(119, 148, 255, 0.35) 0%,
            rgba(83, 110, 255, 0) 100%
        );
        border-radius: 0 0 32px 32px;
        margin-top: 48px;
        margin-bottom: 44px;
        padding-bottom: 33px;
        .profess {
            color: #5ad9ff;
            font-weight: 500;
        }
        > div:nth-child(2) {
            margin: 32px auto 40px;
            font-weight: 500;
            font-size: 26px;
            color: #ffffff;
            width: 400px;
            height: 62px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220701/1656647580659_58729_t.png)
                no-repeat;
            background-size: 100% 100%;
            @include flex-center(row, null, center);
            padding-left: 28px;
            > span {
                color: #ff55a3;
                font-size: 32px;
                font-weight: 500;
            }
        }
        > div:nth-child(3) {
            font-weight: 500;
            font-size: 34px;
            color: #ffffff;
            margin-left: 10px;
            margin-bottom: 33px;
        }
        > ul:nth-child(4) {
            padding-left: 24px;
            > li {
                font-weight: 400;
                font-size: 32px;
                color: #ffffff;
                margin-bottom: 26px;
                > span:nth-child(1) {
                    border-radius: 50%;
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    margin-right: 16px;
                    background-image: linear-gradient(
                        129deg,
                        #6487fe 9%,
                        #d694ff 92%
                    );
                }
                > span:nth-child(2) {
                    color: #5ad9ff;
                    font-weight: 500;
                }
            }
            > li:nth-child(4) {
                margin-bottom: 40px;
            }
        }
    }
}
</style>
