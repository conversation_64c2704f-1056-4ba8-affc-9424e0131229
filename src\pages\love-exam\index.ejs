<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>恋爱段位鉴定考试</title>
    <%= require('./templates/adaptation2.html') %>
    <script>
        var cssStart = new Date();
        var AD_URL = window.encodeURIComponent(window.location.href);
        localStorage.setItem('AD_URL', AD_URL);
        window.getParam = function (name, url) {
            var u = (url || location.href).split('#')[0] // 去掉hash
            var r = new RegExp('(\\?|&)' + name + '=(.*?)(#|&|$)', 'i')
            var m = u.match(r)
            return decodeURIComponent(m ? m[2] : '')
        }
        function filterXSS(e) {
            if (!e) return e;

            for (; e != decodeURIComponent(e);) {
                e = decodeURIComponent(e);
            }
            var r = ['<', '>', "'", '"', '%3c', '%3e', '%27', '%22', '%253c', '%253e', '%2527', '%2522'],
                n = ['&#x3c;', '&#x3e;', '&#x27;', '&#x22;', '%26%23x3c%3B', '%26%23x3e%3B', '%26%23x27%3B', '%26%23x22%3B', '%2526%2523x3c%253B', '%2526%2523x3e%253B', '%2526%2523x27%253B', '%2526%2523x22%253B'];
            for (var i = 0; i < r.length; i++) {
                e = e.replace(new RegExp(r[i], 'gi'), n[i]);
            }
            return e;
        };
        function random(m, n){
            return m + Math.floor( Math.random()*(n-m+1) );
        }
        var oCookie = {
          set: function (name, value, domain, path, hour){
                var expire = null;

                if (hour) {
                    expire = new Date();
                    expire.setTime(expire.getTime() + 3600000 * hour);
                }

                document.cookie = name + '=' + encodeURIComponent(value) + '; ' +
                    (expire ? ('expires=' + expire.toGMTString() + ';') : '') +
                    'domain=' + (domain || location.hostname) + ';' +
                    'path=' + (path || '/') + ';';
            },
            get: function (name){
                var cookieV = document.cookie.match(RegExp('(^|;\\s*)' + name + '=([^;]*)(;|$)'));
                return filterXSS(cookieV ? decodeURIComponent(cookieV[2]) : '');
            }
        };
    </script>

</head>
<body>
    <div id="app"></div>
    <style>
        html {
          touch-action: none;
          touch-action: pan-y;
        }

      </style>

    <!-- 老注册页OCPX相关 -->
    <script>
        // 新增的百度ocpc代码
        (function () {
            var channelId = getParam("channelId");
            window._agl = [];
            function addOcpc(userId, channelId){
                window._agl.push(
                    ['production', '_f7L2XwGXjyszb4d1e2oxPybgD'],
                    ['ext', {
                        userid: userId,
                        url: 'https://i.zhenai.com/m/portal/register/index.html?channelId='+ channelId +'&subChannelId=#step2Page'
                    }]
                );
                var agl = document.createElement('script');
                agl.type='text/javascript';
                agl.async = true;
                agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(agl, s);
            }

            try{
                var ocpcMap = { '904845': 23640138, '905530': 24438410, '905531': 24438410, '905532': 24438410, '905533': 24438410, '905534': 24438410, '905535': 24721770, '905536': 24721770, '905537': 24721770, '905538': 24721770, '905539': 24721770 };
                ocpcMap['904842'] = 23640138;
                var userId = ocpcMap[channelId];
                if(userId){
                    addOcpc(userId, channelId);
                }else{
                    (function () {
                        window._agl.push(
                            ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
                        );
                        (function () {
                            var agl = document.createElement('script');
                            agl.type='text/javascript';
                            agl.async = true;
                            agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js';
                            var s = document.getElementsByTagName('script')[0];
                            s.parentNode.insertBefore(agl, s);
                        })();
                    })();
                }
            }catch(e){
                console.log(e);
            }
        })();
    </script>

    <script>
        // 接入小米ocpc
        ;(function () {
            var channelId = getParam("channelId");
            if(['909457','909458','910105','910106','910107','910108','910109'].indexOf(channelId)!==-1){
                !function(e,c){var r=e.mi_tracker=e.mi_tracker||[];r.loaded||(r.log=function(){r.push(arguments)},r.load=function(){var e=c.getElementsByTagName("script")[0],t=c.createElement("script");t.async=!0,t.src="//cdn.cnbj1.fds.api.mi-img.com/prd-static/mi_tracker/1.0.0/sdk.js",e.parentNode.insertBefore(t,e),r.loaded=!0},r.load(),r.log("view",{conversionId: "627"}))}(window,document);
            }
        })();

        // 接入网易ocpc
        ;(function () {
            var channelId = getParam("channelId");
            if(['910743','914463'].indexOf(channelId)!==-1){
                var ns = document.createElement('script');ns.type = 'text/javascript';ns.async = true;
                ns.src = 'https://163h5.nos-jd.163yun.com/h5/libs/analyze.js';
                var f = document.getElementsByTagName('script')[0];f.parentNode.insertBefore(ns,f);
            }
        })();

        // 接入微博ocpc
        (function(root) {
            var channelId = getParam("channelId");
            if(['913271','913272','913273','913274','913275'].indexOf(channelId)===-1){
                return;
            }
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.crossOrigin = 'anonymous';
            script.src = 'https://cheka.biz.weibo.com/index';
            script.onerror = function() {
              var request = new XMLHttpRequest();
              var web_url = window.encodeURIComponent(window.location.href);
              var url = 'https://cheka.biz.weibo.com/v1/error';
              var data = {
                "error": {
                  "url": web_url,
                  "message": "404",
                  "name": "__SDK_CDN__",
                  "detail": {}
                }
              }
              request.open('POST', url, true);
              request.send('param=' + encodeURIComponent(data));
            }
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(script, s);
        })(window);

        // 接入快手ocpc
        (function(root) {
            var channelId = getParam("channelId");
            if(channelId!='914334'){
                return;
            }
            var ksscript = document.createElement('script'); ksscript.type = 'text/javascript'; ksscript.async = true;
            ksscript.src = 'https://static.yximgs.com/udata/pkg/ks-ad-trace-sdk/ks-trace.0.0.8.beta.js';
            ksscript.onerror = function () {
                var request = new XMLHttpRequest();
                var cb = (function(t){var e=new RegExp("(^|&)"+t+"=([^&|#]*)(&|#|$)"),n=window.location.href.indexOf('?')>-1?window.location.href.split('?')[1].match(e):null;return null!=n?decodeURIComponent(n[2]):null}('callback'));
                var url = 'https://e.kuaishou.com/rest/log/activate?jsEventType=3&callback=' + cb + '&eventType=4&eventTime=' + Date.now();
                request.open('GET', url, true);
                request.send(null);
            }
            var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ksscript, s);
        })(window);



        // 接入vivo ocpc
        (function(root) {
            var channelId = getParam("channelId");
            if(channelId!='914418'){
                return;
            }
            var script = document.createElement('script'); script.type = 'text/javascript'; script.async = true;
            script.src = 'https://sspstatic.vivo.com.cn/ssp/js/vadevent.1.0.0.js';
            var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(script, s);
        })(window);
    </script>

    <% if (htmlWebpackPlugin.options.env === 'production') { %>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vue.min.2.5.21.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vue-router.min.3.5.3.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vuex.min.3.6.2.js"></script>
    <% } else { %>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vue.2.5.21.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vue-router.3.5.3.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/vuex.3.6.2.js"></script>
    <% } %>
    <%= require('@/common/templates/adaptation.html') %>
    <script type="text/javascript" src="//webapi.amap.com/maps?v=1.3&key=5453f47bcc9edb857b93e3aed91ed530"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/zhenai-2.0.6.min.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/svga.min.2.3.2.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/zhenai_hub-1.0.0.min.js"></script>
    <script type="text/javascript" src="//i.zhenai.com/common/m/base/js/web-monitor-1.0.3.min.js"></script>
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script> -->
</body>
</html>
