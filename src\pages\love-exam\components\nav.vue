<template>
    <div
        class="billboard_nav"
    >
        <img
            v-if="showBack"
            @click="goBack"
            class="bak_img"
            src="https://photo.zastatic.com/images/common-cms/it/20240731/1722414363831_87565.png"
            alt=""
        >
    </div>
</template>

<script>
export default {
    name: 'NavApp',
    props: {
        title: {
            type: String,
        },
        type: {
            type: String,
            default: 'back',
        },
        showBack: {
            type: Boolean,
            default: true,
        },
    },
    methods: {
        goBack() {
            if (this.type === 'close') {
                Z.client.invoke('ui', 'closeWebView');
            } else if (this.type === 'jump') {
                this.$emit('goBack');
            } else {
                this.$router.go(-1);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.billboard_nav {
    .bak_img {
        width: 122px;
        height: 32px;
    }
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 50px 0px 40px 24px;
  line-height: 36px;
  font-size: 18px;
  color: #26273C;
  position: relative;
  box-sizing: border-box;
  width: 100vw;
  &_arrow {
    position: absolute;
    left: 0;
    width: 36px;
    height: 36px;
    background: url('https://photo.zastatic.com/images/common-cms/it/20240422/1713755789672_475577.png')
        no-repeat;
    background-size: 100% 100%;
  }
  &_ques {


    }
}
</style>
