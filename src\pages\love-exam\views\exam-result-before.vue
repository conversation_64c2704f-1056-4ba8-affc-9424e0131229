<template>
    <div class="exam-result-before">
        <img
            class="top"
            src="https://photo.zastatic.com/images/common-cms/it/20240801/1722480676663_760125.png"
            alt=""
        >
        <div class="result_container">
            <img
                class="bg"
                src="https://photo.zastatic.com/images/common-cms/it/20240801/1722480722269_648766.png"
                alt=""
            >
            <div class="pager">
                <img
                    class="gif"
                    src="https://file.zastatic.com/images/common-cms/it/20240801/1722481623226_518685.gif"
                    alt=""
                >

                <common-submit
                    ref="refCommonSubmit"
                    :page-type="PAGE_TYPE"
                    :style-config="{
                        modalConfig: {
                            confirmButtonColor: '#FFFFFF',
                            confirmButtonBgColor: 'linear-gradient(180deg, #ff9e8b80 4%, #ff5b8780 100%);',
                            cancleButtonColor: '#999999',
                        },
                        protocolConfig: {
                            textColor: '#222833',
                            protocolColor: '#D7204A',
                            protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20240801/1722482717378_921287.png'
                        }
                    }"
                    :handle-after-regisiter="handleJump"
                    :handle-login="handleJump"
                    :handle-cancle="handleCancel"
                    :text-confirm="{
                        overwirteText: '覆盖注册',
                    }"
                >
                    <div
                        class="panel-submit"
                        @click="handleReport"
                    >
                        立即解锁
                    </div>
                </common-submit>
            </div>
        </div>
    </div>
</template>
<script>
import CommonSubmit from '../components/common/CommonSubmit';
import Phone from "../components/RegForm/phone.vue";
import { PAGE_TYPE } from "../config";
import { bindEvaluate } from '../api';
import { storage,session as Session } from "@/common/utils/storage";
import Api from '@/common/server/base';

export default {
    name: "Finish",
    components: {
        Phone,
        CommonSubmit,
    },
    data() {
        return {
            isShow: true,
            PAGE_TYPE,
        };
    },
    created(){

    },
    activated(){
        this.$report(106, '手机验证页访问');
        // 获取当前可视区域的高度  键盘弹起页面图片被压缩 vh被键盘占据了一部分
        let height = document.documentElement.clientHeight;
        window.onresize = () => { // 在页面大小发生变化时调用
            // 把获取到的高度赋值给根div
            document.getElementById('loginMain').style.height = height + 'px';
        };
    },
    methods: {
        async isSingleQR() {

            try {
                const registerForm = storage.getItem(`cachedRegisterForm-${this.PAGE_TYPE}`);
                const params = {
                    workCity: registerForm.workCity,
                    channelId: Z.getParam("channelId"),
                    memberId: Session.getItem('reg_memberid'),
                    pageType: 8024
                };
                const res = await Api.singleShowWechatCode(params);
                if (res.code === 0 && res.data.codeResult) {
                    sessionStorage.setItem('drQwQrCodeUrl', res.data.codeResult.qrCodeUrl);
                    this.$router.push({ path: '/resultSingleQr'});
                } else {
                    sessionStorage.setItem('drQwQrCodeUrl', '');
                    this.$router.push({ path: '/result'});
                }
            } catch (e) {
                sessionStorage.setItem('drQwQrCodeUrl', '');
                this.$router.push({ path: '/result'});
            }

            // sessionStorage.setItem('drQwQrCodeUrl', 'https://photo.zastatic.com/images/common-cms/it/20240509/1715246645033_531308_t.jpg')
        },
        handleJump(resultImg) {
            this.$router.push({ name: 'Result', params:{
                resultImg
            }});


        },
        handleReport(e) {
            this.$report(107, '手机验证页-查看报告按钮点击');
            if(this.$refs.refPhone.inputValue.length !== 11){
                this.$toast("请输入手机号");
                e.stopPropagation();
            }
        },
        //取消
        handleCancel() {

        }
        // handleSubmit() {
        //     // 触发提交手机号逻辑
        //     this.$refs.refCommonSubmit.handleSubmit(false);
        // }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.exam-result-before {
  background: #FFE7E1;
  min-height: 100vh;
  .top {
    width: 100vw;
    position: relative;
    z-index: 9;
  }
}
.result_container {
  position: relative;
  margin-top: -34px;
  padding-bottom: 49px;
  .bg {
    position: absolute;
    top: 0;
    transform: translateY(-30%);
    left: 0;
    width: 100vw;
  }
  .pager {
    position: relative;
    z-index: 2;
    margin: 0 24px;
    height: 1161px;
    background: url('https://photo.zastatic.com/images/common-cms/it/20240801/1722480907939_811598.png') no-repeat;
    background-size: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .gif {
      margin-top: 313px;
      width: 630px;
      height: 308px;
    }
    .panel-phone {
    }
    .panel-submit {
      margin-top: 40px;
      width: 608px;
      height: 100px;
      background-image: linear-gradient(180deg, #FF9E8B 4%, #FF5B87 100%);
      border-radius: 54px;
      font-weight: 600;
      font-size: 32px;
      color: #FFFFFF;
      text-align: center;
      line-height: 100px;
    }
  }
}

.finish{
    // width: 100vw;
    // height: 100vh;
    // background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825084549_711274_t.png');
    // background-repeat: no-repeat;
    // margin:0px;
    // background-size:100% 100%;
    // background-attachment:fixed;
    // display: flex;
    // align-items: center;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    .panel{
        padding: 0 88px;
        width: 99vw;
        height: 83vh;
        background-image: url('https://photo.zastatic.com/images/common-cms/it/20230812/1691825172826_472496_t.png');
        background-repeat: no-repeat;
        margin: 0;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        &-result{
            margin-top: 96px;
            position: relative;
            display: inline-block;

            .title {
                font-weight: 400;
                font-size: 40px;
                color: #03355E;
                height: 56px;
                line-height: 56px;
                margin: 0 auto;
                position: relative;
                z-index: 2;

                &::before{
                    content: '';
                    position: absolute;
                    bottom: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 168px;
                    height: 10px;
                    background-image: linear-gradient(90deg, #99f4ffde 0%, #16abfbab 100%);
                    z-index: -1;
                }
            }

            .rate {
                font-size: 140px;
                font-weight: 700;
                letter-spacing: 0;
                text-align: center;
                background-image: linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF, #6E77FF);
                -webkit-background-clip: text;
                color: transparent;
                position: relative;

                .subfix {
                    font-size: 60px;
                    position: absolute;
                    right: -52px;
                    bottom: 14px;
                    background-image: linear-gradient(-200deg, #31A3E0, #4C94FF);
                    background-image: -webkit-linear-gradient(-200deg, #31A3E0, #4C94FF);
                    -webkit-background-clip: text;
                }
            }
        }

        &-title {

            margin-top: 175px;
            font-weight: 400;
            line-height: 1.2;
            font-family: 'love';
            font-size:52px;
            text-align: left;
            color: #251A34;
            letter-spacing: 8px;
            margin-bottom: 130px;

        }



        &-submit{
            margin: 32px auto 0;
            width: 550px;
            height: 100px;
            box-shadow: 0 4px 28px 0 rgba(30,151,226,0.22);
            border-radius: 55px;
            font-weight: 600;
            font-size: 32px;
            color: #FFFFFF;
            text-align: center;
            line-height: 100px;
            background: linear-gradient(180deg,#5243FE, #9A55F0);

        }
    }

}
</style>
