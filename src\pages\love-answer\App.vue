<template>
    <div id="app"  :class="{app:name !== 'Result', appresult: name === 'Result',  appresultsingle:  name === 'resultSingle' || name === 'resultSingleQr'}">
        <div :class="{top:true, hidden_mis: !isShowLeft}">

            <img
                class="panel-back panel-back-call"
                src="https://photo.zastatic.com/images/common-cms/it/20230814/1691987255408_865956_t.png"
                v-show="isShowLeft"
                @click="goback"
            ></img>
            <Player
                :musicUrl="'https://file.zastatic.com/images/common-cms/it/20230815/1692083231229_666714.mp3'"
                :class="{music:true}"
                v-if="isShowRight"
                ref="PlayerMusic"
            />
        </div>
        <keep-alive exclude="['Register','Result']">
            <router-view
                :key="$route.fullPath"
                v-if="canRender"
            />
        </keep-alive>
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';
import Player from './components/Player.vue';
export default createRoot({
    name: "App",
    components:{
        Player
    },
    data() {
        return {
            canRender: false,
            isShowLeft: false,
            isShowRight: true,
            name: '',
            to: null
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    created() {
        this.routeChange(this.$route);
        this.$router.beforeEach((to, from, next) => {
            this.to = to;
            this.routeChange(to);
            next();
        });
        // this.preloadImg();


    },
    mounted() {
        judgeIfInToutiaoIos();
        this.initAsync();
    },
    methods: {
        async initAsync() {
            // 处理异步
            this.canRender = true;
        },
        routeChange(to) {

            this.name = to.name;
            switch(to.name){
            case 'quiz':
                this.isShowLeft = false;
                this.isShowRight = true;
                break;
            case 'TestResult':
                this.isShowLeft = true;
                this.isShowRight = true;
                break;
            case 'IsSingle':
                this.isShowLeft = true;
                this.isShowRight = true;
                break;
            case 'Register':
                this.isShowLeft = true;
                this.isShowRight = true;
                break;
            case 'finishV2':
                this.isShowLeft = true;
                this.isShowRight = true;
                break;
            case 'Result':
                this.isShowLeft = false;
                this.isShowRight = false;
                break;
            }
        },
        goback() {
            this.$router.back();
        },
        // preloadImg() {
        //     let image = new Image();
        //     image.src ="./assets/images/f.svga";
        // },
    }
});
</script>

<style lang="scss">
// 全局样式
html,body{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    width: 100%;
    height: 100%;
}
</style>
<style scoped>
.app{
    position: fixed;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* min-width: 1000px; */
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-position: center 0;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230816/1692156387146_431306_t.jpg');
    overflow: hidden;

}
.appresult{
    position: relative;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230811/1691745433928_1320_t.png');
    background-size: contain;
    background-repeat: no-repeat;
    width: 100vw;
    min-height: 2930px;
    background-color: red;
    background-color: #1e1441;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    align-items: center;
}
.appresultsingle {
    position: relative;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230811/1691745433928_1320_t.png');
    background-size: contain;
    background-repeat: no-repeat;
    width: 100vw;
    min-height: 100vh;
    background-color: red;
    background-color: #1e1441;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    align-items: center;
}
 .top{
        position: fixed;
        top: 0px;
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 25px 0 25px;
    }
    .hidden_mis{
        /* justify-content: flex-end; */
        width: auto;
        right: 0;
        z-index: 100;
    }
    .panel-back{
        width: 147px;
        height: 56px;

    }
    .panel-back-call{
        width: 34px;
        height: 54px;
    }
    .music{
        width: 74px;
        height: 74px;
    }
</style>
