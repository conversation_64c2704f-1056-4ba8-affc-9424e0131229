import { PROTOCOL } from "@/common/js/const.js";
import { session } from '@/common/utils/storage.js';
import { isDef, getType, strMatchLists, getUrlListsParams } from './ocpx_util';
import config from '../config/config'
const domain = config.API_HOST_NAME()
import {reportKibana} from '@/common/utils/report.js';

/**
 * 返回clickID，值是url
 * @param paramList {Array}   参数列表
 * @param AD_URL {String}   url字符
 * @returns {{}}
 */
function getClickIdUrl(paramList, AD_URL) {
    var params = {};
    if (getType(paramList) === 'Array' && getType(AD_URL) === 'String') {
        // 需要返回媒体的落地页链接
        var paramsResult = getUrlListsParams(paramList);
        if (!strMatchLists(AD_URL, paramList) && paramsResult.match) {
            AD_URL = Z.addParam(paramsResult.data, AD_URL);
        }
        if (strMatchLists(AD_URL, paramList)) {
            params = {
                clickId: encodeURIComponent(AD_URL),
            };
        }
    }
    return params;
}

/**
 * 根据url的参数获取clickID
 * @param clickIDParam
 * @returns {{}}
 */
function getClickIDParam(clickIDParam) {
    var params = {};
    // 只需要返回媒体的单个标识字段
    if (getType(clickIDParam) === 'String') {
        var paramValue = Z.getParam(clickIDParam);
        if (paramValue) {
            params = {
                clickId: paramValue
            };
        }
    }
    return params;
}

export function ocpxApi(ocpxConfig) {
    return function (appointConvertId) {
        var params = {};
        var convertId = appointConvertId ? appointConvertId : ocpxConfig.convertId; // 转化类型
        var mediaType = ocpxConfig.mediaType; // 媒体
        var AD_URL = ocpxConfig.adUrl;
        try {
            reportKibana("钩子h5", 443, "OCPX前端上报进来", {
                ext25: convertId,
                ext26: mediaType,
                ext27: session.getItem('reg_memberid'),
                ext28: localStorage.getItem('AD_URL')
            });
        } catch(err) {}
        switch (mediaType) {
        case 'xiaohongshu':
            params = {
                // clickId: Z.getParam('trackId') || ''
                clickId: localStorage.getItem('AD_URL')
            };
            break;
        case 'uc':
            params = getClickIdUrl(['uctrackid'], AD_URL);
            break;
        case 'baidu':
            params = getClickIdUrl(['bd_vid'], AD_URL);
            break;
        case 'tencent':
            params = {
                clickId: Z.getParam('qz_gdt') || Z.getParam('gdt_vid'),
            };
            break;
        case 'wechat':
            params = {
                clickId: Z.getParam('qz_gdt') || Z.getParam('gdt_vid'),
            };
            break;
        case 'toutiao':
            params = getClickIdUrl(['clickid', 'adid', 'creativeid', 'creativetype'], AD_URL);
            break;
        case 'qihu_360':
            params = getClickIDParam('qhclickid');
            break;
        case 'sogou':
            params = getClickIdUrl(['sg_vid'], AD_URL);
            break;
        case 'weibo':
            params = getClickIDParam('mark_id');
            break;
        case 'huawei':
            params = getClickIdUrl([], AD_URL);
            break;
        case 'oppo':
            params = getClickIdUrl(['pageId', 'tid', 'lbid'], AD_URL);
            break;
        case 'zhihu':
            params = getClickIDParam('cb');
            break;
        case 'qihu_360_information_flow':
            params = getClickIDParam('impression_id');
            break;
        case 'kuaishou':
            params = getClickIDParam('callback');
            break;
        default:
            AD_URL = localStorage.getItem('AD_URL');
            params = {clickId:AD_URL};
        }
        params = Object.assign({}, params, {
            mediaType: mediaType,
            convertId: convertId,
            memberID: session.getItem('reg_memberid')
        });
        var verifyReport = isDef(params.clickId) && isDef(params.mediaType) && isDef(params.convertId);
        if (verifyReport) {
            Z.ajax({
                url: PROTOCOL + `//${domain}/api/common/ad/account/report/registeredH5Report`,
                type: 'POST',
                data: params,
                success: function (res) {
                },
                error: function (xhr, textStatus, errorThrown) {
                }
            });
        }
        return verifyReport;
    };
}

