<template>
    <div
        class="quiz"
    >
        <van-progress
            style="position: absolute;width: 100vw"
            :percentage="percentage"
            :show-pivot="false"
            stroke-width="6"
            color="#FF6688"
            track-color="#ffe7e1"
        />
        <navApp
            :showBack="currentIndex !== 0"
            title="2024年恋爱段位考试"
            type="jump"
            @goBack="goBack"
        />
        <div class="panel">
            <img
                src="https://photo.zastatic.com/images/common-cms/it/20240731/1722414148917_128511.png"
                class="panel_icon"
                alt=""
            >
            <div :class="{'panel-title':true, bottomtext: currentIndex === 0 ? true : false}">
                {{ curQuiz.title }}
            </div>

            <!-- 动态组件 -->
            <component
                :is="curComp"
                :index="currentIndex"
                :list="curQuiz"
                @go-next="goNext"
            />
        </div>
    </div>
</template>

<script>
import { QUIZ_LIST_REGISTER } from '../config';
import QuizItem from '../components/QuizItem.vue';
import Gender from "../components/RegForm/gender.vue";
import Birthday from "../components/RegForm/birthday.vue";
import Education from "../components/RegForm/education.vue";
import Marriage from "../components/RegForm/marriage.vue";
import Salary from "../components/RegForm/salary.vue";
import WorkCity from "../components/RegForm/workcity.vue";
import Height from "../components/RegForm/Height.vue";
import IsSingle from "../components/RegForm/isSingle.vue";
import exam from "./exam/exam.vue";
import navApp from '../components/nav.vue';
import { Progress as VanProgress } from 'vant';
import debounce from 'lodash-es/debounce';

export default {
    name:'Register',
    components:{
        QuizItem,
        Gender,
        Birthday,
        Education,
        Marriage,
        Salary,
        WorkCity,
        Height,
        IsSingle,
        navApp,
        exam,
        VanProgress
    },
    data(){
        return {
            reList: QUIZ_LIST_REGISTER, //注册列表
            currentIndex: 0, //当前项
            rendered: false,
        };
    },
    activated(){
        this.rendered = true;
        this.currentIndex = +this.$route.params.id;




    },
    watch: {
        curComp(val) {

            switch(val){
            case 'IsSingle':
                this.$report(89, '询问单身页访问');
                break;
            case 'Gender':
                this.$report(92, '性别页访问');
                break;
            case 'WorkCity':
                this.$report(94, '工作地页访问');
                break;
            case 'Birthday':
                this.$report(96, "出生年份页访问");
                break;
            case 'Height':
                this.$report(28, "身高页访问");
                break;
            case 'Education':
                this.$report(100, '学历页访问');
                break;
            case 'Marriage':
                this.$report(102, '婚况页访问');
                break;
            case 'Salary':
                this.$report(104, '收入页访问');
                break;
            }
        }
    },
    computed:{
        percentage() {
            return (this.currentIndex / this.reList.length) * 100;
        },
        curQuiz(){
            return this.reList[this.currentIndex];
        },
        curComp(){
            return this.curQuiz.comp;
        },
        // curProgress(){
        //     if(this.rendered){
        //         const fullLength = this.$refs.refProgress.offsetWidth;
        //         return this.currentIndex/this.quizList.length * fullLength;
        //     }
        //     return 0;
        // }
    },
    created() {
        //获取答题
        // let questionList = JSON.parse(localStorage.getItem('questionList'));
        // this.reList = JSON.parse(questionList);


    },
    mounted(){
    },
    methods:{
        goNext: debounce(async function (){
            //如果是最后一道题 跳转到手机注册页面
            if(this.reList.length  === (this.currentIndex + 1) ){
                this.$router.push({
                    path:`/examResultBefore`
                });
                return;
            }
            this.currentIndex++;
            this.$router.push({
                path:`/register/${this.currentIndex}`
            });
        }, 600, {
            leading: true,
            trailing: false,
        }),
        goBack(){
            this.currentIndex --;
            this.$router.push({
                path:`/register/${this.currentIndex}`
            });
            // this.$router.back();
        }

    }
};
</script>
<style lang="scss">
.van-progress__portion {
    transition: width 0.3s;
}
    
</style>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.quiz{
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #FFE7E1;

    .panel{
        background: url('https://photo.zastatic.com/images/common-cms/it/20240731/1722422125964_68517.png') no-repeat;
        width: 95vw;
        box-sizing: border-box;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        position: relative;
        // margin-top: 128px;
        .panel_icon {
            position: absolute;
            right: -24px;
            top: -42px;
            width: 113px;
            height: 94px;
        }
        .panel_div{
            margin-top: 136px;
            display: flex;
            flex-direction: column;
            align-items: center;
            p{
                font-weight: Oblique;
                height: 48px;
                font-size: 30px;
                text-align: center;
                color: #8A54F8;
                letter-spacing: 2px;
                margin-bottom: 10px;

            }
        }

        .panel-progress{
            width: 590px;
            height: 24px;
            border-radius: 20px;
            width: 486px;
            border:1px solid #8a54f8;
            display: flex;
            margin-right: 11px;


            .panel-progress-bar{
                height: 24px;
                background: #8a54f8;
                border-radius: 20px;

            }
        }

        &-title{
            font-weight: 400;
            line-height: 1.2;
            // font-family: 'love';
            margin-top: 30px;
            letter-spacing:4px;
            font-weight: 600;
            font-size: 36px;
            color: #333333;
            text-align: center;
            line-height: 50px;
        }


    }
    .top{
        position: fixed;
        top: 0px;
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 34px 25px 0 25px;
    }
    .panel-back{
        width: 147px;
        height: 56px;

    }
    .music{
        width: 74px;
        height: 74px;
    }
    .hidden_mis{
        justify-content: flex-end;
    }
    .flex_box{
        display: flex;
        font-family: 'love';
            height: 27px;
            font-size: 20px;
            text-align: left;
            color: #8A54F8 ;
            letter-spacing: 4px;
            // #9a55f0
    }
}
</style>
