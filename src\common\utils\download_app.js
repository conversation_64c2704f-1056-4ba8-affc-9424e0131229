var appId = '902803_28';

// 打开App，兼容所有版本
export function openApp() {
    var appHref = 'zhenaiapp://platformapi/startApp';
    // var appHref = 'http://s.zhenai.com/a';
    // 尝试安卓使用intent方式唤端，有app时可唤起APP，原生浏览器在无APP时无法跳转至back页面
    // var appHref = "intent://s.zhenai.com/a/#Intent;scheme=http;package=com.zhenai.android;S.browser_fallback_url=https%3A%2F%2Fwww.baidu.com%2F;end";

    if (Z.platform.isIos) {
        appHref = 'zhenaiwang://';
    }

    // let frame = document.createElement('iframe');
    // frame.src = appHref;
    // frame.style.display = 'none';
    // document.body.appendChild(frame);
    // setTimeout(function() { document.body.removeChild(frame); }, 4);

    window.location.href = appHref;
}

// 定时执行fn，如果在delayTime触发离开，则取消定时
export function visibilityChangeDelay(fn, delayTime) {
    var delay = delayTime || 3000;
    var timerToApp = setTimeout(fn, delay);

    // 离开页面时中断倒计时(中止中转到应用市场)
    try {
        var hidden = '';
        var visibilityChange = '';
        if (typeof document.hidden !== 'undefined') {
            hidden = 'hidden';
            visibilityChange = 'visibilitychange';
        } else if (typeof document.msHidden !== 'undefined') {
            hidden = 'msHidden';
            visibilityChange = 'msvisibilitychange';
        } else if (typeof document.webkitHidden !== 'undefined') {
            hidden = 'webkitHidden';
            visibilityChange = 'webkitvisibilitychange';
        }

        if (visibilityChange) {
            document.addEventListener(visibilityChange, function() {
                if (document[hidden]) {
                    clearTimeout(timerToApp);
                }
            }, false);
        }
    } catch (e) {}
}

// 下载app
export function downloadApp(channel) {
    var ch = channel || appId;
    var ua = navigator.userAgent;
    var  IOSAppId = "id575846819";
    var  IOS_TURN_ON = sessionStorage.getItem("IOS_TURN_ON");
    if(IOS_TURN_ON === "1"){
        IOSAppId = "id908928391";
    }
    if (/(iphone|ipod|ipad)/ig.test(ua)) {
        var appLink = `itms-apps://itunes.apple.com/cn/app/zhen-ai-wang/${IOSAppId}?mt=1`;
        if (/weibo/ig.test(ua)) {
            appLink = `https://itunes.apple.com/cn/app/zhen-ai-wang/${IOSAppId}?mt=1`;
        }
        window.location.href = appLink;
    } else if (/micromessenger/ig.test(ua)) {
        window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.zhenai.android';
    } else if (
        !/(baiduboxapp|weibo|VivoBrowser)/ig.test(ua) &&
        /(HUAWEI|O11|vivo|PKQ1|op|HONOR|OPM|mi)/ig.test(ua)
    ) {
        visibilityChangeDelay(function() {
            visibilityChangeDelay(function(){
                downAPK(ch);
            },800);
            //调起QQ浏览器跳转下载
            window.location.href = `mttbrowser://url=https://images.zastatic.com/apk/zhenai/zhenai_${ch}.apk?1=1`;
        }, 2000);
        if (/(HUAWEI|HONOR)/ig.test(ua)) {
            var iframe = document.createElement('iframe');
            iframe.src = 'appmarket://details?id=com.zhenai.android';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            setTimeout(function() {
                document.body.removeChild(iframe);
            }, 500);
        } else {
            window.location.href = 'market://details?id=com.zhenai.android';
        }
    } else {
        downAPK(ch);
    }
}

// 下载apk
export function downAPK(channel) {
    var ch = channel || appId;
    var iframe = document.createElement('iframe');
    iframe.src = 'https://images.zastatic.com/apk/zhenai/zhenai_'+ ch +'.apk?1=1';
    document.body.appendChild(iframe);
    setTimeout(function() {
        document.body.removeChild(iframe);
    }, 2000);
    // setTimeout(function() {
    //     window.location.href = 'https://images.zastatic.com/apk/zhenai/zhenai_'+ ch +'.apk?1=1';
    // }, 300);
}