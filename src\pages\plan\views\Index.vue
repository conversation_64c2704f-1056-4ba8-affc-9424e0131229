<template>
    <div class="plan-index">
        <img
            class="bg-img"
            :src="cmsConfig.downloadImg"
            :style="{
                visibility: cmsConfig.downloadImg ? 'visible' : 'hidden',
                height: cmsConfig.downloadImg ? 'auto' : '0px',
            }"
        />
        <div class="bottom-wrapper">
            <button
                class="bottom-btn"
                :style="cmsConfig.buttonColor"
                @click="handleEnter"
            >
                {{ cmsConfig.downloadButtonText }}
            </button>
            <div class="protocol-wrapper">
                <i
                    role="icon"
                    class="checkbox"
                    :class="{ active: hasCheckProtocal }"
                    @click="handleProtocolClick"
                />
                <span>
                    已阅读并同意<a @click="goProtocol(1)">《珍爱网服务协议》</a><a @click="goProtocol(2)">《个人信息保护政策》</a>
                </span>
            </div>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="modalVisible"
            @close-modal="closeModal"
            @ok="handleModalOk"
            :modal-type="modalType"
            :modal-param="modalParam"
        />
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { reportKibana } from "@/common/utils/report.js";
import { Modal } from "../components/common/index.js";
export default {
    components: {
        Modal
    },
    data() {
        return {
            modalVisible: false,
            modalType: "",
            modalParams: {}
        };
    },
    computed: {
        ...mapState(["planInfo", "cmsConfig", "hasCheckProtocal", "materialId"])
    },
    created() {
        // 获取CMS配置
        this.setCmsConfig();
    },
    mounted() {
        reportKibana("脱单计划H5", 1, "首页访问", { ext16: this.materialId });
    },
    methods: {
        ...mapActions(["setCmsConfig"]),
        ...mapMutations(["setHasCheckProtocal"]),
        handleEnter() {
            reportKibana("脱单计划H5", 2, "首页按钮点击", { ext16: this.materialId });
            if (!this.hasCheckProtocal) {
                this.openModal("modalProtocol", {});
                return;
            }
            this.$router.push("purpose");
        },
        handleProtocolClick() {
            // 默认都是勾选
            // CMS配置不需要勾选
            if (this.cmsConfig.agreementStatus === 0) {
                return;
            }
            // 需要勾选
            this.setHasCheckProtocal(!this.hasCheckProtocal);
            // 将勾选协议的状态，存入缓存
            localStorage.setItem("protocolStatus", this.hasCheckProtocal);
        },
        goProtocol(type) {
            if (type === 1) {
                location.href = "//i.zhenai.com/m/portal/register/prDeal.html";
            } else if (type === 2) {
                location.href = "//i.zhenai.com/m/portal/register/serverDeal.html";
            }
        },
        closeModal() {
            this.modalVisible = false;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.modalVisible = true;
        },
        handleModalOk() {
            this.$router.push("purpose");
        },
        
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.plan-index {
  position: relative;
}
.bg-img {
  position: absolute;
  width: 100%;
  z-index: -1;
}
.bottom-wrapper {
  margin-top: 180%;
  padding-top: 40px;
  position: fixed;
  height: 266px;
  right: 0;
  bottom: 0;
  left: 0;
  @include set-img("../assets/images/bg-index-mask.png");
  .bottom-btn {
    z-index: 1;
    display: block;
    height: 110px;
    width: calc(100% - 120px);
    margin: 0 auto;
    padding: 0;
    border-radius: 80px;
    font-size: 32px;
    font-weight: 500;
    color: #fff;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.17),
      inset 0px 2px 12px 0px rgba(255, 239, 239, 0.76);
  }
  .protocol-wrapper {
    height: 36px;
    font-size: 24px;
    font-weight: 400;
    line-height: 36px;
    color: #191c32;
    margin-top: 36px;
    text-align: center;
    .checkbox {
      display: inline-block;
      width: 30px;
      height: 30px;
      line-height: 36px;
      vertical-align: middle;
      @include set-img("../assets/images/icon-unchecked.png");
      background-size: 100% 100%;
      &.active {
        @include set-img("../assets/images/icon-checked.png");
      }
    }
    span {
        vertical-align: middle;
    }
  }
}
</style>
