<template>
    <div class="radio-list">
        <div v-for="(item, index) in list"
             :key="index">
            <div
                :class="getItemClass(item)"
                @click="$emit('change', item, index)">
                {{ item.label }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "radio-list",
    props: {
        value: {
            type: String,
        },
        list: {
            type: Array
            // [{ label }]
        }
    },
    data() {
        return {
            inited: false
        }
    },
    methods: {
        getItemClass(item) {
            const result = [ 'radio-list__item' ];

            if (this.value === item.label) {
                result.push(this.inited ? 'radio-list__item--active-animation' : 'radio-list__item--active');
                return result;
            }

            return result;
        },
    },
    async created() {
        let changeTime = 0;

        const unwatch = this.$watch('value', () => {
            if (changeTime >= 1) {
                this.inited = true;
                unwatch();
                return;
            }

            changeTime++;
        }, {
            immediate: true,
        });
    }
}
</script>

<style scoped lang="scss">
@mixin active() {
    background-image: none;
    background-color: #767DFF;
}

@mixin normal() {
    background-color: transparent;
    background-image: url("../assets/images/question-radio-bg.png");
}

.radio-list {
    width: 526px;
    margin-top: 32px;

    &__item {
        font-weight: 500;
        font-size: 28px;
        line-height: 80px;
        text-align: center;
        margin-bottom: 16px;
        border-radius: 42px;
        background-size: cover;
        width: 526px;
        height: 80px;
        background-image: url("../assets/images/question-radio-bg.png");
    }

    &__item--active {
        @include active();
    }

    &__item--active-animation {
        animation: flash-radio-item 0.8s forwards;
    }
}

@keyframes flash-radio-item {
    20% {
        @include active();
    }
    40% {
        @include normal();
    }
    60% {
        @include active();
    }
    80% {
        @include normal();
    }
    90% {
        @include active();
    }
    100% {
        @include active();
    }
}
</style>
