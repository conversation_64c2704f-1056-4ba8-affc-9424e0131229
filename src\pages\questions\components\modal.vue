<template>
    <div class="modal" v-if="show">
        <div class="modal_con" :class="{'m74': confirmTextOrigin}">
        <div class="modal_con_img" v-if="confirmTextOrigin"></div>
            <slot name="title"></slot>
            <slot name="subTitle"></slot>
            <div
                class="modal_con_btn spe"
                @click="handleConfirm"
                v-html="confirmText"
                v-if="confirmText"
            ></div>
            <div
                class="modal_con_btn spe"
                @click="handleConfirmOrigin"
                v-if="confirmTextOrigin"
            >
                {{ confirmTextOrigin }}
            </div>
            <div class="modal_con_btn" @click="handleCancel">
                {{ concelText }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "modal",
    props: {
        show: {
            type: Boolean,
            default: false
        },
        confirmText: {
            type: String,
            default: "确认"
        },
        concelText: {
            type: String,
            default: "取消"
        },
        confirmTextOrigin: {
            type: String
        }
    },
    methods: {
        handleConfirm() {
            this.$emit("handleConfirm");
        },
        handleCancel() {
            this.$emit("handleCancel");
        },
        handleConfirmOrigin() {
            this.$emit("handleConfirmOrigin");
        }
    }
};
</script>

<style lang="scss">
.modal {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    &_con {
        padding: 60px 40px 24px;
        margin: 0 96px;
        background: #fff url("../assets/images/bg.png") no-repeat;
        background-size: 100% 220px;
        border-radius: 32px;
        &.m74 {
            margin: 0 74px;
            padding: 0 40px 24px;
        }
        &_img {
            height: 264px;
            margin-top: -48px;
            margin-bottom: 30px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220106/1641452881231_843006_t.png) center center no-repeat;
            background-size: 264px 264px;
        }
        &_title {
            color: #26273c;
            font-size: 36px;
            font-weight: 500;
        }
        &_subtitle {
            color: #6c6d75;
            font-size: 28px;
            line-height: 42px;
        }
        &_subtitle >a {
            color: #ada2ff;
             text-decoration: underline;
        }
        &_btn {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 88px;
            color: #767dff;
            font-weight: 400;
            font-size: 32px;
            &.spe {
                color: #fff;
                margin-top: 32px;
                background: #767dff;
                border-radius: 44px;
            }
        }
        .tips {
            padding-top: 8px;
            font-size: 24px;
        }
    }
}
</style>
