<template>
    <div class="collection-form-wrapper">
        <!-- 填写内容 -->
        <div class="content__collection">
            <!-- 注册项 -->
            <div
                v-for="(item, index) in formInfo"
                :key="index"
                class="collection__item"
                @click="openSelect(item)"
            >
                <div class="collection__item__label">
                    {{ item.label }}
                </div>
                <!-- 性别项 -->
                <div
                    v-if="item.index === 'gender'"
                    class="collection__item__select--gender"
                >
                    <button
                        v-for="(item, index) in formInfo[0].selectArr"
                        :key="index"
                        class="gender-item"
                        @click="setGender(item)"
                        :class="
                            item.key === registerInfo.gender ? 'gender-item--selected' : ''
                        "
                    >
                        {{ item.text }}
                    </button>
                </div>

                <!-- 工作地、出生年份、学历、婚况、月收入 -->
                <div
                    v-else
                    class="collection__item__select"
                >
                    <span v-if="item.value">{{ item.value }}</span>
                    <span
                        v-else
                        class="color--4633EF"
                    >{{ "待完善" }}</span>
                </div>
                <div class="address_title_txt" v-if="item.index === 'workCity'">填写正确工作地，以便本地红娘为您更好的服务</div>
            </div>

            <!-- 手机号 -->
            <div class="collection__phone">
                <div class="collection__phone__label">
                    您的手机号
                </div>
                <div class="collection__phone__input">
                    <input
                        ref="refPhoneInput"
                        type="tel"
                        :value="phone"
                        @input="limit"
                        placeholder="请输入11位手机号"
                        maxlength="13"
                    />
                </div>
                <div
                    class="collection__phone__clear"
                    @click="phone = ''"
                ></div>
            </div>

            <!-- 图形验证码 -->
            <div
                v-if="showImgCode"
                class="collection__code"
            >
                <div class="collection__code__input">
                    <input
                        ref="refImgCodeInput"
                        type="text"
                        v-model="imgCode"
                        placeholder="请输入验证码"
                        maxlength="4"
                    />
                </div>
                <div
                    class="collection__code__img"
                    ref="refImgCode"
                ></div>
                <div
                    class="collection__code__refresh"
                    @click="setImgCode"
                ></div>
            </div>
        </div>

        <div
            class="bell__submit"
            :style="cmsConfig.subButtonUrl"
            @click="submitRegisterInfo('buttonIndex')"
            :class="finished ? 'bell__submit__finished' : ''"
        >
            <div class="bell__submit__icon"></div>
        </div>

        <!-- 协议 -->
        <div class="content__protocal" v-show="cmsConfig.showAgreeButton === 0">
            <div
                :class="
                    hasCheckProtocal
                        ? 'content__protocal__checked'
                        : 'content__protocal__uncheck'
                "
                @click="checkProtocal"
            ></div>
            已阅读并同意<span @click="goUrl(1)">《幸福汇服务协议》</span>和<span
                @click="goUrl(2)"
            ><br />《幸福汇隐私政策》&nbsp;</span>
        </div>

        <!-- 弹窗 -->
        <modal
            v-if="showModal"
            @close-modal="closeModal"
            :modal-type="modalType"
            :modal-param="modalParam"
            :validate-code="validateCode"
        />

        <!-- 底部选择框 -->
        <select-panel
            v-if="showSelect"
            @close-select="closeSelect"
            :select-type="selectType"
            :select-param="selectParam"
            :isAutoLoop="true"
        />
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters, mapActions } from "vuex";
import { SelectPanel, Modal } from "../common/index.js";
import { reportError, reportMagic } from "@/common/utils/report.js";
import { channelId, subChannelId, oExt9 } from "@/common/js/const.js";
import { reportKibana } from "@/common/utils/report.js";
import oUserSelect from "@/common/ocpx/huichuan.js";
import Api from "@/common/server/base";
import Prototype from "@/common/framework/prototype";

import {
    registerResult,
    pageTypeMap
} from "@/common/config/register-dictionary.js";

export default {
    components: {
        SelectPanel,
        Modal
    },
    data() {
        return {
            showModal: false,
            modalType: "modalValidate",
            modalParam: {},
            showSelect: false,
            selectType: "null",
            selectParam: {},
            showImgCode: false,
            lockBaseInfo: false,
            lockPhone: false,
            lockOverwrite: false,
            messageCode: ""
        };
    },
    computed: {
        ...mapState([
            "formInfo",
            "registerInfo",
            "code",
            "cmsConfig",
            "isCover",
            "hasCheckProtocal",
            "regMemberId"
        ]),
        ...mapGetters(["getProgress", "getNormalPhone"]),

        phone: {
            get() {
                return this.registerInfo.phone;
            },
            set(newVal) {
                // console.log("手机号校验",newVal);
                // newVal = newVal.replace(/[^\d]/g,'');
                this.setRegisterInfo({
                    phone: newVal
                });
                // this.phone2 = e.target.value.replace(/[^\d]/g,"");
            }
        },
        imgCode: {
            get() {
                return this.code;
            },
            set(newVal) {
                this.setCode(newVal);
            }
        },
        finished() {
            // 完成7项基本资料的填写
            return this.getProgress === 7;
        }
    },
    created() {},
    mounted() {
        this.scrollInput();
    },
    methods: {
        scrollInput() {
            // 【兼容】安卓端控制输入组件不被软键盘遮挡
            if (/Android/i.test(navigator.userAgent)) {
                window.addEventListener("resize", () => {
                    if (
                        document.activeElement.tagName.toUpperCase() === "INPUT" ||
                        document.activeElement.tagName.toUpperCase() === "TEXTAREA"
                    ) {
                        window.setTimeout(() => {
                            document.activeElement.scrollIntoView({
                                block: "center"
                            });
                        }, 0);
                    }
                });
            }
        },
        limit(e) {
            e.target.value = e.target.value.replace(/[^(\d|\s)]/g, "");
            this.phone = this.formatPhone(e);
        },
        formatPhone(e) {
            // 存储当前光标位置用于之后重置
            let position = e.target.selectionStart;

            if (e.inputType === "deleteContentBackward") {
                // 删除的情况
                // 删到空格时 需要多删除空格前一位数
                if (position === 3 || position === 8) {
                    let temArr = e.target.value.split("");
                    temArr.splice(position - 1, 1);
                    e.target.value = temArr.join("");
                    // 光标也要跟着前移一位
                    position -= 1;
                }
                // 格式化
                e.target.value = e.target.value
                    .replace(/\D/g, "")
                    .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3")
                    .trim();
                // 重置光标,setTimeout用于兼容苹果手机
                setTimeout(() => {
                    e.target.selectionStart = e.target.selectionEnd = position;
                }, 0);
                // e.target.selectionStart = e.target.selectionEnd = position;
            } else if (e.inputType === "insertText") {
                // 插入的情况
                if (e.target.value.length < 9) {
                    e.target.value = e.target.value
                        .replace(/\D/g, "")
                        .replace(/(\d{3})(\d{0,4})/, "$1 $2");
                } else {
                    e.target.value = e.target.value
                        .replace(/\D/g, "")
                        .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
                }
                if (position < e.target.value.length) {
                    // 输入空格后第一位数时 光标要往后移一位
                    if (position === 4 || position === 9) {
                        position += 1;
                    }
                    // 重置光标
                    setTimeout(() => {
                        e.target.selectionStart = e.target.selectionEnd = position;
                    }, 0);
                }
            } else {
                // 复制粘贴的情况
                let pasteStr = e.target.value.replace(/\D/g, "");
                // maxlenth为13，所以这里要限制位数大于11时截取
                if (pasteStr > 11) {
                    e.target.value = pasteStr
                        .substr(0, 11)
                        .replace(/(\d{3})(\d{0,4})(\d{0,4})/, "$1 $2 $3");
                }
            }

            return e.target.value;
        },
        formatMobile(mobile, e) {
            if (!mobile) return "";
            let result = mobile.replace(/[^(\d|\s)]/g, "");
            if (e.inputType === "insertText") {
                if (result.length === 3 || result.length === 8) {
                    return result.replace(result, result + " ");
                }
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/(\d$)/g, " $1");
                }
            } else if (e.inputType === "deleteContentBackward") {
                if (result.length === 4 || result.length === 9) {
                    return result.replace(/\s$/, "");
                }
            }
            // 复制粘贴的情况
            result = result.replace(/\D/g, "");

            if (result.length > 11) {
                result = result.substr(0, 11);
            }

            if (result.length > 3 && result.length < 8) {
                result = result.replace(/^(\d{3})/g, "$1 ");
            } else if (result.length >= 8) {
                result = result.replace(/^(\d{3})(\d{4})/g, "$1 $2 ");
            }

            return result;
        },
        ...mapMutations([
            "setRegisterInfo",
            "setFormInfo",
            "setCmsConfig",
            "setCode",
            "setOverwriteRegistrationSwitch",
            "setRegMemberId",
            "setIsCover",
            "setHasCheckProtocal"
        ]),
        ...mapActions([
            // "setModelInfo"
        ]),
        closeModal() {
            this.showModal = false;
            this.modalParam = {};
        },
        closeSelect() {
            this.showSelect = false;
        },
        // 页面跳转
        jump(path) {
            this.$router.push({
                path,
                query: {
                    // plan:123
                }
            });
        },
        async submitRegisterInfo(from) {
            // 自测，改

            // localStorage.setItem("flagFilled", "1");
            // this.jump("blindinfo")
            // return;

            let ext17 = [];
            this.formInfo.forEach(item => {
                if (item.value) {
                    ext17.push(item.value);
                }
            });
            if (this.phone) {
                ext17.push(this.phone.replace(/\s/g, ""));
            }
            ext17 = ext17.join(",");
            // 如果是大表单页按钮点击时触发，需要打桩
            if (from === "buttonIndex") {
                // 信息未填写完整却点击了提交
                if (!this.finished) {
                    reportKibana("线下大表单", 2, "首页-提交按钮点击", {
                        ext17
                    });
                } else {
                    reportKibana("线下大表单", 3, "首页-提交按钮点击（可点状态）", {
                        ext17
                    });
                }
            }

            // 锁，已经发送请求
            if (this.lockBaseInfo || this.lockPhone) {
                return;
            }

            // 如果是确认协议弹窗时触发,需额外处理
            if (from === "buttonProtocol") {
                // 更新UI
                this.setHasCheckProtocal(true);
                // 关闭弹窗，因为此时可能还需要填写图形验证码
                this.closeModal();
            }

            // 信息未填完整
            if (!this.finished) {
                this.$toast("请完善资料再提交");
                return;
            }

            // 未勾选协议 并且 crm配置 展示勾选协议
            if (!this.hasCheckProtocal  && this.cmsConfig.showAgreeButton === 0) {
                this.openModal("modalProtocol", {});
                return;
            }
            this.lockBaseInfo = true;

            const resData = await Api.sendXfhMessageCode({phone: this.getNormalPhone});
            if (resData.code === 0) {
                this.openModal("modalValidate", {})
                this.lockPhone = true
            } else {
                this.$toast(resData.msg)
            }
        },
        async submitPhoneInfo() {
            // 老注册页魔方上报逻辑迁移
            reportMagic();

            let sendData = {
                phone: this.getNormalPhone,
                type: 0,
                imgCode: this.imgCode,
                // 极验参数，暂不迁移
                // challenge: self.gtObj.challenge,
                // validate: self.gtObj.validate,
                // seccode: self.gtObj.seccode,
                // 落地页url, 用于记录百度投放跳转到落地页时添加的参数
                landingUrl: document.referrer || undefined
            };

            // 【归因】头条
            const toutiaoParamlist = {
                clickid: Z.getParam("clickid"),
                adid: Z.getParam("adid"),
                creativeid: Z.getParam("creativeid"),
                creativetype: Z.getParam("creativetype")
            };

            for (const v in toutiaoParamlist) {
                if (toutiaoParamlist[v]) {
                    sendData[v] = toutiaoParamlist[v];
                }
            }

            this.lockPhone = true;
            // 自改
            // let resData = await _submitWapRegNoPasswordInfo(sendData);
            let resData = await Api.sendWapMessageCodeV2(sendData);
            // let resData = {
            //     data:{
            //         type: -1,
            //         memberID: 20000000,
            //         overwriteRegistrationSwitch: false
            //     },
            //     isError:false,
            //     errorMessage:"ddddddd"
            // }

            this.lockPhone = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                // 重置图形验证码
                // this.setImgCode();
                return;
            }

            // 走验证码逻辑
            this.goValidate();
        },
        setGender(item) {
            this.setFormInfo({
                gender: item.text
            });
            this.setRegisterInfo({
                gender: item.key
            });

            // 处理回传
            oUserSelect.mark({
                gender: item.key
            });
        },
        openSelect(currentItem) {
            if (["gender"].includes(currentItem.index)) {
                return;
            } else if (["workCity", "birthday"].includes(currentItem.index)) {
                this.selectType = "selectSlide";
            } else if (
                ["marriage", "education", "salary"].includes(currentItem.index)
            ) {
                this.selectType = "selectBoard";
            }
            this.selectParam = currentItem;
            this.showSelect = true;
        },
        openModal(modalType, modalParam) {
            this.modalType = modalType;
            this.modalParam = modalParam;
            this.showModal = true;
        },
        checkProtocal() {
            // 默认都是勾选
            // CMS配置不需要勾选
            if (this.cmsConfig.agreementStatus === 0) {
                return;
            }

            // 需要勾选
            this.setHasCheckProtocal(!this.hasCheckProtocal);
            // this.hasCheckProtocal = !this.hasCheckProtocal;
        },
        modifyCustomer(data) {
            Z.ajax({
                type: "POST",
                url:
                    "https://call-api.zhenai.com/workapi/externalController/modifyCustomer",
                dataType: "json",
                opts: {
                    headers: {
                        "Content-Type": "application/json;charset=UTF-8"
                    }
                },
                data,
                success: function(results) {
                    console.log(results);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.log(xhr, textStatus, errorThrown);
                }
            });
        },
        setImgCode() {
            // 重置验证码
            this.imgCode = "";

            this.$nextTick(() => {
                let refImgCode = this.$refs.refImgCode,
                    refImgCodeInput = this.$refs.refImgCodeInput;

                let src =
                    window.location.protocol +
                    "//api.zhenai.com/register/getWapRegisterImgCode.do?t=" +
                    new Date().getTime();

                if (refImgCode) {
                    refImgCode.style.backgroundImage = `url(${src})`;
                    refImgCodeInput.focus();
                    // 输入框位置调整
                    refImgCodeInput.scrollIntoView({
                        block: "center"
                    });
                }
                return;
            });
        },
        // 覆盖注册
        async coverRegister() {
            if (this.lockOverwrite) {
                return;
            }

            this.lockOverwrite = true;

            const sendData = Prototype.$gather.getOverwriteAccountParams(
                this.messageCode,
                pageTypeMap.OFFLINETABLE
            );
            const resData = await Api.overwriteAccount(sendData);

            this.lockOverwrite = false;

            if (resData.isError) {
                this.$toast(resData.errorMessage);
                return;
            }

            if (resData.data.memberID) {
                // 存vuex
                this.setRegMemberId(resData.data.memberID);
                // 记录ocpc回传状态
                oUserSelect.mark({
                    msgValid: true
                });
            }

            reportKibana("线下大表单", 4, "注册成功并生成ID", {
                ext17: "覆盖注册"
            });

            this.closeModal();
            this.onRegisterFinished();
        },
        // 先处理OCPX相关，然后打开验证码弹窗
        goValidate() {
            Prototype.$gather.setBeforeValidateCodeOCPC();

            // 打开验证码窗口
            this.openModal("modalValidate", {});
        },
        goUrl(type) {
            if (type === 1) {
                this.$router.push({
                    path: "/service"
                });
            } else if (type === 2) {
                this.$router.push({
                    path: "/privacy"
                });
            }
        },
        async validateCode(messageCode) {
            const sendData = {
                phone: this.getNormalPhone,
                messageCode,
                pageType: pageTypeMap.OFFLINETABLE,
                channelId: Z.getParam('channelId'),
                subChannelId: Z.getParam('subChannelId'),
                ...this.registerInfo
            }
            delete sendData.height
            sessionStorage.setItem('advertPhone', this.getNormalPhone)
            const resData = await Api.registerXfh(sendData);
            
            if (resData.code === 0) {
                reportKibana("幸福汇", 4003, "注册成功并生成ID", {
                    ext17: JSON.stringify(resData),
                    ext16: JSON.stringify(resData.data.userId)
                });
                // 注册H5上报
                const reportData = {
                    ...sendData,
                    ua: Z.getUA(),
                    clickId: localStorage.getItem('AD_URL'),
                    memberID: resData.data.userId,
                    submitPhone: true,
                    msgValid: true,
                    height: -1,
                }
                try {
                    if (this.registerInfo.birthday) {
                        reportData.year = new Date(this.registerInfo.birthday).getFullYear()
                    }
                    reportKibana("幸福汇", 4004, "回传调用参数", {
                        ext17: JSON.stringify(reportData),
                        ext16: JSON.stringify(resData.data.userId)
                    });
                } catch (error) {
                    console.log(error)
                    reportKibana("幸福汇", 4005, "回传调用参数报错", {
                        ext17: JSON.stringify(error),
                        ext16: JSON.stringify(resData.data.userId)
                    });
                }
                delete reportData.messageCode
                // if (resData.data.userId && resData.data.userId !== '用户已经注册') {
                try {
                    const result = await Api.registeredH5CheckReport(reportData);
                    if (result.code === 0) {
                        reportKibana("幸福汇", 4006, "回传调用成功", {
                            ext17: JSON.stringify(result),
                            ext16: JSON.stringify(resData.data.userId),
                            ext15: JSON.stringify(reportData),
                        });
                    } else {
                        reportKibana("幸福汇", 4007, "回传调用失败", {
                            ext17: JSON.stringify(result),
                            ext16: JSON.stringify(resData.data.userId),
                            ext15: JSON.stringify(reportData),
                        });
                    }
                } catch (error) {
                    console.log(error)
                    reportKibana("幸福汇", 4008, "回传调用失败", {
                        ext17: JSON.stringify(error),
                        ext16: JSON.stringify(resData.data.userId),
                        ext15: JSON.stringify(reportData),
                    });
                }
                // }
                this.$toast('注册成功')
                this.showModal = false
            } else {
                this.$toast(resData.msg)
            }
            this.lockPhone = false
            this.lockBaseInfo = false
            return resData;
        },
        async handlegudgeonMark() {
            const sendData = {memberId: this.regMemberId || null, memberTaskType: 8};
            const result = await Api.gudgeonMark(sendData);

            if (result.isError) {
                this.$toast(result.errorMessage);
                return;
            }
        },
        async onRegisterFinished() {
            // 核对成功
            this.$toast("注册成功");

            // h5注册标记
            this.handlegudgeonMark();

            this.closeModal();

            this.clearCache();

            // 设置注册态
            localStorage.setItem("flagFilled", "1");

            // this.jump("info");
            // 打开下载弹窗
            // 遇众小程序临时解决
            if (Z.getParam('isMini') == 'yuzhong') {
                setTimeout(() => {
                        window.location.href = 'https://wxaurl.cn/1ysr7nk3Pnp'
                }, 1000)
                // location.href = 'https://wxaurl.cn/1ysr7nk3Pnp'
                return false
            }
            if (this.cmsConfig.registerJumpPage == '1') { // 匹配跳转
                if (this.registerInfo.gender == '0' && this.registerInfo.marriage == '1') { // 男 单身
                    this.$router.push({
                        path: "/mateResult?type=0"
                    });
                } else if (this.registerInfo.gender == '1' && this.registerInfo.marriage == '1') { // 女 单身
                    this.$router.push({
                        path: "/mateResult?type=1"
                    });
                } else if (this.registerInfo.gender == '0' && (this.registerInfo.marriage == '3' || this.registerInfo.marriage == '4')) { // 男 离异/丧偶
                    this.$router.push({
                        path: "/mateResult?type=2"
                    });
                } else if (this.registerInfo.gender == '1' && (this.registerInfo.marriage == '3' || this.registerInfo.marriage == '4')) { // 女 离异/丧偶
                    this.$router.push({
                        path: "/mateResult?type=3"
                    });
                } else {
                    this.$router.push({
                        path: "/mateResult?type=0"
                    });
                }
            } else if (this.cmsConfig.registerJumpPage == '2') {
                this.$router.push({
                    path: "/mateSelection/0"
                });
            } else { // 默认跳转
                this.$router.push({
                    path: "/downApp"
                });
            }


            // if(this.cmsConfig.downloadApp === 0){
            //     if (this.cmsConfig.jumpULoveCupid == '1') {
            //         this.$router.push({
            //             path: "/successResult"
            //         });
            //     } else if (this.cmsConfig.jumpULoveCupid == '0') {
            //         // this.openModal("modalDownload", {});
            //         if (this.cmsConfig.registerJumpPage == '1') {
            //             if (this.registerInfo.gender == '0' && this.registerInfo.marriage == '1') { // 男 单身
            //                 this.$router.push({
            //                     path: "/mateResult?type=0"
            //                 });
            //             } else if (this.registerInfo.gender == '1' && this.registerInfo.marriage == '1') { // 女 单身
            //                 this.$router.push({
            //                     path: "/mateResult?type=1"
            //                 });
            //             } else if (this.registerInfo.gender == '0' && (this.registerInfo.marriage == '3' || this.registerInfo.marriage == '4')) { // 男 离异/丧偶
            //                 this.$router.push({
            //                     path: "/mateResult?type=2"
            //                 });
            //             } else if (this.registerInfo.gender == '1' && (this.registerInfo.marriage == '3' || this.registerInfo.marriage == '4')) { // 女 离异/丧偶
            //                 this.$router.push({
            //                     path: "/mateResult?type=3"
            //                 });
            //             } else {
            //                 this.$router.push({
            //                     path: "/mateResult?type=0"
            //                 });
            //             }
            //         } else {
            //             this.$router.push({
            //                 path: "/downApp"
            //             });
            //         }
            //     } else {
            //         this.$router.push({
            //             path: "/downApp"
            //         });
            //     }
            // } else {
            //     this.$router.push({
            //         path: "/successResult"
            //     });
            // }


        },
        clearCache() {
            // 清空注册信息
            localStorage.setItem("localFormInfo", "");
            localStorage.setItem("localRegisterInfo", "");
            localStorage.setItem("defaultBirthday", "");
            localStorage.setItem("defaultWorkCity", "");
            localStorage.setItem("defaultEducation", "");
            localStorage.setItem("defaultSalary", "");
            localStorage.setItem("defaultMarriage", "");
            // 清空协议勾选状态
            localStorage.removeItem("protocolStatus");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../index.scss";
.collection-form-wrapper {
  padding-bottom: 70px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content__collection {
//   margin-top: -100px;
  padding-bottom: 78px;
  width: 694px;
  // height: 911px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.9), #ffffff);
  border-radius: 32px;
  overflow: hidden;
//   border: 2px solid #000;
}

.collection__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 36px;
  width: 686px;
  height: 100px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
  line-height: 100px;
  position: relative;
    .address_title_txt {
        position: absolute;
        top: 50px;
        left: 35px;
        font-size: 25px;
        color: red;
    }
}

.collection__item:nth-child(1) {
  margin-top: 46px;
}

.collection__item__label {
  width: 240px;
}

.collection__item__select {
  position: relative;
  padding-right: 36px;
  font-size: 32px;
  font-weight: 400;
  line-height: 40px;
}

.color--4633EF {
  color: $themeColor;
}

.collection__item__select::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 12px;
  height: 20px;
  @include set-img("../../assets/imgs/right-arrow.png");
}

.collection__item__select--gender {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 304px;
  height: 100px;
}

.gender-item {
  @include set-flex(center, center);
  width: 138px;
  height: 60px;
  border: 2px solid #26273c;
  border-radius: 30px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
  line-height: 60px;
  text-align: center;
}

.gender-item--selected {
  border: none;
  background: #020202;
  color: #ffffff;
}

.collection__phone {
  position: relative;
  // margin: 0 auto;
  padding: 0 36px;
  font-size: 32px;
  font-weight: 400;
  color: #26273c;
}

.collection__phone__label {
  height: 100px;
  line-height: 100px;
}

.collection__phone__input {
  // 兼容处理safari光标漂移，input外多套一层+padding
  margin: 0 auto;
  padding-left: 50px;
  padding-top: 24px;
  width: 621px;
  height: 88px;
  background-color: rgba($color: #26273c, $alpha: 0.05);
  border-radius: 44px;
  input {
    height: 40px;
    background-color: transparent;
    font-size: 32px;
    color: #26273c;
    line-height: 40px;
    text-align: left;
    width: 500px;
  }
}

.collection__phone__clear {
  position: absolute;
  top: 130px;
  right: 85px;
  width: 32px;
  height: 32px;
  @include set-img("../../assets/imgs/icon-clear.png");
}

.collection__code {
  position: relative;
  margin: 32px auto 0;
  padding: 0 36px;
  height: 88px;
}

.collection__code__input {
  margin: 0 auto;
  padding-left: 50px;
  padding-top: 24px;
  width: 621px;
  height: 88px;
  background-color: rgba($color: #26273c, $alpha: 0.05);
  border-radius: 44px;
  text-align: left;
  input {
    display: block;
    height: 40px;
    background-color: transparent;
    font-size: 32px;
    color: #26273c;
    line-height: 40px;
    text-align: left;
  }
}

.collection__code__img {
  display: block;
  position: absolute;
  top: 16px;
  right: 150px;
  width: 147px;
  height: 56px;
  border-radius: 31px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collection__code__refresh {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 86px;
  width: 32px;
  height: 32px;
  @include set-img("../../assets/imgs/icon-refresh.png");
}

.content__submit {
  display: block;
  margin: 41px auto 0;
  width: 686px;
  height: 110px;
  // background: linear-gradient(0deg, #FFC334, #FF8F02);
  border-radius: 55px;
  font-size: 30px;
  font-weight: 700;
  color: #ffffff;
  line-height: 110px;
  text-align: center;
  box-shadow: inset 0 0 16px 2px #ffffff;
  background: linear-gradient(0deg, #979797, #888888);
  border: 2px solid #000;
}

.content__submit--unfinished {
}

.bell__submit {
  margin: 41px auto 0;
  width: 493px;
  height: 113px;
  overflow: hidden;
//   @include set-img("../../assets/imgs/new/button.png");
background-size: 100% 100%;
background-repeat: no-repeat;
  /*.bell__submit__icon {
    margin: 40px auto 0;
    width: 119px;
    height: 28px;
    // @include set-img("../../assets/imgs/form_bell_disabled.png");
  }*/
}

.bell__submit__finished {
  @include set-img("../../assets/imgs/new/button.png");
    /*.bell__submit__icon {
      margin: 40px auto 0;
      width: 119px;
      height: 28px;
      @include set-img("../../assets/imgs/form_bell.png");

    animation: shake 0.8s linear infinite;
    transform-origin: top right;

    @keyframes shake {
      25% {
        transform: scale(1.2);
      }

      75% {
        transform: scale(1);
      }
    }
  }*/
}

.content__protocal {
  margin: 49px auto 0;
  // width: 500px;
  font-size: 24px;
  color: #000;
  line-height: 38px;
  text-align: center;
  span {
    color:  $themeColor;
  }
}

.content__protocal__checked {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  @include set-img("../../assets/imgs/checked.png");
}

.content__protocal__uncheck {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 22px;
  height: 22px;
  @include set-img("../../assets/imgs/uncheck.png");
}
</style>
