<template>
    <div class="anim">
        <Back @on-back="goBack" />
        <div class="anim-card">
            <div class="anim-card-info">
                <h4>
                    正在为你匹配 <span>{{ cityName }}市</span>
                </h4>
                <p>符合你择偶条件的异性</p>
            </div>
            <div class="anim-card-progress">
                <div class="acp-line" :style="{ width: `${curNum}%` }">
                    <div class="acp-line-circle"></div>
                    <div class="acp-line-num">{{ curNum }}%</div>
                </div>
            </div>
            <div class="anim-card-logo">
                <div class="acl-nail">
                    <span class="rotateRing"></span>
                </div>
            </div>
        </div>
        <div class="anim-list" ref="animList">
            <div class="anim-list-item">
                <div class="ali-info">
                    <h4>你的基本资料审核</h4>
                    <p v-if="!pace.show1">
                        计算中...
                    </p>
                    <p v-else class="spe">
                        符合要求
                    </p>
                </div>
                <div class="ali-ok" id="aliOk1"></div>
            </div>
            <div class="anim-list-item">
                <div class="ali-info">
                    <h4>同城异性的在库用户数</h4>
                    <p v-if="!pace.show2">
                        计算中...
                    </p>
                    <p v-else class="spe">
                        库存充足
                    </p>
                </div>
                <div class="ali-ok" id="aliOk2"></div>
            </div>
            <div class="anim-list-item">
                <div class="ali-info">
                    <h4>同城异性的择偶要求</h4>
                    <p v-if="!pace.show3">
                        计算中...
                    </p>
                    <p v-else class="spe">
                        已满足要求
                    </p>
                </div>
                <div class="ali-ok" id="aliOk3"></div>
            </div>
            <div class="anim-list-item">
                <div class="ali-info">
                    <h4>为你筛选优质异性</h4>
                    <p v-if="!pace.show4">
                        计算中...
                    </p>
                    <p v-else class="spe">
                        已锁定优质用户
                    </p>
                </div>
                <div class="ali-ok" id="aliOk4"></div>
            </div>
            <div class="anim-list-item">
                <div class="ali-info">
                    <h4>优质异性与你的匹配度</h4>
                    <p v-if="!pace.show5">
                        计算中...
                    </p>
                    <p v-else class="spe">
                        匹配度98%
                    </p>
                </div>
                <div class="ali-ok" id="aliOk5"></div>
            </div>
        </div>
    </div>
</template>

<script>
import { storage as Storage } from "@/common/utils/storage";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js";
import Back from "../components/Back.vue";
export default {
    name: "Anim",
    data() {
        return {
            curAnim: 0,
            curNum: 0,
            timer1: null,
            timer2: null,
            cityName: "肇庆",
            pace: {
                show1: false,
                show2: false,
                show3: false,
                show4: false,
                show5: false,
                show6: false
            }
        };
    },
    inject: ["cmsConfig"],
    components: {
        Back
    },
    mounted() {
        this.$report(10, "匹配动效页访问");

        const workCity =
            (Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) &&
                Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`)
                    .workCity) ||
            "";
        if (workCity) {
            const cityArr = findWorkCity(workCity);
            this.cityName = cityArr[1];
        }

        const animList = this.$refs.animList;
        this.timer1 = setTimeout(() => {
            clearTimeout(this.timer1);
            this.timer1 = setInterval(() => {
                if (this.curAnim >= 7) {
                    clearInterval(this.timer1);
                    this.$router.push({ path: "/success" });
                } else {
                    animList.children[this.curAnim] &&
                        animList.children[this.curAnim].classList.add(
                            "fadeInDownBig"
                        );
                    if (this.curAnim >= 1) {
                        this.pace["show" + (this.curAnim - 1)] = true;
                        if (this.curAnim <= 5) {
                            this.setSVGA(this.curAnim);
                        }
                    }
                    this.curAnim++;
                }
            }, 800);
        }, 500);
        this.timer2 = setInterval(() => {
            if (this.curNum >= 100) {
                clearInterval(this.timer2);
            } else {
                this.curNum += 2;
            }
        }, 30);
    },
    methods: {
        goBack() {
            this.$report(10, "匹配动效页-返回按钮点击");
            this.$router.push({ path: "/about", query: { id: 5 } });
        },
        setSVGA(id) {
            let player = new SVGA.Player(`#aliOk${id}`),
                parser = new SVGA.Parser(`#aliOk${id}`);
            parser.load(require("../assets/images/loading.svga"), videoItem => {
                player.setVideoItem(videoItem);
                player.loops = 1;
                player.startAnimation();
            });
        }
    },
    beforeDestroy() {
        clearInterval(this.timer1);
        clearTimeout(this.timer2);
        clearInterval(this.timer1);
    }
};
</script>
<style lang="scss" scoped>
.anim {
    min-height: 100vh;
    padding-bottom: 60px;
    overflow: hidden;
    background: linear-gradient(
        -30deg,
        rgba(183, 191, 255, 0.8) 46%,
        rgba(250, 251, 255, 0.8) 86%
    );
    &-card {
        position: relative;
        height: 452px;
        border-radius: 24px;
        margin: 0 38px;
        background: #fff
            url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668984495_956239_t.png)
            no-repeat;
        background-size: 100% 274px;
        background-position: left bottom;
        &-logo {
            position: absolute;
            top: 132px;
            right: 56px;
            width: 176px;
            height: 78px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668978505_115317_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        .acl-nail {
            position: absolute;
            left: 30px;
            bottom: 56px;
            width: 130px;
            height: 168px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668978095_951224_t.png)
                no-repeat;
            background-size: 100% 100%;
            > span {
                position: absolute;
                left: 108px;
                bottom: 130px;
                width: 116px;
                height: 108px;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668977904_736522_t.png)
                    no-repeat;
                background-size: 100% 100%;
                transform: rotate(-10deg);
                transform-origin: left bottom;
            }
        }
        &-info {
            padding: 64px 0 0 32px;
            > h4 {
                color: #0f1122;
                font-size: 40px;
                font-weight: 500;
                > span {
                    color: #354cce;
                }
            }
            > p {
                margin-top: 24px;
                color: #6c6d75;
                font-size: 28px;
            }
        }
        &-progress {
            position: relative;
            width: 578px;
            height: 12px;
            margin-left: 48px;
            margin-top: 182px;
            border-radius: 6px;
            background: #fff;
        }
        .acp-line {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 50%;
            background: linear-gradient(90deg, #6580fe 0%, #6541ff 100%);
            box-shadow: 0 4px 4px 0 rgba(104, 112, 235, 0.5);
            border-radius: 6px;
            &-circle {
                position: absolute;
                right: -14px;
                top: -8px;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background: #6444ff;
            }
            &-num {
                position: absolute;
                right: -60px;
                top: -80px;
                width: 116px;
                height: 64px;
                padding-top: 8px;
                color: #000000;
                font-size: 32px;
                font-weight: 500;
                text-align: center;
                background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659671655850_670323_t.png)
                    no-repeat;
                background-size: 100% 100%;
            }
        }
    }
    &-list {
        margin: 40px 38px 0;
        &-item {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding: 24px 40px 22px 24px;
            background: #fff;
            border-radius: 24px;
            background-size: 118px 112px;
            background-repeat: no-repeat;
            background-position: left center;
            transform: translate3d(0, 0, 0) scale(0);
        }
        &-item:nth-of-type(1) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668977863_572129_t.png);
        }
        &-item:nth-of-type(2) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668981741_757676_t.png);
        }
        &-item:nth-of-type(3) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668977851_988995_t.png);
        }
        &-item:nth-of-type(4) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668977853_299379_t.png);
        }
        &-item:nth-of-type(5) {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668977858_57147_t.png);
        }
        .ali-info {
            flex: 1;
            > h4 {
                color: #26273c;
                font-size: 32px;
                font-weight: 500;
            }
            > p {
                margin-top: 8px;
                color: #6c6d75;
                font-size: 28px;
            }
            .spe {
                color: #5368f0;
            }
        }
        .ali-ok {
            width: 48px;
            height: 48px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220805/1659668986387_228098_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
    }
}
@keyframes fadeInDownBig {
    from {
        opacity: 0;
        transform: translate3d(0, 0, 0) scale(1);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
.fadeInDownBig {
    animation-name: fadeInDownBig;
    animation-duration: 0.7s;
    animation-delay: 0.2s;
    animation-timing-function: ease-in;
    animation-fill-mode: forwards;
}

@keyframes rotateRing {
    from {
        transform: rotate(-10deg);
    }
    to {
        transform: rotate(10deg);
    }
}
.rotateRing {
    animation-name: rotateRing;
    animation-duration: 0.6s;
    animation-timing-function: ease;
    animation-iteration-count: infinite;
    animation-direction: alternate;
}
</style>
