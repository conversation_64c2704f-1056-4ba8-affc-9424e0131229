<template>
    <van-popup
        class="love-gene-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange">

        <h1 class="love-gene-modal__title">
            该手机已在<br>
            珍爱网APP注册过！
        </h1>

        <div class="love-gene-modal__btn love-gene-modal__btn-continue"
             v-if="registerResult.MANUAL_OVERWRITE_ACCOUNT.value === type"
             @click="selectOverwrite">
            <div>继续</div>
            <div>（原有帐号的资料将被覆盖）</div>
        </div>

        <div class="love-gene-modal__btn"
             @click="selectOrigin">
            登录原有帐号
        </div>

        <div class="love-gene-modal__cancel"
             @click="closeModal">
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { registerResult } from '../../enum';

export default {
    name: 'register-overwrite-modal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        type: {
            type: [Number, String],
            required: true,
        }
    },
    data() {
        return {
            registerResult: this.$z_.clone(registerResult),
        }
    },
    watch: {
      value: {
          handler(value) {
              if (value) {
                  this.$report(310, '已注册提醒弹窗-访问');
              }
          },
          immediate: true,
      }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        selectOverwrite() {
            this.$report(312, '已注册提醒弹窗-覆盖注册按钮点击');
            this.$emit('select-overwrite');
        },
        selectOrigin() {
            this.$report(311, '已注册提醒弹窗-登录按钮点击');
            this.$emit('select-origin');
        },
        closeModal() {
            this.$emit('input', false);
        }
    }
}
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.love-gene-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__btn {
        @include flex-center(column);
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #FFB900;
        border-radius: 44px;
        margin-bottom: 28px;

        &-continue {
            padding-top: 3px;
            > div:last-child {
                font-size: 24px;
                color: #26273C;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #6C6D75;
        text-align: center;
    }
}
</style>
