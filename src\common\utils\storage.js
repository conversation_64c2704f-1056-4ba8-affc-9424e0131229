export var storage = {
    setItem: function(k, v) {
        try {
            return localStorage.setItem(k, JSON.stringify(v));
        } catch (ex) {
            localStorage.clear();
            return false;
        }
    },
    getItem: function(k) {
        try {
            var jsonString = localStorage.getItem(k) || '';

            if (jsonString.length > 0) {
                return JSON.parse(jsonString);
            }
            return null;

        } catch (ex) {
            return null;
        }
    },
    removeItem: function(k) {
        try {
            return localStorage.removeItem(k);
        } catch (ex) {
            return false;
        }
    }
};

export var session = {
    setItem: function(k, v) {
        try {
            sessionStorage.setItem(k, JSON.stringify(v));
        } catch (ex) {
            return false;
        }
    },
    getItem: function(k) {
        try {
            var jsonString = sessionStorage.getItem(k) || '';

            if (jsonString.length > 0) {
                return JSON.parse(jsonString);
            }
            return null;

        } catch (ex) {
            return null;
        }
    },
    removeItem: function(k) {
        try {
            sessionStorage.removeItem(k);
        } catch (ex) {
            return false;
        }
    }
};
