// import { storage as Storage } from "@/common/utils/storage";
// import { findWorkCity } from "@/common/business/utils/localRegisterForm.js"; 

import Vue from 'vue';
import z_ from "@/common/zdash";
Vue.prototype.$z_ = z_;

export const locationMixin = {
    methods:{
        handleLocate: function() {
            window.AMap.plugin("AMap.Geolocation", () => {
                const geolocation = new window.AMap.Geolocation({
                // 是否使用高精度定位，默认：true
                    enableHighAccuracy: true,
                    // 设置定位超时时间，默认：无穷大
                    timeout: 5000,
                    useNative: true
                });

                // 优先拿手机的获取定位，可以拿到区
                geolocation.getCurrentPosition((status, result) => {  //获取用户当前的精确位置
                    if (status === "complete") {
                        if (result.addressComponent) {
                            const areaArr = this.handleLocationPair([result.addressComponent.province, result.addressComponent.city, result.addressComponent.district]);
                            this.$refs.refPicker.setValues(areaArr);
                        }
                    }
                });

                // 如果手机拿精准定位有问题，那么就取IP地址里的，只会返回城市
                geolocation.getCityInfo((status, result) => {
                    if(status === 'complete'){
                        const areaArr = this.handleLocationPair([result.province, result.city, '']);
                        this.$refs.refPicker.setValues(areaArr);
                    } else {
                        this.$refs.refPicker.setValues(['广东', '肇庆', '端州区']); // 缺省地区
                    }
                });


            });
        },
        handleLocationPair: function(areaArr) {
            const sliceProvince = areaArr[0].slice(0, 2);
            const sliceCity = areaArr[1].slice(0, 2);
            const sliceDistrict = areaArr[2].slice(0, 2);
            
            // 兼容不同钩子的参数名字
            const targetProvince = this.$z_.find(this.selectParam.selectArr || this.selectParam.options, province => {
                return province.text.indexOf(sliceProvince) >= 0;
            });
            const targetCity = this.$z_.find(targetProvince.children, city => {
                return city.text.indexOf(sliceCity) >= 0;
            });
            const targetDistrict = this.$z_.find(targetCity.children, district => {
                return district.text.indexOf(sliceDistrict) >= 0;
            });

            return[targetProvince.text, targetCity.text, targetDistrict.text];
        }  
    }
};