<template>
    <van-popup
        class="wechat-phone-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="wechat-phone-modal__title">
            查看完整{{ isWechat ? '微信号' : '手机号' }}的功能仅限APP内实现
        </h1>

        <p class="wechat-phone-modal__desc">
            请前往应用市场搜索下载珍爱APP
        </p>

        <div
            class="wechat-phone-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';

export default {
    name: 'ShareModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        isWechat: {
            type: Boolean,
            default: false,
        }
    },

    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.wechat-phone-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 54px 48px 48px;
    @include flex-center(column, null, center);

    &__title {
        width: 360px;
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        font-size: 28px;
        font-weight: 400;
        color: #000000;
        margin-bottom: 48px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
