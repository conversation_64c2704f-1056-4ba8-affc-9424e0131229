<template>
    <div class="container">
        <div
            class="simple-button"
            :style="customStyle"
            @click="$emit('click', $event)"
        >
            <slot />
        </div>
    </div>
</template>

<script>
export default {
    name: "SimpleButton",
    props: {
        width: {
            type: Number,
        },
        height: {
            type: Number,
            default: 96,
        },
        background: {
            type: String,
            default: '#FFFFFF',
        },
        disable: {
            type: Boolean,
            default: false,
        },
        fixBottom: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        customStyle() {
            const result = {
                background: this.disable ?  '#79797E' : this.background,
                color: this.disable ? '#FFFFFF' : '#000000',
                border: this.disable ? 'none' : '2px solid #00FFFF',
                width: this.$utils.pxToRem(this.width),
                height: this.$utils.pxToRem(this.height),
                'line-height': this.$utils.pxToRem(this.height),
            };

            if (this.fixBottom) {
                Object.assign(result, {
                    position: 'fixed',
                    bottom: this.$utils.pxToRem(48),
                });
            }

            return result;
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.container {
    width: 100%;

    // 使用 flex 居中兼容性没 relative 好
    .simple-button {
        @include relative-center();
        text-align: center;
        font-weight: 400;
        font-size: 36px;
        border-radius: 58px;
    }
}
</style>
