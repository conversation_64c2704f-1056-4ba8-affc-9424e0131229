<template>
    <div class="guide-to-matchmaker">
        <div class="inner">
            <div class="hongniang-card">
                <div class="item" v-for="t in teachers" :key="t.name">
                    <div class="content">
                        <img class="avatar" :src="t.avatar" />
                        <div class="text">
                            <div class="title">
                                累计牵线成功<span class="highlight">{{
                                    t.count
                                }}</span
                                >对情侣
                            </div>
                            <ul class="intro-list">
                                <li
                                    class="intro-item"
                                    v-for="i in t.intro"
                                    :key="i"
                                >
                                    {{ i }}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="tag">从业{{ t.years }}年</div>
                </div>
            </div>
            <!-- <div class="more-card">
                <div class="title"></div>
                <div class="more-wrapper">
                    <div class="more-item" v-for="v in moreList" :key="v">
                        <img :url="v" />
                    </div>
                </div>
            </div> -->
            <div class="more-card"></div>
        </div>
        <div class="guide-bottom">
            <button class="bottom-btn" @click="handleClick">
                完善资料
            </button>
            <div class="bubble">完善资料，红娘老师会主动与你联系</div>
        </div>
    </div>
</template>

<script>
import avatar1 from "../assets/images/teacher1.png";
import avatar2 from "../assets/images/teacher2.png";
import avatar3 from "../assets/images/teacher3.png";
import avatar4 from "../assets/images/teacher4.png";
import { mapState } from "vuex";
import {reportKibana} from '@/common/utils/report.js';
export default {
    components: {},
    data() {
        return {
            teachers: [
                {
                    avatar: avatar1,
                    name: "王英老师",
                    count: "463",
                    intro: [
                        "中科院二级心理学咨询师",
                        "擅长洞察情感需求，情感课程辅导"
                    ],
                    years: 6
                },
                {
                    avatar: avatar2,
                    name: "妍颋老师",
                    count: "387",
                    intro: [
                        "高级婚恋咨询师、家庭教育师",
                        "擅长两性指导，情感分析"
                    ],
                    years: 5
                },
                {
                    avatar: avatar3,
                    name: "兰楠老师",
                    count: "219",
                    intro: ["2021全国十佳红娘", "擅长情感疏通和情感分析"],
                    years: 3
                },
                {
                    avatar: avatar4,
                    name: "闪闪老师",
                    count: "196",
                    intro: ["高级婚恋咨询师", "擅长亲密关系问题、形象指导"],
                    years: 3
                }
            ]
            // moreList: [
            //     { avatar: "" },
            //     { avatar: "" },
            //     { avatar: "" },
            //     { avatar: "" },
            //     { avatar: "" }
            // ]
        };
    },
    computed: {
        ...mapState(['materialId'])
    },
    created() {},
    mounted() {
        reportKibana("脱单计划H5", 18, "红娘脱单页访问", { ext16: this.materialId });
    },
    methods: {
        handleClick() {
            reportKibana("脱单计划H5", 19, "红娘脱单页点击报名按钮", { ext16: this.materialId });
            this.$router.push("/collection");
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
.guide-to-matchmaker {
    width: 100vw;
    min-height: 100vh;
    @include set-img("../assets/images/bg-help.png");
    background-color: #ecf6ff;
    background-size: contain;
    background-position: 50% 0%;
    padding: 34px 0 234px;
    .inner {
        margin-top: 48px;
        .hongniang-card {
            @include set-img("../assets/images/bg-hongniang.png");
            background-size: contain;
            background-position: 0% 50%;
            margin: 0 48px;
            height: 1164px;
            padding: 178px 0 36px;
            .item {
                background-color: rgba(255, 255, 255, 0.81);
                margin: 20px 12px 0;
                border-radius: 17px;
                position: relative;
                overflow: hidden;
                &:first-child {
                    margin-top: 0;
                }
                .content {
                    display: flex;
                    padding: 20px 30px;
                    align-items: flex-end;
                    height: 220px;
                    position: relative;
                    z-index: 2;
                    .avatar {
                        flex: 0 0 auto;
                        width: 166px;
                        margin-right: 22px;
                    }
                    .text {
                        position: absolute;
                        top: 18px;
                        left: 220px;
                        flex: 1;
                        .title {
                            font-size: 32px;
                            font-family: SourceHanSansCN-Medium, SourceHanSansCN;
                            font-weight: bold;
                            color: #191c32;
                            line-height: 1;
                            margin-top: 34px;
                            .highlight {
                                font-size: 32px;
                                font-family: SourceHanSansCN-Medium, SourceHanSansCN;
                                color: #ff668a;
                            }
                        }
                        .intro-list {
                            margin-top: 24px;
                            li {
                                position: relative;
                                font-size: 24px;
                                font-weight: 400;
                                color: #3c4269;
                                line-height: 1;
                                padding-left: 18px;
                                margin-bottom: 24px;
                                &::before {
                                    content: "";
                                    display: inline-block;
                                    width: 6px;
                                    height: 20px;
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    @include set-img(
                                        "../assets/images/icon-listdot.png"
                                    );
                                }
                            }
                        }
                    }
                }
                .tag {
                    position: absolute;
                    right: 0;
                    top: 0;
                    font-size: 20px;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 34px;
                    width: 136px;
                    height: 34px;
                    background-color: #ff7599;
                    border-radius: 0 0 0 17px;
                    text-align: center;
                    z-index: 1;
                }
                &::before {
                    content: "";
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background-color: #ffcedb;
                    box-shadow: 0 196px 0 0 #ffcedb, 606px 196px 0 0 #ffcedb;
                    position: absolute;
                    top: 7px;
                    left: 6px;
                    z-index: 1;
                }
                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    top: 12px;
                    right: 10px;
                    bottom: 12px;
                    left: 10px;
                    border: 2px solid #ffcedb;
                    background-color: transparent;
                }
            }
        }
        .more-card {
            margin: 20px 50px 0;
            height: 250px;
            @include set-img("../assets/images/more-teacher-card.png");
            background-size: contain;
            background-position: 0% 50%;
        }
        // .more-card {
        //     margin: 20px 50px 0;
        //     height: 250px;
        //     background-color: #f9fbfc;
        //     border-radius: 40px;
        //     .title {
        //         width: 430px;
        //         height: 82px;
        //         @include set-img("../assets/images/bg-hongniang-title.png");
        //         background-size: contain;
        //         background-position: 0% 50%;
        //         margin: 0 auto;
        //     }
        //     .more-wrapper {
        //         display: flex;
        //         margin: 26px 0 42px;
        //         justify-content: space-between;
        //         .more-item {
        //             flex: 1;
        //             text-align: center;
        //             height: 100px;
        //             img {
        //                 width: 100px;
        //                 height: 100px;
        //                 border-radius: 50%;
        //             }
        //         }
        //     }
        // }
    }
    @include set-bottom-btn("../assets/images/bg-button-bubble.png");
}
</style>
