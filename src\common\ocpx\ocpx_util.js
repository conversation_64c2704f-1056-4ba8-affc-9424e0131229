/**
 * 参数是否定义
 * @param param
 * @returns {boolean}
 */
export function isDef(param) {
    return param !== '' && param !== null && typeof param !== 'undefined';
}

/**
 * 参数未定义
 * @param param
 * @returns {boolean}
 */
export function isUnDef(param) {
    return !isDef(param);
}

/**
 * 获取参数类型
 * @param param
 * @returns {string}
 */
export function getType(param) {
    return Object.prototype.toString.call(param).slice(8, -1)
}

/**
 * str参数包含lists所有的数据
 * @param str
 * @param lists
 * @returns {boolean}
 */
export function strMatchLists(str, lists) {
    var matchAll = true;
    for(var i = 0; i < lists.length; i += 1) {
        if (str.indexOf(lists[i]) === -1) {
            matchAll = false;
            break;
        }
    }
    return matchAll;
}

/**
 * 获取lists的所有页面url的参数，并返回结果
 * @param lists
 * @returns {{data: {}, match: boolean}}
 */
export function getUrlListsParams(lists) {
    var addParamObj = {};
    var paramAllMatch = true; // 媒体所需的所有参数都能获取
    for(var i = 0; i < lists.length; i += 1) {
        var paramKey = lists[i];
        var paramValue = Z.getParam(paramKey);
        addParamObj[paramKey] = paramValue;
        if (isUnDef(paramValue)) {
            paramAllMatch = false;
        }
    }
    return {
        data: addParamObj,
        match: paramAllMatch
    }
}
