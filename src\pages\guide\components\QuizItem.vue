<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in this.list.options"
            :key="index"
            @click="goNext(item)"
            class="item"
            :class="item === curOptionContent?'active':''"
        >
            {{ item }}
        </div>
    </div>
</template>

<script>
import { session as Session } from '@/common/utils/storage.js';

export default {
    name:"QuizItem",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data(){
        return {
            answerList:Session.getItem('sessionAnswerList') || [],
            lock:false
        };
    },
    computed:{
        curOptionContent(){
            let optionContent;
            this.answerList.forEach((item)=>{
                if(item.questionId === this.list.id){
                    optionContent = item.optionContent;
                }
            });
            return optionContent;
        }
    },
    mounted(){

    },
    methods:{
        goNext(optionContent){
            if(this.lock){
                return;
            }
            this.lock=true;

            // 存储答案至sessionStorage
            if(this.answerList.map(item=>+item.questionId).includes(this.list.id)){
                this.answerList.forEach(item=>{
                    if(item.questionId === this.list.id){
                        item.optionContent = optionContent;
                    }
                });
            }else{
                this.answerList.push({
                    questionId:this.list.id,
                    optionContent
                });
            }
            Session.setItem(`sessionAnswerList`,this.answerList);
            
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper{
    position: absolute;
    top: 360px;
    left: 50%;
    transform: translateX(-50%);
    @include flex-center(column, flex-start, center);
    > .item{
        @include flex-center(column, center, center);
        margin-top: 32px;
        padding: 0 48px;
        width: 564px;
        height: 100px;
        background: #EAF1FA;
        border-radius: 55px;
        font-size: 32px;
        color: #17263D;
        line-height: 1.2;
    }

    > .active{
        background: #17263D;
        color: #fff;
    }
}
</style>