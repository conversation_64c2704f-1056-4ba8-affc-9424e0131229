import VueRouter from "vue-router";

const Index = () => import("./views/Index");
const Form = () => import("./views/Form");
const Result = () => import("./views/Result");

const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            name: "index",
            component: Index
        },
        {
            path: "/form",
            name: "form",
            component: Form
        },{
            path: "/result",
            name: "result",
            component: Result
        }
    ],
});


// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0;

    // firefox
    document.documentElement.scrollTop = 0;

    // safari
    window.pageYOffset = 0;
});

export default router;
