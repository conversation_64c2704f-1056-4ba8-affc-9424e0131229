<template>
    <div
        class="z-image"
        :style="customStyle"
        @click="$emit('click', $event)"
    >
        <slot />
    </div>
</template>

<script>
import { createComponent } from "@/common/framework";

export default createComponent({
    props: {
        src: {
            type: String,
            required: true,
        },
        imgSize: {
            type: String,
            default: 'contain',
        },
        width: {
            type: Number,
        },
        height: {
            type: Number,
            required: true,
        },
        borderRadius: {
            type: [ Number, String ],
            default: 0,
        },
        block: {
            type: Boolean,
            default: false,
        },
        lazy: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            ready: !this.lazy,
        };
    },
    computed: {
        isRemoteImage() {
            // TODO 优化远程图片判断方式，用 http、https、// 开头作判断也不是最稳的方式
            return this.$z_.includes(this.src, 'photo.zastatic.com');
        },
        customStyle() {
            const borderRadius = this.$z_.includes(this.borderRadius, '%') ?
                this.borderRadius : this.$utils.pxToRem(this.borderRadius);

            const result = {
                borderRadius,
                backgroundSize: this.imgSize,
                display: this.block ? 'block' : 'inline-block',
            };

            if (this.$z_.isNumber(this.width)) {
                result.width = this.$utils.pxToRem(this.width);
            }

            if (this.$z_.isNumber(this.height)) {
                result.height = this.$utils.pxToRem(this.height);
            }

            if (!this.ready) {
                return;
            }

            if (this.isRemoteImage && !this.$z_.includes(this.src, 'imageMogr2/thumbnail') &&
                (this.$z_.isNumber(this.width) || this.$z_.isNumber(this.height))) {
                // https://cloud.tencent.com/document/product/460/36540
                // https://photo.zastatic.com/images/common-cms/it/20210831/1630390128852_215401_t.jpg?imageMogr2/thumbnail
                let backgroundImage = `${ this.src }?imageMogr2/thumbnail/`;

                const params = [];

                if (this.$z_.isNumber(this.width)) {
                    params.push(this.width);
                } else {
                    params.push('');
                }

                if (this.$z_.isNumber(this.height)) {
                    params.push(this.height);
                } else {
                    params.push('');
                }

                return Object.assign(result, {
                    backgroundImage: `url(${ backgroundImage + params.join('x') })`,
                });
            }

            return Object.assign(result, {
                backgroundImage: `url(${ this.src })`,
            });
        }
    },
    mounted() {
        if (!this.lazy) {
            return;
        }

        const observer = new IntersectionObserver((entries) => {
            const [ entry ] = entries;
            if (entry.intersectionRatio > 0) {
                observer.unobserve(entry.target);
                this.ready = true;
            }
        }, {
            rootMargin: '200px',
        });

        observer.observe(this.$el);
    }
});
</script>

<style lang="scss" scoped>
.z-image {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    // TODO 使用其他方式去空格
    //font-size: 0;
    overflow: hidden;
    background: center / cover no-repeat transparent;
}
</style>
