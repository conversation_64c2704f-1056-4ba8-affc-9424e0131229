<template>
    <div class="quiz" :class="{ spe: cmsConfig.reportViewType === 4 }">
        <template v-if="cmsConfig.reportViewType === 4">
            <div class="bar">
                <span :style="{ width: widths }"></span>
                <p>完成测试即可获赠【MBTI恋爱人格报告】</p>
            </div>
            <div class="caption" :class="{ convert: isConvert }">
                <span>{{ curList.title }}</span>
            </div>
            <div class="options">
                <button
                    class="options-btn"
                    v-for="option in curList.options"
                    :key="option.value"
                    :class="{
                        active: currentValue === option.value,
                        convert: isConvert && currentValue === option.value,
                        alterFont: option.value.length > 17
                    }"
                    @click="goNextQuiz(option)"
                >
                    {{ option.value }}
                </button>
            </div>
        </template>
        <template v-else>
            <div class="progress">
                <span :style="{ width: widths }"></span>
                <p>完成测试即可获赠【MBTI恋爱人格报告】</p>
            </div>
            <div class="title" :class="{ convert: isConvert }">
                <p>{{ curList.title }}</p>
            </div>
            <div class="choice">
                <button
                    class="choice-btn"
                    v-for="option in curList.options"
                    :key="option.value"
                    :class="{
                        active: currentValue === option.value,
                        convert: isConvert && currentValue === option.value,
                        alterFont: option.value.length > 17
                    }"
                    @click="goNextQuiz(option)"
                >
                    {{ option.value }}
                </button>
            </div>
        </template>
    </div>
</template>

<script>
import { QuizList } from "../lib/data.js";
import { storage } from "../lib/utils.js";
import { mapState } from "vuex";

export default {
    name: "Quiz",
    data() {
        return {
            QuizList,
            curList: {},
            curId: 0,
            currentValue: "",
            flag: false,
            widths: 0,
            isConvert: false
        };
    },
    computed: {
        ...mapState(["cmsConfig", "resourceKey"])
    },
    created() {
        this.curId = Number(this.$route.params.id) + 1;
        this.$reportKibana(
            this.resourceKey,
            this.curId + 60,
            `问题${this.curId}页访问`
        );
        this.curList = this.QuizList[this.$route.params.id];
        if(this.cmsConfig.reportViewType === 4) {
            this.widths = Math.floor((this.curId / 28) * 100) + "%";
        }else {
            this.widths = Math.floor((this.curId / 28) * 92) - 0.5 + "%";
        }

        let answer = storage.getItem("answer");
        if (answer) {
            this.currentValue =
                answer[this.$route.params.id] &&
                answer[this.$route.params.id].value;
        }
    },
    methods: {
        goNextQuiz(option) {
            if (this.flag) return;
            this.isConvert = true;
            this.flag = true;
            this.currentValue = option.value;
            let answer = storage.getItem("answer");
            if (!answer) {
                answer = [];
            }
            answer[this.$route.params.id] = option;

            storage.setItem("answer", answer);

            this.timer = setTimeout(() => {
                this.flag = false;
                this.isConvert = false;
                if (this.curId < this.QuizList.length) {
                    this.$router.push({
                        path: `/quiz/${Number(this.curId)}`
                    });
                } else {
                    this.$router.push({
                        path: `/fulfill`
                    });
                }
            }, 500);
        }
    }
};
</script>

<style lang="scss" scoped>
.quiz {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    &::after {
        content: "";
        position: fixed;
        z-index: -1;
        bottom: -38px;
        right: -52px;
        width: 338px;
        height: 284px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220425/1650867232763_646681_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    &.spe::after {
        content: "";
        position: fixed;
        z-index: -1;
        bottom: -38px;
        right: -52px;
        width: 428px;
        height: 516px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656583915108_711942_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .progress {
        position: relative;
        z-index: 1;
        margin-top: -12px;
        width: 100%;
        height: 104px;
        line-height: 104px;
        color: #fff;
        font-size: 28px;
        text-align: center;
        background: rgba(0, 255, 255, 0.1)
            url(https://photo.zastatic.com/images/common-cms/it/20220427/1651059243031_29917_t.png)
            no-repeat;
        background-size: 100% 100%;
        overflow: hidden;
        text-shadow: 0 0 10px #5db3ee;
        > span {
            position: absolute;
            z-index: 1;
            top: 12px;
            bottom: 12px;
            left: 32px;
            background: linear-gradient(
                90deg,
                #4a9cfb 0%,
                #85f9b6 55%,
                #f591f9 100%
            );
        }
        > p {
            position: relative;
            z-index: 2;
        }
    }
    .title {
        margin: 116px auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 686px;
        height: 434px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220421/1650510273722_727983_t.png)
            no-repeat;
        background-size: 100% 100%;
        transition: all 0.5s ease-out;
        transform-origin: center top;
        transform: translateX(0);
        opacity: 1;
        > p {
            max-width: 526px;
            color: #fff;
            font-size: 40px;
            font-weight: 500;
            line-height: 54px;
        }
        &.convert {
            transform: translateX(100px);
            opacity: 0.5;
        }
    }
    .choice {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 16px;
        &-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 56px;
            width: 624px;
            height: 118px;
            color: #222833;
            font-size: 34px;
            border-radius: 60px;
            border: 4px solid #3be0ef;
            transition: all 0.5s ease-out;
            transform: scale(1);
            box-sizing: border-box;
        }
        .active {
            border: 8px solid #3be0ef;
            box-sizing: border-box;
        }
        .convert {
            transform: scale(0.7);
        }
        .alterFont {
            font-size: 28px;
        }
    }
    .bar {
        position: relative;
        width: 686px;
        height: 72px;
        color: #fff;
        font-size: 26px;
        line-height: 72px;
        text-align: center;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656581854234_452846_t.png)
            no-repeat;
        background-size: 100% 100%;
        border-radius: 40px;
        overflow: hidden;
        &::after {
            content: "";
            position: absolute;
            z-index: 2;
            left: 12px;
            top: 8px;
            width: 662px;
            height: 30px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656926519426_636347_t.png) no-repeat;
            background-size: 100% 100%;
        }
        > span {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 1;
            background: linear-gradient(90deg, #9d91ff 0%, #6381ff 100%);
            border-radius: 40px 0 0 40px;
        }
        > p {
            position: relative;
            z-index: 3;
        }
    }
    .caption {
        height: 108px;
        margin-top: 64px;
        margin-right: auto;
        padding-left: 114px;
        padding-right: 26px;
        color: #fff;
        font-size: 36px;
        font-weight: 500;
        line-height: 1.5;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656577029972_719710_t.png)
            no-repeat;
        background-size: 60px 54px;
        background-position: left 38px top;

        > span {
            position: relative;
            top: 6px;
        }
    }
    .options {
        display: flex;
        flex-direction: column;
        align-items: center;
        &-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 40px;
            width: 624px;
            height: 120px;
            color: #05032a;
            font-size: 34px;
            transition: all 0.5s ease-out;
            transform: scale(1);
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656583393343_128864_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        &-btn:first-of-type {
            margin-top: 78px;
        }
        .active {
            color: #fff;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656583393428_899660_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
        .convert {
            transform: scale(0.9);
        }
        .alterFont {
            font-size: 28px;
        }
    }
}
</style>
