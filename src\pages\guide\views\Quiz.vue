<template>
    <div
        class="quiz"
        :class="{'is-test': page_type === 79}"
    >
        <div class="panel">
            <div class="panel-type">
                {{ curQuiz.type }}
            </div>
            <div 
                ref="refProgress"
                class="panel-progress"
            >
                <div 
                    class="panel-progress-bar"
                    :style="{width: `${curProgress}px`}"
                ></div>
            </div>
            <div class="panel-title">
                {{ curQuiz.title }}
            </div>

            <!-- 动态组件 -->
            <component
                :is="curComp"
                :list="curQuiz"
                @go-next="goNext"
            />
            <div 
                v-show="isNormalQuiz && currentIndex > 0"
                @click="goBack"
                class="panel-back"
            >
                上一题
            </div>
        </div>
    </div>
</template>

<script>
import { PAGE_TYPE } from "../config";
import { pageTypeChnMap } from "@/common/config/register-dictionary.js";
import { NORMAL_QUIZ_TYPES, QUIZ_LIST } from '../config';
import QuizItem from '../components/QuizItem.vue';
import Gender from "../components/RegForm/gender.vue";
import Birthday from "../components/RegForm/birthday.vue";
import Education from "../components/RegForm/education.vue";
import Marriage from "../components/RegForm/marriage.vue";
import Salary from "../components/RegForm/salary.vue";
import WorkCity from "../components/RegForm/workcity.vue";

export default {
    name:"Quiz",
    components:{
        QuizItem,
        Gender,
        Birthday,
        Education,
        Marriage,
        Salary,
        WorkCity,
    },
    data(){
        return {
            quizList: QUIZ_LIST,
            currentIndex: 0,
            rendered: false,
            page_type: pageTypeChnMap[PAGE_TYPE],
        };
    },
    computed:{
        curQuiz(){
            return this.quizList[this.currentIndex];
        },
        curComp(){
            return this.isNormalQuiz ? 'QuizItem' : this.curQuiz.comp;
        },
        isNormalQuiz(){
            return NORMAL_QUIZ_TYPES.includes(this.curQuiz.type);
        },
        curProgress(){
            if(this.rendered){
                const fullLength = this.$refs.refProgress.offsetWidth;
                return this.currentIndex/this.quizList.length * fullLength;
            }
            return 0;
        }
    },
    mounted(){
        this.rendered = true;
        this.currentIndex = +this.$route.params.id;
        // 打桩区分 普通题目页 和 注册信息页
        if(this.isNormalQuiz){
            this.$report(this.currentIndex+4, `题目${ this.currentIndex+1 }页访问`);
        }
    },
    methods:{
        goNext(){
            this.currentIndex++;
            this.$router.push({
                path:`/quiz/${this.currentIndex}`
            });
        },
        goBack(){
            this.currentIndex --;
            this.$router.push({
                path:`/quiz/${this.currentIndex}`
            });
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.quiz{
    width: 750px;
    height: 100vh;
    @include set-img("https://photo.zastatic.com/images/common-cms/it/20221115/1668478265933_906032_t.png");
    background-size: cover;
    overflow: hidden;

    &.is-test {
        background-image: url(https://photo.zastatic.com/images/common-cms/it/20230516/1684220211223_573572_t.png);
    }

    .panel{
        margin: 74px auto 0;
        padding: 0 48px;
        width: 686px;
        height: 1128px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668154375892_888557_t.png");
        overflow: hidden;

        &-type{
            margin-top: 74px;
            font-weight: 600;
            font-size: 40px;
            color: #30A5CD;
            
        }

        &-progress{
            margin-top: 16px;
            width: 590px;
            height: 14px;
            background: #F0F0F0;
            border-radius: 20px;

            &-bar{
                height: 14px;
                background: #36A7CF;
                border-radius: 20px;
            }
        }

        &-title{
            margin-top: 32px;
            font-weight: 600;
            font-size: 32px;
            color: #162338;
            line-height: 1.2;
        }

        &-back{
            position: absolute;
            top: 1080px;
            padding-left: 18px;
            width: 200px;
            height: 60px;
            background: #EAF1FA;
            border-radius: 55px;
            font-weight: 600;
            font-size: 28px;
            color: #919191;
            line-height: 60px;
            text-align: center;

            &::before{
                content: '';
                position: absolute;
                left: 34px;
                top: 50%;
                transform: translateY(-50%);
                width: 28px;
                height: 28px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20221111/1668155900615_731850_t.png");
            }
        }
    }
}
</style>