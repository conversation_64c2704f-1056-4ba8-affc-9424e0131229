<template>
    <div class="quiz">
        <div class="panel">
            <div v-if="currentIndex == 0" class="sex">
                请问你的性别是
            </div>

            <!-- 动态组件 -->
            <component
                :is="curComp"
                :list="curQuiz"
                @go-next="goNext"
            />
        </div>
    </div>
</template>

<script>
import { QUIZ_LIST_REGISTER } from '../config';
import QuizItem from '../components/QuizItem.vue';
import Gender from "../components/RegForm/gender.vue";
import Birthday from "../components/RegForm/birthday.vue";
import Education from "../components/RegForm/education.vue";
import Marriage from "../components/RegForm/marriage.vue";
import Salary from "../components/RegForm/salary.vue";
import WorkCity from "../components/RegForm/workcity.vue";
import Height from "../components/RegForm/Height.vue";

import { answerResultId } from '../api';
import { storage,session as Session } from "@/common/utils/storage";
export default {
    name:'Register',
    components:{
        QuizItem,
        Gender,
        Birthday,
        Education,
        Marriage,
        Salary,
        WorkCity,
        Height
    },
    data(){
        return {
            reList: QUIZ_LIST_REGISTER, //注册列表
            currentIndex: 0, //当前项
            rendered: false,
        };
    },
    activated(){
        this.rendered = true;
        this.currentIndex = +this.$route.params.id;
        switch(this.curComp){
        case 'Gender':
            this.$report(22, '性别页访问');
            break;
        case 'WorkCity':
            this.$report(24, '工作地页访问');
            break;
        case 'Birthday':
            this.$report(26, "出生年份页访问");
            break;
        case 'Height':
            this.$report(28, "身高页访问");
            break;
        case 'Education':
            this.$report(30, '学历页访问');
            break;
        case 'Marriage':
            this.$report(32, '婚况页访问');
            break;
        case 'Salary':
            this.$report(34, '收入页访问');
            break;
        }



    },
    computed:{
        curQuiz(){
            return this.reList[this.currentIndex];
        },
        curComp(){
            return this.curQuiz.comp;
        },
        // curProgress(){
        //     if(this.rendered){
        //         const fullLength = this.$refs.refProgress.offsetWidth;
        //         return this.currentIndex/this.quizList.length * fullLength;
        //     }
        //     return 0;
        // }
    },
    created() {
        //获取答题
        // let questionList = JSON.parse(localStorage.getItem('questionList'));
        // this.reList = JSON.parse(questionList);


    },
    mounted(){
    },
    methods:{
        async goNext(){
            //如果是最后一道题 跳转到手机注册页面
            if(this.reList.length  === (this.currentIndex + 1) ){
                this.$router.push({
                    path:`/successAnimation`
                });
                return;
            }
            this.currentIndex++;
            this.$router.push({
                path:`/register/${this.currentIndex}`
            });
        },
        goBack(){
            this.currentIndex --;
            this.$router.push({
                path:`/register/${this.currentIndex}`
            });
            // this.$router.back();
        }

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.quiz{
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;

    .panel{
        margin-top: 80px;
        width: 99vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        .sex {
            font-size: 40px;
            font-weight: 400;
        }
        // margin-top: 128px;
        .panel_div{
            margin-top: 136px;
            display: flex;
            flex-direction: column;
            align-items: center;
            p{
                font-weight: Oblique;
                height: 48px;
                font-size: 30px;
                text-align: center;
                color: #8A54F8;
                letter-spacing: 2px;
                margin-bottom: 10px;

            }
        }

        .panel-progress{
            width: 590px;
            height: 24px;
            border-radius: 20px;
            width: 486px;
            border:1px solid #8a54f8;
            display: flex;
            margin-right: 11px;


            .panel-progress-bar{
                height: 24px;
                background: #8a54f8;
                border-radius: 20px;

            }
        }

        &-title{
            margin-top: 110px;
            font-weight: 400;
            line-height: 1.2;
            font-family: 'love';
            margin-bottom: 14px;
            font-size: 52px;
            letter-spacing:4px;
            // margin-bottom: 60px;
        }


    }
    .top{
        position: fixed;
        top: 0px;
        width: 100vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 34px 25px 0 25px;
    }
    .panel-back{
        width: 147px;
        height: 56px;

    }
    .music{
        width: 74px;
        height: 74px;
    }
    .hidden_mis{
        justify-content: flex-end;
    }
    .flex_box{
        display: flex;
        font-family: 'love';
            height: 27px;
            font-size: 20px;
            text-align: left;
            color: #8A54F8 ;
            letter-spacing: 4px;
            // #9a55f0
    }
    .bottomtext{
        margin-bottom: 130px;
    }
}
</style>
