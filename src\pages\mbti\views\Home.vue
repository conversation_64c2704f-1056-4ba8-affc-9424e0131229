<template>
    <div class="home">
        <template v-if="cmsConfig.reportViewType === 4">
            <div class="caption"></div>
            <div class="open">
                <div class="open-box1"></div>
                <div class="open-box2"></div>
            </div>
            <div class="footer-fix">
                <div
                    class="begin"
                    @click="goStart"
                >
                    {{ this.cmsConfig.homeButtonText || "开启测试" }}
                </div>
                <common-protocol
                    class="home-wrapper__protocol"
                    :is-checked.sync="isCheckProtocol"
                    :agreement-status="cmsConfig.agreementStatus"
                    :style-config="{
                        textColor: '#fff',
                        protocolColor: '#5AD9FF',
                        protocolCheckedUrl:
                            'https://photo.zastatic.com/images/common-cms/it/20220704/1656926625011_96486_t.png'
                    }"
                />
            </div>
        </template>
        <template v-else>
            <div class="title"></div>
            <div
                class="start"
                @click="goStart"
            >
                <anim-svga>
                    <span>{{ cmsConfig.homeButtonText || "开启测试" }}</span>
                </anim-svga>
            </div>
            <common-protocol
                class="home-wrapper__protocol"
                :is-checked.sync="isCheckProtocol"
                :agreement-status="cmsConfig.agreementStatus"
                :style-config="{
                    textColor: '#fff',
                    protocolColor: '#0ff',
                    protocolCheckedUrl:
                        'https://photo.zastatic.com/images/common-cms/it/20220420/1650456153439_52212_t.png'
                }"
            />
        </template>
        <protocol-modal
            v-model="isShowModal"
            @confirm="confirmCheck"
        />
    </div>
</template>

<script>
import { mapState } from "vuex";
import { storage } from "../lib/utils.js";
import ProtocolModal from "../components/modal/ProtocolModal.vue";
import CommonProtocol from "@/common/business/CommonProtocol.vue";
import AnimSvga from "../components/AnimSvga.vue";

export default {
    name: "Home",
    data() {
        return {
            isCheckProtocol: true,
            isShowModal: false,
        };
    },
    created() {
        storage.removeItem("single");
        storage.removeItem("answer");
        storage.removeItem("userPath");
        this.$watch(
            "cmsConfig.agreementStatus",
            value => {
                if (value === 1) {
                    this.isCheckProtocol = false;
                }
            },
            {
                immediate: true
            }
        );
    },
    components: {
        ProtocolModal,
        CommonProtocol,
        AnimSvga
    },
    computed: {
        ...mapState(["cmsConfig", "resourceKey"])
    },
    watch: {
        resourceKey(val) {
            this.$reportKibana(val, 1, "首页访问", {ext1: location.href});
            this.$reportKibana(val, 3000, "首页访问（监控）");
        }
    },
    methods: {
        goStart() {
            if (this.cmsConfig.agreementStatus && !this.isCheckProtocol) {
                this.isShowModal = true;
                return;
            }
            this.isCheckProtocol = true;
            setTimeout(() => {
                this.$router.push({
                    path: "/single"
                });
            }, 200);
        },
        confirmCheck() {
            this.isShowModal = false;
            this.isCheckProtocol = true;
            setTimeout(() => {
                this.$router.push({
                    path: "/single"
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss" scoped>
.home {
    position: relative;
    z-index: 1;
    color: #fff;
    .tips {
        display: flex;
        justify-content: flex-end;
        width: fit-content;
        margin-top: 40px;
        margin-left: auto;
        text-align: center;
    }
    .title {
        width: 100%;
        height: 584px;
        margin-top: 68px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220826/1661485269684_975561_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .caption {
        position: relative;
        z-index: 1;
        width: 618px;
        height: 338px;
        margin: -80px auto 0;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220826/1661485197709_702423_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .open {
        position: relative;
        width: 750px;
        height: 862px;
        margin-top: -70px;
        overflow: hidden;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220630/1656573187494_413888_t.png)
            no-repeat;
        background-size: 100% 100%;
        &-box1 {
            position: absolute;
            top: 146px;
            left: 90px;
            width: 570px;
            height: 570px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656925645448_115774_t.png)
                no-repeat;
            background-size: 100% 100%;
            transform: rotate(0deg);
            animation: rotateZ 6s linear infinite;
        }
        &-box2 {
            position: absolute;
            top: 72px;
            left: 16px;
            width: 718px;
            height: 718px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656925645518_610729_t.png)
                no-repeat;
            background-size: 100% 100%;
            animation: rotateZ 7s linear infinite;
        }
    }
    .begin {
        width: 476px;
        height: 140px;
        margin: 0 auto 70px;
        line-height: 140px;
        color: #fff;
        font-size: 46px;
        text-align: center;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220704/1656931632682_459807_t.png)
            no-repeat;
        background-size: 100% 100%;
    }
    .footer-fix {
        position: fixed;
        bottom: 24px;
        left: 46px;
    }
}

@keyframes rotateZ {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
