<template>
    <div class="question">
        <div class="question_tab" @click="goPrev">
            <div class="question_tab_arrow"></div>
            <span v-show="$route.params.id !== '0'">上一题</span>
        </div>
        <div class="question_title" :class="{spe: currentPage.question.length > 70}">
            <p>{{ currentPage.question }}</p>
        </div>
        <section class="question_item" id="quesItem">
            <keep-alive>
                <component
                    :is="QuesEnum[currentPage.type]"
                    :list="currentPage"
                    :key="currentPage.question"
                />
            </keep-alive>
        </section>
    </div>
</template>

<script>
import Birthday from "../components/birthday.vue";
import Education from "../components/education.vue";
import Gender from "../components/gender.vue";
import Height from "../components/height.vue";
import Marriage from "../components/marriage.vue";
import Ques from "../components/ques.vue";
import Salary from "../components/salary.vue";
import WorkCity from "../components/workCity.vue";
import { openPlayer } from "../lib/common/player.js";
import Player from "../components/player.vue";
const QuesEnum = {
    1: Ques,
    2: Gender,
    3: WorkCity,
    4: Birthday,
    5: Height,
    6: Education,
    7: Marriage,
    8: Salary
};

export default {
    name: "Question",
    components: {
        Birthday,
        Education,
        Gender,
        Height,
        Marriage,
        Ques,
        Salary,
        WorkCity
    },
    data() {
        return {
            contentList: [],
            currentPage: [],
            QuesEnum,
            provinceDict: [],
            heightDict: [],
            educationDict: [],
            salaryDict: [],
            marriageDict: [],
            timer: null
        };
    },
    watch: {
        $route() {
            if (this.$route.params.id < this.contentList.length) {
                this.init();
            } else if (this.$route.params.id >= this.contentList.length) {
                this.$router.replace({
                    path: "/login"
                });
            }
        }
    },
    async created() {
        this.init();
    },
    activated() {
        const trigger = document.querySelector("#trigger");
        if(trigger) {
            trigger.style.opacity = 1;
        }else {
            openPlayer(Player, { musicUrl: window.localStorage.getItem("musicUrl"), autoplay: false });
        }
    },
    methods: {
        init() {
            this.contentList = JSON.parse(
                window.sessionStorage.getItem("contentList")
            );

            this.currentPage = this.contentList[this.$route.params.id];
        },
        goPrev() {
            if (this.$route.params.id === "0") {
                this.$router.push({
                    path: "/index"
                });
                return;
            }
            if (this.timer) {
                clearTimeout(this.timer);
            }
            this.timer = setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) - 1}`
                });
            }, 300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../assets/css/common.scss";
.question {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    text-align: center;
    &_tab {
        display: flex;
        align-items: center;
        margin: 64px 72px 0 50px;
        height: 72px;
        color: #fff;
        font-size: 30px;
        &_arrow {
            width: 72px;
            height: 72px;
            margin-right: 6px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220117/1642415397017_645063_t.png)
                no-repeat;
            background-size: 100% 100%;
        }
    }
    &_title {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 200px;
        margin: 0 84px;
        text-align: center;
        color: #fff;
        font-size: 36px;
        font-weight: 500;
        line-height: 48px;
        overflow-y: scroll;
        & >p {
            margin-top: 20px;
            display: inline-block;
            text-align: left;
        }
        &.spe {
            display: inline-block;
        }
    }
    &_item {
        max-height: 860px;
        overflow-y: scroll;
    }
}
</style>
