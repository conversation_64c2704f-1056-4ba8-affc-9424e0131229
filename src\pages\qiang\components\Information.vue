<template>
    <div class="information-wrap">
        <div class="header">
            <img
                :src="memberInfo.photo1 && `${memberInfo.photo1}?imageMogr2/rquality/35`"
                alt="图片1"
            >
            <div>
                <img
                    :src="memberInfo.photo2 && `${memberInfo.photo2}?imageMogr2/rquality/35`"
                    alt="图片2"
                >
                <img
                    :src="memberInfo.photo3 && `${memberInfo.photo3}?imageMogr2/rquality/35`"
                    alt="图片3"
                >
            </div>
        </div>
        <div
            class="tag"
        >
            <div :style="{'background' : styleConfig.indexInformationCard.tagBgColor, 'color': styleConfig.indexInformationCard.tagColor}">
                <img :src="styleConfig.leftIcon">
                珍爱会员ID：<span>{{ memberInfo && memberInfo.memberId }}</span>
            </div>
            <div
                :style="{'color': styleConfig.indexInformationCard.tagCopyColor, 'border': `2px solid ${styleConfig.indexInformationCard.tagCopyColor}`}"
                @click="handleCopy(memberInfo.memberId)"
            >
                复制
            </div>
        </div>
        <div class="detail">
            <div
                class="name"
                :style="{'color': styleConfig.indexInformationCard.titleColor}"
            >
                {{ memberInfo && memberInfo.nickName }}
            </div>
            <div class="info">
                <div
                    v-for="(item, key) in this.detailInfo"
                    :key="key"
                >
                    <span :style="{'color': styleConfig.mainColor}">{{ item }}：</span>
                    <span :style="{'color': styleConfig.indexInformationCard.fontColor}">{{ memberInfo && memberInfo[key] }}</span>
                </div>
            </div>
        </div>
        <div
            class="interest"
            v-if="!isEmptyHobbies"
        >
            <div :style="{'color': styleConfig.mainColor}">
                {{ cmsConfig.reportViewType === 1 ? '她' : '他' }}的兴趣爱好
            </div>
            <div>
                <span
                    v-for="(item, key) in filterHobbies"
                    :key="key"
                    :style="{'border': `2px solid ${styleConfig.mainColor}`, 'color': styleConfig.indexInformationCard.fontColor}"
                >
                    <img :src="interestMap[key]">
                    <span>{{ item }}</span>
                </span>
            </div>
        </div>
        <div
            class="button"
            :style="{'backgroundColor': styleConfig.mainColor, 'color': styleConfig.buttonFontColor}"
            @click="handleGetContract"
        >
            <img :src="styleConfig.leftIcon">
            <span>{{ cmsConfig.homeButtonText }}</span>
            <img :src="styleConfig.rightIcon">
        </div>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    name: "Information",
    inject: ["cmsConfig", "styleConfig"],
    props: {
        memberInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            detailInfo: {
                occupation: '职业',
                education: '学历',
                age: '年龄',
                salary: '收入',
            }
        };
    },

    computed: {
        isQiang() {
            return this.cmsConfig.reportViewType === 1;
        },
        filterHobbies() {
            return this.$z_.pickBy(this.memberInfo.hobbies, o => o !== '');
        },
        isEmptyHobbies() {
            return this.$z_.isEmpty(this.filterHobbies);
        },
        interestMap() {
            return {
                food: this.isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658115534384_90678_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658287959375_529646_t.png',
                song: this.isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658116473915_723531_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658288048483_282654_t.png',
                book: this.isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658116543813_181209_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658288103193_226931_t.png',
                celebrity: this.isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658116543809_111748_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658288115511_911087_t.png',
                something: this.isQiang ? 'https://photo.zastatic.com/images/common-cms/it/20220718/1658116543792_145629_t.png' : 'https://photo.zastatic.com/images/common-cms/it/20220720/1658288115446_895614_t.png'
            };
        }
    },

    methods: {
        handleGetContract() {
            this.$emit('handleJump');
        },

        handleCopy(memberId) {
            const input = document.createElement("input"); //
            input.value = memberId; // 设置复制内容
            document.body.appendChild(input); //
            input.select();
            document.execCommand("Copy");
            document.body.removeChild(input);
            this.$report(3, '首页-复制id按钮点击');
            Toast('复制会员ID成功，快去珍爱APP搜索吧~');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.information-wrap {
    width: 590px;
    .header {
        @include flex-center(row, space-between, null);
        >img:nth-child(1) {
            width: 388px;
            height: 388px;
            border-radius: 16px;
            object-fit: cover;
            object-position: 50% 35%;
        }
        >div:nth-child(2) {
            @include flex-center(column, space-between, null);
            img {
                width: 184px;
                height: 184px;
                border-radius: 16px;
                object-fit: cover;
                object-position: 50% 35%;
            }
        }
    }

    .tag {
        margin: 40px 0px;
        display: flex;
        >div:nth-child(1) {
            position: relative;
            z-index: 2;
            display: inline-block;
            border-radius: 26px;
            padding-left: 24px;
            padding-right: 32px;
            font-size: 24px;
            height: 52px;
            line-height: 52px;
            span {
                font-weight: 600;
                font-size: 28px;
            }
            img {
                vertical-align: middle;
                width: 34px;
                height: 36px;
            }
        }
        >div:nth-child(2) {
            width: 156px;
            height: 52px;
            line-height: 52px;
            margin-left: -60px;
            padding-left: 68px;
            font-weight: 500;
            font-size: 28px;
            border-radius: 26px;
        }
    }

    .detail {
        .name {
            margin-bottom: 14px;
            font-weight: 600;
            font-size: 36px;
        }
        .info {
            margin-bottom: 48px;
            >div {
                >span:nth-child(1) {
                    font-weight: 400;
                    font-size: 28px;
                }
                >span:nth-child(2) {
                    font-weight: 400;
                    font-size: 28px;
                }
            }
        }
    }

    .interest {
        >div:nth-child(1) {
            font-weight: 500;
            font-size: 32px;
            margin-bottom: 32px;
        }
        >div:nth-child(2) {
            >span {
                display: inline-block;
                margin-bottom: 16px;
                margin-right: 16px;
                padding: 10px 32px 10px 10px;
                font-size: 28px;
                border-radius: 32px;
                img {
                    width: 36px;
                    height: 36px;
                }
                span {
                    position: relative;
                    top: -7px;
                }              
            }
        }
    }

    .button {
        @include flex-center(row, center, center);
        margin: 48px auto 0px;
        width: 462px;
        height: 88px;
        font-size: 42px;
        font-family: 'fz';
        border-radius: 76px;
        img {
            width: 42px;
            height: 46px;
        }
        >img:nth-child(1) {
            margin-right: 14px;
        }
        >span:nth-child(2) {
            position: relative;
            top: 4px;
        }
        >img:nth-child(3) {
            margin-left: 14px;
        }
    }
}
</style>