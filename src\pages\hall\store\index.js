import Vuex from 'vuex';
import Api from '@/common/server/base';
import * as dict from "@/common/config/register-dictionary";
import z_ from "@/common/zdash";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import oUserSelect from '@/common/ocpx/huichuan';

export default new Vuex.Store({
    state: {
        formItems: [
            {
                key: "workCity",
                label: "您的工作地",
                value: "",
                options: Z.workCity
            },
            {
                key: "birthday",
                label: "您的出生年份",
                value: "",
                options: dict.birthday
            },
            {
                key: "education",
                label: "您的学历",
                value: "",
                options: dict.education
            },
            {
                key: "marriage",
                label: "您的婚姻状况",
                value: "",
                options: dict.marriage
            },
            {
                key: "salary",
                label: "您的月收入",
                value: "",
                options: dict.salary
            }
        ],
        requirement: {
            tags: [],
            driftTag: {
                text:'',
                check:false
            },
            selection: null
        },
        registerForm: {
            gender: -1,
            workCity: '',
            birthday: '',
            marriage: '',
            salary: '',
            education: '',
            phone: ''
        },
        isDefaultCmsConfig: false,
        cmsConfig: {
            homeBackgroundImg: '',
            homeButtonText: '',
            homeButtonColor: '',
            downloadStatus: -1,
            agreementStatus: -1,
        },
        regMemberId: '',
    },
    mutations: {
        setRegMemberId(state, target) {
            state.regMemberId = target;
            Session.setItem('reg_memberid', target);
            console.log('reg_memberid: ', target);
            oUserSelect.mark({
                msgValid: true
            });
        },
        setIsDefaultCmsConfig(state, target) {
            state.isDefaultCmsConfig = target;
        },
        setRequirement(state, target) {
            state.requirement = Object.assign(state.requirement, target);
            Storage.setItem('cachedRequirement', state.requirement);
        },
        setRegisterForm(state, target) {
            const key = z_.get(target, 'key');
            const value = z_.get(target, 'value');

            state.registerForm = Object.assign(state.registerForm, {
                [key]: value
            });

            let isMark = z_.get(target, 'isMark');
            isMark = z_.isNil(isMark) ? true : isMark;

            if (isMark) {
                const getMarkValue = z_.get(target, 'getMarkValue') || (() => {
                    return {
                        [key]: value,
                    }
                });

                const markValue = getMarkValue();
                if (!z_.isEmpty(markValue) && !z_.isNil(markValue)) {
                    oUserSelect.mark(markValue);
                }
            }

            Storage.setItem('cachedHallRegisterForm', state.registerForm);
        },
        setFormItems(state, target) {
            state.formItems.forEach(item => {
                Object.keys(target).forEach(key => {
                    if (item.key === key) {
                        item.value = target[key];
                    }
                })
            });

            Storage.setItem('cachedHallFormItems', state.formItems);
        },
        setCmsConfig(state, { data, storageKey }) {
            state.cmsConfig = Object.assign(state.cmsConfig, z_.pick(data, Object.keys(state.cmsConfig)));
            if (storageKey && !z_.isNil(state.cmsConfig)) {
                Storage.setItem(storageKey, state.cmsConfig);
            }
        },
        initRequirement(state) {
            const cachedRequirement = Storage.getItem('cachedRequirement');
            if (cachedRequirement) {
                state.requirement = Object.assign(state.requirement, cachedRequirement);
            }
        },
        initRegisterForm(state) {
            const cachedHallRegisterForm = Storage.getItem('cachedHallRegisterForm');

            if (cachedHallRegisterForm) {
                state.registerForm = Object.assign(state.registerForm, cachedHallRegisterForm);
            }
        },
        initFormItems(state) {
            const cachedHallFormItems = Storage.getItem('cachedHallFormItems');

            if (cachedHallFormItems) {
                state.formItems = Object.assign(state.formItems, cachedHallFormItems);
            }
        }
    },
    actions: {
        async initCmsConfig({ commit }) {
            const id = Z.getParam('materialId');
            const defaultConfig = {
                homeBackgroundImg: require('../assets/images/home-bg.png'),
                homeButtonText: '点击开始约会',
                homeButtonColor: '#FFB900',
                agreementStatus: 0,
                downloadStatus: 0,
            };

            const loadDefaultConfig = () => {
                commit('setCmsConfig', {
                    data: defaultConfig,
                });
                commit('setIsDefaultCmsConfig', true);
            }

            if (!id) {
                loadDefaultConfig();
                return;
            }

            const storageKey = `hallCmsConfig_${ id }`;
            const cachedData = Storage.getItem(storageKey);
            if (cachedData) {
                commit('setCmsConfig', {
                    storageKey,
                    data:cachedData
                });
            }

            let result = await Api.getMaterial({
                id,
            });

            if (result.isError) {
                loadDefaultConfig();
                return;
            }

            const data = z_.get(result, 'data.materialVo');

            // 处理渐变
            function handleColor(color) {
                if (color.indexOf('&') > -1) {
                    // 按钮为渐变色
                    let [topColor, bottomColor] = color.split("&");
                    return `linear-gradient(0deg,${topColor},${bottomColor})`;

                }
                return color;
            }

            data.homeButtonColor = handleColor(data.homeButtonColor);

            commit("setCmsConfig", {
                storageKey,
                data
            });
        },
    }
});
