import VueRouter from "vue-router";

import BlindInfo from "../views/BlindInfo.vue";
import Collection from "../views/Collection.vue";
import Info from "../views/Info.vue";

const router = new VueRouter({
    mode:"hash",
    routes:[
        {
            path:"/",
            redirect:"/index"
        },
        {
            path:"/index",
            component:Collection
        },
        {
            path:"/blindinfo",
            component:BlindInfo
        },
        {
            path:"/info",
            component:Info
        }
    ],
    scrollBehavior (to, from, savedPosition) {
        return {x:0, y:0}
    }
});

// router.beforeEach((to,from,next) => {
//     if(to.path ==="/index"){
//         console.log("from:",from.path)
//         console.log("to:",to.path)
//         next(true);
//         // next({
//         //     path:"blind",
//         //     query:{
//         //         xixi:2
//         //     }
//         // })
//     } else {
//         next();
//     }
//     // next({
//     //     path:to.path,
//     //     query:{
//     //         xixi:222
//     //     }
//     // });
// })

export default router;
