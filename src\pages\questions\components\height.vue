<template>
    <div
        class="height"
        id="height"
    >
        <div
            class="height_card"
            v-for="(arr, key) in heightDict"
            :key="key"
        >
            <div class="height_card_left">
                {{ key + "cm" }}
            </div>
            <div class="height_card_info">
                <div
                    class="box"
                    v-for="(arr2, i) in arr"
                    :key="i"
                >
                    <div
                        v-for="(item, index) in arr2"
                        :key="index"
                        class="box_item"
                        :class="
                            item.disable
                                ? 'disabled'
                                : selected === item.key
                                    ? 'selected'
                                    : ''
                        "
                        @click="goNext(item)"
                    >
                        {{ item.key }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { height } from "../lib/common/dict";
export default {
    name: "Height",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            heightDict: height,
            selected: -1
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            5, // 记录点
            "注册-身高曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    mounted() {
        this.$nextTick(() => {
            const cardHeight = document.querySelector(".height_card")
                .offsetHeight;
            document.querySelector("#quesItem").scrollTop = cardHeight * 4;
        });
    },
    activated() {
        const cardHeight = document.querySelector(".height_card").offsetHeight;
        document.querySelector("#quesItem").scrollTop = cardHeight * 4;
    },
    methods: {
        goNext(option) {
            if (option.disable) {
                return;
            }
            this.selected = option.key;
            this.$select.mark({
                height: option.key
            });
            this.$storage.saveToStorage("__regInfo__", "height", option.key);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>
<style lang="scss" scoped>
.height {
    padding-top: 76px;
    padding-bottom: 40px;
    &_card {
        display: flex;
        margin: 0 48px 32px;
        padding: 32px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 60px;
    }
    &_card_left {
        position: relative;
        margin-right: 36px;
        color: #767dff;
        font-size: 36px;
        font-weight: 500;
        &::after {
            content: "";
            position: absolute;
            top: 50px;
            bottom: 0;
            left: 50%;
            width: 2px;
            background: #767dff;
        }
    }
    &_card_info {
        display: flex;
        flex-direction: column;
        color: #26273c;
        font-size: 32px;
        .box {
            display: flex;
            margin-bottom: 32px;
        }
        .box_item {
            padding-right: 70px;
            &.disabled {
                opacity: 0.5;
            }
            &.selected {
                color: #8c7afe;
            }
        }
    }
    .height_card_info .box:last-child {
        margin-bottom: 0;
    }
}
</style>
