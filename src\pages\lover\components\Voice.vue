<template>
    <div class="voice">
        <div
            class="voice-item"
            @click="goChoice(option.desc, index)"
            :class="{ active: selectIndex === option.desc }"
            v-for="(option, index) in options"
            :key="index"
        >
            {{ option.desc }}
            <div
                class="voice-anim"
                :id="`option${index}`"
                :class="{ isShow: selectIndex === option.desc && isPlaying }"
            ></div>
            <audio
                :id="`player${index}`"
                :src="option.url"
            ></audio>
        </div>
        <common-btn
            btnText="下一步"
            :btnStyle="btnStyleObj.active"
            :class="{ disabled: !curVoice }"
            @goNext="goNext()"
        />
    </div>
</template>
<script>
import CommonBtn from "@/common/businessV2/components/CommonBtn.vue";
import { session as Session } from "@/common/utils/storage";
import { reportKibana } from "@/common/utils/report";
import { girlVoice, boyVoice } from "../voice.js";

export default {
    name: "Voice",
    props: {
        btnStyleObj: {
            type: Object,
            default: () => {}
        },
        type: {
            type: String
        },
        pageType: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            selectIndex: Session.getItem(this.type) || "",
            options: Session.getItem("gender") ? boyVoice : girlVoice,
            playerAnim: null,
            player: null,
            timer: null,
            isPlaying: false
        };
    },
    computed: {
        curVoice() {
            return this.options.filter(
                item => item.desc === this.selectIndex
            )[0];
        }
    },
    mounted() {
        this.preloadImg();
    },
    components: {
        CommonBtn
    },
    methods: {
        preloadImg() {
            let image = new Image();
            image.src =
                "https://photo.zastatic.com/images/common-cms/it/20220902/1662119402886_859321_t.png";
        },
        goNext() {
            if (!this.curVoice) {
                reportKibana(
                    this.pageType,
                    3,
                    "定制声音页-下一步按钮点击（未选声音时）"
                );
                return this.$toast("请先选择一个声音哦");
            }
            setTimeout(() => {
                reportKibana(
                    this.pageType,
                    3,
                    "定制声音页-下一步按钮点击（已选声音时）"
                );
                this.$router.push({
                    path: `/about/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        },
        goChoice(desc, index) {
            if (this.player) {
                this.player.pause();
            }
            const audios = document.getElementById(`player${index}`);
            audios.addEventListener("ended", () => {
                if (this.playerAnim) {
                    this.playerAnim.stopAnimation();
                    this.isPlaying = false;
                }
            });
            this.player = audios;
            audios.load();
            audios.play();
            Session.setItem(this.type, desc);
            this.selectIndex = desc;
            this.setSVGA(index);
        },
        setSVGA(index) {
            if (this.playerAnim) {
                this.playerAnim.stopAnimation();
            }
            let playerAnim = new SVGA.Player(`#option${index}`),
                parser = new SVGA.Parser(`#option${index}`);
            parser.load(require("../assets/voice.svga"), videoItem => {
                playerAnim.setVideoItem(videoItem);
                playerAnim.loops = 0;
                playerAnim.startAnimation();
                this.isPlaying = true;
                this.playerAnim = playerAnim;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.voice {
    &-item {
        display: flex;
        align-items: center;
        width: 580px;
        height: 100px;
        padding-left: 228px;
        margin-bottom: 40px;
        color: #4a3bc0;
        font-size: 36px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220902/1662119411034_1089_t.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .voice-anim {
            width: 32px;
            height: 40px;
            margin-left: 20px;
            background: url(https://photo.zastatic.com/images/common-cms/it/20220902/1662119630678_101927_t.png)
                no-repeat;
            background-size: 100% 100%;
            &.isShow {
                background: none;
            }
        }

        &.active {
            background-image: url(https://photo.zastatic.com/images/common-cms/it/20220902/1662119402886_859321_t.png);
        }
    }
    &-item:nth-of-type(5) {
        margin-bottom: 64px;
    }
}
</style>
