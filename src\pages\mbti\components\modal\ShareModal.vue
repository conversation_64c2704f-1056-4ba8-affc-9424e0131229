<template>
    <van-popup
        class="share-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="share-modal__title">
            已复制分享链接
        </h1>

        <p class="share-modal__desc">
            请前往微信、微博等社交平台分享你的MBTI测试结果吧～
        </p>

        <div
            class="share-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from "vuex";

export default {
    name: 'ShareModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },

    computed: {
        ...mapState([
            'resourceKey',
        ]),
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.share-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        width: 462px;
        font-size: 28px;
        font-weight: 400;
        color: #6C6D75;
        line-height: 42px;
        margin-bottom: 48px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
