import VueRouter from "vue-router";

const Home = () => import('./views/home');
const Questions = () => import('./views/questions');
const Form = () => import('./views/form');
const Download = () => import('./views/download');
const Transition = () => import('./views/transition');


const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: '/',
            component: Home
        },
        {
            path: '/home',
            component: Home
        },
        {
            name: 'questions',
            path: '/questions/:id',
            component: Questions
        },
        {
            name: 'transition',
            path: '/transition',
            component: Transition
        },
        {
            name: 'form',
            path: '/form',
            component: Form
        }, {
            name: 'download',
            path: '/download',
            component: Download
        }
    ],
});

// 切换页面后回到顶部
// vue router 的 scrollBehavior 兼容性不好，使用以下方式兼容性更好
router.afterEach(() => {
    window.scrollTo(0,0);

    // chrome
    document.body.scrollTop = 0

    // firefox
    document.documentElement.scrollTop = 0

    // safari
    window.pageYOffset = 0
});

export default router;
