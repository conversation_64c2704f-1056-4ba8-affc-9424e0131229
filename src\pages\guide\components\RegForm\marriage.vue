<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curMarriage === item.key?'active':''"
        >
            {{ item.text }}
        </div>
    </div>
</template>
<script>
import { setLocalRegisterForm } from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE } from "../../config";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            lock:false,
            curMarriage: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).marriage || ''
        };
    },
    mounted() {
        this.$report(36, '婚况页访问');
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;
            
            this.curMarriage = val;
            const params = {
                key: "marriage",
                value: val
            };
            this.$report(37, '婚况页-按钮点击');
            setLocalRegisterForm(params, PAGE_TYPE);
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper{
    position: absolute;
    top: 300px;
    left: 50%;
    transform: translateX(-50%);
    @include flex-center(column, flex-start, center);
    > .item{
        @include flex-center(column, center, center);
        margin-top: 32px;
        padding: 0 48px;
        width: 564px;
        height: 100px;
        background: #EAF1FA;
        border-radius: 55px;
        font-size: 32px;
        color: #17263D;
        line-height: 1.2;
    }
    > .active{
        background: #17263D;
        color: #fff;
    }
}
</style>
