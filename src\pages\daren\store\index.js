import Vuex from 'vuex';
import * as dict from "@/common/config/register-dictionary";
import {
    _getMaterial,
    _getWapRecommendMember,
    _getInterestAnswerRecordListForUser
} from '../js/api.js';
import { session } from '@/common/utils/storage.js';

const formInfo = [
    {
        index:"gender",
        label:"您的性别",
        value:"",
        selectArr:dict.gender
    },
    {
        index:"workCity",
        label:"您的工作地",
        value:"",
        selectArr: Z.workCity
    },
    {
        index:"birthday",
        label:"您的出生年份",
        value:"",
        selectArr:dict.birthday
    },
    {
        index:"education",
        label:"您的学历",
        value:"",
        selectArr:dict.education
    },
    {
        index:"marriage",
        label:"您的婚姻状况",
        value:"",
        selectArr:dict.marriage
    },
    {
        index:"salary",
        label:"您的月收入",
        value:"",
        selectArr:dict.salary
    }
];

const store = new Vuex.Store({
    state: {
        // 用于表单显示
        formInfo,
        // 用于注册提交
        registerInfo: {
            gender: "",
            workCity: "",
            birthday: "",
            marriage: "",
            education: "",
            salary: "",
            phone: ""
        },
        // cms的配置
        cmsConfig:{
            formImg:'', //大表单页头图
            downloadImg:'',  //详细信息页头图
            pageColor:null,  //页面底色（可能是渐变色）
            buttonColor:null, //底部按钮底色（可能是渐变色）
            formButtonText:'', //大表单页主按钮文案
            downloadButtonText:'', //详细信息页主按钮文案
            flopButtonText:'', //详细信息页翻牌按钮文案
            agreementStatus:0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认)
            channelType:1 //该链接是否用于头条投放 1为是(默认) 0为否
        },
        // 图形验证码
        code:'',
        // 详情页模特信息
        modelInfo:{
            avatar:'',
            educationString:'',
            memberID:'',
            mainImg:'', // 处理后的主图
            momentImg1:'', // 动态图
            momentImg2:'',
            momentImg3:'',
            momentImg4:'',
            momentImg5:'',
            hobbyFood:'',
            hobbySong:'',
            hobbyBook:'',
            hobbyName:'',
            hobbyThing:''
        },
        // 是否覆盖资料
        isCover:false,
        // 是否允许尝试打开/下载APP
        overwriteRegistrationSwitch:false,
        // 后台返回的memberId
        regMemberId:'',
        // 老注册页打桩使用
        EXT9:'',
        // 协议勾选状态，默认勾选
        hasCheckProtocal:true,
        // 控制是否打开引导刷新
        showError:false
    },
    mutations: {
        setFormInfo(state, target){
            state.formInfo.forEach(item =>{
                Object.keys(target).forEach(key=>{
                    if(item.index === key){
                        return item.value = target[key];
                    }
                })
            })

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localFormInfo',JSON.stringify(state.formInfo));
        },
        setRegisterInfo(state, target){
            state.registerInfo = Object.assign(state.registerInfo, target);

            // 每次更新后同步本地，防止用户刷新后数据丢失
            localStorage.setItem('localRegisterInfo',JSON.stringify(state.registerInfo));
        },
        setCmsConfig(state, target){
            state.cmsConfig = Object.assign(state.cmsConfig, target);
        },
        setCode(state, target){
            state.code = target;
        },
        setIsCover(state, target){
            state.isCover = target;
        },
        setModelInfo(state, target){
            state.modelInfo = Object.assign(state.modelInfo, target);
        },
        setOverwriteRegistrationSwitch(state, target){
            state.overwriteRegistrationSwitch = target;
        },
        setRegMemberId(state, target){
            state.regMemberId = target;
            session.setItem('reg_memberid', target);
        },
        setEXT9(state, target){
            state.EXT9 = target;
        },
        setHasCheckProtocal(state, target){
            state.hasCheckProtocal = target;
        },
        setShowError(state, target){
            state.showError = target;
        }
    },
    actions:{
        async setCmsConfig({ commit }, target){
            // 默认配置
            let defaultCmsConfig = {
                formImg:require("../assets/imgs/banner-collection.png"), //大表单页头图
                downloadImg:require("../assets/imgs/banner-info.png"),  //详细信息页头图
                pageColor:{
                    background:'#1990F0',
                },  //页面底色（可能是渐变色）
                buttonColor:{
                    background:'linear-gradient(0deg, #FF6CDA, #F9BDF9)',
                    textShadow:'0px 5px 13px rgba(255, 108, 218, 0.7)'
                }, //底部按钮底色（可能是渐变色）
                formButtonText:'快来领取你的盲盒', //大表单页主按钮文案
                downloadButtonText:'开启你的心动约会', //详细信息页主按钮文案
                flopButtonText:'打开盲盒', //详细信息页翻牌按钮文案
                agreementStatus:0, //大表单页底部是否勾选协议 1为需要用户手动勾选 0为不需要(默认)
                channelType:1 //该链接是否用于头条投放 1为是(默认) 0为否
            }

            if(!Z.getParam("materialId")){
                // 没有素材id则采用默认配置
                commit("setCmsConfig", defaultCmsConfig)
                return;
            }

            // 拉取CMS配置
            let resData = await _getMaterial({
                id:Z.getParam("materialId"),
            });

            if(resData.isError){
                // 取配置失败也使用默认配置
                // 不能直接写在cmsConfig中否则会出现切换
                commit("setCmsConfig", defaultCmsConfig);

                // 此处的this指向store不是vue
                return this._vm.$toast(resData.errorMessage);
            }

            target = resData.data.materialVo;

            // 处理页面底色和按钮底色
            function handleColor(color){
                if(color.indexOf('&') > -1){
                    // 按钮为渐变色
                    let topColor = color.split("&")[0],
                        bottomColor = color.split("&")[1];
                    return {
                        background:`linear-gradient(0deg,${topColor},${bottomColor})`
                    }
                }
                return {
                    background:color
                };
            }
            target.pageColor = handleColor(target.pageColor);
            target.buttonColor = handleColor(target.buttonColor);
            target.buttonColor.textShadow = "0px 5px 13px rgba(255, 125, 199, 0.7)"; // 字体增亮

            // 头图压缩
            target.formImg = target.formImg && target.formImg + '?imageMogr2/thumbnail/750x';
            target.downloadImg = target.downloadImg && target.downloadImg + '?imageMogr2/thumbnail/750x';

            // 头图压缩 webp格式，兼容性1phone 8P无法显示
            // target.formImg += '?imageMogr2/format/webp';
            // target.downloadImg += '?imageMogr2/format/webp';

            // 设置协议默认值
            commit("setHasCheckProtocal", target.agreementStatus === 0);
            commit("setCmsConfig", target)
        },

        async setModelInfo({ commit, state }, target){
            // 获取模特信息
            let sendData = {};
            let age = new Date().getFullYear() - new Date(state.registerInfo.birthday).getFullYear();
            sendData.age = age;
            sendData.sex = state.registerInfo.gender;

            if(sendData.age === '' || sendData.sex === ''){
                return;
            }

            let resData = await _getWapRecommendMember({
                ...sendData
            });

            if(resData.isError){
                // 打开引导刷新
                if(resData.errorMessage === "当前网络异常"){
                    return state.showError = true;
                }
                return this._vm.$toast(resData.errorMessage);
            }

            // 请求数据正常，关闭引导刷新
            state.showError = false;

            // 防止运营没有压缩图片,前端兜底处理一遍
            let modelInfoVo = resData.data.modelInfoVo;

            modelInfoVo.avatar = modelInfoVo.avatar && modelInfoVo.avatar+'?imageMogr2/thumbnail/200x';
            modelInfoVo.mainImg = modelInfoVo.avatar && modelInfoVo.avatar+'?imageMogr2/thumbnail/700x';
            modelInfoVo.momentImg1 = modelInfoVo.momentImg1 && modelInfoVo.momentImg1+'?imageMogr2/thumbnail/315x';
            modelInfoVo.momentImg2 = modelInfoVo.momentImg2 && modelInfoVo.momentImg2+'?imageMogr2/thumbnail/154x';
            modelInfoVo.momentImg3 = modelInfoVo.momentImg3 && modelInfoVo.momentImg3+'?imageMogr2/thumbnail/154x';
            modelInfoVo.momentImg4 = modelInfoVo.momentImg4 && modelInfoVo.momentImg4+'?imageMogr2/thumbnail/154x';
            // modelInfoVo.momentImg5 = modelInfoVo.momentImg1 && modelInfoVo.momentImg5+'?imageMogr2/thumbnail/154x';

            resData = await _getInterestAnswerRecordListForUser({
                objectID:modelInfoVo.memberID
                // objectID:2000106235
            })

            if(resData.isError){
                // 打开引导刷新
                if(resData.errorMessage === "当前网络异常"){
                    return state.showError = true;
                }
                return this._vm.$toast(resData.errorMessage);
            }

            // 请求数据正常，关闭引导刷新
            state.showError = false;
            let recordVoList = resData.data.recordVoList || [];

            // 兴趣爱好
            recordVoList.forEach((item)=>{
                if(item.questionName === "喜欢做的事"){
                    if(item.answerContentDetailRecords && item.answerContentDetailRecords.length > 1){
                        let thingArr = item.answerContentDetailRecords.map((item)=>{
                            return item.answerContentDetail
                        })
                        modelInfoVo.hobbyThing = thingArr.join('、');
                    } else {
                        modelInfoVo.hobbyThing = item.answerContentDetail;
                    }
                } else if (item.questionName === "喜欢的一道菜"){
                    modelInfoVo.hobbyFood = item.answerContentDetail;
                } else if (item.questionName === "欣赏的一个名人"){
                    modelInfoVo.hobbyName = item.answerContentDetail;
                } else if (item.questionName === "喜欢的一首歌"){
                    modelInfoVo.hobbySong = item.answerContentDetail;
                } else if (item.questionName === "喜欢的一本书"){
                    modelInfoVo.hobbyBook = item.answerContentDetail;
                }
            })

            commit("setModelInfo", modelInfoVo);
        }
    },
    getters:{
        // 根据registerInfo返回已填写的表单项个数
        getProgress:state=>{
            let infoArr = Object.keys(state.registerInfo),
                count = 0;

            infoArr.forEach(item =>{
                // 手机号需填满11位才算填写完整
                if(item === 'phone'){
                    state.registerInfo.phone.length === 13 ? count++ : "";
                }else if(state.registerInfo[item] !== '' ){
                    count++;
                }
            })
            return count;
        },
        // 返回正常格式的手机号
        getNormalPhone:state=>{
            return state.registerInfo.phone.replace(/[^(\d)]/g,"")
        }
    }
  })

  export default store;
