import config from '../config/config'
const domain = config.API_HOST_NAME()

// 获取ocpx配置
var ocpxConfig;
export function getOcpxConf(cb) {
    if (ocpxConfig) {
        typeof cb==='function' && cb(ocpxConfig)
    }
    Z.ajax({
        type: 'GET',
        url: window.location.protocol+`//${domain}/api/common/ad/account/report/getOcpxConf`,
        data: {
            channelId: Z.getParam('channelId'),
            subChannelId: Z.getParam('subChannelId') || 0
        },
        dataType: 'json',
        success: function (res) {
            if(res.isError){
                return;
            }
            ocpxConfig = res.data;
            if(ocpxConfig.mediaType && ocpxConfig.conditions){
                typeof cb==='function' && cb(ocpxConfig)
            }
        },
        error: function (xhr, textStatus, errorThrown) {

        }
    })
}
