<template>
    <question-panel>
        <radio-list
            :list="list"
            :number="requirement.selection"
            @change="handleChange"
        />
    </question-panel>
</template>

<script>
import QuestionPanel from "./question-panel";
import RadioList from "./radio-list";
import { mapState, mapMutations } from "vuex";

export default {
    name: "question-selection",
    components: {
        QuestionPanel,
        RadioList
    },
    data() {
        return {
            list: [
                {
                    number: "A",
                    label: "没必要，当下的感觉"
                },
                {
                    number: "B",
                    label: "先在一起后才会考虑"
                },
                {
                    number: "C",
                    label: "可能会大致做规划"
                },
                {
                    number: "D",
                    label: "必须深思熟虑"
                }
            ]
        };
    },
    computed: {
        ...mapState(["requirement"])
    },
    methods: {
        ...mapMutations(["setRequirement"]),
        handleChange(item) {
            this.setRequirement({
                selection: item.number
            });
        }
    }
};
</script>
