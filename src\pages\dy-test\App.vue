<template>
    <div class="dy-test">
        <router-view
            :key="$route.fullPath"
            v-if="canRender"
        />
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
// import { _getChannelConfig } from "./api";
// import { getRandomInt } from "@/common/utils/tools.js";
import { judgeIfInToutiaoIos } from "@/common/business/utils/channel";
import { session as Session } from "@/common/utils/storage";
import { getRandomInt } from '@/common/utils/tools';
import { planNameMap } from "./config";

export default createRoot({
    name: "App",
    provide() {
        return {
            pageType: '',
        };
    },
    data() {
        return {
            canRender: false,
        };
    },
    async created() {
        // await this.getChannelConfig();
    },
    async mounted() {
        this.handleSplitPlan();
        judgeIfInToutiaoIos();
    },
    methods: {
        handleSplitPlan() {
            const tftype = Z.getParam("tftype");
            if (tftype) {
                if (tftype === "0") {
                    window._zconfig.resourceKey = planNameMap[80];
                    Session.setItem("ext30", '80');
                    Session.setItem("planName", planNameMap[80]);
                } else if (tftype === "1") {
                    window._zconfig.resourceKey = planNameMap[81];
                    Session.setItem("ext30", '81');
                    Session.setItem("planName", planNameMap[81]);
                } else if (tftype === "2") {
                    window._zconfig.resourceKey = planNameMap[82];
                    Session.setItem("ext30", '82');
                    Session.setItem("planName", planNameMap[82]);
                } else if (tftype === "3") {
                    window._zconfig.resourceKey = planNameMap[83];
                    Session.setItem("ext30", '83');
                    Session.setItem("planName", planNameMap[83]);
                }
            } else {
                const ext30 = Z.cookie.get('extstring30');
                if (ext30) {
                    this.pageType = ext30;
                    Session.setItem('ext30', ext30);
                    Session.setItem("planName", planNameMap[ext30]);
                    window._zconfig.resourceKey = planNameMap[ext30];
                } else {
                    const num = getRandomInt(1, 100);
                    if (num <= 25) {
                        window._zconfig.resourceKey = planNameMap[80];
                        Session.setItem("ext30", '80');
                        Session.setItem("planName", planNameMap[80]);
                    } else if (num <= 50) {
                        window._zconfig.resourceKey = planNameMap[81];
                        Session.setItem("ext30", '81');
                        Session.setItem("planName", planNameMap[81]);
                    } else if (num <= 75) {
                        window._zconfig.resourceKey = planNameMap[82];
                        Session.setItem("ext30", '82');
                        Session.setItem("planName", planNameMap[82]);
                    } else {
                        window._zconfig.resourceKey = planNameMap[83];
                        Session.setItem("ext30", '83');
                        Session.setItem("planName", planNameMap[83]);
                    }
                }
            }

            this.canRender = true;
        },
        // async getChannelConfig() {
        //     const channelId = Z.getParam("channelId"), subChannelId = Z.getParam("subChannelId");
        //     const res = await _getChannelConfig({
        //         channel: channelId,
        //         subChannel: subChannelId
        //     });
        //     if(!res.isError) {
        //         const mediaGroupType = res.data.mediaGroupType;
        //         if (mediaGroupType.includes(10)) {
        //             session.setItem("mediaGroupType", 10);
        //         } else if (mediaGroupType.includes(11)) {
        //             session.setItem("mediaGroupType", 11);
        //         } else if (mediaGroupType.includes(12)) {
        //             session.setItem("mediaGroupType", 12);
        //         } else {
        //             session.setItem("mediaGroupType", false);
        //         }
        //     }

        // }
    }
});
</script>

<style lang="scss">
.dy-test {
    min-height: 100vh;
}
</style>
