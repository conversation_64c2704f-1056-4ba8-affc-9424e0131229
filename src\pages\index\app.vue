<template>
    <div
        id="app"
        :style="cmsConfig.pageColor"
    >
        <router-view v-if="renderView==1" />
        <toast />
    </div>
</template>

<script>
import "@/common/styles/reset.scss";
import Toast from "@/common/components/Toast.vue";
import fixedClickDelay from "@/common/utils/fixed_click_delay.js";
import { mapMutations, mapState, mapActions } from "vuex";
import { storage as Storage } from "@/common/utils/storage";
import { judgeIfInToutiaoIos } from "@/common/business/utils/channel";

fixedClickDelay();

Vue.use(Toast);

export default {
    components: {},
    data() {
        return {
            renderView: 0
        };
    },
    computed: {
        ...mapState(["cmsConfig"])
    },
    async created() {
        Storage.setItem("resourceKey", "导量H5大表单翻牌");
        // 获取CMS配置
        await this.setCmsConfig();
        await judgeIfInToutiaoIos();
        this.renderView = 1;

        // 资料存本地
        let localFormInfo = JSON.parse(
            localStorage.getItem("localFormInfo")
                ? localStorage.getItem("localFormInfo")
                : 0
        );
        let localRegisterInfo = JSON.parse(
            localStorage.getItem("localRegisterInfo")
                ? localStorage.getItem("localRegisterInfo")
                : 0
        );

        if (localFormInfo) {
            localFormInfo.forEach(item => {
                this.setFormInfo({ [item.index]: item.value });
            });
        }

        if (localRegisterInfo) {
            for (let key in localRegisterInfo) {
                this.setRegisterInfo({ [key]: localRegisterInfo[key] });
            }
        }

        // 【老注册页】用于后台自然流量注册统计
        (function setAbtParams() {
            var abtParams = Z.cookie.get("abt_params");

            try {
                var arr = abtParams.split("|");
                if (arr.length !== 5) {
                    abtParams = "";
                }
            } catch (e) {}

            if (!abtParams) {
                var pageKey = Z.getParam("pageKey"), // 根据不同业务key写入
                    planId = 0,
                    schemeId = 0;
                var channelId = Z.getParam("channelId") || 0;
                var subChannelId = Z.getParam("subChannelId") || 0;
                var tmp = [pageKey, channelId, subChannelId, planId, schemeId];
                Z.cookie.set(
                    "abt_params",
                    tmp.join("|"),
                    ".zhenai.com",
                    "/",
                    24 * 1
                );
            }
        })();
        
        if (this.cmsConfig.materialType === 12) {
            Storage.setItem("resourceKey", "导量H5大表单企微");
        }

        if (this.$route.path !== "/") {
            return;
        }
        if (this.cmsConfig.schemeType === 3) {
            Storage.setItem("resourceKey", "大表单翻牌(新流程)");
            return this.$router.replace("/home");
        } else {
            return this.$router.replace("/index");
        }
    },
    methods: {
        ...mapActions(["setCmsConfig"]),
        ...mapMutations(["setFormInfo", "setRegisterInfo"])
    }
};
</script>

<style lang="scss">
body {
    margin: 0;
    padding: 0;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
}
// #id {
//     color:red;
//     font-size: 30px;
// }

// .abc{
//     font-size: 80px;
// }
</style>
