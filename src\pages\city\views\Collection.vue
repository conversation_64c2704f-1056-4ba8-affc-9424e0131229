<template>
    <div
        class="collection-wrapper"
        :style="{backgroundColor: cmsConfig.pageColor || '#bbe8ff'}"
    >
        <!-- 进度条 -->
        <div class="collection-wrapper__step">
            <div
                class="collection-wrapper__step-item"
                v-for="item in [0,1,2]"
                :key="item"
                :style="{ backgroundColor: step >= item ? cmsConfig.progessColor : '#F6F7F7'}"
            >
            </div>
        </div>
        <!-- 标题，副标题 -->
        <div
            class="collection-wrapper__title"
            v-html="title"
        >
        </div>
        <div
            class="collection-wrapper__sub-title"
            v-html="subTitle"
        >
        </div>
        <!-- 地址、性别、表单 -->
        <div class="collection-wrapper__main">
            <city-picker v-if="step === 0" />
            <gender-picker v-if="step === 1" />
            <Form v-if="step === 2" />
        </div>
    </div>
</template>

<script>
import CityPicker from '../components/CityPicker';
import GenderPicker from '../components/GenderPicker';
import Form from '../components/Form';
import { storage as Storage} from "@/common/utils/storage";
import { findWorkCity } from "@/common/business/utils/localRegisterForm.js";

export default {
    components: {
        CityPicker,
        GenderPicker,
        Form
    },
    inject: ['cmsConfig'],
    data() {
        return {
            step: Number(this.$route.params.step),
            registerForm: Storage.getItem(`cachedRegisterForm-${this.cmsConfig.planName}`) || {
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
            randomNum: 0
        };
    },
    mounted() {
        this.randomNum = Math.floor(Math.random() * 99) + 100;
        Storage.setItem('cityRandomNum', this.randomNum);
    },
    computed: {

        title() {
            const formTitle = {
                '同城交友A方案(活动)': `总共有 <span style="color: ${this.cmsConfig.homeButtonColor}">${this.randomNum}</span> 位${this.registerForm.gender === 0 ? '女生' : '男生'}参加活动`,
                '同城交友B方案(群聊)': `群聊里有 <span style="color: ${this.cmsConfig.homeButtonColor}">${this.randomNum}</span> 位${this.registerForm.gender === 0 ? '女生' : '男生'}`,
                '同城交友C方案(单人)': `为你匹配到 <span style="color: ${this.cmsConfig.homeButtonColor}">${this.randomNum}</span> 位${this.registerForm.gender === 0 ? '女生' : '男生'}`
            };
            const title = {
                0: `<span style="padding-left: 13px;">${this.cmsConfig.planName === '同城交友A方案(活动)' ? '选择参与活动的地点' : '你的工作地'}</span>`,
                1: '你的性别是',
                2: formTitle[this.cmsConfig.planName]
            };
            return title[this.step];
        },
        subTitle() {
            let selectCity = findWorkCity(this.registerForm.workCity);
            selectCity = ["北京", "上海", "重庆", "天津"].includes(selectCity[0]) ? `${selectCity[0]}市` : `${selectCity[1]}市`;
            
            const citySubtitleMap = {
                '同城交友A方案(活动)': {
                    city: '选择活动地点，可匹配最多16场脱单活动',
                    gender: '选择性别，以便为你匹配异性较多的脱单活动',
                    form: `为你匹配到9个${selectCity}活动`
                },
                '同城交友B方案(群聊)': {
                    city: '选择工作地点，匹配更多同城群聊',
                    gender: '选择性别，以便为你匹配异性较多的脱单群聊',
                    form: `为你匹配到9个${selectCity}群聊`
                },
                '同城交友C方案(单人)': {
                    city: '选择工作地点，匹配更多同城异性好友',
                    gender: '选择性别，以便为你匹配更多优质异性好友',
                    form: ''
                },
            };
            const subTitle = {
                0: `<span style="padding-left: 13px;">${citySubtitleMap[this.cmsConfig.planName].city}</span>`,
                1: citySubtitleMap[this.cmsConfig.planName].gender,
                2: citySubtitleMap[this.cmsConfig.planName].form
            };
            return subTitle[this.step];
        }
    }
    
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.collection-wrapper {
    min-height: 100vh;
    &__step {
        width: 690px;
        padding: 40px 0px;
        display: flex;
        margin: 0 auto;
        &-item {
            width: 224px;
            height: 12px;
            border-radius: 47px;
        }
        &-item:first-child {
            margin-right: 9px;
        }
        &-item:last-child {
            margin-left: 9px;
        }
    }

    &__title {
        margin-left: 30px;
        font-weight: 600;
        font-size: 50px;
        color: #26273C;
    }

    &__sub-title {
        margin-top: 23px;
        margin-left: 30px;
        font-weight: 400;
        font-size: 28px;
        color: #26273C;
    }

    // &__main {
    // }
}
</style>