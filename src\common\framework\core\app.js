import '@/common/bootstrap.js';
import '../../styles/reset.scss';
import Vue from 'vue';
import 'intersection-observer';
import toast from '@/common/plugins/toast';
import loading from '@/common/plugins/loading';
import z_ from "@/common/zdash";
import Prototype from '../prototype';
import fixedClickDelay from '../../utils/fixed_click_delay';

fixedClickDelay();

Vue.use(toast);
Vue.use(loading);

if (process.env.NODE_ENV === 'development') {
    Vue.config.devtools = true;
    Vue.config.productionTip = false;
}

export default function createApp(options) {
    const extendsProps = [ 'resourceKey' ];

    const finalConfig = Object.assign({
        el: '#app',
    }, options);

    window._zconfig = {
        resourceKey: options.resourceKey,
    };

    Prototype.$gather.setAbtPageKeyCookie();
    return new Vue(z_.omit(finalConfig, extendsProps));
}
