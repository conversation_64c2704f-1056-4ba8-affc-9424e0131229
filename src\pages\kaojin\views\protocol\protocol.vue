<template>
  <section class="potocol-container">
    <!--<section v-if="type === 1" v-html="serviceHtml"></section>-->
    <section v-html="(type == 1 || type == 3) ? qyhPersonalHtml : zhenxiPersonalHtml"></section>
  </section>
</template>

<script>
import { personalHtml, serviceHtml, xpersonalHtml, qyhPersonalHtml, zhenxiPersonalHtml } from "./protocol-html";
export default {
  data() {
    return {
      serviceHtml,
      xpersonalHtml,
      qyhPersonalHtml,
      zhenxiPersonalHtml,
      type: '', // 默认是珍惜  1：去约会
      hasRegZATenant: true,
    };
  },
  created() {
      this.$report(610, "隐私协议页面", {});
      this.type = Z.getParam("type")
  },
};
</script>

<style lang="scss" scope>
.potocol-container {
  background-color: #fff;
  padding: 40px;
}
</style>
