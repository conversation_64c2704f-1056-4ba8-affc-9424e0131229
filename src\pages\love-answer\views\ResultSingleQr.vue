<template>
    <div
        class="result"
    >
        <img
            src="https://photo.zastatic.com/images/common-cms/it/20230811/1691745687440_167018_t.png"
            class="header">
        </img>
        <div class="content">
            <img src="https://photo.zastatic.com/images/common-cms/it/20230811/1691750030566_90956_t.png" alt="" class="content_img">
            <div>
                <p class="title">恋爱人格关键词</p>
                <p class="title_type">{{ result.desc }}</p>
            </div>
        </div>
        <!--人格类型图片 -->
        <div class="text_grad" v-show="result.img">
            <img :src="result.img" alt="">
        </div>
        <!--<img src="https://photo.zastatic.com/images/common-cms/it/20230811/1691752181886_355957_t.png" alt="" class="record">-->
        <span class="single-title">想要更多专业测评请保存企微二维码添加您的专属情感助手。</span>
        <div class="qr-code">
            <img :src="drQwQrCodeUrl" alt="">
        </div>

        <div
            class="content_t"
            v-for="(item, index) in result.options"
            :key="item.title">
            <div class="total_title">
                <img src="https://photo.zastatic.com/images/common-cms/it/20230812/1691823059780_680309_t.png" alt="" class="total">
                <p>{{ item.title }}：</p>
            </div>
            <div v-if="!(item.content instanceof Array)" style="white-space: pre-wrap;" :class="index ? 'content-blur' : ''">{{ item.content }}</div>

            <template v-else>
                <div
                    v-for="(nav,indexContene) in item.navtitle"
                    :key="nav"
                    class="title_content"
                    :class="index ? 'content-blur' : ''"
                >
                    <div class="title-nav">{{ nav }}</div>
                    <div>{{ item.content[indexContene] }}</div>

                </div>
            </template>
        </div>
        <div
            @click="handleDownload"
            class="downLoad">
            立即脱单
        </div>
    </div>
</template>

<script>
import { getAnswerResult } from '../api';
import { storage } from "@/common/utils/storage";
import { getTypeImg } from '../utils/index';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { storage as Storage, session as Session } from "@/common/utils/storage";

import { Toast } from "vant";
export default {
    name:'Result',
    components:{
    },
    data(){
        return {
            drQwQrCodeUrl: '',
            reportLock:{
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            result:{
                desc: '',
                note: '',
                options: [],
                type:'',
                img:''
            },
        };
    },
    async created(){
        this.getResult();

    },
    activated(){
        this.getResult();
        this.$report(43, "测试报告页访问", {
            ext28: sessionStorage.getItem('reg_memberid'),
            ext29: 8024,
        });
    },
    mounted(){

    },
    methods:{
        // goWeixin() {
        //     const memberId = sessionStorage.getItem('reg_memberid')
        //     location.href = `https://work.weixin.qq.com/ca/cawcded2060972dcd5?customer_channel=drnytest|${memberId}|temp`
        // },
        getResult() {
            this.drQwQrCodeUrl = sessionStorage.getItem('drQwQrCodeUrl')
            let form = {
                type:1,
                resultType: storage.getItem('resultTypeData')
            };
            getAnswerResult(form).then((res)=>{
                if(res.code === 0) {
                    this.result = res.data;
                    this.result.img = getTypeImg(res.data.desc);
                    this.result.options = this.spliceData(res.data.options);
                } else {
                    //系统错误
                }

            });
        },
        handleDownload() {
            this.$report(44, "报告页-点击下载APP按钮", {
                ext29: 8024
            });
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({ value: true });
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        spliceData(options){
            let data = options.map(ele => {
                let title = ele.content.match(/\d[^\d|\r\n]*?\r\n/g);
                if(title && title.length > 0){
                    ele.navtitle = title;
                    ele.content = ele.content + "\r\n\r\n";
                    ele.content = ele.content.match(/\r\n[^\r\n|\r\n\r\n]*?\r\n\r\n/g);
                }
                return ele;
            });

            return data;
        }

    },
    destroyed(){
        window.removeEventListener('scroll',this.listenScreen);
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.result{
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: scroll;
    .header{
        width: 538px;
        height: 88px;
        object-fit: contain;
        margin-top: 76px;
    }
    .content{
        margin-top: 22px;
        width: 680px;
        height: 357.5px;
        border-radius: 30px 30px 30px 30px;
        background: #fff;
        box-shadow: 0 0 80px 0 #eac8ff inset;
        display: flex;
        box-sizing: border-box;
        align-items: center;
        .content_img{
            width: 274px;
            height: 262px;
            margin-left: 38px;

            margin-right: 20px;
        }
        .title{
            font-family: 'love';
            height: 50px;
            font-size: 44px;
            text-align: center;
            color: #59179D;
            letter-spacing: 2px;
            margin-bottom: 29px;
        }
        .title_type{
            font-family: 'love';
            height: 80px;
            font-size: 65px;
            color: #59179D;
        }
    }
    .text_grad{
        margin-top: 34px;
        width: 680px;
        height: 500px;
        border-radius: 30px 30px 30px 30px;
        background: #1d144dde;
        box-shadow: 0 0 46px 0 #410e7159;
        border: 1px solid #A096FF;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
            width: 485px;
            height: 485px;
            object-fit: contain;
        }
    }
    .single-title {
        color: #ffffff;
        font-size: 30px;
        line-height: 35px;
        padding: 20px 40px;
    }
    .qr-code {
        width: 100%;
        padding: 0 150px;
        img {
            width: 100%;
        }
    }
    .content-blur {
        // filter: blur(5px);
    }
    .record{
        width: 670px;
        height: 138px;
        object-fit: contain;

    }
    .content_t{
        margin-bottom: 54.6px;
        width: 680px;
        border-radius: 30px 30px 30px 30px;
        background: #fff;
        box-shadow: 0 0 80px 0 #eac8ff inset;
        display: flex;
        box-sizing: border-box;
        padding: 58.5px 41px 65.6px 41px;
        font-weight: 65 Medium;
        font-size: 26px;
        color: #443952;
        line-height: 40px;
        flex-direction: column;
        .total_title{
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            .total{
                width: 97px;
                height: 89px;
                object-fit: contain;
                flex-shrink: 1;
            }
            p{
                font-family: 'love';
                height: 63px;
                font-size: 40px;
                text-align: left;
                color: #412A97;

            }
        }


    }
    .downLoad{
        position: fixed;
        bottom: 40px;
        width: 326px;
        height: 96px;
        border-radius: 62px 62px 62px 62px;
        font-family: 'love';
        font-size: 50px;
        text-align: center;
        color: #FFFFFF;
        letter-spacing: 4px;
        box-shadow: 0 0 10px 0 #fff;
        background: linear-gradient(180deg,#B29BFF, #8560FF);
        display:flex;
        justify-content: center;
        align-items: center;

    }
    .title-nav{
        color: #6E4EE5;
        font-size: 32px;
        margin-bottom: 8px;
    }
    .title_content{
        width: 100%;
        border-radius: 25px 25px 25px 25px;
        background: #f7f1fb;
        padding: 26px;
        margin-bottom: 26px;
    }
}
</style>
