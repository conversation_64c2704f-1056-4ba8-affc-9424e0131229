<template>
    <div
        v-if="overlaySrc"
        class="z-avatar-overlay-container"
        @click="$emit('click', $event)"
        :width="overlayWidth"
        :height="overlayHeight">
        <z-image
            lazy
            :style="customStyle"
            mode="cover"
            class="z-avatar"
            :border-radius="borderRadius"
            :src="imageSrc"
            :width="size"
            :height="size" />
        <z-image
            lazy
            class="z-avatar-overlay"
            mode="cover"
            :src="overlaySrc"
            :width="overlayWidth"
            :height="overlayHeight" />
    </div>

    <z-image
        v-else
        lazy
        mode="cover"
        @click="$emit('click', $event)"
        :style="customStyle"
        class="z-avatar"
        :border-radius="borderRadius"
        :src="imageSrc"
        :width="size"
        :height="size" />
</template>

<script>
import { createComponent } from "@/common/framework";

export default createComponent({
    name: 'z-avatar',
    props: {
        src: {
            type: String,
            default: '',
        },
        size: {
            type: Number,
            required: true,
        },
        borderColor: {
            type: String,
            default: 'transparent',
        },
        borderWidth: {
            type: Number,
            default: 1,
        },
        borderRadius: {
            type: String,
            default: '50%',
        },
        defaultAvatar: {
            type: String,
            default: '',
        },
        overlaySrc: {
            type: String,
        },
        overlayWidth: {
            type: Number,
        },
        overlayHeight: {
            type: Number,
        },
        overlayTop: {
            type: Number,
            default: 0,
        },
        overlayLeft: {
            type: Number,
            default: 0,
        },
        lazy: {
            type: Boolean,
            default: false,
        },
    },
    created() {
        this.defaultAvatar = this.defaultAvatar || this.$z_.get(window, '_zconfig.defaultAvatar');
    },
    computed: {
        customStyle() {
            const result = {
                border: `${this.borderWidth}px solid ${ this.borderColor }`,
            }

            if (this.overlaySrc) {
                Object.assign(result, {
                    position: 'absolute',
                    top: this.$utils.pxToRem(this.overlayTop),
                    left: this.$utils.pxToRem(this.overlayLeft),
                });
            }

            return result;
        },
        imageSrc() {
            return this.src ? this.src : this.defaultAvatar;
        }
    },
})
</script>

<style lang="scss" scoped>
.z-avatar-overlay-container {
    position: relative;
    display: inline-block;
}

.z-avatar {
    box-sizing: border-box;
}
</style>
