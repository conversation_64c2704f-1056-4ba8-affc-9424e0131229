// *** variables ***


// *** layout ***

@mixin flex-center($direction: row, $justify: center, $align: center, $flex-wrap: null) {
  display: flex !important;

  @if ($direction != null) {
    flex-direction: $direction;
  }
  @if ($justify != null) {
    justify-content: $justify;
  }
  @if ($align != null) {
    align-items: $align;
  }
  @if ($flex-wrap != null) {
    flex-wrap: $flex-wrap;
  }
}

@mixin set-img($url, $size: contain) {
    background-image: url($url);
    background-size: $size;
    background-repeat: no-repeat;
    background-position: center center;
}

@mixin relative-center() {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}

// *** other ***
@mixin truncate {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin ellipse($count: 1) {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: normal;
  -webkit-line-clamp: $count;
}

// *** z-index ***
// 按层叠顺序从低到高维护list，由sass自动生成z-index
// https://www.w3cplus.com/preprocessor/sassy-z-index-management-for-complex-layouts.html
$base-index: mask,
             avatar-bottom,
             avatar-top,
             popover, 
             sticky-header, 
             sticky-footer, 
             modal;

@function za-index($list, $element) {
  $z-index: index($list, $element);

  @if $z-index {
     @return $z-index;
  }

  @warn '当前维护的的层叠list中不存在 "#{$element}" , 请在: #{$list} 中选择一项，或扩充当前list！';
  @return null;
}   

