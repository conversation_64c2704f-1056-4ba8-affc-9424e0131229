<template>
    <div
        class="root"
    >
        <router-view v-if="canRender" />

        <CommonDownloadGuideModal
            :styleConfig="{
                confirmButtonColor: '#FFFFFF',
                confirmButtonBgColor: '#F971AD'
            }"
            v-model="showModal.downloadGuide"
            :page-type="pageType"
        >
            <template slot="default">
                请到应用商店搜索<br />“珍爱网”下载珍爱APP
            </template>
            <template slot="desc">
                快来与Ta相遇，收获你的爱情吧！
            </template>
        </CommonDownloadGuideModal>
    </div>
</template>

<script>
// 初始化
import { createRoot } from '@/common/framework';
import Api from "@/common/server/base";

// 下载相关
import { Toast } from "vant";
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModal from "@/common/business/components/CommonDownloadGuideModal.vue";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { judgeIfInToutiaoIos } from "@/common/business/utils/channel";
import { session } from "@/common/utils/storage.js";

// 弹窗相关
import Vue from "vue";
import { Popup } from 'vant';
Vue.component('VanPopup', Popup);

export default createRoot({
    name: "App",
    provide() {
        return {
            pageType: this.pageType,
            cmsConfig: this.cmsConfig,
            download: this.download,
        };
    },
    components:{
        CommonDownloadGuideModal
    },
    data() {
        return {
            pageType: window._zconfig.resourceKey,
            cmsConfig: {
                downloadStatus: -1,
                agreementStatus: -1,
                homeHeadImg:'',
                homeButtonColor: '',
                homeButtonText: '',
                goDownHeadImg:'',
                goDownButtonColor:'',
                goDownButtonText:'',
            },
            canRender: false,
            showModal: {
                downloadGuide: false
            }
        };
    },
    async mounted() {
        await this.initCmsConfig();
        this.canRender = true;
        judgeIfInToutiaoIos();
    },
    methods: {
        async initCmsConfig() {
            const id = Z.getParam("materialId");

            if (!id) {
                const defaultCmsConfig = {
                    downloadStatus: 1,
                    agreementStatus: 0,
                    homeHeadImg:'https://photo.zastatic.com/images/common-cms/it/20221009/1665308600410_57385_t.png',
                    homeButtonColor: '#f971ad',
                    homeButtonText: '立即报名',
                    goDownHeadImg:'https://photo.zastatic.com/images/common-cms/it/20221010/1665396466504_508156_t.png',
                    goDownButtonColor:'#f971ad',
                    goDownButtonText:'立即约会',
                };
                Object.assign(this.cmsConfig, defaultCmsConfig);
                return;
            }

            const result = await Api.getHookMaterialInfo({ id });
            if (result.isError) {
                return;
            }

            const data = {
                ...result.data,
                ...result.data.entity[0]
            };

            this.cmsConfig = Object.assign(
                this.cmsConfig,
                this.$z_.pick(data, Object.keys(this.cmsConfig))
            );
        },
        download(){
            if (this.cmsConfig.downloadStatus === 0) {
                this.showModal.downloadGuide = true;
            } else {
                // 尝试打开app，500毫秒后再去下载
                visibilityChangeDelay(function() {
                    if (session.getItem("isToutiaoIos")) {
                        CommonDownloadGuideModalV2({value: true});
                    } else {
                        Toast({
                            message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                            duration: 5000
                        });
                        downloadApp();
                    }
                }, 500);
                openApp();
            }
        }

    }
});
</script>

<style lang="scss">
.root {
    min-height: 100vh;
    font-weight: 400;
}

</style>
