<template>
    <div class="love-gene-root">
        <router-view/>
    </div>
</template>

<script>
import { createRoot } from '@/common/framework';
import { mapActions, mapMutations } from "vuex";

export default createRoot({
    methods: {
        ...mapMutations([
            'initFormItems',
            'initRegisterForm',
            'initRequirement',
        ]),
        ...mapActions([
            'initCmsConfig',
        ]),
    },
    created() {
        // 全局数据初始化
        this.initCmsConfig();
        this.initRequirement();
        this.initFormItems();
        this.initRegisterForm();
    },
    mounted() {
        const callbackTips = this.$storage.getItem('callbackTips');

        if (callbackTips) {
            this.$toast(callbackTips);
            this.$storage.removeItem('callbackTips');
        }
    },
});
</script>

<style lang="scss" scoped>
.love-gene-root {
    color: #fff;
}
</style>
