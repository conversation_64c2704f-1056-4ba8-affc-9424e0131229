import Vuex from 'vuex';
import Api from '@/common/server/base';
import * as dict from "@/common/config/register-dictionary";
import z_ from "@/common/zdash";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import oUserSelect from '@/common/ocpx/huichuan';
import { getRandomInt } from "@/common/utils/tools.js";

export default new Vuex.Store({
    state: {
        formItems: [
            {
                key: "gender",
                label: "您的性别",
                value: "",
                options: dict.genderV2
            },
            {
                key: "workCity",
                label: "您的工作地",
                value: "",
                options: Z.workCity
            },
            {
                key: "birthday",
                label: "您的出生年份",
                value: "",
                options: dict.birthday
            },
            {
                key: "education",
                label: "您的学历",
                value: "",
                options: dict.education
            },
            {
                key: "marriage",
                label: "您的婚姻状况",
                value: "",
                options: dict.marriage
            },
            {
                key: "salary",
                label: "您的月收入",
                value: "",
                options: dict.salary
            }
        ],
        registerForm: {
            gender: '',
            workCity: '',
            birthday: '',
            marriage: '',
            salary: '',
            education: '',
            phone: '',
            height: -1
        },

        cmsConfig: {
            homeBackgroundImg: '',
            homeButtonText: '',
            bgmURL: '',
            reportViewType: 1,
            downloadStatus: -1,
            agreementStatus: -1,
        },
        // 打桩的2套方案
        resourceKey: '',
        // memberId
        regMemberId: '',
        wxInfo: {
            link: '', //小程序链接
            code: '', //企微二维码
            workerId: '' //工号id
        }
    },
    mutations: {
        setResourceKey(state, target) {
            state.resourceKey = target;
        },

        setRegisterForm(state, target) {
            const id = Z.getParam('materialId');
            const key = z_.get(target, 'key');
            const value = z_.get(target, 'value');

            state.registerForm = Object.assign(state.registerForm, {
                [key]: value
            });

            let isMark = z_.get(target, 'isMark');
            isMark = z_.isNil(isMark) ? true : isMark;

            if (isMark) {
                const getMarkValue = z_.get(target, 'getMarkValue') || (() => {
                    return {
                        [key]: value,
                    };
                });

                const markValue = getMarkValue();
                if (!z_.isEmpty(markValue) && !z_.isNil(markValue)) {
                    // oUserSelect.mark(markValue);
                    if (key == 'workCity') {
                        oUserSelect.mark({workCity: sessionStorage.getItem('workCityReport')});
                    } else {
                        oUserSelect.mark(markValue);
                    }
                }
            }

            Storage.setItem(`cachedMBTIRegisterForm_${id}`, state.registerForm);
        },
        initRegisterForm(state) {
            const id = Z.getParam('materialId');
            const cachedMBTIRegisterForm = Storage.getItem(`cachedMBTIRegisterForm_${id}`);

            if (cachedMBTIRegisterForm) {
                state.registerForm = Object.assign(state.registerForm, cachedMBTIRegisterForm);
            }
        },
        initFormItems(state) {
            const id = Z.getParam('materialId');
            let cachedMBTIFormItems = Storage.getItem(`cachedMBTIFormItems_${id}`);

            if (cachedMBTIFormItems) {
                state.formItems = Object.assign(state.formItems, cachedMBTIFormItems);
            }
        },

        adjustFormItems(state, {data} ) {
            const height = {
                key: "height",
                label: "您的身高",
                value: "",
                options: dict.height
            };
            // 如果是C方案，需要身高的选项
            if (data.reportViewType === 3 ) {

                if (state.formItems.length < 7) {
                    state.formItems.splice(4, 0, height);
                }

            }
        },

        setFormItems(state, target) {
            const id = Z.getParam('materialId');
            state.formItems.forEach(item => {
                Object.keys(target).forEach(key => {
                    if (item.key === key) {
                        item.value = target[key];
                    }
                });
            });

            Storage.setItem(`cachedMBTIFormItems_${id}`, state.formItems);
        },
        setCmsConfig(state, { data, storageKey }) {
            state.cmsConfig = Object.assign(state.cmsConfig, z_.pick(data, Object.keys(state.cmsConfig)));
            if (storageKey && !z_.isNil(state.cmsConfig)) {
                Storage.setItem(storageKey, state.cmsConfig);
            }
        },
        setRegMemberId(state, target) {
            state.regMemberId = target;
            Session.setItem('reg_memberid', target);
            oUserSelect.mark({
                msgValid: true
            });
        }
    },
    actions: {
        async initCmsConfig({ commit }) {
            const id = Z.getParam('materialId');
            const defaultConfig = {
                homeBackgroundImg: '',
                homeButtonText: '',
                bgmURL: '',
                reportViewType: 1,
                agreementStatus: 0,
                downloadStatus: 0,
            };

            const loadDefaultConfig = () => {
                commit('setCmsConfig', {
                    data: defaultConfig,
                });
                commit('adjustFormItems', {
                    data: defaultConfig,
                });
                commit('setResourceKey', 'MBTI钩子A方案');
            };

            const judgeIfNeedNewDownload = async () => {
                const channelId = Z.getParam("channelId"), subChannelId = Z.getParam("subChannelId");

                const params = {
                    channel: channelId,
                    subChannel: subChannelId
                };

                let result = {};

                try {
                    result = await Api.getChannelConfig(params);
                } catch (err) {
                    console.log(err);
                }

                if (result.isError) {
                    return commit("setResourceKey", 'MBTI钩子D方案');
                }

                // 13为品牌组媒介组
                if (result.data.mediaGroupType.includes(13)){
                    commit("setResourceKey", 'MBTI钩子D方案(企微)');
                } else {
                    commit("setResourceKey", 'MBTI钩子D方案');
                }

                if (result.data.trafficChannel && Z.platform.isIos) {
                    Storage.setItem('isToutiaoIos', true);
                } else {
                    Storage.setItem('isToutiaoIos', false);
                }
            };

            if (!id) {
                loadDefaultConfig();
                return;
            }

            const storageKey = `MbtiTestCmsConfig_${id}`;
            const cachedData = Storage.getItem(storageKey);
            if (cachedData) {
                commit('setCmsConfig', {
                    storageKey,
                    data: cachedData
                });
                commit('adjustFormItems', {
                    data: cachedData
                });
            }

            let result = await Api.getMaterial({
                id,
            });

            if (result.isError) {
                loadDefaultConfig();
                return;
            }

            const data = z_.get(result, 'data.materialVo');
            const { reportViewType } = data || {};
            const reportViewTypeMap = {
                1: 'MBTI钩子A方案',
                2: 'MBTI钩子B方案',
                3: 'MBTI钩子C方案',
            };
            const resourceKey = reportViewTypeMap[reportViewType];

            // D方案分流
            if (reportViewType === 4) {
                judgeIfNeedNewDownload();
            } else {
                commit("setResourceKey", resourceKey);
            }

            commit("setCmsConfig", {
                storageKey,
                data
            });
            commit('adjustFormItems', {
                data
            });
        },
    }
});
