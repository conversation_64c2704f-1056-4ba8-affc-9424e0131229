import Api from '@/common/server/base';
import { Toast } from "vant";
import { session } from '@/common/utils/storage.js';

export const getWxCode = async (pageType) => {
    const params = {
        pageType,
        memberID: session.getItem("reg_memberid")
    };
    let res = {};
    try {
        res = await Api.getWxCode(params);
    } catch(e) {
        console.log(e);
    }
    if (!res.isError) {
        return {
            ...res.data
        };
    }
    return {};
};

// 最开始用pageType区分来源和打桩，后统一使用ext30
// 为了兼容保留pageType, 之后只需传ext30进行区分即可
export const getMiniPath = async (code, pageType, path = "pages/loveSurvey/addQyWx/addQyWx", materialId = null) => {
    const channelId = Z.getParam("channelId"),
        subChannelId = Z.getParam("subChannelId"),
        memberId = session.getItem("reg_memberid"),
        wxQr = code;
    
    const params = {
        path: path,
        query: `channelId=${channelId}&subChannelId=${subChannelId}&memberId=${memberId}&wxQr=${wxQr}&ext30=${pageType}&pageType=${pageType}&materialId=${materialId}`,
        noToken: 1,
    };

    let res = {};
    try {
        res = await Api.getWxLink(params);
    } catch(e) {
        console.log(e);
    }
    
    if (res.code == 200 && res.data) {
        return res.data.urlLink;
    } else {
        Toast(res.msg);
    }
};

export const getMiniPathV2 = async (path = "", queryObj={}) => {
    const channelId = Z.getParam("channelId"),
        subChannelId = Z.getParam("subChannelId"),
        memberId = session.getItem("reg_memberid");

    let queryStr = '';
    Object.keys(queryObj).forEach((item)=>{
        queryStr += `&${item}=${encodeURIComponent(queryObj[item])}`;
    });
    
    const params = {
        path: path,
        query: `channelId=${channelId}&subChannelId=${subChannelId}&memberId=${memberId}${queryStr}`,
        noToken: 1,
    };

    let res = {};
    try {
        res = await Api.getWxLinkV2(params);
    } catch(e) {
        console.log(e);
    }
    
    if (res.isError) {
        Toast(res.errorMessage);
    } else {
        return res.data.urlLink;
    }
};