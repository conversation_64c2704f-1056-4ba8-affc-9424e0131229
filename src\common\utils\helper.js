/**
 * 异步加载页面
 * @param {*} mod  模块名称
 * @param {*} page 页面名称
 */
// export function loadPage(mod, page) {
//     return function(resolve) {
//         System.import(`@/pages/${mod}/${page}.vue`).then(mod => {
//             resolve(mod);
//         });
//     };
// }

/**
 * 对象转成url 参数
 * @param {*} obj
 */
export function param(obj) {
    if (obj === null || typeof obj !== 'object') {
        return;
    }
    let result = Object.keys(obj).map((k) => {
        return encodeURIComponent(k) + '=' + encodeURIComponent(obj[k]);
    }).join('&');
    return result;
}

/**
 * 检测是否跨域
 * @param {string} url - The target URL.
 * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.
 */
export function isCrossOriginURL(url) {
    const REGEXP_ORIGINS = /^(https?:)\/\/([^:/?#]+):?(\d*)/i;
    const parts = url.match(REGEXP_ORIGINS);
    return parts && (
          parts[1] !== location.protocol ||
          parts[2] !== location.hostname ||
          parts[3] !== location.port
      );
}


let zIndex = 100
export function getZIndex() {
    return zIndex++
}