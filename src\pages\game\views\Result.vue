<template>
    <div class="result-wrap">
        <z-image
            class="content"
            :width="750"
            :height="1489"
            src="https://photo.zastatic.com/images/common-cms/it/20220812/1660295237709_720995_t.png"
        >
            <z-image
                class="title"
                :width="550"
                :height="226"
                src="https://photo.zastatic.com/images/common-cms/it/20220729/1659061997656_682377_t.png"
            />
            <div class="box">
                <div>
                    报名成功啦！<br>
                    添加小助手参与活动吧~
                </div>
                <div>
                    <img
                        @touchstart="handleTouchStart"
                        @touchend="handleTouchEnd"
                        @touchcancel="handleTouchEnd"
                        :src="wxInfo.code"
                    />
                </div>
                <div>
                    小助手会通过微信的方式联系您活动时间等信息哦
                </div>
            </div>
            <div class="button">
                <common-button
                    :config="{width: 486, height: 96, fontSize: 32, des: '戳我，添加小助手', isNeedIcon: false}"
                    @click="handleJump"
                />
            </div>
        </z-image>
    </div>
</template>

<script>
import CommonButton from '../components/CommonButton.vue';

import { getMiniPath, getWxCode } from '@/common/business/utils/wecom';

export default {
    name: 'Result',
    data() {
        return {
            link: '',
            timer: null,
            wxInfo: {
                link: '', //小程序链接
                code: '', //企微二维码
                userId: '' //工号id
            }
        };
    },
    components: {
        CommonButton
    },
    async mounted() {
        const res = await getWxCode(29);
        this.wxInfo.code = res.qrCode;
        this.wxInfo.userId = res.userId;
        this.$report(9, '添加企微页访问', {
            ext18: res.userId
        });
        this.wxInfo.link = await getMiniPath(res.qrCode, null, 'pages/dateGame/dateGame');
    },
    methods: {
        handleJump() {
            this.$report(11, '添加企微页-点击去微信', {
                ext18: this.wxInfo.userId
            });
            setTimeout(() => { window.location.href = this.wxInfo.link; }, 300);
        },

        handleTouchStart() {
            this.timer = setTimeout(() => {
                // 模拟长按事件
                this.$report(10, '添加企微页-长按保存图片', {
                    ext18: this.wxInfo.userId
                });
            }, 500);
        },

        handleTouchEnd() {
            clearTimeout(this.timer);
        },
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.result-wrap {
    min-height: 100vh;
    background-color: #FBB933;
    .content {
        position: relative;
        .title {
            position: absolute;
            top: 126px;
            left: 100px;
        }
        .box {
            @include flex-center(column, null, center);
            position: absolute;
            top: 361px;
            left: 175px;
            width: 400px;
            height: 486px;
            border-radius: 12px;
            >div:nth-child(1) {
                margin: 4px auto 24px;
                text-align: center;
                font-weight: Bold;
                font-size: 28px;
                line-height: 38px;
                color: #1F1F1F;
            }
            >div:nth-child(2) {
                padding: 10px;
                width: 360px;
                height: 360px;
                background: #FFDE4B;
                border: 1px solid #000000;
                border-radius: 12px;
                >img {
                    border-radius: 12px;
                    width: 337px;
                    height: 337px;
                }
            }
            >div:nth-child(3) {
                text-align: center;
                margin-top: 16px;
                width: 300px; 
                font-weight: 400;
                font-size: 20px;
                color: #1F1F1F;
                line-height: 28px;
            }

            &::after {
                content: "";
                position: absolute;
                left: 354px;
                top: 16px;
                width: 67px;
                height: 67px;
                @include set-img("https://photo.zastatic.com/images/common-cms/it/20220729/1659077425812_534912_t.png")
            }
        }
        .button {
            position: absolute;
            z-index: 1;
            top: 928px;
            left: 132px;
        }
    }
}
</style>