import Vuex from 'vuex';
import Api from '@/common/server/base';
import * as dict from "@/common/config/register-dictionary";
import z_ from "@/common/zdash";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import oUserSelect from '@/common/ocpx/huichuan';

const preloadImages = (cmsConfig) => {
    setTimeout(() => {
        z_.forEach(z_.pick(cmsConfig, [
            'questionHeaderImg',
            'questionBodyImg1',
            'questionBodyImg2',
            'questionBodyImg3',
            'questionBodyImg4',
        ]), (src) => {
            const image = new Image();
            image.src = src;
        })
    }, 0);
}

export default new Vuex.Store({
    state: {
        formItems: [
            {
                key: "workCity",
                label: "您的工作地",
                value: "",
                options: Z.workCity
            },
            {
                key: "birthday",
                label: "您的出生年份",
                value: "",
                options: dict.birthday
            },
            {
                key: "education",
                label: "您的学历",
                value: "",
                options: dict.education
            },
            {
                key: "marriage",
                label: "您的婚姻状况",
                value: "",
                options: dict.marriage
            },
            {
                key: "salary",
                label: "您的月收入",
                value: "",
                options: dict.salary
            }
        ],
        requirement: {
            ageRange: '',
            salaryRange: '',
            tags: [],
        },
        registerForm: {
            gender: -1,
            workCity: '',
            birthday: '',
            marriage: '',
            salary: '',
            education: '',
            phone: ''
        },
        isDefaultCmsConfig: false,
        cmsConfig: {
            homeBackgroundImg: '',
            homeButtonText: '',
            homeButtonColor: '',
            questionHeaderImg: '',
            questionBodyImg1: '',
            questionBodyImg2: '',
            questionBodyImg3: '',
            questionBodyImg4: '',
            downloadStatus: -1,
            agreementStatus: -1,
        },
        regMemberId: '',
    },
    mutations: {
        setIsDefaultCmsConfig(state, target) {
            state.isDefaultCmsConfig = target;
        },
        setRequirement(state, target) {
            state.requirement = Object.assign(state.requirement, target);

            Storage.setItem('cachedRequirement', state.requirement);
        },
        setRegisterForm(state, target) {
            const key = z_.get(target, 'key');
            const value = z_.get(target, 'value');

            state.registerForm = Object.assign(state.registerForm, {
                [key]: value
            });

            let isMark = z_.get(target, 'isMark');
            isMark = z_.isNil(isMark) ? true : isMark;

            if (isMark) {
                const getMarkValue = z_.get(target, 'getMarkValue') || (() => {
                    return {
                        [key]: value,
                    }
                });

                const markValue = getMarkValue();
                if (!z_.isEmpty(markValue) && !z_.isNil(markValue)) {
                    oUserSelect.mark(markValue);
                }
            }

            Storage.setItem('cachedRegisterForm', state.registerForm);
        },
        initRequirement(state) {
            const cachedRequirement = Storage.getItem('cachedRequirement');

            if (cachedRequirement) {
                state.requirement = Object.assign(state.requirement, cachedRequirement);
            }
        },
        initRegisterForm(state) {
            const cachedRegisterForm = Storage.getItem('cachedRegisterForm');

            if (cachedRegisterForm) {
                state.registerForm = Object.assign(state.registerForm, cachedRegisterForm);
            }
        },
        initFormItems(state) {
            const cachedFormItems = Storage.getItem('cachedFormItems');

            if (cachedFormItems) {
                state.formItems = Object.assign(state.formItems, cachedFormItems);
            }
        },
        setFormItems(state, target) {
            state.formItems.forEach(item => {
                Object.keys(target).forEach(key => {
                    if (item.key === key) {
                        item.value = target[key];
                    }
                })
            });

            Storage.setItem('cachedFormItems', state.formItems);
        },
        setCmsConfig(state, { data, storageKey }) {
            state.cmsConfig = Object.assign(state.cmsConfig, z_.pick(data, Object.keys(state.cmsConfig)));
            if (storageKey && !z_.isNil(state.cmsConfig)) {
                Storage.setItem(storageKey, state.cmsConfig);
            }
        },
        setRegMemberId(state, target) {
            state.regMemberId = target;
            Session.setItem('reg_memberid', target);
            console.log('reg_memberid: ', target);
            oUserSelect.mark({
                msgValid: true
            });
        }
    },
    actions: {
        async initCmsConfig({ commit }) {
            const id = Z.getParam('materialId');
            const defaultConfig = {
                homeBackgroundImg: require('../assets/images/main-bg.jpeg'),
                homeButtonText: '开始选择',
                homeButtonColor: 'linear-gradient(90deg, #FC9CFF 0%, #7146FF 50%, #57F3FF 100%)',
                questionHeaderImg: require('../assets/images/question-header-img.png'),
                questionBodyImg1: require('../assets/images/question-body-img-1.png'),
                questionBodyImg2: require('../assets/images/question-body-img-2.png'),
                questionBodyImg3: require('../assets/images/question-body-img-3.png'),
                questionBodyImg4: require('../assets/images/question-body-img-4.png'),
                agreementStatus: 0,
                downloadStatus: 0,
            };

            const loadDefaultConfig = () => {
                commit('setCmsConfig', {
                    data: defaultConfig,
                });
                commit('setIsDefaultCmsConfig', true);
                preloadImages(defaultConfig);
            }

            if (!id) {
                loadDefaultConfig();
                return;
            }

            const storageKey = `loveGeneCmsConfig_${id}`;
            const cachedData = Storage.getItem(storageKey);
            if (cachedData) {
                commit('setCmsConfig', {
                    storageKey,
                    data:cachedData
                });
            }

            let result = await Api.getMaterial({
                id,
            });

            if (result.isError) {
                loadDefaultConfig();
                return;
            }

            const data = z_.get(result, 'data.materialVo');

            // 处理渐变
            function handleColor(color) {
                if (color.indexOf('&') > -1) {
                    // 按钮为渐变色
                    let [topColor, bottomColor] = color.split("&");
                    return `linear-gradient(0deg,${topColor},${bottomColor})`;

                }
                return color;
            }

            data.homeButtonColor = handleColor(data.homeButtonColor);

            commit("setCmsConfig", {
                storageKey,
                data
            });
        },
    }
});
