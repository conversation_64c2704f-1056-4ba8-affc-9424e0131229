<template>
    <div class="birthday">
        <div class="title">
            {{ list.label }}
        </div>
        <div class="subtitle">
            {{ list.desc }}
        </div>
        <div class="birthday-scroll" ref="birthScroll">
            <div class="birthday-info" v-for="(option, name) in list.options">
                <h4 class="birthday-info-title">{{ name }}后</h4>
                <div class="birthday-info-card" :class="{ spe: name === '00' }">
                    <span
                        class="bic-num"
                        :class="{ active: item === curBirthday }"
                        v-for="item in option"
                        @click="goNext(item)"
                        >{{ item }}</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    setLocalRegisterForm,
    keyToValue
} from "@/common/business/utils/localRegisterForm.js";
import { storage as Storage } from "@/common/utils/storage";
export default {
    name: "Birthday",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    inject: ["cmsConfig"],
    data() {
        return {
            curBirthday: keyToValue(
                "birthday",
                (Storage.getItem(
                    `cachedRegisterForm-${this.cmsConfig.planName}`
                ) &&
                    Storage.getItem(
                        `cachedRegisterForm-${this.cmsConfig.planName}`
                    ).birthday) ||
                    ""
            )
        };
    },
    methods: {
        goNext(val) {
            this.curBirthday = val;
            const params = {
                key: "birthday",
                value: new Date(val + "/" + 1 + "/" + 1).getTime(),
                isMark: false
            };
            setLocalRegisterForm(params, this.cmsConfig.planName);
            setLocalRegisterForm(
                { key: "year", value: val },
                this.cmsConfig.planName
            );
            this.$report(6, "出生年份页-具体年份点击");

            setTimeout(() => {
                this.$emit("val-updated", val);
            }, 300);
        }
    },
    mounted() {
        this.$report(6, "出生年份页访问");
        this.$refs.birthScroll.scrollTo(0, 340);
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    .title {
        padding: 48px 0 26px 48px;
        color: #0f1122;
        font-size: 64px;
        font-weight: 500;
    }
    .subtitle {
        padding-left: 48px;
        color: #0f1122;
        font-size: 32px;
    }
    .birthday-scroll {
        margin-top: 46px;
        overflow-y: scroll;
        height: calc(100vh - 354px);
    }
    &-info {
        &-title {
            margin-left: 74px;
            color: #0f1122;
            font-size: 36px;
            font-weight: 400;
        }
        &-card {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            padding: 34px 4px;
            margin: 24px 48px 48px;
            background: #fff;
            border-radius: 60px;
            > span {
                padding: 0 26px;
                color: #26273c;
                font-size: 32px;
            }
            > span:first-child {
                margin-bottom: 52px;
            }
            .bic-num:active {
                opacity: 0.7;
            }
            .active {
                color: #6568ff;
                font-weight: 600;
            }
            &.spe {
                > span {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
