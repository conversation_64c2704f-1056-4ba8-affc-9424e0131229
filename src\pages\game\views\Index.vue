<template>
    <div class="index-wrap">
        <z-image
            class="header"
            :width="750"
            :height="1550"
            src="https://photo.zastatic.com/images/common-cms/it/20220728/1658989795917_182407_t.png"
        >
            <div
                class="header-catch"
                @click="handleCatch"
            >
            </div>
            <div class="header-button">
                <common-button
                    :config="{width: 558, height: 110, fontSize: 36, des: '即刻开启挑战', isNeedIcon: true}"
                    @click="handleScrollToForm(6)"
                />
            </div>
        </z-image>
        
        <z-image
            class="content"
            :width="714"
            :height="2348"
            src="https://photo.zastatic.com/images/common-cms/it/20220728/1658999411301_336740_t.png"
        >
            <div class="content-button">
                <common-button
                    :config="{width: 630, height: 110, fontSize: 36, des: '立刻报名', isNeedIcon: true}"
                    @click="handleScrollToForm(7)"
                />
            </div>
        </z-image>

        <div class="form">
            <z-image
                class="form-header"
                :width="750"
                :height="436"
                src="https://photo.zastatic.com/images/common-cms/it/20220728/1659000418325_472267_t.png"
            />
            <div class="form-detail">
                <common-form
                    page-type="脱单游戏"
                    :filter-config="['gender', 'workCity','birthday','education','marriage','salary','phone']"
                    :style-config="{
                        color: '#000000',
                        fontColor: '#FFFFFF',
                        labelColor: '#202832',
                        valueColor: '#202832',
                        phoneBgColor: '#F4F4F4',
                        selectorColor: '#000000',
                        selectorFontColor: '#FFFFFF',
                    }"
                />
            </div>
            <div class="form-submit">
                <common-submit
                    page-type="脱单游戏"
                    :agreement-status="1"
                    :is-need-protocol="true"
                    :style-config="{
                        modalConfig: {
                            confirmButtonColor: '#000000',
                            confirmButtonBgColor: '#b0f344',
                            cancleButtonColor: '#000000',
                        },
                        protocolConfig: {
                            textColor: '#000000',
                            protocolColor: '#FF8100',
                            protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20220729/1659088335870_950056_t.png'
                        }
                    }"
                    :handle-after-regisiter="handleJump"
                    :handle-login="handleJump"
                >
                    <common-button :config="{width: 630, height: 110, fontSize: 36, des: 'START', isNeedIcon: true}" />
                </common-submit>
            </div>
        </div>

        <z-image
            class="guide"
            :width="714"
            :height="944"
            src="https://photo.zastatic.com/images/common-cms/it/20220728/1659002800293_926386_t.png"
        >
            <div class="guide-title">
                《脱单指南》
            </div>
            <div class="guide-subtitle">
                如果这期没有遇到合适的对象，将免费赠送1次资深脱单指导老师为您定制的《脱单指南》哦！
            </div>
            <div class="guide-button">
                <common-button
                    :config="{width: 630, height: 110, fontSize: 36, des: '领取脱单指南', isNeedIcon: true}"
                    @click="handleScrollToForm(8)"
                />
            </div>
        </z-image>
    </div>
</template>

<script>
import CommonButton from '../components/CommonButton.vue';
import CommonForm from '@/common/business/CommonForm';
import CommonSubmit from '@/common/business/CommonSubmit';

export default {
    name: 'Index',
    components: {
        CommonButton,
        CommonForm,
        CommonSubmit
    },
    data() {
        return {
            reportLock: {
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            screenHeight: 0,
        };
    },
    mounted() {
        this.$report(1, '首页第1屏曝光');

        window.addEventListener("scroll", this.handleExposure);
        this.screenHeight = document.documentElement.clientHeight;
    },
    methods: {
        handleScrollToForm(accessPoint) {
            const REPORTENUM = {
                6: '首页按钮1-开启挑战',
                7: '首页按钮2-立即报名',
                8: '首页按钮4-领取脱单指南'
            };

            this.$report(accessPoint, REPORTENUM[accessPoint]);

            const header= document.querySelector(".index-wrap .form .form-header");
            const target = document.querySelector(".index-wrap .form");
            window.scrollTo({
                top: target.offsetTop + header.offsetHeight / 7,
                behavior: "smooth"
            });
        },


        handleJump() {
            this.$router.push('/result');
        },

        handleExposure() {
            if (
                document.documentElement.scrollTop > this.screenHeight &&
                !this.reportLock.secondScreen
            ) {
                this.$report(2, "首页第2屏曝光");
                this.reportLock.secondScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 2 &&
                !this.reportLock.thirdScreen
            ) {
                this.$report(3, "首页第3屏曝光(注册表单)");
                this.reportLock.thirdScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 3 &&
                !this.reportLock.fourthScreen
            ) {
                this.$report(4, "首页第4屏曝光");
                this.reportLock.fourthScreen = true;
            }
            if (
                document.documentElement.scrollTop > this.screenHeight * 4 &&
                !this.reportLock.fifthScreen
            ) {
                this.$report(5, "首页第5屏曝光");
                this.reportLock.fifthScreen = true;
            }
            // 如果已经触发五次上报则取消对scroll的监听
            let reportedNum = Object.values(this.reportLock).filter(
                item => item === true
            ).length;
            if (reportedNum === this.reportLock.length) {
                window.removeEventListener("scroll", this.handleExposure);
            }
        },

        handleCatch() {
            this.$report(13, '首页-抓取按钮点击');
        }
        
    }
};

</script>

<style lang="scss" scope>
@import '~@/common/styles/common.scss';

.index-wrap {
    @include flex-center(column, null, center);
    position: relative;
    &::after {
        content: '';
        position: absolute;
        right: 0px;
        top: 1495px;
        width: 130px;
        height: 152px;
        @include set-img("https://photo.zastatic.com/images/common-cms/it/20220728/1658999898298_218369_t.png");
    }

    .header {
        position: relative;
        &-catch {
            position: absolute;
            width: 200px;
            height: 110px;
            left: 430px;
            top: 575px;
        }
        &-button {
            z-index: 1;
            position: absolute;
            top: 1022px;
            left: 96px;
        }
    }

    .content {
        position: relative;
        &-button {
            z-index: 1;
            position: absolute;
            top: 2165px;
            left: 40px;
        }
    }

    .form {
        margin-top: 36px;
        &-detail {
            margin: 16px auto 0px;
            padding: 16px 32px 0px;
            width: 686px;
            height: 1066px;
            border: 2px solid #000000;
            border-radius: 28px;
            background-color: #ffffff;
        }
        &-submit {
            position: relative;
            margin-top: -220px;
            z-index: 1;
        }
    }

    .guide {
        margin: 60px 12px 100px 24px;
        position: relative;
        &-title {
            position: absolute;
            top: 555px;
            left: 253px;
            font-weight: 500;
            font-size: 32px;
            color: #121212;
        }
        &-subtitle {
            position: absolute;
            top: 632px;
            left: 44px;
            width: 628px;
            line-height: 41px;
            font-weight: 400;
            font-size: 28px;
            color: #4D4D4D;
        }
        &-button {
            position: absolute;
            z-index: 1;
            top: 762px;
            left: 33px;
        }
    }
}
</style>