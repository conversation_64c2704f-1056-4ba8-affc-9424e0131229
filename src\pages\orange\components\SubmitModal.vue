<template>
    <van-popup
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
        class="submit"
    >
        <z-image
            class="submit__close"
            :width="72"
            :height="72"
            src="https://photo.zastatic.com/images/common-cms/it/20221010/1665385213180_374260_t.png"
            @click="close"
        />
        <z-image
            class="submit__icon"
            :width="214"
            :height="214"
            src="https://photo.zastatic.com/images/common-cms/it/20221010/1665385232831_678645_t.png"
        />
        <div class="submit__tips">
            表单提交成功
        </div>
        <div
            class="submit__download"
            @click="goDownload"
        >
            报名成功&nbsp;开始下载
        </div>
    </van-popup>
</template>

<script>
export default {
    name: 'SubmitModal',
    props: {
        value: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$report(5, '下载弹窗曝光');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            if(!value){
                this.$emit('openRecommendModal');
            }
            this.$emit('input', value);
        },
        goDownload() {
            this.$report(5, '下载弹窗-点击下载');
            this.$emit('download');
        },
        close() {
            this.$report(5, '下载弹窗-点击关闭');
            this.$emit('input', false);
            this.$emit('openRecommendModal');
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';

.submit {
    @include flex-center(column,flex-start,center);
    width: 622px;
    height: 590px;
    background: #FFFFFF;
    border-radius: 32px;
    text-align: center;

    &__close{
        align-self: flex-end;
    }

    &__icon{
        margin: 20px auto 0;
    }

    &__tips{
        margin-top: 24px;
        width: 216px;
        height: 36px;
        font-weight: 500;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        line-height: 36px;
    }

    &__download{
        margin-top: 64px;
        width: 490px;
        height: 88px;
        background: #F971AD;
        border-radius: 44px;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        line-height: 88px;
    }
}
</style>
