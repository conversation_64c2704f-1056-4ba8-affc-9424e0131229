<template>
    <div id="app">
        <div
            :class="{top:true, hidden_mis: !isShowLeft}"
            class="nav"
            v-show="showNav"
        >
            <div
                v-show="isShowLeft && id != '0'"
                @click="goback"
                class="panel-back"
            >
                <img
                    class="back_arrow"
                    src="https://photo.zastatic.com/images/common-cms/it/20240522/1716358552311_886905.png"
                    alt=""
                >
            </div>
            <div
                v-if="isShowMid"
                class="panel-full"
            >
                {{ infoList[id].title }}<br />
            </div>
            <div
                v-show="isShowRight"
                @click="login"
                class="panel-login"
            >
                登录
            </div>
        </div>
        <div
            class="progress"
            v-if="id"
        >
            {{ +id + 1 }}/{{ infoList.length }}
        </div>

        <keep-alive exclude="['Register','Result']">
            <router-view
                :key="$route.fullPath"
                v-if="canRender"
            />
        </keep-alive>
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';
import Player from './components/Player.vue';
import { QUIZ_LIST_REGISTER } from './config';
export default createRoot({
    name: "App",
    data() {
        return {
            canRender: false,
            isShowLeft: false,
            isShowRight: false,
            isShowMid: true,
            showNavImg: true,
            showNav: true,
            backImg: '',
            name: '',
            to: null,
            id: this.$route.params.id,
            infoList: QUIZ_LIST_REGISTER,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    created() {
        console.log(this.$route);
        this.routeChange(this.$route);
        this.$router.beforeEach((to, from, next) => {
            this.to = to;
            this.routeChange(to);
            next();
        });
    },
    mounted() {
        // judgeIfInToutiaoIos();
        this.initAsync();
    },
    methods: {
        async initAsync() {
            // 处理异步
            this.canRender = true;
        },
        routeChange(to) {
            this.name = to.name;
            this.id = to.params.id;
            console.log(to.name, this.id);
            switch(to.name){
            case 'Message':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = true;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264115763_199295_t.jpg';
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'Gender':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = true;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264115763_199295_t.jpg';
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'Register':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = true;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264041914_81726_t.jpg';
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'Hometown':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = true;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264041914_81726_t.jpg';
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'SuccessAnimation':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = true;
                break;
            case 'DownApp':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = false;
                break;
            case 'protocol':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = true;
                break;
            case 'Start':
                this.isShowLeft = false;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = false;
                break;
            default:
                this.isShowLeft = false;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = false;
                break;
            }
        },
        goback() {
            this.$router.back();
        },
        login() {
            location.href = 'https://i.zhenai.com/m/portal/login.html' + location.search;
        },
        download() {

        },
        // preloadImg() {
        //     let image = new Image();
        //     image.src ="./assets/images/f.svga";
        // },
    }
});
</script>

<style lang="scss">
// 全局样式
html,body{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    width: 100%;
    height: 100%;
}
</style>
<style lang="scss" scoped>
#app{
    position: fixed;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: url("https://photo.zastatic.com/images/common-cms/it/20240521/1716280553192_908841_t.png") no-repeat top center;
    background-size: cover;

}
.top{
    font-size: 34px;
    font-weight: 600;
    width: 100%;
    color: #222833;
    padding: 0 20px 0 40px;
    margin-top: 27px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 88px;
    box-sizing: border-box;
    .panel-back{
        color: #999999;
    }
    .panel-back-call{
        width: 34px;
        height: 54px;
    }
    .panel-full {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;

    }
    .panel-login {
        position: absolute;
        right: 20px;
    }
}
.progress {
    color: #222833;
    font-size: 36px;
    text-align: center;
}
.nav-img {
    background-size: 100% 100%;
    width: 100%;
    height: 240px;
    line-height: 60px;
    padding-top: 56px;
    font-size: 38px;
    font-weight: 700;
    .message-box {
        padding-left: 40px;
        .sail {
            color: #ff6b6b;
            font-size: 46px;
            margin-left: 15px;
        }
        .step {
            color: #8b76f9;
        }
    }
    .register-box {
        color: #ffffff;
        text-align: center;
        .address_title_txt {

        }
    }
    &.nav-img-register {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 0;
    }
}
.appresult{
    position: relative;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230811/1691745433928_1320_t.png');
    background-size: contain;
    background-repeat: no-repeat;
    width: 100vw;
    min-height: 2930px;
    background-color: red;
    background-color: #1e1441;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    align-items: center;
}

    .hidden_mis{
        /* justify-content: flex-end; */
        width: auto;
        right: 0;
        z-index: 100;
    }

    .music{
        width: 74px;
        height: 74px;
    }
    .back_arrow {
      width: 16.69px;
height: 30.05px;
    }
</style>
