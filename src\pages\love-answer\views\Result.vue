<template>
    <div
        class="result"
    >
        <img
            src="https://photo.zastatic.com/images/common-cms/it/20230811/1691745687440_167018_t.png"
            class="header">
        </img>
        <div class="content">
            <img src="https://photo.zastatic.com/images/common-cms/it/20230811/1691750030566_90956_t.png" alt="" class="content_img">
            <div>
                <p class="title">恋爱人格关键词</p>
                <p class="title_type">{{ result.desc }}</p>
            </div>
        </div>
        <!--人格类型图片 -->
        <div class="text_grad" v-show="result.img">
            <img :src="result.img" alt="">
        </div>
        <img src="https://photo.zastatic.com/images/common-cms/it/20230811/1691752181886_355957_t.png" alt="" class="record">

        <div
            class="content_t"
            v-for="(item) in result.options"
            :key="item.title">
            <div class="total_title">
                <img src="https://photo.zastatic.com/images/common-cms/it/20230812/1691823059780_680309_t.png" alt="" class="total">
                <p>{{ item.title }}：</p>
            </div>
            <div v-if="!(item.content instanceof Array)" style="white-space: pre-wrap;">{{ item.content }}</div>

            <template v-else>
                <div
                    v-for="(nav,indexContene) in item.navtitle"
                    :key="nav"
                    class="title_content"
                >
                    <div class="title-nav">{{ nav }}</div>
                    <div>{{ item.content[indexContene] }}</div>

                </div>
            </template>
        </div>
        <div
            @click="handleDownload"
            class="downLoad">
            立即脱单
        </div>
    </div>
</template>

<script>
import { getAnswerResult } from '../api';
import { storage } from "@/common/utils/storage";
import { getTypeImg } from '../utils/index';
import {
    openApp,
    visibilityChangeDelay,
    downloadApp
} from "@/common/utils/download_app.js";
import CommonDownloadGuideModalV2 from "@/common/business/components/CommonDownloadGuideModalV2/index";
import { storage as Storage, session as Session } from "@/common/utils/storage";
import Api from '@/common/server/base';
import { CryptoDes } from '@/common/utils/crypto';
const cryptoDes = new CryptoDes('rlWU9Uty8atXZVE8')
import { Toast } from "vant";
export default {
    name:'Result',
    components:{
    },
    data(){
        return {
            reportLock:{
                secondScreen: false,
                thirdScreen: false,
                fourthScreen: false,
                fifthScreen: false
            },
            result:{
                desc: '',
                note: '',
                options: [],
                type:'',
                img:''
            },
            expertJumpULoveApplet: false, // 是否跳优恋空间
            youlianLink: '', // 跳优恋空间链接
        };
    },
    async created(){
        this.getResult();
        this.isJumpYoulian();
    },
    activated(){
        this.getResult();
        this.$report(43, "测试报告页访问");
    },
    mounted(){

    },
    methods:{
        async isJumpYoulian() {
            const res = await Api.getRegChainConfig({
                channelId: Z.getParam('channelId'),
                subChannelId: Z.getParam('subChannelId'),
            })
            if (res.code === 0) {
                this.expertJumpULoveApplet = res.data.results.expertJumpULoveApplet
            }
        },
        async getYoulianLink() {

            if (this.expertJumpULoveApplet) {
                const memberId = sessionStorage.getItem('reg_memberid')
                this.$report(123, " 测试结果页-短链生成进入");
                let res1 = {}
                if (memberId) {
                    const demem = cryptoDes.encode(memberId)
                    res1 = await Api.getValidToken({memberId: demem})
                    this.$report(123, " 测试结果页-换取token", {
                        ext1: JSON.stringify(res1)
                    });
                }
                const linkUrl = Api.domain().includes('api-test.zajiebao.com') ? encodeURIComponent('https://mp.weixin.qq.com/s/MODFT629m9B3w9v8H15Gig') : encodeURIComponent('https://mp.weixin.qq.com/s/QeszlKRc_D3oKYCoCDE-4w')
                const params = {
                    path: 'pages/main/meet/index',
                    query: `channelId=${Z.getParam('channelId')}&subChannelId=${Z.getParam('subChannelId')}&zaAPPToken=${res1.data ? res1.data : ''}&linkUrl=${linkUrl}`,
                };
                const res2 = await Api.goYouLianMini(params)
                if (res2.code === 0) {
                    this.$report(123, " 测试结果页-换取token", {
                        ext1: JSON.stringify(res2)
                    });
                    this.youlianLink = res2.data
                    location.href = this.youlianLink
                }
            }
        },
        getResult() {
            let form = {
                type:1,
                resultType: storage.getItem('resultTypeData')
            };
            getAnswerResult(form).then((res)=>{
                if(res.code === 0) {
                    this.result = res.data;
                    this.result.img = getTypeImg(res.data.desc);
                    this.result.options = this.spliceData(res.data.options);
                } else {
                    //系统错误
                }

            });
        },
        handleDownload() {
            this.$report(44, "报告页-点击下载APP按钮");
            if (this.expertJumpULoveApplet) {
                this.getYoulianLink()
                return false
            }
            // 尝试打开app，500毫秒后再去下载
            visibilityChangeDelay(function() {
                if (Session.getItem("isToutiaoIos")) {
                    CommonDownloadGuideModalV2({ value: true });
                } else {
                    Toast({
                        message: "正在下载APP，Ta在珍爱APP等你，赶紧去约会吧！",
                        duration: 5000
                    });
                    downloadApp();
                }
            }, 500);
            openApp();
        },
        spliceData(options){
            let data = options.map(ele => {
                let title = ele.content.match(/\d[^\d|\r\n]*?\r\n/g);
                if(title && title.length > 0){
                    ele.navtitle = title;
                    ele.content = ele.content + "\r\n\r\n";
                    ele.content = ele.content.match(/\r\n[^\r\n|\r\n\r\n]*?\r\n\r\n/g);
                }
                return ele;
            });

            return data;
        }

    },
    destroyed(){
        window.removeEventListener('scroll',this.listenScreen);
    }

};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.result{
    display: flex;
    flex-direction: column;
    align-items: center;
    .header{
        width: 538px;
        height: 88px;
        object-fit: contain;
        margin-top: 76px;
    }
    .content{
        margin-top: 22px;
        width: 680px;
        height: 357.5px;
        border-radius: 30px 30px 30px 30px;
        background: #fff;
        box-shadow: 0 0 80px 0 #eac8ff inset;
        display: flex;
        box-sizing: border-box;
        align-items: center;
        .content_img{
            width: 274px;
            height: 262px;
            margin-left: 38px;

            margin-right: 20px;
        }
        .title{
            font-family: 'love';
            height: 50px;
            font-size: 44px;
            text-align: center;
            color: #59179D;
            letter-spacing: 2px;
            margin-bottom: 29px;
        }
        .title_type{
            font-family: 'love';
            height: 80px;
            font-size: 65px;
            color: #59179D;
        }
    }
    .text_grad{
        margin-top: 34px;
        width: 680px;
        height: 500px;
        border-radius: 30px 30px 30px 30px;
        background: #1d144dde;
        box-shadow: 0 0 46px 0 #410e7159;
        border: 1px solid #A096FF;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
            width: 485px;
            height: 485px;
            object-fit: contain;
        }
    }
    .record{
        width: 670px;
        height: 138px;
        object-fit: contain;

    }
    .content_t{
        margin-bottom: 54.6px;
        width: 680px;
        border-radius: 30px 30px 30px 30px;
        background: #fff;
        box-shadow: 0 0 80px 0 #eac8ff inset;
        display: flex;
        box-sizing: border-box;
        padding: 58.5px 41px 65.6px 41px;
        font-weight: 65 Medium;
        font-size: 26px;
        color: #443952;
        line-height: 40px;
        flex-direction: column;
        .total_title{
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            .total{
                width: 97px;
                height: 89px;
                object-fit: contain;
                flex-shrink: 1;
            }
            p{
                font-family: 'love';
                height: 63px;
                font-size: 40px;
                text-align: left;
                color: #412A97;

            }
        }


    }
    .downLoad{
        position: fixed;
        bottom: 40px;
        width: 326px;
        height: 96px;
        border-radius: 62px 62px 62px 62px;
        font-family: 'love';
        font-size: 50px;
        text-align: center;
        color: #FFFFFF;
        letter-spacing: 4px;
        box-shadow: 0 0 10px 0 #fff;
        background: linear-gradient(180deg,#B29BFF, #8560FF);
        display:flex;
        justify-content: center;
        align-items: center;

    }
    .title-nav{
        color: #6E4EE5;
        font-size: 32px;
        margin-bottom: 8px;
    }
    .title_content{
        width: 100%;
        border-radius: 25px 25px 25px 25px;
        background: #f7f1fb;
        padding: 26px;
        margin-bottom: 26px;
    }
}
</style>
