import { get, post, ajax } from "@/common/utils/ajax";

export function domain() {
    const url = location.href
    if (url.includes('i.zhenai.com') || url.includes('a.zhenai.com')) {
        return 'https://api.zajiebao.com'
    } else if (url.includes('i-pre.zhenai.com')) {
        return 'https://api-pre.zajiebao.com'
    } else {
        return 'https://api-test.zajiebao.com'
    }
}

export function xftDomain() {
    const url = location.href
    if (url.includes('i.zhenai.com') || url.includes('a.zhenai.com')) {
        return 'https://yht-api.kaojin.cc'
    } else if (url.includes('i-pre.zhenai.com')) {
        return 'https://yht-api-pre.kaojin.cc'
    } else {
        return 'https://yht-api-test.kaojin.cc'
    }
}

export default {
    domain: domain,
    submitWapRegBaseInfo: (data) => post('/register/submitWapRegBaseInfo.do', data),
    sendWapMessageCodeV2: (data) => post('/register/sendWapMessageCodeV2.do', data),
    overwriteAccount: (data) => get('/register/validAndRegNew.do', data),
    submitWapRegNoPasswordInfoV2: (data) => {
        return post("/register/submitWapRegNoPasswordInfoV2.do", data);
        // return  {
        //     "data":
        //         {
        //             "memberInfoVo": {
        //                     "age": 27,
        //                     "education": "大学本科",
        //                     "gender": 0,
        //                     "memberId": **********,
        //                     "workPlace": "深圳"
        //                 },
        //             "oldMemberID": **********,
        //             "overwriteRegistrationSwitch": true,
        //             "type": -2
        //         },
        //     "errorCode": "",
        //     "errorMessage": "",
        //     "isError": false
        // }
    },
    getSpecifyGenderRandomAvatar: (data) => {
        return get("/register/getSpecifyGenderRandomAvatar.do", data);
        // return {
        //     "data": {
        //         "list": [ {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211216/1639622094661_223806_t.jpg",
        //             "id": 154,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211223/1640254282622_962701_t.jpg",
        //             "id": 160,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211216/1639652760530_585573.jpg",
        //             "id": 164,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211216/1639621933384_593421_t.jpg",
        //             "id": 176,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211223/1640253189880_722548_t.jpg",
        //             "id": 186,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211217/1639738196566_476027_t.jpg",
        //             "id": 195,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20211215/1639558870532_398819_t.jpg",
        //             "id": 208,
        //             "sex": 1
        //         }, {
        //             "avatar": "https://photo.zastatic.com/images/common-cms/it/20220119/1642563749938_331917_t.jpg",
        //             "id": 235,
        //             "sex": 1
        //         } ]
        //     }, "errorCode": "", "errorMessage": "", "isError": false
        // }
    },
    getMaterial: (data) => get("/register/getMaterial.do", data),
    // CMS获取素材（新接口）
    getHookMaterialInfo: (data) => get("/register/getHookMaterialInfo", data),
    // 获取7个随机头像
    getRandomAvatar: (data) => get("/register/getRandomAvatar.do", data),
    // H5钩子注册用户标记
    gudgeonMark: (data) => post('/register/advertisement/gudgeonMark.do', data),

    // 获取跳转小程序短链 seed版
    getWxLink: (data) => get(`/api/seed/zaq/msgapi/gen/urllink/zhenai_miniprogram`, data),

    // 获取跳转小程序短链 主站版
    getWxLinkV2: (data) => post(`/loveAnswerBook/getAppletUrlLink.do`, data),

    // 判断是否参与分流渠道
    getChannelConfig: (data) => get(`/register/getChannelConfig.do`, data),

    // 记录锦囊点击用户点击行为
    recordProposalClickBehavior: (data) => post(`/register/recordMBTIClickBehavior.do`, data),
    // 导量企微路径优化 二维码+分配
    getWxCode: (data) => get(`/loveAnswerBook/lastVersionQrCode.do`, data),
    // 手机前置发送验证码
    sendMessageCode: (data) => post('/api/advertisement/phoneFrontReg/sendMessageCode', data, {
        headers: {
            "Content-Type": "application/json;charset=UTF-8"
        }
    },),
    // 校验验证码并注册
    checkMessageCodeAndReg: (data) => post('/api/advertisement/phoneFrontReg/checkMessageCodeAndReg', data, {
        headers: {
            "Content-Type": "application/json;charset=UTF-8"
        }
    },),
    // 完善注册资料接口
    complementRegInfo: (data) => post('/api/advertisement/phoneFrontReg/complementRegInfo', data, {
        headers: {
            "Content-Type": "application/json;charset=UTF-8"
        }
    },),

    // 修改资料项
    updateProfileItem: (data) => post(`/api/advertisement/phoneFrontReg/updateProfile`, data, {
        headers: {
            "Content-Type": "application/json;charset=UTF-8"
        }
    },),

    // 跳转优恋空间小程序

    goYouLianMini: (data) => ajax({
        url: '/api/minigram/wechat/getAppletUrlLink',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/cupid/url/getAppletUrlLink`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
    // 恋爱人格非单身注册
    checkMessageCodeAndRegUnSingle: (data) => ajax({
        url: '/marriedRegister/checkMessageCodeAndReg',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/advertisement/marriedRegister/checkMessageCodeAndReg`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
    // 注册成功-大表单答题-小记接口
    hookAnswerNotes: (data) => ajax({
        url: '/api/common/registerPopSchemaConfig/hookAnswerNotes',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/common/registerPopSchemaConfig/hookAnswerNotes`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),

    // 单身判断是否跳转加企微接口
    singleShowWechatCode: (data) => ajax({
        url: '/api/advertisement/marriedRegister/singleShowWechatCode',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/advertisement/marriedRegister/singleShowWechatCode`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),

    // 获取注册链路配置
    getRegChainConfig: (data) => ajax({
        url: '/api/common/ad/account/report/getRegChainConfig',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/common/ad/account/report/getRegChainConfig`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),

    // 根据memberId获取token
    getValidToken: (data) => ajax({
        url: '/api/common/ad/account/report/getValidToken',
        type: 'GET',
        data,
        customUrl: `${domain()}/api/common/ad/account/report/getValidToken`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
    // 注册H5上报
    registeredH5CheckReport: (data) => ajax({
        url: '/api/common/ad/account/report/registeredH5CheckReport.do',
        type: 'POST',
        data,
        customUrl: `${domain()}/api/common/ad/account/report/registeredH5CheckReport.do`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
    // 幸福汇注册接口
    registerXfh: (data) => ajax({
        url: '/auth/register/xfh',
        type: 'POST',
        data,
        customUrl: `${xftDomain()}/api/user/auth/register/xfh`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
    // 幸福汇发送验证码
    sendXfhMessageCode: (data) => ajax({
        url: '/auth/sendVerificationCode',
        type: 'POST',
        data,
        customUrl: `${xftDomain()}/api/user/auth/sendVerificationCode`,
        opts: {
            headers: {
                "Content-Type": "application/json;charset=UTF-8"
            }
        },
    }),
};
