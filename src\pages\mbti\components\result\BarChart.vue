<template>
    <div class="chart">
        <div class="part">
            <ul class="part-top">
                <li
                    class="part-top-item"
                    v-for="item in topData"
                    :key="item.text"
                    :style="{ transform: scaleNum }"
                >
                    <span>{{ item.text + item.desc }}</span>
                    <p
                        :style="{
                            height: item.num * 10 + 'px'
                        }"
                    ></p>
                </li>
            </ul>
            <div class="line" :style="{ transform: scaleNum }"></div>
            <ul class="part-bottom">
                <li
                    class="part-bottom-item"
                    v-for="item in bottomData"
                    :key="item.text"
                    :style="{ transform: scaleNum }"
                >
                    <p
                        :style="{
                            height: item.num * 10 + 'px'
                        }"
                    ></p>
                    <span>{{ item.text + item.desc }}</span>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import { storage } from "../../lib/utils.js";
export default {
    name: "BarChart",
    data() {
        return {
            topData: [],
            bottomData: [],
            scaleNum: "scale(0)"
        };
    },
    created() {
        let answer = storage.getItem("answer");
        let result = [
            {
                E: (answer.filter(item => item.type === 'E')).length,
                I: (answer.filter(item => item.type === 'I')).length,
            },
            {
                N: (answer.filter(item => item.type === 'N')).length,
                S: (answer.filter(item => item.type === 'S')).length,
            },
            {
                F: (answer.filter(item => item.type === 'F')).length,
                T: (answer.filter(item => item.type === 'T')).length,
            },
            {
                J: (answer.filter(item => item.type === 'J')).length,
                P: (answer.filter(item => item.type === 'P')).length,
            }
        ];
        if (result) {
            const descText = [
                {
                    E: "外向",
                    I: "内向"
                },
                {
                    N: "直觉",
                    S: "感觉"
                },
                {
                    F: "情感",
                    T: "思维"
                },
                {
                    J: "判断",
                    P: "知觉"
                }
            ];
            result.forEach((k, index) => {
                const [a, b] = Object.keys(k);
                this.topData.push({
                    text: `${a}(${k[a]})`,
                    num: k[a],
                    desc: descText[index][a]
                });
                this.bottomData.push({
                    text: `${b}(${k[b]})`,
                    num: k[b],
                    desc: descText[index][b]
                });
            });
        }
    },
    mounted() {
        setTimeout(() => {
            this.scaleNum = "scale(1)";
        }, 500);
    }
};
</script>

<style lang="scss" scoped>
.chart {
    position: relative;
    margin-top: 32px;
    padding-bottom: 32px;
    border: 2px solid #00ffff;
    border-bottom: none;
    border-radius: 16px 16px 0 0;
    background: rgba(0, 255, 255, 0.1);
    box-shadow: inset 0 0 48px rgba(255, 255, 255, 0.5);
    overflow: hidden;
    &::after {
        content: "";
        position: absolute;
        left: 8px;
        right: 8px;
        bottom: 0;
        width: 690px;
        height: 2px;
        background: url(https://photo.zastatic.com/images/common-cms/it/20220425/1650881186222_985641_t.png) no-repeat;
        background-size: 100% 100%;
    }
}
.part {
    padding-top: 40px;
    padding-bottom: 20px;
    .part-top,
    .part-bottom {
        display: flex;
        color: #fff;
        font-size: 28px;
        > li {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 110px;
            margin-right: 36px;
            padding-left: 60px;
            transition: 1s ease-in;
            > p {
                width: 92px;
            }
            > span {
                white-space: nowrap;
            }
        }
    }
    .part-top {
        > li {
            justify-content: flex-end;
            transform: scale(0);
            transform-origin: center bottom;
            > p {
                margin-top: 8px;
                background: linear-gradient(0deg, #4a9cfb 0%, #f591f9 100%);
            }
        }
    }
    .part-bottom {
        > li {
            transform: scale(0);
            transform-origin: center top;
            > p {
                margin-bottom: 8px;
                background: linear-gradient(180deg, #4a9cfb 0%, #85f9b6 100%);
            }
        }
    }
    .line {
        position: relative;
        width: 642px;
        height: 2px;
        background: #00ffff;
        transition: 1s ease-in;
        transform: scale(0);
        transform-origin: left center;
        &::after {
            content: "";
            position: absolute;
            right: 0;
            top: -5px;
            width: 12px;
            height: 12px;
            background: #00ffff;
            border-radius: 50%;
        }
    }
}
</style>
