<template>
    <div id="app">
        <div :class="{top:true, hidden_mis: !isShowLeft}" class="nav" v-show="showNav">
            <span v-show="isShowLeft" @click="goback" class="panel-back">
                < 返回
            </span>
            <span v-show="isShowMid" class="panel-full">
                完善资料<br/>
                <span v-if="id && id > 0">({{id}}/7)</span>
            </span>
            <span v-show="isShowRight" @click="login" class="panel-login">
                登录
            </span>
        </div>
        <div class="nav-img" v-show="showNavImg" :class="id ? 'nav-img-register' : ''" :style="{backgroundImage:`url(${backImg})`}">
            <div class="message-box" v-if="!id">
                <p>乘风破浪，为爱<span class="sail">启航！</span></p>
                <p><span class="step">缘分第一步！</span>手机号验证！</p>
            </div>
            <div class="register-box">
                <p>{{id && infoList[id].title}}</p>
                <p v-if="id && infoList[id].title1">{{id && infoList[id].title1}}</p>

            </div>
        </div>
        <keep-alive exclude="['Register','Result']">
            <router-view
                :key="$route.fullPath"
                v-if="canRender"></router-view>
        </keep-alive>
    </div>
</template>

<script>
import { createRoot } from "@/common/framework";
import { judgeIfInToutiaoIos } from '@/common/business/utils/channel';
import Player from './components/Player.vue';
import { QUIZ_LIST_REGISTER } from './config';
export default createRoot({
    name: "App",
    data() {
        return {
            canRender: false,
            isShowLeft: false,
            isShowRight: false,
            isShowMid: true,
            showNavImg: true,
            showNav: true,
            backImg: '',
            name: '',
            to: null,
            id: this.$route.params.id,
            infoList: QUIZ_LIST_REGISTER,
        };
    },
    provide() {
        return {
            cmsConfig: this.cmsConfig
        };
    },
    created() {
        console.log(this.$route)
        this.routeChange(this.$route);
        this.$router.beforeEach((to, from, next) => {
            this.to = to;
            this.routeChange(to);
            next();
        });
    },
    mounted() {
        judgeIfInToutiaoIos();
        this.initAsync();
    },
    methods: {
        async initAsync() {
            // 处理异步
            this.canRender = true;
        },
        routeChange(to) {
            this.name = to.name;
            this.id = to.params.id
            switch(to.name){
            case 'Message':
                this.isShowLeft = false;
                this.isShowRight = false;
                this.isShowMid = false;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264115763_199295_t.jpg'
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'Register':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = true;
                this.backImg = 'https://photo.zastatic.com/images/common-cms/it/20240103/1704264041914_81726_t.jpg'
                this.showNavImg = true;
                this.showNav = true;
                break;
            case 'SuccessAnimation':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = true;
                break;
            case 'DownApp':
                this.isShowLeft = true;
                this.isShowRight = false;
                this.isShowMid = false;
                this.showNavImg = false;
                this.showNav = false;
                break;
            }
        },
        goback() {
            this.$router.back();
        },
        login() {
            location.href = 'https://i.zhenai.com/m/portal/login.html' + location.search
        },
        // preloadImg() {
        //     let image = new Image();
        //     image.src ="./assets/images/f.svga";
        // },
    }
});
</script>

<style lang="scss">
// 全局样式
html,body{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    width: 100%;
    height: 100%;
}
</style>
<style lang="scss" scoped>
.app{
    position: fixed;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

}
.top{
    height: 100px;
    font-size: 32px;
    width: 100%;
    background-image: -webkit-linear-gradient(left,#864dd4,#716bef);
    background-image: linear-gradient(90deg,#864dd4,#716bef);
    box-shadow: 0 0.06667rem 0.26667rem 0 rgba(131,82,216,.5);
    color: #fff;
    padding: 0 20px;
    display: flex;
    align-items: center;
    position: relative;
    .panel-back{

    }
    .panel-back-call{
        width: 34px;
        height: 54px;
    }
    .panel-full {
        position: absolute;
        left: 300px;
        text-align: center;
    }
    .panel-login {
        position: absolute;
        right: 20px;
    }
}
.nav-img {
    background-size: 100% 100%;
    width: 100%;
    height: 240px;
    line-height: 60px;
    padding-top: 56px;
    font-size: 38px;
    font-weight: 700;
    .message-box {
        padding-left: 40px;
        .sail {
            color: #ff6b6b;
            font-size: 46px;
            margin-left: 15px;
        }
        .step {
            color: #8b76f9;
        }
    }
    .register-box {
        color: #ffffff;
        text-align: center;
    }
    &.nav-img-register {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 0;
    }
}
.appresult{
    position: relative;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230811/1691745433928_1320_t.png');
    background-size: contain;
    background-repeat: no-repeat;
    width: 100vw;
    min-height: 2930px;
    background-color: red;
    background-color: #1e1441;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    align-items: center;
}

    .hidden_mis{
        /* justify-content: flex-end; */
        width: auto;
        right: 0;
        z-index: 100;
    }

    .music{
        width: 74px;
        height: 74px;
    }
</style>
