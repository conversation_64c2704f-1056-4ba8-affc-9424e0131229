<template>
    <van-popup
        class="mbti-test-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="mbti-test-modal__title">
            该手机已在<br>
            珍爱网APP注册过！
        </h1>

        <!--<div
            class="mbti-test-modal__btn mbti-test-modal__btn-continue"
            v-if="registerResult.MANUAL_OVERWRITE_ACCOUNT.value === type"
            @click="selectOverwrite"
        >
            <div>继续</div>
            <div>（原有帐号的资料将被覆盖）</div>
        </div>-->

        <div
            class="mbti-test-modal__btn"
            @click="selectOrigin"
        >
            登录原有帐号
        </div>

        <div
            class="mbti-test-modal__cancel"
            @click="closeModal"
        >
            取消
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { registerResult } from '../../enum';
import { mapState } from "vuex";

export default {
    name: 'RegisterOverwriteModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        type: {
            type: [Number, String],
            required: true,
        }
    },
    data() {
        return {
            registerResult: this.$z_.clone(registerResult),
        };
    },

    computed: {
        ...mapState([
            'resourceKey'
        ]),
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$reportKibana(this.resourceKey, 310, '已注册提醒弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        selectOverwrite() {
            this.$reportKibana(this.resourceKey, 312, '已注册提醒弹窗-覆盖注册按钮点击');
            this.$emit('select-overwrite');
        },
        selectOrigin() {
            this.$reportKibana(this.resourceKey, 311, '已注册提醒弹窗-登录按钮点击');
            this.$emit('select-origin');
        },
        closeModal() {
            this.$emit('select-origin');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.mbti-test-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px 48px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__btn {
        @include flex-center(column);
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        border-radius: 44px;
        margin-bottom: 28px;

        &-continue {
            padding-top: 3px;
            > div:last-child {
                font-size: 24px;
                color: #FFFFFF;
                line-height: 36px;
            }
        }
    }

    &__cancel {
        margin-top: 4px;
        font-size: 32px;
        line-height: 50px;
        color: #000000;
        text-align: center;
    }
}
</style>
