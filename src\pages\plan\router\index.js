import VueRouter from "vue-router";

import Index from "../views/Index.vue";
import Purpose from "../views/Purpose.vue";
import Hope from "../views/Hope.vue";
import Way from "../views/Way.vue";
import GuideToApp from "../views/GuideToApp.vue";
import GuideToActivity from "../views/GuideToActivity.vue";
import GuideToMatchmaker from "../views/GuideToMatchmaker.vue";
import Collection from "../views/Collection.vue";
import Result from "../views/Result.vue";

const router = new VueRouter({
    mode: "hash",
    routes: [
        {
            path: "/",
            redirect: "/index"
        },
        {
            path: "/index",
            component: Index
        },
        {
            path: "/purpose",
            component: Purpose
        },
        {
            path: "/hope",
            component: Hope
        },
        {
            path: "/way",
            component: Way
        },
        {
            path: "/guidetoapp",
            component: GuideToApp
        },
        {
            path: "/guidetoactivity",
            component: GuideToActivity
        },
        {
            path: "/guidetomatchmaker",
            component: GuideToMatchmaker
        },
        {
            path: "/collection",
            component: Collection
        },
        {
            path: "/result",
            component: Result
        }
    ]
});

export default router;
