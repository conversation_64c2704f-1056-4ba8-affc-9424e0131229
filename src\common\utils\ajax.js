const BASE_URL = `${location.protocol}//api.zhenai.com`;
const SEED_URL = location.host.indexOf('test') === -1 ? `${location.protocol}//seed-api.zhenai.com`:`${location.protocol}//seed-api-test.zhenai.com`;
export function domain() {
    const url = location.href
    if (url.includes('i.zhenai.com')) {
        return 'https://api.zajiebao.com'
    } else if (url.includes('i-pre.zhenai.com')) {
        return 'https://api-pre.zajiebao.com'
    } else {
        return 'https://api-test.zajiebao.com'
    }
}
const MATCHMAKER_URL = `${domain()}`;


let promise = null;
export const getDeviceSign = () => {
    if (promise) return promise;
    promise = new Promise(resolve => {
        window._fmOpt = {
            partner: 'zhenai',
            appName: 'appName',
            token: 'zhenai' + '-' + new Date().getTime() + '-' + Math.random().toString(16).substr(2),
            fpHost: 'https://secdfinger.zhenai.com',
            fmb: true,
            cub: true,
            success: function(data) {
            // 在成功完成采集后，success回调中可以获取到black_box
            // 这里可以设置客户需求的操作，传输black_box等操作
            // 获取设备指纹黑盒数据，并提交到业务服务器
            // 在传输过程中请对blackbox进行url编码
                resolve(data);
            }
        };
    });
    var fm = document.createElement('script'); fm.type = 'text/javascript'; fm.async = true;
    fm.src = window._fmOpt.fpHost + '/static/fm.js?ver=0.1&t=' + (new Date().getTime() / 3600000).toFixed(0);
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(fm, s);
    return promise;
};

export const ajax = async ({
    url,
    type="GET",
    data={},
    customUrl='',
    opts
}) => {
    // api地址
    let apiUrl = `${url.includes('seed/zaq') ? SEED_URL : url.includes('api/advertisement') ? MATCHMAKER_URL : BASE_URL}${url}`;

    // 设备指纹
    data.data = await getDeviceSign();
    // data.data = 'eyJ2IjoiOHNsZ0ZyTlMyZzJ2VHF6bm1vZVJBdz09Iiwib3MiOiJ3ZWIiLCJpdCI6MjYzLCJ0IjoiVTB5RExxLzF4d29jR25PR2FKVU5FZDQ5Uk5lVzdjUGVkMUJnVHFxaFNBNmZ2dnJiZFl4ZDFMVDRxekxwTlA2ZkdKRzhiTUJrekc1dHZjbWZibjNaMnc9PSJ9'

    // 如果不是api.zhenai.com域名下的
    if(customUrl){
        apiUrl = customUrl;
    }

    return new Promise((resolve,reject)=>{
        try{
            Z.ajax({
                type,
                url:apiUrl,
                data,
                opts
            },response=>{
                // 成功回调
                if(response.isError === true){
                    // 接口逻辑错误
                    resolve(response);
                } else {
                    // 成功
                    resolve(response);
                }
            },error=>{
                // 非逻辑错误
                try{
                    let err = error.originError;
                    let $toast = typeof Vue.prototype.$toast==='function' ? Vue.prototype.$toast : function (){};
                    if(err.response && err.response.status >= 500){
                        $toast('系统请求异常，请稍后再试');
                    }else if(/network/i.test(err.message)){
                        $toast('网络异常，请稍后再试');
                    }else if(/timeout/i.test(err.message)){
                        // 基础库axios 最多等待6秒
                        $toast('网络好像出问题了，请稍后试试');
                        // console.log(err.message); //"timeout of 6000ms exceeded"
                    }
                    else{
                        $toast(error.errorMessage);
                    }

                    // 封装一个网络异常的response对象, 抛到业务侧用于网络异常时引导用户刷新
                    resolve({
                        isError:true,
                        errorMessage:"当前网络异常",
                    });

                }catch(e){
                    console.log("ajax异常：",e);
                }
                reject(error);
            });
        }catch(error){
            reject(error);
        }
    });

};

// get请求封装
export const get = (url, data, opts) =>{
    return ajax({url, data, type:'get', opts});
};

// post请求封装
export const post = (url, data, opts) =>{
    return ajax({url, data, type:'post', opts});
};
