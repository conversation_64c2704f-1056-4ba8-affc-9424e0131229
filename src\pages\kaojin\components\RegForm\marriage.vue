<template>
    <div class="wrapper">
        <div
            v-for="(item,index) in list.options"
            :key="index"
            @click="goNext(item.key)"
            class="item"
            :class="curMarriage === item.key?'active':''"
        >
            <img src="https://photo.zastatic.com/images/common-cms/it/20240521/1716282359883_359550.png" alt="" class="active_img">
            {{ item.text }}
        </div>
        <img :src="bannerMap[gender]" alt="" class="banner">
    </div>
</template>
<script>
import { setLocalRegisterForm } from "../../utils/index";
import { storage as Storage } from "@/common/utils/storage";
import { PAGE_TYPE, changeProfile } from "../../config";
export default {
    name: "Marriage",
    props: {
        list: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            bannerMap: {
                0: 'https://photo.zastatic.com/images/common-cms/it/20240522/1716372870288_94065.png',
                1: 'https://photo.zastatic.com/images/common-cms/it/20240522/1716373508131_424436.png'
            },
            gender: 0,
            lock:false,
            curMarriage: Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`) && Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).marriage || ''
        };
    },
    activated () {
        this.gender = Storage.getItem(`cachedRegisterForm-${PAGE_TYPE}`).gender
    },
    mounted() {
    },
    methods: {
        goNext(val) {
            if(this.lock){
                return;
            }
            this.lock=true;

            this.curMarriage = val;
            const params = {
                key: "marriage",
                value: val,
                isMark: false,
            };
            this.$report(10, '婚况页-按钮点击',{
                ext28: val,
            });
            setLocalRegisterForm(params, PAGE_TYPE);
            // changeProfile({...params, token: oCookie.get('token')})
            setTimeout(()=>{
                this.lock = false;
                this.$emit('go-next');
            },300);
        }
    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 180px;
    .item{
        width: 550px;
        height: 90px;
        line-height: 90px;
        background: #F1F2F4;
        border-radius: 45px;
        font-size: 32px;
        color: #222833;
        text-align: center;
        margin-bottom: 32px;
        position: relative;
    }
    .active_img {
        position: absolute;
        right: 70px;
        top: 0;
        transform: translateY(-50%);
        width: 35px;
        height: 35px;
        display: none;
    }
    > .active{
        background-image: linear-gradient(90deg, #B2F8F4 0%, #CBFFEB 100%);
        border-radius: 45px;
        .active_img {
            display: block;
        }
    }
    .banner {
        width: 100vw;
        margin-top: 60px;
    }
}
</style>
