<template>
    <van-popup
        class="download-guide-modal"
        v-if="value"
        :value="value"
        @input="handleVisibleChange"
    >
        <h1 class="download-guide-modal__title">
            请前往各大应用市场搜索<br>“珍爱网”下载珍爱APP
        </h1>

        <p class="download-guide-modal__desc">
            Ta在珍爱APP等你，奇妙的缘分即将来临，赶紧去约会吧
        </p>

        <p
            class="download-guide-modal__lock"
            v-if="cmsConfig.reportViewType === 2"
        >
            解锁你的专业版MBTI测试报告
        </p>

        <div
            class="download-guide-modal__btn"
            @click="closeModal"
        >
            好的
        </div>
    </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { mapState } from "vuex";

export default {
    name: 'DownloadGuideModal',
    components: {
        VanPopup: Popup,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },

    computed: {
        ...mapState([
            'resourceKey',
            'cmsConfig'
        ]),
    },

    watch: {
        value: {
            handler(value) {
                if (value) {
                    this.$reportKibana(this.resourceKey, 400, '引导去市场的弹窗-访问');
                }
            },
            immediate: true,
        }
    },
    methods: {
        handleVisibleChange(value) {
            this.$emit('input', value);
        },
        closeModal() {
            this.$reportKibana(this.resourceKey, 401, '引导去市场的弹窗-按钮点击');
            this.$emit('input', false);
        }
    }
};
</script>

<style lang="scss">
@import '~@/common/styles/common.scss';

.download-guide-modal {
    width: 558px;
    background: #FFFFFF;
    border-radius: 32px;
    padding: 48px 32px;
    @include flex-center(column, null, center);

    &__title {
        font-weight: 500;
        line-height: 54px;
        font-size: 36px;
        color: #26273C;
        text-align: center;
        margin-bottom: 24px;
    }

    &__desc {
        width: 462px;
        font-size: 28px;
        font-weight: 400;
        color: #6C6D75;
        line-height: 42px;
        margin-bottom: 48px;
    }

    &__lock {
        color: #3BE0EF;
        font-size: 28px;
        margin-bottom: 16px;
    }

    &__btn {
        width: 462px;
        height: 88px;
        font-size: 32px;
        background: #000000;
        color: #ffffff;
        border-radius: 44px;
        @include flex-center();
    }
}
</style>
