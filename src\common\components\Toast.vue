﻿<template>
    <div
        class="f-text-center"
        :class="toastWrapperClass"
        :style="{ 'max-width': maxWidth, 'text-align':'center'}"
        v-if="toastVisible === true"
    >
        <div class="bd f-flex-box flex-inline align-center">
            <div class="icon">
                <span
                    v-if="toastIconClass.length > 0"
                    :class="toastIconClass"
                ></span>
            </div>
            <div
                class="content"
                :class="{ 'f-text-left': toastIcon!==0 }"
            >
                <div
                    v-for="(item,index) in toastArray"
                    :key="index"
                    class="word"
                >
                    {{ item }}
                </div>
            </div>
        </div>
    </div>  
</template>
<script>
let $thisScope = null;

const DEFAULT_toast = "你没有写文案就是个BUG";
const MAX_WIDTH = "80%";
const POSITION = 'center';

// 图标类型
export const IconType = {
    NONE: 0,
    OK: 1
};

let timer1 = null;
let timer2 = null;
let timer3 = null;
export default {
    name: "Toast",
    install(Vue) {
        Vue.component("Toast", this);
        Vue.prototype.$toast = (text, duration, icon, maxWidth, position, cb) => {
            $thisScope.toastVisible = true;

            // 参数为json格式
            if (
                Object.prototype.toString.call(text).toLowerCase() === "[object object]"
            ) {
                duration = text.duration;
                icon = text.icon;
                maxWidth = text.maxWidth;
                position = text.position;
                text = text.text;
            }

            $thisScope.maxWidth = maxWidth || MAX_WIDTH;
            $thisScope.toastIcon = icon || IconType.NONE;
            $thisScope.postion = position || POSITION;


            if ($thisScope.toastIcon !== IconType.NONE) {
                $thisScope.toastIconClass = `ti ti_${$thisScope.toastIcon} sprite-common icon-common-toast-ok`;
            } else {
                $thisScope.toastIconClass = ``;
            }

            if (text instanceof Array === true) {
                $thisScope.toastArray = text;
            } else {
                $thisScope.toastArray = [text] || [DEFAULT_toast];
            }

            timer1 && clearTimeout(timer1);
            timer1 = setTimeout(() => {
                $thisScope.toastWrapperClass = $thisScope.postion + " m-toast-wrapper in";
            }, 100);

            timer2 && clearTimeout(timer2);
            timer2 = setTimeout(() => {
                $thisScope.toastWrapperClass = $thisScope.postion + " m-toast-wrapper";

                timer3 && clearTimeout(timer3);
                timer3 = setTimeout(() => {
                    $thisScope.toastVisible = false;
                    cb && cb();
                }, 1000);
            }, duration + 100 || 2000);
        };
    },
    data() {
        $thisScope = this;

        return {
            toastWrapperClass: "m-toast-wrapper",
            toastArray: [DEFAULT_toast],
            toastIcon: IconType.NONE,
            toastIconClass: "",
            toastVisible: false,
            maxWidth: MAX_WIDTH,
            postion: POSITION,
        };
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.m-toast-wrapper {
  position: fixed;
  z-index: 99999;
  left: 50%;
  &.center{
    top: 50%;
  }
  &.top{
    top: 20%;
  }
  &.bottom{
    bottom: 20%;
  }
  transform: translate(-50%, -50%);
  transition: opacity 0.5s;
  opacity: 0;
  width: 80%;
  .bd {
    margin: 0 auto;
    min-height: 88px;
    padding: 31px 61px 28px 51px;
    background-color: #42475c;
    border-radius: 8px;
    box-shadow: 0px 5px 20px 0px rgba(106, 114, 148, 0.5);
    color: #fff;
    font-size: 30px;
  }
  .icon {
    .ti {
      display: inline-block;
      width: 36px;
      height: 36px;
      margin-right: 19px;
    }

    .ti.ti_1 {
      background-repeat: no-repeat;
    }
  }

  .content {
    .word {
      line-height: 42px;
    }
  }
}

.m-toast-wrapper.in {
  opacity: 0.949;
}
</style>


