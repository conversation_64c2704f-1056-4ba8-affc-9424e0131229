<template>
    <div id="app">
        <Keep-alive exclude="matchmakerDetail,matchmakerScore,lovePoint">
            <router-view />
        </Keep-alive>
    </div>
</template>

<script>

export default {
    components: {

    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {

    },
    created() {
    },
    methods: {
    },
};
</script>
<style scoped lang="scss">
    #app{
    }
</style>
<style>

</style>
