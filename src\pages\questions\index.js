import "@/common/bootstrap.js";
import App from './App.vue';
import router from './router/index.js';
import toast from '@/common/plugins/toast';
import loading from '@/common/plugins/loading';
import bus from './lib/common/bus.js';
import storage from './lib/common/storage.js';
import oUserSelect from "@/common/ocpx/huichuan.js";
import { reportKibana } from '@/common/utils/report.js';

Vue.prototype.$bus = bus;
Vue.prototype.$storage = storage;
Vue.prototype.$select = oUserSelect;
Vue.prototype.$reportKibana = reportKibana;

Vue.use(toast);
Vue.use(loading);


new Vue({
    el: '#app',
    router,
    template: '<App/>',
    components: {
        App
    }
});
