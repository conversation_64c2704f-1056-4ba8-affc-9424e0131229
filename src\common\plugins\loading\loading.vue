<template>
    <section id="c-loading" class="c-loading" v-if="isLoading">
        <div class="mask" @touchmove.prevent></div>
        <div class="g-loading-box">
            <div class="text">{{loadingText}}</div>
        </div>
    </section>
</template>

<script>
    export default {
        name: 'loading',
        data() {
            return {
                isLoading: false,
                // 延迟多少时间后展示loading
                // 有些接口时间响应时间很短，没必要马上就加上loading
                delay: 0,
                loadingText: ''
            }
        }
    }
</script>

<style lang="scss">
    .c-loading {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        .mask {
            width: 100%;
            height: 100%;
        }

        .g-loading-box {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: flex-end;
            width: 208px;
            height: 208px;
            border-radius: 12px ;
            z-index: 20;
            background: url('../../../common/ico/loading.gif') no-repeat;
            background-size: 100% 100%;
            opacity: 0.75;
            .text {
                margin-bottom: 50px;
                font-size:26px;
                color:rgba(255,255,255,1);
                line-height:25px;
                text-align: center;
            }
        }
    }
</style>



