<template>
    <div class="collection-wrapper">
        <img class="bg-img" src="https://photo.zastatic.com/images/common-cms/it/20240219/1708308448577_675863_t.png" alt="">
        <img @click="goMin" class="button" src="https://photo.zastatic.com/images/common-cms/it/20240219/1708308576214_557719_t.png" alt="">
    </div>
</template>

<script>

import {reportKibana} from '@/common/utils/report.js';
import Api from "@/common/server/base";
import { channelId, subChannelId } from '@/common/js/const';
export default {
    data() {
        return {

        };
    },
    async created(){},
    mounted(){
        // 打桩
        reportKibana("线下大表单", 111, '跳转小程序uv', {});
    },
    methods:{
        async goMin () {
            reportKibana("线下大表单", 111, '点击跳转小程序按钮', {});
            const params = {
                path: 'pages/main/meet/index',
                query: `channelId=${channelId}&subChannelId=${subChannelId}`,
            };
            let resData = await Api.goYouLianMini(params);
            if (resData.code === 0) {
                reportKibana("线下大表单", 111, '跳转小程序接口成功', {});
                location.href = resData.data
            } else {
                reportKibana("线下大表单", 111, '跳转小程序接口失败', {});
            }
        }
    }
};
</script>

<style lang="scss" scoped>

@keyframes scaleDraw {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
.collection-wrapper{
    width: 750px;
    background-color: #ffb6d6;
    position: relative;
    .bg-img {
        width: 100%;
    }
    .button {
        position: fixed;
        left: 10%;
        bottom: 50px;
        width: 80%;
        animation-name: scaleDraw;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
        animation-duration: 2s;
    }
}

</style>
