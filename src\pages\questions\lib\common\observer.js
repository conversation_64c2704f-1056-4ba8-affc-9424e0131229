let observer = {};

(function (o) {
    let obj = {},
        id = -1,
        emitObj = {}; // 若on的监听还没加载到，emit的发布存放

    o.on = function (name, fn) {
        if (!obj[name]) {
            obj[name] = [];
        }

        let token = (++id).toString();

        obj[name].push({
            token: token,
            fn: fn,
        });

        // 检查emitObj中是否有name
        if (emitObj[name]) {
            let tempArr = obj[name],
                len = tempArr ? tempArr.length : 0;

            while (len--) {
                tempArr[len].fn(emitObj[name]);
            }

            delete emitObj[name];
        }

        return token;
    };

    o.emit = function (name, param) {
        let i = 0;

        if (obj[name]) {
            let tempArr = obj[name],
                len = tempArr ? tempArr.length : 0;

            while (len--) {
                tempArr[len].fn(param);
            }
        } else {
            emitObj[name] = param;
            console.error(`组件${name}还未加载成功`);
        }
    };

    o.cancel = function (token) {
        for (let name in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, name)) {
                obj[name].forEach(function (item, index) {
                    if (item.token === token) {
                        obj[name].splice(index, 1);
                        // 改用删除某个属性
                        delete obj[name];
                        return token;
                    }
                });
            }
        }
    };
})(observer);

export default observer;
