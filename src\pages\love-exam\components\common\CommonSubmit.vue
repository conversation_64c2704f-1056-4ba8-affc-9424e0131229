<template>
    <div
        class="common-submit"
    >
        <phone
            ref="refPhone"
            class="common-submit-phone"
            @submit="handleSubmit"
        />
        <div
            @click="handleSubmit"
            class="common-submit__button"
        >
            <slot></slot>
        </div>

        <common-protocol
            class="common-submit__protocol"
            v-if="isNeedProtocol"
            :is-checked.sync="isCheckProtocol"
            :agreement-status="agreementStatus"
            :style-config="styleConfig.protocolConfig"
        />

        <!-- 虎扑合规要求 -->
        <div
            v-if="isHupu"
            class="common-submit__app-info"
        >
            开发者：深圳市珍爱网信息技术有限公司
            <br>
            应用名称：珍爱
            | 应用版本：7.30.0
        </div>

        <!-- 协议弹窗 -->
        <common-protocol-modal
            v-model="modalVisible.protocolModal"
            @confirm="handleConfirmProtocol"
            :page-type="pageType"
            :style-config="styleConfig.modalConfig"
        />

        <!-- 短信验证码弹窗 -->
        <common-msg-code-modal
            v-model="modalVisible.msgCodeModal"
            :page-type="pageType"
            :validate-code="handleValidateCode"
            :style-config="styleConfig.modalConfig"
        />
    </div>
</template>

<script>
import CommonProtocol from './CommonProtocol.vue';
import CommonProtocolModal from './CommonProtocolModal.vue';
import CommonMsgCodeModal from './CommonMgsCodeModal.vue';
import CommonRegisterOverwriteModal from '@/common/business/components/CommonRegisterOverwriteModal.vue';
import { Toast } from "vant";

import z_ from '@/common/zdash';
import {checkMsgCodeAndExamResult, regFailGetExamResult} from '../../api';
import Prototype from "@/common/framework/prototype";
import oUserSelect from '@/common/ocpx/huichuan';
import { storage as Storage, session as Session } from "@/common/utils/storage";

import { reportError, reportKibana, reportLoveKibana, reportLoveKibana1 } from "@/common/utils/report";
import pick from 'lodash-es/pick';
import { registeredH5CheckReport } from '@/pages/kaojin/api';
import Api from '@/common/server/base';
import { pageTypeChnMap, registerResult } from "@/common/config/register-dictionary.js";
import Phone from '../RegForm/phone.vue';

export default {
    name: 'CommonSubmit',
    components: {
        CommonProtocol,
        CommonProtocolModal,
        CommonMsgCodeModal,
        Phone,
    },
    props: {
        // 勾子类型
        pageType: {
            type: String,
            default: '导量H5大表单翻牌'
        },
        agreementStatus: {
            type: Number,
            default: 1
        },
        // 是否需要协议
        isNeedProtocol: {
            type: Boolean,
            default: true
        },
        styleConfig: {
            type: Object,
            default: {
                modalConfig: {
                    confirmButtonColor: '#ffffff',
                    confirmButtonBgColor: '#000000',
                    cancleButtonColor: '#000000',
                },
                protocolConfig: {
                    textColor: "#FFFFFF",
                    protocolColor: "#767DFF",
                    protocolCheckedUrl: 'https://photo.zastatic.com/images/common-cms/it/20220512/1652336753832_292583_t.png'
                }
            }
        },
        // 注册成功之后的回调
        handleAfterRegisiter: {
            type: Function,
            default: () => {}
        },

        handleLogin: {
            type: Function,
            default: () => {}
        },

        handleCancle: {
            type: Function,
            default: () => {}
        },
        //注册弹窗
        textConfirm: {
            type: Object,
            default: {
                overwirteText: null,
            }
        }
    },
    mounted() {
        this.$watch('agreementStatus', (value) => {
            this.isCheckProtocol = value === 0;
        }, {
            immediate: true
        });
    },
    data() {
        return {
            // 控制弹窗是否展示
            modalVisible: {
                protocolModal: false,
                registerOverwriteModal: false,
                msgCodeModal: false
            },
            isCheckProtocol: false,
            // 校验账号结果
            validateAccountResult: {},
            // 防止多次触发
            lockSubmit: false,
            lockOverwrite: false,
            registerForm:{
                gender: "",
                workCity: "",
                birthday: "",
                education: "",
                marriage: "",
                salary: "",
                phone: ""
            },
        };
    },
    created () {
        reportLoveKibana1(8029, 50, '进入手机号输入页', {
            ext27: localStorage.getItem('AD_URL'),
            ext29: 8029,
        });
    },
    computed: {
        isHupu() {
            return [ "924829", "924830" ].includes(Z.getParam("channelId"));
        },
    },
    methods: {
        // 确认协议
        handleConfirmProtocol() {
            this.isCheckProtocol = true;
            this.modalVisible.protocolModal = false;
            this.handleSubmit(false);
        },

        // reg_id存session，原因是ocpx从session里取的值是reg_memberid
        handleSetRegMemberId(id) {
            Session.setItem('reg_memberid', id);
            oUserSelect.mark({
                msgValid: true
            });
        },

        handleAllFillIn() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            return z_.every(registerForm , value => {
                return !z_.isNil(value) && value !== "";
            });
        },

        handleValidatedPhone() {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            // 由于用了3-4-4结构 所以还要把空格换掉
            return /^1\d{10}/.test(
                registerForm.phone.replace(/[^(\d)]/g, "")
            );
        },


        // 处理提交基本信息
        async handleSubmit(isReport = true) {


            if (this.lockSubmit) {
                return;
            }

            // if (!this.handleAllFillIn()) {
            //     Toast('请完善资料再提交');
            //     return;
            // }

            if (!this.handleValidatedPhone()) {
                Toast('请输入正确的手机号');
                return;
            }

            if (this.isNeedProtocol && !this.isCheckProtocol) {
                this.modalVisible.protocolModal = true;
                return;
            }


            this.modalVisible.msgCodeModal = true;

            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;
            if (registerForm.isSingle) {
                const sendData = {
                    ...pick(registerForm, [ 'phone', 'birthday', 'education', 'gender', 'marriage','salary', 'workCity']),
                    ua: Z.getUA(),
                    birthday: new Date(registerForm.birthday).getTime(),
                    pageType: pageTypeChnMap[this.pageType]
                };
                this.lockSubmit = true;

                try {
                    const baseInfoPostResult = await Api.submitWapRegBaseInfo(sendData);

                    if (baseInfoPostResult.isError) {
                        this.$toast(baseInfoPostResult.errorMessage);
                        return;
                    }

                    this.modalVisible.msgCodeModal = true;

                } finally {
                    this.lockSubmit = false;
                }
            }


        },
        handleValidateCode(messageCode) {
            const isSingle =Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ).isSingle;
            if (isSingle) {
                this.handleValidateCodeSingle(messageCode);
            } else {
                this.handleValidateCodeNoSingle(messageCode);

            }
        },

        // 处理校验验证码相关
        async handleValidateCodeSingle(messageCode) {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;

            Prototype.$gather.setAbtZaTTTCookie();

            // const phone = registerForm.phone.replace(/[^(\d)]/g, "");
            const regForm = {
                ...pick(registerForm, [ 'phone', 'birthday', 'education', 'gender', 'marriage','salary', 'workCity']),
                ua: Z.getUA(),
                birthday: new Date(registerForm.birthday).getTime(),
                messageCode,
                pageType: pageTypeChnMap[this.pageType]
            };

            const registerResult = await Api.submitWapRegNoPasswordInfoV2(regForm);

            if (!registerResult.isError) {
                // 注册成功
                reportLoveKibana1(8029, 51, '注册成功', {
                    ext17: registerResult && JSON.stringify(registerResult),
                    ext27: localStorage.getItem('AD_URL'),
                    ext28: registerResult && registerResult.data && registerResult.data.memberID,
                    ext29: 8029,
                });
                sessionStorage.setItem('reg_memberid', registerResult && registerResult.data && registerResult.data.memberID)
                this.handleFailedRegister(regForm);
                const reportData = {
                    ...regForm,
                    memberID: registerResult.data.memberID,
                    clickId: localStorage.getItem('AD_URL'),
                    year: registerForm.birthday.slice(0, 4),
                    submitPhone: true,
                    msgValid: !!registerResult.data.memberID,
                }
                registeredH5CheckReport(reportData);
                reportLoveKibana1(8029, 55, '上报成功', {
                    ext17: JSON.stringify(reportData),
                    ext27: localStorage.getItem('AD_URL'),
                    ext28: registerResult && registerResult.data && registerResult.data.memberID,
                    ext29: 8029,
                });

            } else {
                this.$toast(registerResult.errorMessage);
                reportLoveKibana1(8029, 52, '注册失败', {
                    ext17: JSON.stringify(registerResult),
                    ext27: localStorage.getItem('AD_URL'),
                    ext28: registerResult.memberId,
                    ext29: 8029,
                });
            }

            return registerResult;
        },
        // 处理校验验证码相关
        async handleValidateCodeNoSingle(messageCode) {
            const registerForm = Storage.getItem(
                `cachedRegisterForm-${this.pageType}`
            ) || this.registerForm;

            Prototype.$gather.setAbtZaTTTCookie();

            // const sendData = Prototype.$gather.getValidateCodeParams(registerForm.phone.replace(/[^(\d)]/g, ""), messageCode, pageTypeChnMap[this.pageType]);
            const phone = registerForm.phone.replace(/[^(\d)]/g, "");

            // 注册成功
            const sendParam = {
                phone: phone,
                messageCode,
                gender: registerForm.gender,
                clickId: localStorage.getItem('AD_URL'),
            };

            const result = await checkMsgCodeAndExamResult(sendParam);
            if (result.code === 0) {

                this.handleFinishedRegister(result.data.examResultPic);

            } else {
                this.$toast(result.msg);
            }

            return result;
        },




        handleFinishedRegister(img_url) {

            this.handleAfterRegisiter(img_url);
        },
        async handleFailedRegister(regForm) {
            const res = await regFailGetExamResult(regForm);
            if (res.code ===0) {
                this.handleAfterRegisiter(res.data.examResultPic);

            } else {
                this.$toast(res.msg);
            }
        },

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';


.common-submit {
    &-phone{
      margin-top: 85px;
    }
    &__button {
        @include flex-center(row, center, center);
    }
    &__protocol {
        margin-top: 49px;
    }
    &__app-info {
        color: #FFFFFF;
        opacity: 0.6;
        font-size: 22px;
        line-height: 40px;
        padding: 40px 39px 0;
        text-align: center;
    }
}
</style>
