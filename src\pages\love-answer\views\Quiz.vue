<template>
    <div style="display:flex">
        <!-- 覆盖首页面 -->
        <div class="overhidden" v-show="showBtn">
            <div
                class="bgc_img"
            >
                <!-- 按钮 -->
                <img src="https://photo.zastatic.com/images/common-cms/it/20230814/1692006101315_135432_t.png" alt="" @click="goNextone" :class="{btn:true}">
            </div>
        </div>
        <!-- 动画效果 -->
        <div
            id="ilove"
            :class="{ilove:true,iloveActive:showBtn?true:false}"
        >
        </div>
        <!-- 题目 -->
        <div :class="{iloveActive:!showBtn && list?false:true}">
            <div :class="{top:true}" v-if="currentIndex > 0">
                <img
                    class="panel-back"
                    src="https://photo.zastatic.com/images/common-cms/it/20230812/1691829216534_796934_t.png"
                    @click="goBack"
                ></img>
            </div>

            <div class="bg_pan" :style="reat">
                <div class="panel_div">
                    <p>完成测试即可解锁专属报告</p>
                    <div class="flex_box">
                        <div
                            ref="refProgress"
                            class="panel-progress"
                        >
                            <div
                                class="panel-progress-bar"
                                :style="{width: `${curProgress}px`}"
                            ></div>
                        </div>
                        <span style="width:40px">{{ currentIndex + 1 }}/{{ quizList.length }}</span>
                    </div>
                </div>
                <div class="panel-title">
                    {{ currentIndex + 1 }}、{{ curQuiz.title }}
                </div>

                <!-- 动态组件 -->
                <component
                    is="QuizItem"
                    :list="curQuiz"
                    :currentIndex="currentIndex"
                    :length="quizList.length"
                    @go-next="goNext"
                />
            </div>
        </div>
    </div>
</template>

<script>
import QuizItem from '../components/QuizItem.vue';
import { answerResultId } from '../api';
import { storage as Storage,session as Session } from "@/common/utils/storage";
import { getList } from '../api';
import { uuidv4 } from '../utils/index';
export default {
    name:"Quiz",
    components:{
        QuizItem,
    },
    data(){
        return {
            quizList: [],
            currentIndex: 0,
            rendered: false,
            showAnimation: true,
            player: null,
            parser: null,
            showBtn: true, //按钮是否展示
            top: 0,
            left: 0,
            bottom: 0,
            list: false,
            timeStart: null,

        };
    },
    computed:{
        curQuiz(){
            if(this.quizList && this.quizList.length > 0){
                return this.quizList[this.currentIndex];
            }
            return {};

        },
        curProgress(){

            if(this.rendered){
                const fullLength = this.$refs.refProgress.offsetWidth;
                return (this.currentIndex+1)/this.quizList.length * fullLength;
            }
            return 0;
        },
        reat() {
            return {top:this.top+50+'px',left:this.left+46+'px'};
        },
        bottomReat() {
            return {bottom:this.bottom+'px'};
        }
    },
    watch:{
    },
    created() {
        this.timeStart = new Date().getTime();
        //第一页 首次 播放动画
        this.getList();
        this.$report(1,'首页访问', {ext1: location.href});
        this.$report(3000,'首页访问（监控）');
    },
    mounted(){
        // 加载动画
        this.setSVGA();
        //第一页禁用返回物理键
        history.pushState(null, null, document.URL);
        window.addEventListener('popstate', this.disableBrowserBack);
    },
    activated(){

        if(!this.showBtn){
            this.$report(this.currentIndex + 3,`题目${this.currentIndex + 1}页访问`);
        }

    },

    methods:{
        async goNext(){
            //如果是最后一道题 跳转到是否单身页面
            if(this.quizList.length  === (this.currentIndex + 1) ){
                //提交测评获取结果id
                let result = await this.getAnswerResultId();
                if(result && result.code === 0){
                    //结果Id先保存
                    Storage.setItem('resultTypeData',result.data.evaluateResult);
                    this.$router.push({name:'TestResult'});
                    //销毁监听
                    window.removeEventListener("popstate", this.disableBrowserBack, false);

                }
                return;
            }
            if(this.currentIndex === 0 && this.timeStart){
                let time = ( new Date().getTime() - this.timeStart)/1000;
                this.$report(45,`首页停留时长`,{ext17:time});
                this.timeStart = null;

            }
            this.currentIndex++;
            this.$report(this.currentIndex + 3,`题目${this.currentIndex + 1}页访问`);
        },
        goBack(){
            this.currentIndex --;
            this.$report(this.currentIndex + 3,`题目${this.currentIndex + 1}页访问`);
        },
        getAnswerResultId () {
            return new Promise((resolve,reject)=>{
                let form ={
                    uuid: Storage.getItem('uuid'),
                    evaluateType: 1,
                    ansOptions: Storage.getItem('sessionAnswerList')
                };
                answerResultId(form).then((res)=>{
                    resolve(res);
                }).catch((err)=>{
                    reject(err);
                });
            });

        },
        disableBrowserBack() {
            history.pushState(null, null, document.URL);
        },
        //获取题目
        getList() {
            //用户唯一标识storage.getItem('uuid')||
            let uuid = Storage.getItem('uuid')||uuidv4();
            let form = {
                type: 1,
                uuid
            };
            //用户唯一标识
            Storage.setItem('uuid',uuid);
            getList(form).then((res)=>{
                if(res.code === 0){
                    Storage.setItem('questionList',JSON.stringify(res.data.questionList));
                    //获取答题
                    let questionList = JSON.parse(localStorage.getItem('questionList'));
                    this.quizList = JSON.parse(questionList);
                }
            });
        },
        goNextone() {
            if(!this.canload) return;
            this.showBtn = false;
            //开始播放
            this.player.startAnimation();
            this.$report(2,'首页-按钮点击');
            //进度条开始渲染
            this.rendered = true;
            // 点击开始测试 隐藏按钮
            setTimeout(()=>{
                this.list = true;
                this.$report(this.currentIndex + 3,`题目${this.currentIndex + 1}页访问`);

            },1000);

        },
        setSVGA() {
            this.player = new SVGA.Player('#ilove'),
            this.parser = new SVGA.Parser('#ilove');

            this.parser.load(
                require("../assets/images/active.svga"),
                videoItem => {
                    this.player.loops = 1;
                    this.player.clearsAfterStop= false;
                    this.player.setVideoItem(videoItem);
                    let el = document.querySelector('canvas');
                    this.getElemDis(el);
                    this.canload = true;
                }
            );
        },
        //获取canvas的top left
        getElemDis(el) {
            var tp = document.documentElement.clientTop,
                lt = document.documentElement.clientLeft,
                rect = el.getBoundingClientRect();
            // this.eleRect = {
            //     top: rect.top - tp,
            //     right: rect.right - lt,
            //     bottom: rect.bottom - tp,
            //     left: rect.left - lt
            // };
            this.top=rect.top - tp;
            this.left = rect.left - lt;
            this.bottom = (rect.bottom - rect.height + 120);

        }

    }
};
</script>

<style lang="scss" scoped>
@import '~@/common/styles/common.scss';
.bgc_img{
    width: 720px;
    height: 1170px;
    background-image: url('https://photo.zastatic.com/images/common-cms/it/20230817/1692267292063_639448.png');
    background-repeat: no-repeat;
    margin: 0;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    justify-content: center;
}
.panel_div{
    margin-top: 136px;
    display: flex;
    flex-direction: column;
    align-items: center;
    p{
        font-family: 'love';
        height: 48px;
        font-size: 36px;
        text-align: center;
        color: #8A54F8;
        letter-spacing: 2px;
        margin-bottom: 10px;

    }
}

.panel-progress{
    height: 24px;
    border-radius: 20px;
    width: 460px;
    border:1px solid #8a54f8;
    display: flex;
    margin-right: 11px;
    box-sizing: border-box;


    .panel-progress-bar{
        // height: 24px;
        background: #8a54f8;
        border-radius: 20px;

    }
}

.panel-title{
    margin-top: 60px;
    // font-weight: 600;
    line-height: 1.2;
    font-family: 'love';
    font-size: 33px;
    text-align: left;
    color: #251A34;
    letter-spacing: 4px;
    margin-bottom: 14px;
    width: 70vw;
}
.overhidden{
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}



.top{
    position: fixed;
    top: 0px;
    width: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 34px 25px 0 25px;
    // z-index: 100;
}
.panel-back{
    width: 147px;
    height: 56px;

}
.music{
    width: 74px;
    height: 74px;
}
.flex_box{
    display: flex;
    font-weight: Oblique;
        height: 27px;
        font-size: 20px;
        text-align: left;
        color: #8A54F8 ;
        letter-spacing: 4px;
}
.bg_pan{
    position: absolute;
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
}
</style>
<style lang="scss" scoped>
.ilove{
    position: absolute;
    width: 100vw;
    height: 100vh;
    // z-index: 1;

}
.iloveActive{
   opacity: 0;

}

.btn{
    position: absolute;
    width: 490px;
    height: 192px;
    bottom: 100px;
    animation: free_download 0.4s linear alternate infinite;
    z-index: 99;
}
@-webkit-keyframes free_download{
    0%{-webkit-transform:scale(1);}
    100%{-webkit-transform:scale(1.05);}
}
@keyframes free_download{
    0%{transform:scale(1);}
    100%{transform:scale(1.05);}
}



</style>
<style>
@font-face {
    font-family: 'love';
    src: url('../assets/love.ttf');
    font-style: normal;
    font-weight: normal;
}
</style>
