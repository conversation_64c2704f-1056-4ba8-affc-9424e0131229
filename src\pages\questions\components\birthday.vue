<template>
    <div class="birthday" id="birthday">
        <div class="birthday_card" v-for="(arr, key) in ageDict" :key="key">
            <h4 class="birthday_card_txt">
                {{ key + "后" }}
            </h4>
            <div class="birthday_card_info">
                <div class="box" v-for="(arr2, i) in arr" :key="i">
                    <div
                        v-for="(item, index) in arr2"
                        :key="index"
                        class="box_item"
                        :class="
                            item.disable
                                ? 'disabled'
                                : selected === item.key
                                ? 'selected'
                                : ''
                        "
                        @click="goNext(item)"
                    >
                        {{ item.key }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ageDict } from "../lib/common/dict";
export default {
    name: "Birthday",
    data() {
        return {
            ageDict: ageDict,
            selected: -1
        };
    },
    created() {
        this.$reportKibana(
            "h5-test-recommend", // 资源标识
            4, // 记录点
            "注册-年龄曝光", // 点描述
            {
                ext1: Z.getParam("materialId")
            }
        );
    },
    mounted() {
        this.$nextTick(() => {
            const cardHeight = document.querySelector(".birthday_card")
                .offsetHeight;
            document.querySelector("#birthday").scrollTop = cardHeight * 6;
        });
    },
    activated() {
        const cardHeight = document.querySelector(".birthday_card")
            .offsetHeight;
        document.querySelector("#quesItem").scrollTop = cardHeight * 6;
    },
    methods: {
        goNext(option) {
            if (option.disable) {
                return;
            }
            this.selected = option.key;
            this.$select.mark({
                year: "" + option.key,
                month: "1",
                day: "1"
            });
            const birthday = +new Date(option.key.toString());
            this.$storage.saveToStorage("__regInfo__", "birthday", birthday);
            setTimeout(() => {
                this.$router.push({
                    path: `/ques/${Number(this.$route.params.id) + 1}`
                });
            }, 200);
        }
    }
};
</script>

<style lang="scss" scoped>
.birthday {
    padding-top: 28px;
    padding-bottom: 40px;
    &_card {
        margin: 0 48px;
        &_txt {
            padding-left: 26px;
            padding-bottom: 24px;
            color: #fff;
            font-size: 36px;
            font-weight: 500;
            text-align: left;
        }
        &_info {
            margin-bottom: 46px;
            padding: 32px;
            color: #26273c;
            font-size: 32px;
            background: #ffffff;
            border-radius: 60px;
            opacity: 0.9;
        }
        .box {
            display: flex;
            margin-bottom: 52px;
        }
        .box_item {
            padding-right: 52px;
            &.disabled {
                opacity: 0.5;
            }
            &.selected {
                color: #8c7afe;
            }
        }
    }
}

.birthday_card_info .box:last-child {
    margin-bottom: 0;
}
</style>
