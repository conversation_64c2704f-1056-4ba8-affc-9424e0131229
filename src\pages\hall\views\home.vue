<template>
    <love-page>
        <div class="home">
            <!-- 背景，使用image方便自适应高度 -->
            <img class="home__bg" :src="cmsConfig.homeBackgroundImg" />

            <!-- 主按钮：可配置颜色和内容 -->
            <div
                class="home__button"
                :style="{ background: cmsConfig.homeButtonColor }"
                @click="start"
            >
                <z-image
                    class="home__button__logo"
                    :width="62"
                    :height="56"
                    :src="require('../assets/images/home-heart.png')"
                />
                {{ cmsConfig.homeButtonText }}
            </div>
        </div>
    </love-page>
</template>

<script>
import { mapState } from "vuex";
import { createPage } from "@/common/framework";

export default createPage({
    name: "Home",
    visitReport: {
        accessPoint: 1,
        accessPointDesc: "首页访问"
    },
    computed: {
        ...mapState(["cmsConfig"])
    },
    methods: {
        start() {
            this.$router.push({
                path: "/questions/0"
            });
        }
    }
});
</script>

<style scoped lang="scss">
@import "~@/common/styles/common.scss";

.home {
    &__bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100vw;
    }

    &__button {
        position: absolute;
        top: 1212px;
        left: 50%;
        transform: translateX(-50%);
        width: 566px;
        height: 112px;
        font-size: 40px;
        font-weight: 700;
        color: #000;
        text-align: center;
        line-height: 112px;
        border-radius: 77px;

        &__logo {
            position: absolute;
            top: -20px;
            left: -5px;
        }
    }
}
</style>
