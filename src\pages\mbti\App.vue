<template>
    <div
        id="app"
        @click="handleReTest"
        v-show="isShowPage"
    >
        <template v-if="cmsConfig.reportViewType === 4">
            <div class="bgs"></div>
        </template>
        <template v-else>
            <div
                class="bg"
                :style="{
                    backgroundImage: `url(${this.cmsConfig.homeBackgroundImg ||
                        bgUrl})`
                }"
            >
                <div
                    class="meteor"
                    id="svgaMeteor"
                ></div>
            </div>
        </template>

        <div class="nav">
            <div
                class="back"
                :class="{
                    'back-hide': ['/index', '/result'].includes($route.path)
                }"
                @click="goBack"
            ></div>
            <Player
                :musicUrl="
                    cmsConfig.bgmURL ||
                        'https://zaw-10009734.cos.ap-beijing-1.myqcloud.com/images/common-cms/it/20220117/1642411718423_314813.mp3'
                "
            />
        </div>
        <keep-alive>
            <router-view :key="$route.fullPath" />
        </keep-alive>
    </div>
</template>

<script>
import "@/common/styles/reset.scss";
import fixedClickDelay from "@/common/utils/fixed_click_delay.js";
import Player from "./components/Player.vue";
import { mapState, mapMutations, mapActions } from "vuex";

fixedClickDelay();

export default {
    name: "App",
    components: {
        Player
    },
    data() {
        return {
            bgUrl:
                "https://photo.zastatic.com/images/common-cms/it/20220427/1651060222384_289046_t.jpg",
            isShowPage: false
        };
    },
    created() {
        // 【老注册页】用于后台自然流量注册统计
        (function setAbtParams() {
            var abtParams = Z.cookie.get("abt_params");

            try {
                var arr = abtParams.split("|");
                if (arr.length !== 5) {
                    abtParams = "";
                }
            } catch (e) {}

            if (!abtParams) {
                var pageKey = Z.getParam("pageKey"), // 根据不同业务key写入
                    planId = 0,
                    schemeId = 0;
                var channelId = Z.getParam("channelId") || 0;
                var subChannelId = Z.getParam("subChannelId") || 0;
                var tmp = [pageKey, channelId, subChannelId, planId, schemeId];
                Z.cookie.set(
                    "abt_params",
                    tmp.join("|"),
                    ".zhenai.com",
                    "/",
                    24 * 1
                );
            }
        })();

        this.initRegisterForm();
        this.initCmsConfig();
        this.initFormItems();
    },
    mounted() {
        if (this.cmsConfig.reportViewType !== 4) {
            this.setSVGA();
        }
    },
    computed: {
        ...mapState(["cmsConfig", "resourceKey"]),
        isShareResult() {
            return Z.getParam("from") === "share" && Z.getParam("type");
        }
    },
    watch: {
        resourceKey() {
            this.isShowPage = true;
        }
    },
    methods: {
        ...mapMutations(["initFormItems", "initRegisterForm"]),
        ...mapActions(["initCmsConfig"]),

        goBack() {
            this.$router.back();
        },
        async setSVGA() {
            let player = new SVGA.Player("#svgaMeteor"),
                parser = new SVGA.Parser("#svgaMeteor");

            parser.load(require("./assets/images/meteor.svga"), videoItem => {
                player.setVideoItem(videoItem);
                player.loops = 0;
                player.startAnimation();
            });
        },

        handleReTest() {
            // 如果当前的页面是分享结果页，则点击屏幕任何区域会触发重新测试流程
            if (this.isShareResult) {
                this.$reportKibana(
                    this.resourceKey,
                    111,
                    "分享页-按钮点击（点击进入MBTI首页）"
                );
                let search = location.search;
                // 把from之后的参数都删掉
                const delIndex = search.indexOf("&type");
                search = search.substring(0, delIndex);
                console.log(search);
                const url =
                    location.protocol +
                    "//" +
                    location.hostname +
                    ":" +
                    location.port +
                    location.pathname +
                    search;

                setTimeout(() => { window.location.href = url; }, 300);
            }
        }
    }
};
</script>

<style lang="scss">
.bg {
    position: fixed;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 1000px;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-position: center 0;
    .meteor {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}

.bgs {
    position: fixed;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #05032a
        url(https://photo.zastatic.com/images/common-cms/it/20220630/1656572895289_234229_t.png)
        repeat-y;
}

.nav {
    position: relative;
    z-index: 99;
    display: flex;
    align-items: center;
    height: 72px;
    margin: 8px 32px 40px 32px;
    .back {
        width: 72px;
        height: 72px;
        background: url(./assets/images/arrow-back.png) no-repeat;
        background-size: 100% 100%;
        margin-right: auto;
        color: #fff;
        font-size: 30px;
        &-hide {
            opacity: 0;
        }
    }
}
</style>
