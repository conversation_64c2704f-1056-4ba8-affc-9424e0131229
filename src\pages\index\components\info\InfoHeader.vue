<template>
    <!-- CMS配置的头图 -->
    <img
        v-if="cmsConfig.schemeType"
        class="info-header-wrapper"
        :src="
            cmsConfig.schemeType === 1
                ? cmsConfig.downloadImg
                : cmsConfig.formWecahtRecHead
        "
    />
</template>

<script>
import { mapState } from "vuex";

export default {
    components: {},
    data() {
        return {};
    },
    computed: {
        ...mapState(["cmsConfig"])
    },
    created() {},
    mounted() {
        // 打桩
        const { schemeType } = this.cmsConfig;
        const description = [2, 10, 11].includes(schemeType)
            ? "H5添加企微页-访问"
            : "翻牌下载页访问";
        this.$report(5, description, {
            ext16: 1, // 1 投放版 2 达人版
            ext18: this.$route.path === "/info" ? 1 : 2 // 1 无盲盒 2 有盲盒
        });
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.info-header-wrapper {
    width: 750px;
}
</style>
